[tool:pytest]
DJANGO_SETTINGS_MODULE = src.settings.test
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --tb=short
    --disable-warnings
    --reuse-db
    -v
testpaths = tests
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    region_filtering: marks tests related to region filtering functionality
    api: marks tests that make API calls
    widget: marks tests for widget functionality
    finder_v2: marks tests specific to finder-v2 widget

# Django-specific configuration
django_find_project = true 