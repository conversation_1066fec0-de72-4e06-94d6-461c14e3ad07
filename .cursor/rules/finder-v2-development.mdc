---
description: 
globs: 
alwaysApply: false
---
# Finder-v2 Widget Development Guide

## Architecture Overview

Finder-v2 is a modern Vue 3 + TailwindCSS widget that replaces the legacy LESS-based finder widget.

## Key Development Files

### Backend (Django)
- **API proxy**: [src/apps/widgets/api_proxy/views.py](mdc:src/apps/widgets/api_proxy/views.py) - Contains `FinderV2WidgetProxyView` with parameter normalization
- **Widget type**: [src/apps/widgets/finder_v2/widget_type.py](mdc:src/apps/widgets/finder_v2/widget_type.py) - Widget registration and configuration
- **Forms**: [src/apps/widgets/finder_v2/forms.py](mdc:src/apps/widgets/finder_v2/forms.py) - Configuration forms
- **Default config**: [src/apps/widgets/finder_v2/default_config/config.py](mdc:src/apps/widgets/finder_v2/default_config/config.py) - Default widget settings

### Frontend (Vue 3)
- **Vue app root**: [src/apps/widgets/finder_v2/app/](mdc:src/apps/widgets/finder_v2/app) - Vue 3 application source
- **Main store**: [src/apps/widgets/finder_v2/app/src/stores/finder.js](mdc:src/apps/widgets/finder_v2/app/src/stores/finder.js) - Pinia store with API calls
- **Static build**: [src/apps/widgets/finder_v2/static/finder_v2/](mdc:src/apps/widgets/finder_v2/static/finder_v2) - Built Vue app assets

## Deployment Commands

### Vue.js Build and Deploy
```bash
# Full deployment (build + copy + restart)
./deploy-finder-v2.sh

# Manual build process
docker exec ws_services bash -c "cd src/apps/widgets/finder_v2/app && npm run build"
cp -R src/apps/widgets/finder_v2/app/dist/* src/apps/widgets/finder_v2/static/finder_v2/
```

## Critical Bug Fix: Region Filtering

### Issue
- Frontend sent `region[]=usdm&region[]=cdm` 
- Backend API proxy didn't normalize `region[]` → `region`
- External API ignored bracketed parameters, returned all regions

### Solution
- **Backend**: Parameter normalization in [src/apps/widgets/api_proxy/views.py](mdc:src/apps/widgets/api_proxy/views.py)
- **Frontend**: Custom serializer in [src/apps/widgets/finder_v2/app/src/stores/finder.js](mdc:src/apps/widgets/finder_v2/app/src/stores/finder.js)
- **Tests**: Regression tests in [tests/widget/finder_v2/test_region_filtering.py](mdc:tests/widget/finder_v2/test_region_filtering.py)

## API Integration

### Parameter Normalization Pattern
```python
# Strip [] suffixes from parameter names
normalized_params = {}
for key, value in request.query_params.items():
    clean_key = key.rstrip('[]')
    normalized_params[clean_key] = value
```

### Frontend API Calls
```javascript
// Custom URLSearchParams serializer for arrays
const params = new URLSearchParams()
regionFilters.forEach(region => params.append('region', region))
// Results in: region=usdm&region=cdm (not region[]=usdm)
```

## Testing

### Run Finder-v2 Tests
```bash
# Region filtering tests (critical)
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py

# All finder-v2 tests
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/
```

## Development Workflow

1. **Backend changes**: Modify Django files, restart if needed
2. **Frontend changes**: Edit Vue files in `app/src/`, run build, copy static files
3. **Test changes**: Run relevant tests before deployment
4. **Deploy**: Use `./deploy-finder-v2.sh` for production deployment

## Key Differences from Legacy Finder

- **No LESS compilation**: Uses TailwindCSS instead
- **Vue 3 + Pinia**: Modern reactive state management
- **API parameter normalization**: Handles array parameters correctly
- **Isolated testing**: Clean test environment without LESS errors

