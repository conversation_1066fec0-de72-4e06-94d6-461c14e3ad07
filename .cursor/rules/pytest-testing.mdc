---
description: 
globs: 
alwaysApply: false
---
# Pytest Testing Guide

## Test Commands

### Running Tests
```bash
# Run specific test file
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py

# Run all tests in directory
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/

# Alternative test runner (for future pytest integration)
docker compose exec web bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py
```

## Test Infrastructure Files

- **Main test runner**: [scripts/run_tests.sh](mdc:scripts/run_tests.sh) - Working test runner with LESS compilation disabled
- **Alternative runner**: [scripts/test_runner.sh](mdc:scripts/test_runner.sh) - For future pytest integration
- **Test settings**: [src/settings/test.py](mdc:src/settings/test.py) - Optimized Django settings for testing
- **Pytest config**: [pytest.ini](mdc:pytest.ini) - Pytest configuration with Django integration

## Test Documentation

- **Usage guide**: [docs/development/pytest-usage-guide.md](mdc:docs/development/pytest-usage-guide.md) - Complete daily testing guide
- **Quick start**: [docs/development/pytest-quick-start.md](mdc:docs/development/pytest-quick-start.md) - 30-minute setup
- **Integration plan**: [docs/development/pytest-docker-integration-plan.md](mdc:docs/development/pytest-docker-integration-plan.md) - Future enhancements

## Working Test Examples

- **Region filtering**: [tests/widget/finder_v2/test_region_filtering.py](mdc:tests/widget/finder_v2/test_region_filtering.py) - ✅ Validated working tests
- **Widget type**: [tests/widget/finder_v2/test_widget_type.py](mdc:tests/widget/finder_v2/test_widget_type.py) - Database test example

## Test Patterns

### Django TestCase for Database Tests
```python
from django.test import TestCase
from src.apps.widgets.common.models.config import WidgetConfig

class WidgetTest(TestCase):
    def setUp(self):
        # Create test data
        pass
    
    def test_functionality(self):
        # Test implementation
        pass
```

### SimpleTestCase for Logic Tests
```python
from django.test import SimpleTestCase

class LogicTest(SimpleTestCase):
    def test_parameter_normalization(self):
        # Test business logic without database
        pass
```

## Key Testing Commands

- **Before commits**: Run relevant widget tests
- **After bug fixes**: Run specific regression tests  
- **Before deployment**: Run all widget tests

## LESS Compilation Fix

Tests run with `DISABLE_LESS_COMPILATION=true` to eliminate CSS compilation errors during testing. Production LESS compilation is unaffected.

