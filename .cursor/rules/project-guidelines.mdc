---
description: 
globs: 
alwaysApply: true
---
# Project Guidelines (Curated)

## Core Development Practices
- Follow current Django best practices for upgrades, including security improvements and modernized settings configurations.
- Use Docker for containerization and Poetry for Python project management.
- Use `development.local` host for local development, not `localhost`.
- Create proper forks and publish to private PyPI; do not patch site-packages directly. Follow WS package upgrade pattern (v1.x → v2.0.0).
- Prefer concise documentation with only key information. Provide detailed GRUNT build process documentation and comprehensive Python code comments for finder-related functions/classes.
- Avoid installing RequireJS for widget JavaScript. Find alternative solutions compatible with production.
- Modify WS packages directly when needed for bug fixes rather than implementing workarounds.
- Ensure 100% backward compatibility for existing client widget installations during Django upgrades. Backend changes should not affect client-side JavaScript.
- Use published versions from private PyPI for WS packages, not local development versions.
- Implement configurable CSRF hostname validation that works across development (with ports) and production environments. Verify `services.wheel-size.com` compatibility.
- Move detailed comments from security-sensitive JavaScript files (e.g., CSRF token generation in [app-config-token.js](mdc:src/apps/core/static/js/app-config-token.js)) to separate markdown files in the same folder for security.
- Prefer the legacy CSRF token algorithm in [app-config-token.js](mdc:src/apps/core/static/js/app-config-token.js) as it's considered more secure and works in production.
- Use the minified version ([app-config-token.min.js](mdc:src/apps/core/static/js/app-config-token.min.js)) in [Gruntfile.js](mdc:Gruntfile.js) configuration and rebuild the general JavaScript file for production.
- Minified JavaScript files should behave identically to their source files in Grunt build processes.
- Maintain an organized project structure: `tests/` and `docs/` directories, consistent file naming, comprehensive `README.md`, keep test/doc files in git. Specific folders: `docs/security/` and `docs/upgrade/`.
- Use `ws-django-tire-calc` version 2.0.0 (not 2.0.2).
- Consider TailwindCSS v4.1 for frontend styling; prefer systematic upgrades with documentation analysis if proceeding.
- Implement public widget creation interfaces for new widget types (like finder-v2) following existing patterns, allowing anonymous and registered user access without admin rights.


## Project State & Key Documentation
- System uses Docker, Poetry, Django 4.2, PostgreSQL 15. Admin: `http://development.local:8000/admin/`.
- Key documentation:
    - [docs/upgrade/upgrade-plan.md](mdc:docs/upgrade/upgrade-plan.md)
    - @docs/upgrade/production-deployment-guide.md
    - [docs/development/finder-v2-master-implementation-plan.md](mdc:docs/development/finder-v2-master-implementation-plan.md) (Dev dir for feature plans, upgrade dir for Django upgrade docs)
- Update @docs/upgrade/production-deployment-guide.md when Django migrations are applied.
- Document API testing procedures, including CSRF token testing commands, in markdown files for widget endpoints.

## Common Troubleshooting & Critical Info
- **CSRF:**
    - Prefer the legacy CSRF token algorithm in [app-config-token.js](mdc:src/apps/core/static/js/app-config-token.js).
    - Production uses legacy CSRF token algorithm in [app-config-token.min.js](mdc:src/apps/core/static/js/app-config-token.min.js) (works). Current algorithm fails in dev (port issues post-upgrade).
    - Legacy and current CSRF algorithms differ mathematically but may yield identical results for typical browser User-Agent strings. They differ for shorter inputs.
    - Primary CSRF issue post-upgrade was port mismatch in hostname validation (`development.local` vs `development.local:8000`), not build or algorithm differences.
    - `WIDGET_CSRF_SETTINGS` configuration might be missing from [src/settings/dev_docker.py](mdc:src/settings/dev_docker.py).
- **API & Routing:**
    - Widget embedding auth errors: often due to domains in `WS_DOMAINS_BLACK_LIST` (Live Settings in Django admin). Middleware/view logic validates domain permissions.
    - Production API access (`REST_PROXY` in [src/settings/aws_prod.py](mdc:src/settings/aws_prod.py)): `HOST: 'https://api3.wheel-size.com/v1'`, `X-WS-API-SECRET-TOKEN`, `X-AMZN-TRACE-ID` headers.
    - `REST_PROXY HOST` in [src/settings/dev_docker.py](mdc:src/settings/dev_docker.py) is `'https://api3.wheel-size.com'` (no `/v1`), affecting widget proxy view routing.
    - Production API auth: `X-WS-API-SECRET-TOKEN` header (value `uJnxEaznliaMfXIy`), `Host` header `api3.wheel-size.com`, routed via AWS Load Balancer HTTPS:443.
    - Finder widget (v1) API endpoints (e.g., `/v1/makes/`, `/v1/__rd/`). Calc widget (v2) API (`/v2/upsteps/`). Requires version-specific routing in `WidgetProxyView`, shared auth headers.
- **Build & Deployment:**
    - Calc widget Grunt build: concatenates/uglifies JS in specific order. `utils.js` (with Collapse factory) loaded early. `calc-app.js` is minified concatenation.
    - After code fixes in development: always restart Django/Docker containers.
- **Debugging Tips:**
    - Widget API routing: check cached JS, template URL generation, test endpoints directly, browser dev tools (network tab for actual request URLs/headers).
    - Browser-based widget debugging: check actual request URLs in dev tools Network tab, verify JS URL generation, ensure cache invalidation.

## Widget Security Model
- Same-origin iframe previews in widget config pages should always work.
- Cross-domain embedding controlled by authorized domains list in widget config.
- Security validation must distinguish same-origin portal previews (always allowed) vs. cross-domain client embedding (domain-restricted).

## Finder-v2 Widget Development
- Prioritize make/model/year API endpoints over tire/rim search. v2 API structure differs from v1. Reference [docs/api/wheel-fitment-api-v2.md](mdc:docs/api/wheel-fitment-api-v2.md).
- Vue.js deployment:
    1. Build: `docker exec ws_services bash -c "cd src/apps/widgets/finder_v2/app && npm run build"`
    2. Copy static files: `cp -R src/apps/widgets/finder_v2/app/dist/* src/apps/widgets/finder_v2/static/finder_v2/`
- Prefer automated shell scripts for Vue.js dev workflow (Docker npm build, static file copy, Django server restart).
- Automated deployment scripts for finder-v2 Vue.js dev exist: e.g., [./deploy-finder-v2.sh](mdc:deploy-finder-v2.sh) and a one-liner command.
