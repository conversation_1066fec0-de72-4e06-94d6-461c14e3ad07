---
description: Use when CSS styles are used
globs: 
alwaysApply: false
---
- Implement proper Tailwind CSS purging for production builds.
- Use Tailwind's @layer directive for custom styles.
- Implement utility-first CSS approach.
- Follow Tailwind naming conventions.
- Implement Tailwind CSS classes for styling.
- Utilize @apply directive in CSS files for reusable styles.
- Implement responsive design using Tailwind's responsive classes.
- Use Tailwind's configuration file for customization.
- Implement Tailwind CSS purging for production builds.