# Design Document

## Overview

This design document outlines the comprehensive migration from Google reCAPTCHA to Cloudflare Turnstile in the wheel-size-services Django application. The solution includes replacing the existing CAPTCHA implementation, extending protection to login forms, implementing additional anti-spam measures, and ensuring robust error handling and fallback mechanisms.

The current implementation uses `django-recaptcha` package with reCAPTCHA only on the registration form. We will create a custom Turnstile integration that provides better spam protection while maintaining the existing Django authentication patterns.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Registration/Login] --> B[Django Form Validation]
    B --> C[Turnstile Validation]
    C --> D[Rate Limiting Check]
    D --> E[Additional Anti-Spam Validation]
    E --> F[Email Verification]
    F --> G[User Account Creation/Authentication]
    
    C --> H[Turnstile API]
    H --> I[Cloudflare Servers]
    
    J[Fallback Mechanism] --> B
    K[Error Handling] --> A
    
    L[Configuration Management] --> C
    L --> D
    L --> E
```

### Component Integration

The solution integrates with existing Django components:

- **Django Forms**: Custom form fields and widgets for Turnstile
- **Django Authentication**: Extends existing registration and login flows
- **Django Middleware**: Rate limiting and security enhancements
- **Django Settings**: Environment-based configuration management
- **Django Templates**: Updated UI components for Turnstile rendering

## Components and Interfaces

### 1. Turnstile Integration Components

#### TurnstileField (Custom Form Field)
```python
class TurnstileField(forms.Field):
    """Custom Django form field for Cloudflare Turnstile validation"""
    
    def __init__(self, site_key=None, secret_key=None, **kwargs):
        # Field initialization with Turnstile-specific parameters
        
    def validate(self, value):
        # Server-side validation against Cloudflare API
        
    def widget_attrs(self, widget):
        # HTML attributes for Turnstile widget rendering
```

#### TurnstileWidget (Custom Form Widget)
```python
class TurnstileWidget(forms.Widget):
    """Custom Django widget for rendering Turnstile challenge"""
    
    def render(self, name, value, attrs=None, renderer=None):
        # Renders HTML div with Turnstile configuration
        
    def media(self):
        # Returns CSS/JS media files needed for Turnstile
```

#### TurnstileValidator (Validation Service)
```python
class TurnstileValidator:
    """Service class for Turnstile server-side validation"""
    
    def validate_response(self, response_token, remote_ip=None):
        # Validates Turnstile response with Cloudflare API
        
    def handle_api_errors(self, response):
        # Handles various API error conditions
```

### 2. Enhanced Authentication Forms

#### Enhanced Registration Form
```python
class EnhancedRegistrationForm(RegistrationFormUniqueEmail):
    """Extended registration form with Turnstile and anti-spam measures"""
    
    turnstile = TurnstileField()
    honeypot = forms.CharField(required=False, widget=forms.HiddenInput())
    
    def clean_email(self):
        # Email domain validation against disposable providers
        
    def clean_turnstile(self):
        # Turnstile response validation
        
    def clean(self):
        # Form timing validation and honeypot check
```

#### Enhanced Login Form
```python
class EnhancedAuthenticationForm(AuthenticationForm):
    """Extended login form with Turnstile protection"""
    
    turnstile = TurnstileField()
    
    def clean(self):
        # Combined authentication and Turnstile validation
```

### 3. Rate Limiting Middleware

#### RegistrationRateLimitMiddleware
```python
class RegistrationRateLimitMiddleware:
    """Middleware for rate limiting registration attempts"""
    
    def __init__(self, get_response):
        # Initialize with configurable rate limits
        
    def __call__(self, request):
        # Check and enforce rate limits per IP address
        
    def is_rate_limited(self, ip_address):
        # Check if IP has exceeded rate limits
        
    def record_attempt(self, ip_address):
        # Record registration attempt for rate limiting
```

### 4. Anti-Spam Validation Services

#### EmailDomainValidator
```python
class EmailDomainValidator:
    """Service for validating email domains against spam lists"""
    
    def is_disposable_email(self, email_domain):
        # Check against known disposable email providers
        
    def is_suspicious_pattern(self, email):
        # Pattern-based spam detection
```

#### FormTimingValidator
```python
class FormTimingValidator:
    """Service for detecting automated form submissions"""
    
    def validate_submission_timing(self, form_start_time):
        # Check if form was submitted too quickly
        
    def generate_timing_token(self):
        # Generate timestamp token for form timing validation
```

### 5. Configuration Management

#### TurnstileConfig
```python
class TurnstileConfig:
    """Configuration management for Turnstile settings"""
    
    @property
    def site_key(self):
        # Environment-specific site key
        
    @property
    def secret_key(self):
        # Environment-specific secret key
        
    @property
    def is_enabled(self):
        # Feature flag for Turnstile activation
        
    def get_fallback_behavior(self):
        # Fallback configuration when Turnstile is unavailable
```

## Data Models

### Rate Limiting Storage

#### RegistrationAttempt Model
```python
class RegistrationAttempt(models.Model):
    """Model for tracking registration attempts for rate limiting"""
    
    ip_address = models.GenericIPAddressField()
    timestamp = models.DateTimeField(auto_now_add=True)
    success = models.BooleanField(default=False)
    user_agent = models.TextField(blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['ip_address', 'timestamp']),
        ]
```

### Enhanced User Registration Tracking

#### SpamAttempt Model
```python
class SpamAttempt(models.Model):
    """Model for tracking and analyzing spam attempts"""
    
    ip_address = models.GenericIPAddressField()
    email_attempted = models.EmailField()
    attempt_type = models.CharField(max_length=50)  # 'registration', 'login'
    blocked_reason = models.CharField(max_length=100)
    timestamp = models.DateTimeField(auto_now_add=True)
    user_agent = models.TextField(blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['attempt_type', 'timestamp']),
        ]
```

## Error Handling

### Error Handling Strategy

#### Turnstile API Errors
- **Network Timeouts**: Graceful degradation with configurable fallback
- **Invalid Keys**: Clear error messages in development, silent fallback in production
- **Rate Limiting**: Exponential backoff and retry logic
- **Service Unavailable**: Temporary bypass with logging for monitoring

#### User Experience Errors
- **Challenge Failure**: Clear instructions for retry
- **Network Issues**: Fallback messaging with alternative contact methods
- **Rate Limiting**: Informative waiting period messages
- **Validation Errors**: Specific, actionable error messages

#### Implementation Pattern
```python
class TurnstileErrorHandler:
    """Centralized error handling for Turnstile integration"""
    
    def handle_api_error(self, error_type, context):
        # Route different error types to appropriate handlers
        
    def get_user_message(self, error_type):
        # Return user-friendly error messages
        
    def should_fallback(self, error_type, environment):
        # Determine if fallback behavior should activate
        
    def log_error(self, error, context):
        # Structured logging for monitoring and debugging
```

## Testing Strategy

### Unit Testing

#### Form Field Testing
- Turnstile field validation with various response scenarios
- Widget rendering with different configuration options
- Error handling for malformed responses

#### Validation Service Testing
- Email domain validation against known spam lists
- Form timing validation with edge cases
- Rate limiting logic with concurrent requests

#### Configuration Testing
- Environment variable handling
- Fallback behavior activation
- Feature flag functionality

### Integration Testing

#### Authentication Flow Testing
- Complete registration flow with Turnstile validation
- Login flow with CAPTCHA protection
- Error scenarios and fallback mechanisms

#### Anti-Spam Testing
- Rate limiting enforcement across multiple IPs
- Email validation with various domain types
- Honeypot field effectiveness

#### API Integration Testing
- Turnstile API communication
- Error response handling
- Network timeout scenarios

### End-to-End Testing

#### User Experience Testing
- Registration form completion with Turnstile
- Login form with CAPTCHA challenges
- Error message display and user guidance
- Mobile and desktop compatibility

#### Performance Testing
- Form load times with Turnstile integration
- API response time impact
- Rate limiting performance under load

### Security Testing

#### CAPTCHA Bypass Testing
- Automated submission attempts
- Token manipulation attempts
- Replay attack prevention

#### Rate Limiting Testing
- IP-based rate limiting effectiveness
- Distributed attack simulation
- Rate limit bypass attempts

## Implementation Phases

### Phase 1: Core Turnstile Integration
1. Create custom Turnstile form field and widget
2. Implement server-side validation service
3. Update registration form with Turnstile field
4. Configure environment variables and settings

### Phase 2: Login Form Protection
1. Create enhanced authentication form
2. Update login template with Turnstile widget
3. Implement login-specific validation logic
4. Test authentication flow integration

### Phase 3: Anti-Spam Enhancements
1. Implement rate limiting middleware
2. Create email domain validation service
3. Add form timing validation
4. Implement honeypot field protection

### Phase 4: Error Handling and Monitoring
1. Implement comprehensive error handling
2. Create fallback mechanisms
3. Add logging and monitoring
4. Implement user-friendly error messages

### Phase 5: Testing and Deployment
1. Comprehensive testing suite implementation
2. Performance optimization
3. Security validation
4. Production deployment with monitoring

## Security Considerations

### Data Protection
- Turnstile response tokens are not stored or logged
- User IP addresses are hashed for rate limiting storage
- Sensitive configuration data uses environment variables
- CSRF protection maintained for all forms

### Attack Prevention
- Rate limiting prevents brute force attempts
- Honeypot fields catch automated submissions
- Form timing validation detects bot behavior
- Email domain validation blocks disposable addresses

### Privacy Compliance
- Minimal data collection for anti-spam purposes
- Clear privacy policy updates for Turnstile usage
- User consent handling for CAPTCHA challenges
- Data retention policies for rate limiting data

## Performance Considerations

### Frontend Performance
- Asynchronous Turnstile script loading
- Minimal impact on page load times
- Graceful degradation for slow connections
- Mobile-optimized CAPTCHA rendering

### Backend Performance
- Efficient rate limiting with database indexing
- Cached email domain validation lists
- Optimized API calls to Turnstile service
- Background processing for spam attempt logging

### Scalability
- Horizontal scaling support for rate limiting
- Database optimization for high-volume registration
- CDN integration for Turnstile assets
- Load balancer compatibility

## Monitoring and Analytics

### Key Metrics
- CAPTCHA completion rates
- Spam attempt blocking effectiveness
- User experience impact measurements
- API response time monitoring

### Alerting
- High spam attempt volume alerts
- Turnstile API availability monitoring
- Rate limiting threshold breach notifications
- Error rate spike detection

### Reporting
- Daily spam blocking reports
- User registration success rate analysis
- Performance impact assessments
- Security incident documentation