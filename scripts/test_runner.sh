#!/bin/bash
# Test runner script for Docker environment

set -e

echo "🧪 Running tests in Docker..."

# Configuration
PYTHON_PATH="/root/.pyenv/versions/3.12.0/bin/python"
DJANGO_SETTINGS="src.settings.test"
PROJECT_PATH="/code"

# Set environment variables
export DJANGO_SETTINGS_MODULE=$DJANGO_SETTINGS
export PYTHONPATH=$PROJECT_PATH:$PROJECT_PATH/src

# Change to project directory
cd $PROJECT_PATH

# Run tests with proper configuration
echo "📍 Running tests with settings: $DJANGO_SETTINGS"
echo "📍 Python path: $PYTHONPATH"
echo "📍 Working directory: $(pwd)"

# Execute pytest with all arguments passed through
$PYTHON_PATH -m pytest "$@" 