#!/bin/bash

# TailwindCSS Build Script for Docker Integration
# Usage: ./scripts/build-tailwind.sh [portal|finder-v2|all] [options]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# Production mode flag
PRODUCTION_MODE=false

# Function to parse options
parse_options() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --production)
                PRODUCTION_MODE=true
                shift
                ;;
            *)
                # Unknown option, break
                break
                ;;
        esac
    done
}

# Function to build portal CSS
build_portal() {
    log_info "Building Portal TailwindCSS..."
    
    cd src/apps/portal
    
    if [ ! -f "package.json" ]; then
        log_error "Portal package.json not found!"
        exit 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        log_info "Installing Portal dependencies..."
        npm install
    fi
    
    # Build CSS based on mode
    if [ "$PRODUCTION_MODE" = true ]; then
        log_info "Building production CSS (minified)..."
        npm run build-css
        
        # Verify production output
        if [ -f "static/portal/css/tailwind.min.css" ]; then
            SIZE=$(du -h static/portal/css/tailwind.min.css | cut -f1)
            log_success "Portal production CSS built successfully (${SIZE})"
        else
            log_error "Portal production CSS build failed - output file not found"
            exit 1
        fi
    else
        log_info "Building debug CSS (unminified)..."
        npm run debug-css
        
        # Verify debug output
        if [ -f "static/portal/css/tailwind-debug.css" ]; then
            SIZE=$(du -h static/portal/css/tailwind-debug.css | cut -f1)
            log_success "Portal debug CSS built successfully (${SIZE})"
        else
            log_error "Portal debug CSS build failed - output file not found"
            exit 1
        fi
    fi
    
    cd "$PROJECT_ROOT"
}

# Function to build finder-v2 CSS
build_finder_v2() {
    log_info "Building Finder-v2 Vue app..."
    
    cd src/apps/widgets/finder_v2/app
    
    if [ ! -f "package.json" ]; then
        log_error "Finder-v2 package.json not found!"
        exit 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        log_info "Installing Finder-v2 dependencies..."
        npm install
    fi
    
    # Build Vue app
    if [ "$PRODUCTION_MODE" = true ]; then
        log_info "Building Finder-v2 for production..."
        npm run build
    else
        log_info "Building Finder-v2 for development..."
        npm run build
    fi
    
    # Copy static files
    if [ -d "dist" ]; then
        log_info "Copying Finder-v2 static files..."
        cp -R dist/* ../static/finder_v2/ 2>/dev/null || log_warning "Some static files may not have been copied"
        log_success "Finder-v2 built and static files copied"
    else
        log_error "Finder-v2 build failed - dist directory not found"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# Function to run Django collectstatic (if in Docker)
run_collectstatic() {
    if [ "$PRODUCTION_MODE" = true ]; then
        log_info "Running Django collectstatic..."
        
        # Check if we're in a Docker environment
        if [ -f "docker-compose.yml" ] && command -v docker-compose >/dev/null 2>&1; then
            docker-compose exec web python manage.py collectstatic --noinput
            log_success "Django collectstatic completed"
        elif command -v python >/dev/null 2>&1; then
            python manage.py collectstatic --noinput
            log_success "Django collectstatic completed"
        else
            log_warning "Could not run collectstatic - Django not found"
        fi
    fi
}

# Function to run build statistics
show_build_stats() {
    log_info "Build Statistics"
    
    # Portal stats
    if [ "$PRODUCTION_MODE" = true ]; then
        if [ -f "src/apps/portal/static/portal/css/tailwind.min.css" ]; then
            PORTAL_SIZE=$(du -h src/apps/portal/static/portal/css/tailwind.min.css | cut -f1)
            echo -e "  Portal CSS (production): ${GREEN}$PORTAL_SIZE${NC}"
        fi
    else
        if [ -f "src/apps/portal/static/portal/css/tailwind-debug.css" ]; then
            PORTAL_SIZE=$(du -h src/apps/portal/static/portal/css/tailwind-debug.css | cut -f1)
            echo -e "  Portal CSS (debug): ${GREEN}$PORTAL_SIZE${NC}"
        fi
    fi
    
    # Finder-v2 stats
    if [ -d "src/apps/widgets/finder_v2/static/finder_v2" ]; then
        FINDER_SIZE=$(du -sh src/apps/widgets/finder_v2/static/finder_v2 | cut -f1)
        echo -e "  Finder-v2 static files: ${GREEN}$FINDER_SIZE${NC}"
    fi
    
    # Template count
    TEMPLATE_COUNT=$(find src/templates -name "*.html" 2>/dev/null | wc -l)
    echo -e "  Template files scanned: ${GREEN}$TEMPLATE_COUNT${NC}"
}

# Function to build all
build_all() {
    if [ "$PRODUCTION_MODE" = true ]; then
        log_info "Building all TailwindCSS assets for production..."
    else
        log_info "Building all TailwindCSS assets for development..."
    fi
    
    build_portal
    build_finder_v2
    
    if [ "$PRODUCTION_MODE" = true ]; then
        run_collectstatic
    fi
    
    show_build_stats
    log_success "All builds completed successfully!"
}

# Function to show help
show_help() {
    echo "TailwindCSS Build Script for Docker Integration"
    echo ""
    echo "Usage: $0 [target] [options]"
    echo ""
    echo "Targets:"
    echo "  portal     - Build only portal CSS"
    echo "  finder-v2  - Build only finder-v2 Vue app"
    echo "  all        - Build everything (default)"
    echo ""
    echo "Options:"
    echo "  --production  - Build for production (minified CSS, run collectstatic)"
    echo ""
    echo "Examples:"
    echo "  $0 portal                    # Build portal CSS (debug mode)"
    echo "  $0 portal --production       # Build portal CSS (production mode)"
    echo "  $0 all --production          # Build everything for production"
    echo ""
    echo "Docker Integration:"
    echo "  docker exec ws_services ./scripts/build-tailwind.sh all --production"
    echo ""
    echo "Development vs Production:"
    echo "  Development: Builds debug CSS, skips collectstatic"
    echo "  Production:  Builds minified CSS, runs collectstatic"
}

# Parse command line options
parse_options "$@"

# Remove processed options from arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --production)
            shift
            ;;
        *)
            break
            ;;
    esac
done

# Main script logic
case "$1" in
    "portal")
        build_portal
        ;;
    "finder-v2")
        build_finder_v2
        ;;
    "all"|"")
        build_all
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        log_error "Unknown target: $1"
        show_help
        exit 1
        ;;
esac

if [ "$PRODUCTION_MODE" = true ]; then
    log_success "Production build script completed successfully!"
else
    log_success "Development build script completed successfully!"
fi 