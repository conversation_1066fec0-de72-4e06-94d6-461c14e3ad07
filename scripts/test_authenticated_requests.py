#!/usr/bin/env python3
"""
Authenticated Testing Script for Wheel-Size Services

This script provides automated testing capabilities for authenticated endpoints
in the wheel-size-services Django application. It handles user creation,
authentication, CSRF token management, and session handling automatically.

Usage:
    python scripts/test_authenticated_requests.py
    
Or import as a module:
    from scripts.test_authenticated_requests import AuthenticatedTester
    
Example:
    tester = AuthenticatedTester()
    tester.setup_test_user()
    tester.login()
    
    # Test GET request
    response = tester.get('/widget/finder-v2/config/')
    
    # Test form submission
    form_data = {'config-name': 'Test Config', 'config-lang': 'en'}
    response = tester.post_form('/widget/finder-v2/config/', form_data)
"""

import requests
import re
import json
import sys
from urllib.parse import urljoin
from typing import Dict, Optional, Tuple, Any


class AuthenticatedTester:
    """
    Handles authenticated requests to wheel-size-services Django application.
    
    Provides automatic user creation, authentication, CSRF token management,
    and session handling for testing protected endpoints.
    """
    
    def __init__(self, base_url: str = "http://development.local:8000"):
        """
        Initialize the authenticated tester.
        
        Args:
            base_url: Base URL for the Django application
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.username = 'testuser'
        self.password = 'testpass123'
        self.email = '<EMAIL>'
        self.is_authenticated = False
        
        # Common URLs
        self.login_url = urljoin(self.base_url, "/admin/login/")
        self.admin_url = urljoin(self.base_url, "/admin/")
        
    def setup_test_user(self) -> bool:
        """
        Create or update test user with superuser privileges.
        
        Returns:
            bool: True if user setup was successful
        """
        print("🔧 Setting up test user...")
        
        try:
            # Use Django shell to create/update user
            import subprocess
            
            django_command = f'''
from django.contrib.auth.models import User
from django.contrib.auth import authenticate

# Check if test user exists
try:
    user = User.objects.get(username='{self.username}')
    print(f'User exists: {{user.username}}')
    user_created = False
except User.DoesNotExist:
    # Create test user
    user = User.objects.create_user(
        username='{self.username}', 
        password='{self.password}', 
        email='{self.email}'
    )
    print(f'Created user: {{user.username}}')
    user_created = True

# Ensure user has required permissions
user.is_superuser = True
user.is_staff = True
user.save()
print(f'Updated user permissions: superuser={{user.is_superuser}}, staff={{user.is_staff}}')

# Test authentication
auth_user = authenticate(username='{self.username}', password='{self.password}')
print(f'Authentication test: {{auth_user is not None}}')
'''
            
            result = subprocess.run([
                'docker', 'exec', 'ws_services', 'bash', '-c',
                f'cd /code && /root/.pyenv/versions/3.12.0/bin/python manage.py shell -c "{django_command}"'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Test user setup completed successfully")
                print(f"   Username: {self.username}")
                print(f"   Password: {self.password}")
                return True
            else:
                print(f"❌ Failed to setup test user: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Timeout while setting up test user")
            return False
        except Exception as e:
            print(f"❌ Error setting up test user: {e}")
            return False
    
    def get_csrf_token(self, url: str) -> Optional[str]:
        """
        Extract CSRF token from a page.
        
        Args:
            url: URL to get CSRF token from
            
        Returns:
            str: CSRF token if found, None otherwise
        """
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            match = re.search(r'name="csrfmiddlewaretoken" value="([^"]*)"', response.text)
            return match.group(1) if match else None
            
        except Exception as e:
            print(f"❌ Error getting CSRF token from {url}: {e}")
            return None
    
    def login(self) -> bool:
        """
        Authenticate with the Django application.
        
        Returns:
            bool: True if login was successful
        """
        print("🔐 Logging in...")
        
        # Get CSRF token from login page
        csrf_token = self.get_csrf_token(self.login_url)
        if not csrf_token:
            print("❌ Failed to get CSRF token for login")
            return False
        
        # Submit login form
        login_data = {
            'csrfmiddlewaretoken': csrf_token,
            'username': self.username,
            'password': self.password,
            'next': '/admin/',
        }
        
        try:
            response = self.session.post(self.login_url, data=login_data, allow_redirects=False)
            
            if response.status_code == 302:
                print("✅ Login successful")
                self.is_authenticated = True
                return True
            else:
                print(f"❌ Login failed with status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error during login: {e}")
            return False
    
    def get(self, path: str, **kwargs) -> requests.Response:
        """
        Perform authenticated GET request.
        
        Args:
            path: URL path (relative to base_url)
            **kwargs: Additional arguments for requests.get()
            
        Returns:
            requests.Response: Response object
        """
        if not self.is_authenticated:
            raise RuntimeError("Not authenticated. Call login() first.")
        
        url = urljoin(self.base_url, path)
        return self.session.get(url, **kwargs)
    
    def post_form(self, path: str, form_data: Dict[str, Any], 
                  auto_csrf: bool = True, **kwargs) -> requests.Response:
        """
        Perform authenticated POST request with form data.
        
        Args:
            path: URL path (relative to base_url)
            form_data: Form data dictionary
            auto_csrf: Whether to automatically add CSRF token
            **kwargs: Additional arguments for requests.post()
            
        Returns:
            requests.Response: Response object
        """
        if not self.is_authenticated:
            raise RuntimeError("Not authenticated. Call login() first.")
        
        url = urljoin(self.base_url, path)
        
        # Automatically add CSRF token if requested
        if auto_csrf and 'csrfmiddlewaretoken' not in form_data:
            csrf_token = self.get_csrf_token(url)
            if csrf_token:
                form_data['csrfmiddlewaretoken'] = csrf_token
            else:
                print("⚠️  Warning: Could not get CSRF token for form submission")
        
        return self.session.post(url, data=form_data, **kwargs)
    
    def test_endpoint_access(self, path: str, expected_status: int = 200) -> bool:
        """
        Test if an endpoint is accessible with current authentication.
        
        Args:
            path: URL path to test
            expected_status: Expected HTTP status code
            
        Returns:
            bool: True if endpoint returned expected status
        """
        try:
            response = self.get(path)
            success = response.status_code == expected_status
            
            status_icon = "✅" if success else "❌"
            print(f"{status_icon} {path} → {response.status_code} (expected {expected_status})")
            
            return success
            
        except Exception as e:
            print(f"❌ Error testing {path}: {e}")
            return False


def test_widget_config_endpoints():
    """Test common widget configuration endpoints."""
    print("\n🧪 Testing Widget Configuration Endpoints")
    print("=" * 50)

    tester = AuthenticatedTester()

    # Setup and authenticate
    if not tester.setup_test_user():
        return False

    if not tester.login():
        return False

    # Test endpoints with expected status codes
    endpoints = [
        ("/admin/", 200),
        ("/widget/finder-v2/config/", 200),
        ("/widget/calc/config/", 200),  # Now working after fixing language translations
        ("/widget/finder/config/", 200),
    ]

    results = []
    for endpoint, expected_status in endpoints:
        success = tester.test_endpoint_access(endpoint, expected_status)
        results.append(success)

    # Summary
    passed = sum(results)
    total = len(results)
    print(f"\n📊 Results: {passed}/{total} endpoints returned expected status")

    return passed == total


def test_finder_v2_form_submission():
    """Test finder-v2 configuration form submission and redirect behavior."""
    print("\n🧪 Testing Finder-v2 Form Submission and Redirect")
    print("=" * 50)

    tester = AuthenticatedTester()

    # Setup and authenticate
    if not tester.setup_test_user():
        return False

    if not tester.login():
        return False

    # Test form submission
    form_data = {
        'config-name': 'Test Redirect Config',
        'config-lang': 'en',
        'theme-theme_name': 'light',
        'content-by': '',
        'content-regions': '[]',
        'content-brands': '[]',
        'content-brands_exclude': '[]',
        'content-only_oem': '',
        'interface-width': '600',
        'interface-height': '',
        'interface-tabs': 'by_vehicle',
        'interface-primary_tab': 'by_vehicle',
        'interface-button_to_ws': 'on',
        'interface-flow_type': 'primary',
        'permissions-domains': '["localhost", "development.local", "127.0.0.1"]',
    }

    try:
        response = tester.post_form('/widget/finder-v2/config/', form_data, allow_redirects=False)

        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print("✅ Form submitted successfully!")
            print(f"   Redirected to: {redirect_url}")

            # Check if redirect URL contains a UUID (indicating new widget instance)
            if '/widget/' in redirect_url and '/config/' in redirect_url:
                # Extract widget slug from redirect URL
                import re
                match = re.search(r'/widget/([^/]+)/config/', redirect_url)
                if match:
                    widget_slug = match.group(1)
                    if len(widget_slug) == 32:  # UUID hex string
                        print(f"✅ Redirected to new widget instance: {widget_slug}")

                        # Test that the new widget instance is accessible
                        widget_response = tester.get(f'/widget/{widget_slug}/config/')
                        if widget_response.status_code == 200:
                            print("✅ New widget instance is accessible")
                            return True
                        else:
                            print(f"❌ New widget instance not accessible: {widget_response.status_code}")
                            return False
                    elif widget_slug == 'finder-v2':
                        print("❌ Still redirecting to default config instead of new instance")
                        return False
                    else:
                        print(f"✅ Redirected to widget instance: {widget_slug}")
                        return True
                else:
                    print("❌ Could not extract widget slug from redirect URL")
                    return False
            else:
                print("❌ Redirect URL doesn't match expected pattern")
                return False
        else:
            print(f"❌ Form submission failed with status {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error during form submission: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Wheel-Size Services Authenticated Testing Script")
    print("=" * 60)
    
    # Run test suites
    endpoint_test = test_widget_config_endpoints()
    form_test = test_finder_v2_form_submission()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📋 Final Test Results:")
    print(f"   Endpoint Access Tests: {'✅ PASS' if endpoint_test else '❌ FAIL'}")
    print(f"   Form Submission Tests: {'✅ PASS' if form_test else '❌ FAIL'}")
    
    if endpoint_test and form_test:
        print("\n🎉 All tests passed! Authentication system is working correctly.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
