#!/usr/bin/env bash

# TailwindCSS DX Helper Script
# Provides quick toggles between development modes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage
show_usage() {
    echo "TailwindCSS DX Helper Script"
    echo
    echo "Usage: $0 {full|slim|build|status|help}"
    echo
    echo "Commands:"
    echo "  full     Start watch mode with full utilities (all Tailwind classes available)"
    echo "  slim     Start watch mode with curated safelist (current behavior)"
    echo "  build    Build production CSS with minification"
    echo "  status   Show current configuration and file status"
    echo "  help     Show this help message"
    echo
    echo "Examples:"
    echo "  $0 full    # Best for copy-pasting from Tailwind UI"
    echo "  $0 build   # Build optimized production CSS"
    echo "  $0 status  # Check which configs exist"
}

# Check status of configs and files
check_status() {
    log_info "TailwindCSS Configuration Status"
    echo
    
    # Check config files
    echo "Configuration Files:"
    if [[ -f "src/apps/portal/static/portal/css/tailwind.dev.js" ]]; then
        echo "  ✅ tailwind.dev.js (development config)"
    else
        echo "  ❌ tailwind.dev.js (missing)"
    fi
    
    if [[ -f "src/apps/portal/static/portal/css/tailwind.prod.js" ]]; then
        echo "  ✅ tailwind.prod.js (production config)"
    else
        echo "  ❌ tailwind.prod.js (missing)"
    fi
    
    echo
    echo "CSS Output Files:"
    if [[ -f "src/apps/portal/static/portal/css/tailwind-debug.css" ]]; then
        size=$(ls -lh src/apps/portal/static/portal/css/tailwind-debug.css | awk '{print $5}')
        echo "  ✅ tailwind-debug.css ($size)"
    else
        echo "  ❌ tailwind-debug.css (not built)"
    fi
    
    if [[ -f "src/apps/portal/static/portal/css/tailwind.min.css" ]]; then
        size=$(ls -lh src/apps/portal/static/portal/css/tailwind.min.css | awk '{print $5}')
        echo "  ✅ tailwind.min.css ($size)"
    else
        echo "  ❌ tailwind.min.css (not built)"
    fi
    
    echo
    echo "Source Files:"
    if [[ -f "src/apps/portal/static/portal/css/tailwind.css" ]]; then
        echo "  ✅ tailwind.css (source)"
    else
        echo "  ❌ tailwind.css (missing source file)"
    fi
}

# Main command handling
case "$1" in
    full)
        log_info "Starting TailwindCSS watch mode with FULL utilities"
        log_warning "This mode generates ALL Tailwind utilities on-demand"
        log_info "Perfect for copy-pasting from Tailwind UI components"
        echo
        npm run dev:watch
        ;;
    
    slim)
        log_info "Starting TailwindCSS watch mode with CURATED safelist"
        log_warning "This mode uses the production config for development"
        log_info "More limited but matches production output"
        echo
        tailwindcss -c src/apps/portal/static/portal/css/tailwind.prod.js -i src/apps/portal/static/portal/css/tailwind.css -o src/apps/portal/static/portal/css/tailwind-debug.css --watch
        ;;
    
    build)
        log_info "Building production CSS with minification"
        npm run tailwind:portal:build
        log_success "Production CSS built successfully"
        if [[ -f "src/apps/portal/static/portal/css/tailwind.min.css" ]]; then
            size=$(ls -lh src/apps/portal/static/portal/css/tailwind.min.css | awk '{print $5}')
            log_info "Output: tailwind.min.css ($size)"
        fi
        ;;
    
    status)
        check_status
        ;;
    
    help|--help|-h)
        show_usage
        ;;
    
    "")
        log_error "No command specified"
        echo
        show_usage
        exit 1
        ;;
    
    *)
        log_error "Unknown command: $1"
        echo
        show_usage
        exit 1
        ;;
esac 