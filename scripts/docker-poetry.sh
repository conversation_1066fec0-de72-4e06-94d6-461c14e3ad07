#!/bin/bash

# Docker Poetry Helper Script
# Usage: ./scripts/docker-poetry.sh [poetry command]
# Example: ./scripts/docker-poetry.sh update
# Example: ./scripts/docker-poetry.sh "update boto3 django-storages"

set -e

CONTAINER_NAME="ws_services"
POETRY_PATH="/root/.pyenv/versions/3.12.0/bin/poetry"
PYTHON_PATH="/root/.pyenv/versions/3.12.0/bin"

# Check if container is running
if ! docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "❌ Container ${CONTAINER_NAME} is not running"
    echo "Start it with: docker-compose up -d web"
    exit 1
fi

# Default command is 'install' if no arguments provided
POETRY_COMMAND=${1:-"install --no-root"}

echo "🐳 Running Poetry command in Docker container: ${CONTAINER_NAME}"
echo "📦 Command: poetry ${POETRY_COMMAND}"
echo ""

# Execute Poetry command with proper configuration
docker exec ${CONTAINER_NAME} bash -c "
    cd /code && 
    export PATH=${PYTHON_PATH}:\$PATH && 
    ${POETRY_PATH} config virtualenvs.create false && 
    ${POETRY_PATH} config virtualenvs.in-project false && 
    echo '✅ Poetry configuration set (no virtualenv)' &&
    echo '📦 Executing: poetry ${POETRY_COMMAND}' &&
    ${POETRY_PATH} ${POETRY_COMMAND}
"

echo ""
echo "✅ Poetry command completed successfully"
echo "🔄 You may need to restart the container: docker-compose restart web"
