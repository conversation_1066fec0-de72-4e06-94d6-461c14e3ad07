#!/usr/bin/env python3
"""
Example: Using AuthenticatedTester for Custom Widget Testing

This example demonstrates how to use the AuthenticatedTester class
to create custom tests for widget functionality.
"""

import sys
import os

# Add parent directory to path to import the testing module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_authenticated_requests import AuthenticatedTester


def test_widget_creation_flow():
    """Test the complete widget creation and configuration flow."""
    print("🧪 Testing Widget Creation Flow")
    print("=" * 40)
    
    tester = AuthenticatedTester()
    
    # Setup and authenticate
    if not tester.setup_test_user():
        return False
    
    if not tester.login():
        return False
    
    # Test 1: Access widget configuration page
    print("\n1️⃣ Testing widget config page access...")
    response = tester.get('/widget/finder-v2/config/')
    if response.status_code == 200:
        print("✅ Widget config page accessible")
    else:
        print(f"❌ Widget config page failed: {response.status_code}")
        return False
    
    # Test 2: Submit configuration form
    print("\n2️⃣ Testing configuration form submission...")
    config_data = {
        'config-name': 'Example Test Widget',
        'config-lang': 'en',
        'theme-theme_name': 'light',
        'content-by': '',
        'content-regions': '[]',
        'content-brands': '[]',
        'content-brands_exclude': '[]',
        'content-only_oem': '',
        'interface-width': '800',
        'interface-height': '',
        'interface-tabs': 'by_vehicle',
        'interface-primary_tab': 'by_vehicle',
        'interface-button_to_ws': 'on',
        'interface-flow_type': 'primary',
        'permissions-domains': '["example.com", "test.com"]',
    }
    
    response = tester.post_form('/widget/finder-v2/config/', config_data, allow_redirects=False)
    if response.status_code == 302:
        print("✅ Configuration form submitted successfully")
    else:
        print(f"❌ Configuration form failed: {response.status_code}")
        return False
    
    # Test 3: Verify configuration was saved (check redirect location)
    print("\n3️⃣ Testing configuration persistence...")
    redirect_url = response.headers.get('Location', '')
    if '/widget/finder-v2/config/' in redirect_url:
        print("✅ Configuration saved and redirected correctly")
    else:
        print(f"❌ Unexpected redirect: {redirect_url}")
        return False
    
    print("\n🎉 Widget creation flow test completed successfully!")
    return True


def test_demo_vs_authenticated_access():
    """Compare demo and authenticated endpoint access."""
    print("\n🧪 Testing Demo vs Authenticated Access")
    print("=" * 45)
    
    tester = AuthenticatedTester()
    
    # Setup and authenticate
    if not tester.setup_test_user():
        return False
    
    if not tester.login():
        return False
    
    # Test demo endpoint (should be accessible)
    print("\n🔓 Testing demo endpoint...")
    demo_response = tester.get('/widget/finder-v2/config-demo/')
    print(f"Demo endpoint: {demo_response.status_code}")
    
    # Test authenticated endpoint (should be accessible with auth)
    print("\n🔐 Testing authenticated endpoint...")
    auth_response = tester.get('/widget/finder-v2/config/')
    print(f"Authenticated endpoint: {auth_response.status_code}")
    
    # Both should be accessible (200)
    if demo_response.status_code == 200 and auth_response.status_code == 200:
        print("✅ Both endpoints accessible as expected")
        return True
    else:
        print("❌ Unexpected access patterns")
        return False


def test_admin_functionality():
    """Test admin interface access and functionality."""
    print("\n🧪 Testing Admin Functionality")
    print("=" * 35)
    
    tester = AuthenticatedTester()
    
    # Setup and authenticate
    if not tester.setup_test_user():
        return False
    
    if not tester.login():
        return False
    
    # Test admin pages
    admin_pages = [
        ('/admin/', 'Admin Home'),
        ('/admin/widgets/', 'Widgets Admin'),
        ('/admin/auth/user/', 'User Management'),
    ]
    
    all_passed = True
    for url, description in admin_pages:
        response = tester.get(url)
        success = response.status_code == 200
        status_icon = "✅" if success else "❌"
        print(f"{status_icon} {description}: {response.status_code}")
        
        if not success:
            all_passed = False
    
    return all_passed


def main():
    """Run all example tests."""
    print("🚀 Widget Testing Examples")
    print("=" * 30)
    
    # Run test suites
    creation_test = test_widget_creation_flow()
    access_test = test_demo_vs_authenticated_access()
    admin_test = test_admin_functionality()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Example Test Results:")
    print(f"   Widget Creation Flow: {'✅ PASS' if creation_test else '❌ FAIL'}")
    print(f"   Demo vs Auth Access: {'✅ PASS' if access_test else '❌ FAIL'}")
    print(f"   Admin Functionality: {'✅ PASS' if admin_test else '❌ FAIL'}")
    
    all_passed = creation_test and access_test and admin_test
    if all_passed:
        print("\n🎉 All example tests passed!")
        return 0
    else:
        print("\n⚠️  Some example tests failed.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
