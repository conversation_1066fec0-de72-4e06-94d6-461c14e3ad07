#!/bin/bash

# TailwindCSS Development Workflow Script
# Provides watch mode, debugging utilities, and class validation
# Usage: ./scripts/tailwind-dev.sh [command] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${CYAN}[DEBUG]${NC} $1"
}

# Get project root
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# Portal CSS paths
PORTAL_DIR="src/apps/portal"
PORTAL_CSS_DIR="$PORTAL_DIR/static/portal/css"
PORTAL_CONFIG="$PORTAL_CSS_DIR/tailwind.config.js"
PORTAL_INPUT="$PORTAL_CSS_DIR/tailwind.css"
PORTAL_OUTPUT_MIN="$PORTAL_CSS_DIR/tailwind.min.css"
PORTAL_OUTPUT_DEBUG="$PORTAL_CSS_DIR/tailwind-debug.css"

# Template directories for scanning
TEMPLATE_DIRS=(
    "src/templates"
    "src/apps/widgets/*/templates"
    "src/apps/portal/templates"
)

# Function to check if portal dependencies are installed
check_portal_deps() {
    if [ ! -d "$PORTAL_DIR/node_modules" ]; then
        log_info "Installing portal dependencies..."
        cd "$PORTAL_DIR"
        npm install
        cd "$PROJECT_ROOT"
    fi
}

# Function to start watch mode
start_watch() {
    log_info "Starting TailwindCSS watch mode for portal..."
    log_info "Watching templates in: ${TEMPLATE_DIRS[*]}"
    log_info "Output: $PORTAL_OUTPUT_DEBUG"
    log_warning "Press Ctrl+C to stop watching"
    
    check_portal_deps
    cd "$PORTAL_DIR"
    npm run watch-css
}

# Function to build debug CSS
build_debug() {
    log_info "Building debug CSS..."
    check_portal_deps
    cd "$PORTAL_DIR"
    npm run debug-css
    
    if [ -f "$PROJECT_ROOT/$PORTAL_OUTPUT_DEBUG" ]; then
        SIZE=$(du -h "$PROJECT_ROOT/$PORTAL_OUTPUT_DEBUG" | cut -f1)
        log_success "Debug CSS built successfully (${SIZE})"
    else
        log_error "Debug CSS build failed"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# Function to build production CSS
build_production() {
    log_info "Building production CSS..."
    check_portal_deps
    cd "$PORTAL_DIR"
    npm run build-css
    
    if [ -f "$PROJECT_ROOT/$PORTAL_OUTPUT_MIN" ]; then
        SIZE=$(du -h "$PROJECT_ROOT/$PORTAL_OUTPUT_MIN" | cut -f1)
        log_success "Production CSS built successfully (${SIZE})"
    else
        log_error "Production CSS build failed"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# Function to validate utility classes
validate_classes() {
    local search_pattern="$1"
    
    log_info "Validating TailwindCSS classes..."
    
    if [ -z "$search_pattern" ]; then
        log_info "Usage: $0 validate [class-pattern]"
        log_info "Example: $0 validate 'bg-ws-primary'"
        exit 1
    fi
    
    # Build debug CSS first
    build_debug >/dev/null 2>&1
    
    # Search for classes in templates
    log_info "Searching for classes matching '$search_pattern' in templates..."
    local template_matches=0
    for dir in "${TEMPLATE_DIRS[@]}"; do
        if [ -d "$dir" ]; then
            local matches=$(find "$dir" -name "*.html" -exec grep -l "$search_pattern" {} \; 2>/dev/null | wc -l)
            if [ "$matches" -gt 0 ]; then
                template_matches=$((template_matches + matches))
                log_debug "Found $matches files in $dir"
                find "$dir" -name "*.html" -exec grep -H "$search_pattern" {} \; 2>/dev/null | head -5
            fi
        fi
    done
    
    # Search for classes in generated CSS
    log_info "Checking if classes are generated in CSS..."
    if [ -f "$PORTAL_OUTPUT_DEBUG" ]; then
        local css_matches=$(grep -c "$search_pattern" "$PORTAL_OUTPUT_DEBUG" 2>/dev/null || echo "0")
        if [ "$css_matches" -gt 0 ]; then
            log_success "Found $css_matches occurrences in generated CSS"
            log_debug "Sample CSS rules:"
            grep -A 2 "$search_pattern" "$PORTAL_OUTPUT_DEBUG" | head -10
        else
            log_warning "No matches found in generated CSS"
            log_info "This might indicate:"
            log_info "  - Classes not used in templates"
            log_info "  - Classes not in safelist"
            log_info "  - Content scanning issue"
        fi
    else
        log_error "Debug CSS file not found. Run build first."
        exit 1
    fi
    
    # Summary
    log_info "Validation Summary:"
    echo -e "  Template files: ${GREEN}$template_matches${NC}"
    echo -e "  CSS occurrences: ${GREEN}$css_matches${NC}"
}

# Function to show CSS stats
show_stats() {
    log_info "TailwindCSS Build Statistics"
    
    if [ -f "$PORTAL_OUTPUT_MIN" ]; then
        MIN_SIZE=$(du -h "$PORTAL_OUTPUT_MIN" | cut -f1)
        MIN_LINES=$(wc -l < "$PORTAL_OUTPUT_MIN")
        echo -e "  Production CSS: ${GREEN}$MIN_SIZE${NC} ($MIN_LINES lines)"
    else
        echo -e "  Production CSS: ${RED}Not built${NC}"
    fi
    
    if [ -f "$PORTAL_OUTPUT_DEBUG" ]; then
        DEBUG_SIZE=$(du -h "$PORTAL_OUTPUT_DEBUG" | cut -f1)
        DEBUG_LINES=$(wc -l < "$PORTAL_OUTPUT_DEBUG")
        echo -e "  Debug CSS: ${GREEN}$DEBUG_SIZE${NC} ($DEBUG_LINES lines)"
    else
        echo -e "  Debug CSS: ${RED}Not built${NC}"
    fi
    
    if [ -f "$PORTAL_INPUT" ]; then
        SOURCE_SIZE=$(du -h "$PORTAL_INPUT" | cut -f1)
        SOURCE_LINES=$(wc -l < "$PORTAL_INPUT")
        echo -e "  Source CSS: ${GREEN}$SOURCE_SIZE${NC} ($SOURCE_LINES lines)"
    fi
    
    # Check templates count
    local total_templates=0
    for dir in "${TEMPLATE_DIRS[@]}"; do
        if [ -d "$dir" ]; then
            local count=$(find "$dir" -name "*.html" 2>/dev/null | wc -l)
            total_templates=$((total_templates + count))
        fi
    done
    echo -e "  Template files scanned: ${GREEN}$total_templates${NC}"
}

# Function to clean build artifacts
clean() {
    log_info "Cleaning TailwindCSS build artifacts..."
    
    if [ -f "$PORTAL_OUTPUT_MIN" ]; then
        rm "$PORTAL_OUTPUT_MIN"
        log_success "Removed production CSS"
    fi
    
    if [ -f "$PORTAL_OUTPUT_DEBUG" ]; then
        rm "$PORTAL_OUTPUT_DEBUG"
        log_success "Removed debug CSS"
    fi
    
    log_success "Clean completed"
}

# Function to show help
show_help() {
    echo "TailwindCSS Development Workflow Script"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  watch                 Start watch mode for real-time CSS compilation"
    echo "  debug                 Build debug CSS (unminified)"
    echo "  build                 Build production CSS (minified)"
    echo "  validate [pattern]    Validate if utility classes are generated"
    echo "  stats                 Show CSS build statistics"
    echo "  clean                 Clean build artifacts"
    echo "  help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 watch                              # Start watch mode"
    echo "  $0 validate 'bg-ws-primary'          # Check ws-primary background classes"
    echo "  $0 validate 'border-ws-secondary'    # Check ws-secondary border classes"
    echo "  $0 stats                              # Show build statistics"
    echo ""
    echo "Development Workflow:"
    echo "  1. Edit templates with TailwindCSS classes"
    echo "  2. Run '$0 watch' for real-time compilation"
    echo "  3. Use '$0 validate [pattern]' to check class generation"
    echo "  4. Run '$0 build' for production deployment"
}

# Main script logic
case "$1" in
    "watch")
        start_watch
        ;;
    "debug")
        build_debug
        ;;
    "build")
        build_production
        ;;
    "validate")
        validate_classes "$2"
        ;;
    "stats")
        show_stats
        ;;
    "clean")
        clean
        ;;
    "help"|"")
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac 