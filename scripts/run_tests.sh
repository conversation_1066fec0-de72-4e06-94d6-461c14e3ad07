#!/bin/bash
# Simple test runner that works with the current Docker environment

set -e

echo "🧪 Running tests using dev_docker settings..."

# Configuration that works
PYTHON_PATH="/root/.pyenv/versions/3.12.0/bin/python"
DJANGO_SETTINGS="src.settings.dev_docker"
PROJECT_PATH="/code"

# Set environment variables
export DJANGO_SETTINGS_MODULE=$DJANGO_SETTINGS
export PYTHONPATH=$PROJECT_PATH:$PROJECT_PATH/src
export DISABLE_LESS_COMPILATION=true

# Change to project directory
cd $PROJECT_PATH

# Function to run a specific test file
run_test_file() {
    local test_file=$1
    echo "📍 Running: $test_file"
    echo "📍 Settings: $DJANGO_SETTINGS"
    echo "📍 Python path: $PYTHONPATH"
    
    $PYTHON_PATH -c "
import os
import sys
import django
import unittest

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', '$DJANGO_SETTINGS')
django.setup()

# Import test module
sys.path.insert(0, '$PROJECT_PATH')
test_module_path = '$test_file'.replace('/', '.').replace('.py', '')
test_module = __import__(test_module_path, fromlist=[''])

# Find all test classes
test_classes = []
for name in dir(test_module):
    obj = getattr(test_module, name)
    if isinstance(obj, type) and issubclass(obj, unittest.TestCase) and obj != unittest.TestCase:
        test_classes.append(obj)

if not test_classes:
    print('❌ No test classes found in $test_file')
    exit(1)

# Run tests
total_tests = 0
total_failures = 0
total_errors = 0

for test_class in test_classes:
    print(f'\\n🔍 Running {test_class.__name__}...')
    suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    total_tests += result.testsRun
    total_failures += len(result.failures)
    total_errors += len(result.errors)

print(f'\\n{'='*60}')
print(f'📊 SUMMARY')
print(f'Tests run: {total_tests}')
print(f'Failures: {total_failures}')
print(f'Errors: {total_errors}')
print(f'Success: {total_failures == 0 and total_errors == 0}')
print(f'{'='*60}')

exit(0 if total_failures == 0 and total_errors == 0 else 1)
"
}

# Function to run all tests in a directory
run_test_directory() {
    local test_dir=$1
    echo "📁 Running all tests in: $test_dir"
    
    find "$test_dir" -name "test_*.py" -type f | while read -r test_file; do
        echo "📄 Found test file: $test_file"
        run_test_file "$test_file"
    done
}

# Main logic
if [ $# -eq 0 ]; then
    echo "Usage: $0 <test_file_or_directory>"
    echo "Examples:"
    echo "  $0 tests/widget/finder_v2/test_region_filtering.py"
    echo "  $0 tests/widget/finder_v2/"
    exit 1
fi

TARGET=$1

if [ -f "$TARGET" ]; then
    run_test_file "$TARGET"
elif [ -d "$TARGET" ]; then
    run_test_directory "$TARGET"
else
    echo "❌ Target not found: $TARGET"
    exit 1
fi 