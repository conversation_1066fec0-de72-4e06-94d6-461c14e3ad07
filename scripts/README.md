# Wheel-Size Services Testing Scripts

This directory contains automated testing scripts for the wheel-size-services Django application.

## Available Scripts

### `test_authenticated_requests.py`
**Main authenticated testing script** - Provides comprehensive testing capabilities for authenticated endpoints.

**Features:**
- Automatic test user creation with superuser privileges
- Session management and CSRF token handling
- GET and POST request testing
- Form submission validation
- Error handling and detailed reporting

**Usage:**
```bash
# Run all built-in tests
python scripts/test_authenticated_requests.py

# Use as a module in custom scripts
from scripts.test_authenticated_requests import AuthenticatedTester
```

**Test Coverage:**
- ✅ Admin interface access
- ✅ Widget configuration endpoints
- ✅ Form submission workflows
- ✅ Authentication and permissions

### `examples/test_widget_example.py`
**Example implementation** showing how to use the AuthenticatedTester class for custom testing scenarios.

**Features:**
- Widget creation flow testing
- Demo vs authenticated endpoint comparison
- Admin functionality verification
- Custom test patterns

**Usage:**
```bash
python scripts/examples/test_widget_example.py
```

## Quick Start

### 1. Ensure Docker is Running
```bash
docker-compose up -d
```

### 2. Run Basic Tests
```bash
# Test all authenticated endpoints
python scripts/test_authenticated_requests.py

# Run example test scenarios
python scripts/examples/test_widget_example.py
```

### 3. Create Custom Tests
```python
from scripts.test_authenticated_requests import AuthenticatedTester

# Initialize tester
tester = AuthenticatedTester()
tester.setup_test_user()
tester.login()

# Test your endpoints
response = tester.get('/your/endpoint/')
print(f"Status: {response.status_code}")

# Test form submissions
form_data = {'field': 'value'}
response = tester.post_form('/your/form/', form_data)
```

## Test User Details

The scripts automatically create a test user with these credentials:
- **Username**: `testuser`
- **Password**: `testpass123`
- **Email**: `<EMAIL>`
- **Permissions**: Superuser, Staff, `widgets.can_edit`

## Supported Endpoints

### Widget Configuration
- `/widget/finder-v2/config/` - Finder v2 configuration
- `/widget/finder/config/` - Finder v1 configuration
- `/widget/calc/config/` - Calculator configuration (may return 500 until fixed)
- `/widget/*/config-demo/` - Demo configuration endpoints

### Admin Interface
- `/admin/` - Django admin home
- `/admin/widgets/` - Widget management
- `/admin/auth/user/` - User management

## Creating New Tests

### For New Widget Types
1. Add endpoint tests to `test_authenticated_requests.py`
2. Create widget-specific form data
3. Test both GET and POST operations
4. Verify success redirects

### For Custom Workflows
1. Create a new script in `scripts/`
2. Import `AuthenticatedTester`
3. Follow the pattern in `examples/test_widget_example.py`
4. Add comprehensive error handling

## Troubleshooting

### Common Issues
- **Docker not running**: `docker-compose up -d`
- **Database not migrated**: `docker exec ws_services python manage.py migrate`
- **Permission errors**: Scripts create superuser automatically
- **CSRF issues**: Handled automatically by the tester class

### Debug Mode
Add verbose output to your tests:
```python
response = tester.get('/endpoint/')
print(f"Headers: {response.headers}")
print(f"Content: {response.text[:500]}")
```

## Documentation

For detailed usage instructions and examples, see:
- [Testing Procedures Documentation](../docs/development/testing-procedures.md)
- [Finder-v2 Implementation Plan](../docs/development/finder-v2-master-implementation-plan.md)

## Contributing

When adding new testing scripts:
1. Follow the existing patterns and naming conventions
2. Include comprehensive error handling
3. Add clear documentation and examples
4. Test with both success and failure scenarios
5. Update this README with new script information
