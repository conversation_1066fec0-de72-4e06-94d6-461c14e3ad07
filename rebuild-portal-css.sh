#!/bin/bash

# TailwindCSS Portal CSS Rebuild Script
# This script rebuilds the TailwindCSS for the widget portal

echo "🎨 Rebuilding TailwindCSS for Widget Portal..."

# Navigate to portal directory
cd src/apps/portal

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing npm dependencies..."
    npm install
fi

# Build CSS
echo "🔨 Building CSS..."
npm run build-css

if [ $? -eq 0 ]; then
    echo "✅ CSS build completed successfully!"
    echo "📁 Output: src/apps/portal/static/portal/css/tailwind.min.css"
    
    # Show file size
    if [ -f "static/portal/css/tailwind.min.css" ]; then
        SIZE=$(du -h static/portal/css/tailwind.min.css | cut -f1)
        echo "📊 File size: $SIZE"
    fi
    
    echo ""
    echo "🚀 Ready to test at http://development.local:8000/"
    echo ""
    echo "💡 For development with auto-rebuild, run:"
    echo "   cd src/apps/portal && npm run watch-css"
else
    echo "❌ CSS build failed!"
    exit 1
fi
