upstream services_runserver {
    server  ws_services:8000 fail_timeout=0;
}

server {
    listen       80;
    server_name  services.ws.com services.ws.ru development.local;

    location /static {
        alias /static;
        access_log off;
    }

    # location /media {
    #     alias /media/;
    #     access_log off;
    # }

    try_files $uri @proxy_to_app;

    if ($host ~* \.ru$) {
        set $language 'ru';
    }
    if ($host ~* \.es$) {
        set $language 'es';
    }
    if ($host ~* \.com$) {
        set $language 'en';
    }
    if ($host ~* \.pt$) {
        set $language 'pt';
    }
    if ($host ~* \.fr$) {
        set $language 'fr';
    }

    # Setup named location for Django requests and handle proxy details
    location @proxy_to_app {
        proxy_pass         http://services_runserver;
        proxy_redirect     off;
        proxy_set_header   Host             $host;
        proxy_set_header   X-Real-IP        $remote_addr;
        proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
        proxy_set_header   Accept-Language  $language;
        #proxy_send_timeout 120s;
    }
}
