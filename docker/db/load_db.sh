#!/usr/bin/env bash

case "$1" in
    "--manual")
        DUMP_FILE=/tmp/db/ws_services_prod_manual.sql
        ;;
    *)
        DUMP_FILE=/tmp/db/ws_services_prod.sql
        ;;
esac

function ws_printf() {
    printf "\e[1;31m >>> \e[1;32m $1\e[0m\n"
}

function progress() {
    SECS=0
    local PID=$!
    while kill -0 $PID 2> /dev/null;     do
        READABLE_TIME=$(printf '%02d:%02d\n' $(($SECS%3600/60)) $(($SECS%60)))
        echo -ne "\e[1;31m >>> \e[1;32m Elapsed time \e[1;33m ${READABLE_TIME}\e[0m\033[0K\r"
        sleep 1
        ((SECS++))
    done
}

function dump() {
    export PGPASSWORD="7RWApmUJ5DSKFrAy"
    pg_dump -Fc -U services \
        -h saturn.cxbv7of13ybk.us-east-1.rds.amazonaws.com services_portal_prod \
        --exclude-table-data 'django_admin_log' \
        > $DUMP_FILE
    unset PGPASSWORD
}

function load() {
    export PGPASSWORD="_ws_pass_"
    pg_restore -U ws_user --no-owner -d ws_services < $DUMP_FILE
    psql -U ws_user -no-owner -d ws_services < /scripts/domain_names.sql
    unset PGPASSWORD
}

case "$1" in
    "--manual" | "--cached")
        sleep 15
        ;;
    *)
        ws_printf "Dumping production database (will take about 10 minutes)"
        time dump & progress
        ;;
esac

ws_printf "Loading the dump into local database"
time load & progress
