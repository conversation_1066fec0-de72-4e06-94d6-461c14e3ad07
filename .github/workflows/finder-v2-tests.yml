name: Finder-v2 Widget Tests

on:
  push:
    branches: [ main, feature/finder-v2-widget ]
    paths:
      - 'src/apps/widgets/finder_v2/**'
      - 'tests/widget/finder_v2/**'
      - '.github/workflows/finder-v2-tests.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'src/apps/widgets/finder_v2/**'
      - 'tests/widget/finder_v2/**'

jobs:
  django-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
        
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
        
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
      
    - name: Install project
      run: poetry install --no-interaction
      
    - name: Set up environment variables
      run: |
        echo "DJANGO_SETTINGS_MODULE=src.settings.test" >> $GITHUB_ENV
        echo "DATABASE_URL=postgres://postgres:postgres@localhost:5432/test_db" >> $GITHUB_ENV
        
    - name: Run Django migrations
      run: |
        poetry run python manage.py migrate --settings=src.settings.test
        
    - name: Run Finder-v2 Unit Tests
      run: |
        poetry run python -m pytest tests/widget/finder_v2/test_widget_type.py -v
        poetry run python -m pytest tests/widget/finder_v2/test_api_proxy.py -v
        poetry run python -m pytest tests/widget/finder_v2/test_csrf_protection.py -v
        poetry run python -m pytest tests/widget/finder_v2/test_admin_interface.py -v
        
    - name: Run Finder-v2 Integration Tests
      run: |
        poetry run python -m pytest tests/widget/finder_v2/test_integration.py -v
        
    - name: Run Security Tests
      run: |
        poetry run python tests/widget/test_widget_csrf_security.py

  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'src/apps/widgets/finder_v2/app/package-lock.json'
        
    - name: Install dependencies
      working-directory: src/apps/widgets/finder_v2/app
      run: npm ci
      
    - name: Run ESLint
      working-directory: src/apps/widgets/finder_v2/app
      run: npm run lint:check
      
    - name: Run Vue 3 Tests
      working-directory: src/apps/widgets/finder_v2/app
      run: npm run test:run
      
    - name: Run Test Coverage
      working-directory: src/apps/widgets/finder_v2/app
      run: npm run test:coverage
      
    - name: Build Production Bundle
      working-directory: src/apps/widgets/finder_v2/app
      run: npm run build
      
    - name: Check Bundle Size
      working-directory: src/apps/widgets/finder_v2/app
      run: |
        # Check that main bundle is under 90KB gzipped
        BUNDLE_SIZE=$(gzip -c dist/assets/*.js | wc -c)
        echo "Bundle size: $BUNDLE_SIZE bytes"
        if [ $BUNDLE_SIZE -gt 92160 ]; then
          echo "❌ Bundle size exceeds 90KB limit"
          exit 1
        else
          echo "✅ Bundle size is within 90KB limit"
        fi
        
    - name: Upload Build Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: finder-v2-build
        path: src/apps/widgets/finder_v2/app/dist/
        retention-days: 7

  browser-tests:
    runs-on: ubuntu-latest
    needs: [django-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install Poetry
      uses: snok/install-poetry@v1
      
    - name: Install Python dependencies
      run: poetry install --no-interaction
      
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install Playwright
      run: |
        poetry run pip install playwright pytest-playwright
        poetry run playwright install
        
    - name: Build Frontend
      working-directory: src/apps/widgets/finder_v2/app
      run: |
        npm ci
        npm run build
        
    - name: Set up environment variables
      run: |
        echo "DJANGO_SETTINGS_MODULE=src.settings.test" >> $GITHUB_ENV
        echo "DATABASE_URL=postgres://postgres:postgres@localhost:5432/test_db" >> $GITHUB_ENV
        
    - name: Run Django migrations
      run: |
        poetry run python manage.py migrate --settings=src.settings.test
        
    - name: Run Browser Tests
      run: |
        poetry run python -m pytest tests/widget/finder_v2/test_browser.py -v --tb=short
        
    - name: Upload Screenshots on Failure
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: playwright-screenshots
        path: test-results/
        retention-days: 7

  performance-tests:
    runs-on: ubuntu-latest
    needs: [frontend-tests]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install dependencies
      working-directory: src/apps/widgets/finder_v2/app
      run: npm ci
      
    - name: Build for production
      working-directory: src/apps/widgets/finder_v2/app
      run: npm run build
      
    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli@0.12.x
      
    - name: Run Lighthouse CI
      working-directory: src/apps/widgets/finder_v2/app
      run: |
        # Create a simple test server for Lighthouse
        echo '<!DOCTYPE html>
        <html>
        <head><title>Finder-v2 Test</title></head>
        <body>
          <div id="finder-v2-app"></div>
          <script type="module" src="/dist/assets/main.js"></script>
        </body>
        </html>' > test.html
        
        # Start simple HTTP server
        python3 -m http.server 8080 &
        SERVER_PID=$!
        
        # Wait for server to start
        sleep 2
        
        # Run Lighthouse
        lhci autorun --upload.target=temporary-public-storage --collect.url=http://localhost:8080/test.html
        
        # Clean up
        kill $SERVER_PID

  security-audit:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install dependencies
      working-directory: src/apps/widgets/finder_v2/app
      run: npm ci
      
    - name: Run npm audit
      working-directory: src/apps/widgets/finder_v2/app
      run: npm audit --audit-level=moderate
      
    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --file=src/apps/widgets/finder_v2/app/package.json
      continue-on-error: true
