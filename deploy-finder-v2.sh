#!/bin/bash

# Finder-v2 Vue.js Development Deployment Script
# Automates the complete build-and-deploy cycle for finder-v2 widget
# Usage: ./deploy-finder-v2.sh
# Run from wheel-size-services project root directory

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="$(pwd)"
WIDGET_APP_PATH="src/apps/widgets/finder_v2/app"
STATIC_PATH="src/apps/widgets/finder_v2/static/finder_v2"
DIST_PATH="${WIDGET_APP_PATH}/dist"

echo -e "${BLUE}🚀 Starting Finder-v2 Vue.js Deployment Workflow${NC}"
echo -e "${BLUE}=================================================${NC}"

# Step 1: Verify we're in the correct directory
echo -e "\n${YELLOW}📍 Step 1: Verifying project directory...${NC}"
if [[ ! -f "docker-compose.yml" ]] || [[ ! -d "src/apps/widgets/finder_v2" ]]; then
    echo -e "${RED}❌ Error: Must run from wheel-size-services project root directory${NC}"
    echo -e "${RED}   Current directory: ${PROJECT_ROOT}${NC}"
    echo -e "${RED}   Expected files: docker-compose.yml, src/apps/widgets/finder_v2/${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Project directory verified: ${PROJECT_ROOT}${NC}"

# Step 2: Check if Docker containers are running
echo -e "\n${YELLOW}📍 Step 2: Checking Docker containers...${NC}"
if ! docker compose ps | grep -q "ws_services.*Up"; then
    echo -e "${RED}❌ Error: Docker containers are not running${NC}"
    echo -e "${YELLOW}   Starting containers...${NC}"
    docker compose up -d
    echo -e "${GREEN}✅ Docker containers started${NC}"
else
    echo -e "${GREEN}✅ Docker containers are running${NC}"
fi

# Step 3: Build Vue.js application
echo -e "\n${YELLOW}📍 Step 3: Building Vue.js application...${NC}"
echo -e "${BLUE}   Running: npm run build in Docker container${NC}"
if docker compose exec web bash -c "cd ${WIDGET_APP_PATH} && npm run build"; then
    echo -e "${GREEN}✅ Vue.js build completed successfully${NC}"
else
    echo -e "${RED}❌ Error: Vue.js build failed${NC}"
    exit 1
fi

# Step 4: Verify dist directory exists
echo -e "\n${YELLOW}📍 Step 4: Verifying build output...${NC}"
if [[ ! -d "${DIST_PATH}" ]]; then
    echo -e "${RED}❌ Error: Build output directory not found: ${DIST_PATH}${NC}"
    exit 1
fi

# List built files
echo -e "${BLUE}   Built files:${NC}"
ls -la "${DIST_PATH}/" | grep -E '\.(js|css)$' | while read -r line; do
    echo -e "${BLUE}   📄 ${line}${NC}"
done

# Step 5: Copy static files to Django directory
echo -e "\n${YELLOW}📍 Step 5: Copying static files to Django directory...${NC}"
echo -e "${BLUE}   Source: ${DIST_PATH}/*${NC}"
echo -e "${BLUE}   Target: ${STATIC_PATH}/${NC}"

# Create static directory if it doesn't exist
mkdir -p "${STATIC_PATH}"

# Copy files
if cp -R "${DIST_PATH}"/* "${STATIC_PATH}/"; then
    echo -e "${GREEN}✅ Static files copied successfully${NC}"
    
    # Show copied files with timestamps
    echo -e "${BLUE}   Deployed files:${NC}"
    find "${STATIC_PATH}" -name "*.js" -o -name "*.css" | while read -r file; do
        timestamp=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$file" 2>/dev/null || stat -c "%y" "$file" 2>/dev/null | cut -d'.' -f1)
        echo -e "${BLUE}   📄 $(basename "$file") - ${timestamp}${NC}"
    done
else
    echo -e "${RED}❌ Error: Failed to copy static files${NC}"
    exit 1
fi

# Step 6: Restart Django development server
echo -e "\n${YELLOW}📍 Step 6: Restarting Django development server...${NC}"
if docker compose restart web; then
    echo -e "${GREEN}✅ Django server restarted successfully${NC}"
    
    # Wait for server to be ready
    echo -e "${BLUE}   Waiting for server to be ready...${NC}"
    sleep 3
    
    # Check if server is responding
    if curl -s -o /dev/null -w "%{http_code}" "http://development.local:8000/widget/finder-v2/?config" | grep -q "200"; then
        echo -e "${GREEN}✅ Server is responding correctly${NC}"
    else
        echo -e "${YELLOW}⚠️  Server may still be starting up${NC}"
    fi
else
    echo -e "${RED}❌ Error: Failed to restart Django server${NC}"
    exit 1
fi

# Step 7: Success summary
echo -e "\n${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${GREEN}=================================${NC}"
echo -e "${GREEN}✅ Vue.js application built${NC}"
echo -e "${GREEN}✅ Static files deployed${NC}"
echo -e "${GREEN}✅ Django server restarted${NC}"
echo -e "\n${BLUE}🌐 Widget URL: http://development.local:8000/widget/finder-v2/?config${NC}"
echo -e "${BLUE}💡 You can now test your Vue.js changes in the browser${NC}"

# Optional: Open browser (uncomment if desired)
# echo -e "\n${YELLOW}🌐 Opening widget in browser...${NC}"
# open "http://development.local:8000/widget/finder-v2/?config" 2>/dev/null || echo -e "${YELLOW}   (Browser auto-open not available)${NC}"

echo -e "\n${BLUE}📝 To run this script again: ./deploy-finder-v2.sh${NC}"
