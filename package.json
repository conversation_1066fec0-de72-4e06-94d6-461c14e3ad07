{"name": "wheel-size-services", "version": "2.0.0", "description": "Wheel Size Services - Django Portal with TailwindCSS v4.1", "type": "module", "private": true, "scripts": {"dev:watch": "tailwindcss -c src/apps/portal/static/portal/css/tailwind.dev.js -i src/apps/portal/static/portal/css/tailwind.css -o src/apps/portal/static/portal/css/tailwind-debug.css --watch", "tailwind:portal:build": "tailwindcss -c src/apps/portal/static/portal/css/tailwind.prod.js -i src/apps/portal/static/portal/css/tailwind.css -o src/apps/portal/static/portal/css/tailwind.min.css --minify", "tailwind:finder-v2:dev": "cd src/apps/widgets/finder_v2/app && npm run dev", "tailwind:finder-v2:build": "cd src/apps/widgets/finder_v2/app && npm run build", "build:all": "npm run tailwind:portal:build && npm run tailwind:finder-v2:build"}, "devDependencies": {"@tailwindcss/cli": "^4.1.8", "tailwindcss": "^4.1.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "private"}, "author": "Wheel Size Services Team", "license": "PROPRIETARY"}