# Django Environment Variables Template
# Copy this file to .env.local and fill in your actual values
# NEVER commit .env.local to version control

# Django Settings
SECRET_KEY=your-secret-key-here-generate-a-new-one
DEBUG=False
DJANGO_SETTINGS_MODULE=src.settings.dev_docker

# Database Configuration
DB_ENGINE=django.db.backends.postgresql_psycopg2
DB_NAME=ws_services_db
DB_USER=ws_services_user
DB_PASSWORD=your-db-password-here
DB_HOST=postgres15
DB_PORT=5432

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-here
AWS_SECRET_ACCESS_KEY=your-aws-secret-key-here
AWS_STORAGE_BUCKET_NAME=automobile-assets
AWS_SES_REGION_NAME=us-east-1
AWS_SES_REGION_ENDPOINT=email.us-east-1.amazonaws.com

# Email Configuration
SERVER_EMAIL=<EMAIL>
DEFAULT_FROM_EMAIL=WheelSize <<EMAIL>>

# reCAPTCHA Configuration - DEPRECATED (use Turnstile instead)
# These are kept for backward compatibility during migration
RECAPTCHA_PUBLIC_KEY=your-recaptcha-site-key
RECAPTCHA_PRIVATE_KEY=your-recaptcha-secret-key
NORECAPTCHA_SITE_KEY=your-recaptcha-site-key
NORECAPTCHA_SECRET_KEY=your-recaptcha-secret-key

# Cloudflare Turnstile Configuration
TURNSTILE_SITEKEY=your-turnstile-site-key
TURNSTILE_SECRET=your-turnstile-secret-key

# Registration Rate Limiting
REGISTRATION_RATE_LIMIT_ENABLED=True
REGISTRATION_RATE_LIMIT_MAX=3
REGISTRATION_RATE_LIMIT_HOURS=1

# Anti-Spam Validation
EMAIL_DOMAIN_VALIDATION_ENABLED=True
FORM_TIMING_VALIDATION_ENABLED=True
FORM_MIN_SUBMISSION_TIME=5
HONEYPOT_VALIDATION_ENABLED=True

# API Configuration
REST_PROXY_HOST=http://ws_api:9002/v1
REST_PROXY_SECRET_TOKEN=your-api-secret-token

# Security Settings (Production)
SECURE_SSL_REDIRECT=False
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True
X_FRAME_OPTIONS=DENY
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False

# Allowed Hosts (comma-separated)
ALLOWED_HOSTS=.wheel-size.com,development.local,services.ws.com

# Cache Configuration
CACHE_BACKEND=django.core.cache.backends.dummy.DummyCache
CACHE_LOCATION=

# Sentry Configuration
SENTRY_DSN=your-sentry-dsn-here
SENTRY_ENVIRONMENT=development
