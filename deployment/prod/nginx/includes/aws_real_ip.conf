# Look for client IP in the X-Forwarded-For header
real_ip_header X-Forwarded-For;

# Ignore trusted IPs
real_ip_recursive on;

# Set VPC subnet as trusted
set_real_ip_from **********/20;
set_real_ip_from ***********/20;
set_real_ip_from ***********/20;
set_real_ip_from ***********/20;

# Locations and IP Address Ranges of CloudFront Edge Servers
# see http://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/LocationsOfEdgeServers.html

# Set CLOUDFRONT_GLOBAL_IP_LIST subnets as trusted
set_real_ip_from *********/15;
set_real_ip_from *********/18;
set_real_ip_from *********/15;
set_real_ip_from ************/17;
set_real_ip_from **********/16;
set_real_ip_from **********/16;
set_real_ip_from **********/16;
set_real_ip_from ************/18;
set_real_ip_from ************/19;
set_real_ip_from ************/18;
set_real_ip_from *************/22;
set_real_ip_from *************/22;
set_real_ip_from *************/23;
set_real_ip_from *************/20;
set_real_ip_from *************/19;
set_real_ip_from *************/24;
set_real_ip_from *************/23;
set_real_ip_from *************/23;
set_real_ip_from *************/24;
set_real_ip_from ************/19;

# Set CLOUDFRONT_REGIONAL_EDGE_IP_LIST subnets as trusted
set_real_ip_from ************/26;
set_real_ip_from ***********/26;
set_real_ip_from ************/24;
set_real_ip_from ************/24;
set_real_ip_from ***********/24;
set_real_ip_from ************/24;
set_real_ip_from ***********/24;
set_real_ip_from **************/29;
set_real_ip_from ************/24;
set_real_ip_from *************/26;
set_real_ip_from **************/26;
set_real_ip_from *************/26;
set_real_ip_from *************/26;
set_real_ip_from ***********/25;
set_real_ip_from ***********/24;
set_real_ip_from *************/26;
set_real_ip_from *************/26;
set_real_ip_from **************/26;
set_real_ip_from ************/26;
set_real_ip_from ************/26;
set_real_ip_from **************/26;
