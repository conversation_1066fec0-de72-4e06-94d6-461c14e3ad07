upstream ws-services_server {
    server 127.0.0.1:9000;
}

# Serve static files and redirect any other request to Apache
server {

    listen       80;

    server_name  services.wheel-size.com services3.wheel-size.com;

    root        /opt/driveate/wheel-size-services;

    access_log  /var/log/nginx/ws-services-access.log main;
    error_log   /var/log/nginx/ws-services-error.log;

    ssi on;

    large_client_header_buffers 4 8k;

    # Close server from access (test version) django - 159753
    #auth_basic "Restricted";
    #auth_basic_user_file /etc/nginx/.htpasswd;


    # Stop deep linking or hot linking
    location ^~ /static {
        alias /opt/driveate/wheel-size-services/collected_static/;
        access_log off;
        expires 7d;
    }

    location ^~ /code {
        alias /opt/driveate/wheel-size-services/collected_static/widget/code/stable/;
        access_log off;
        expires 7d;
    }

    location ^~ /media {
        access_log off;
        expires 7d;
    }

    location /favicon.ico {
        access_log off;
        log_not_found off;
    }

    location /demo {
       rewrite ^/demo(.*) $scheme://$server_name/widget/demo$1 permanent;
    }
    
    location / {
        # block common exploits, sql injections etc
        include /etc/nginx/includes/block.conf;

        try_files $uri @proxy_to_app;
    }


    # Setup named location for Django requests and handle proxy details
    location @proxy_to_app {
        proxy_pass       http://ws-services_server;
        proxy_redirect   off;
        proxy_set_header Host       $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    #include /etc/nginx/conf/staticfiles.conf;
    include /etc/nginx/includes/drop.conf;
    include /etc/nginx/includes/aws_real_ip.conf;
}