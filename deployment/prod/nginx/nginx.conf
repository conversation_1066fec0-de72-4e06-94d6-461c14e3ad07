user nginx nginx;

worker_processes auto;
worker_priority -10;

worker_rlimit_nofile 260000;
timer_resolution 100ms;

pid        /var/run/nginx.pid;
pcre_jit on;

events {
    multi_accept on;
    worker_connections  2048;
    accept_mutex on;
    accept_mutex_delay 100ms;
    use epoll;
}


http {
    #include /etc/nginx/includes/cache.conf;
    #include /etc/nginx/includes/aws_real_ip.conf;

    index  index.php index.html index.htm;
    include /etc/nginx/mime.types;
    default_type  application/octet-stream;
    charset utf-8;

    log_format test 'remote_addr: $remote_addr | realip_remote_addr: $realip_remote_addr | http_x_forwarded_for: $http_x_forwarded_for | $proxy_add_x_forwarded_for';

    log_format main '$remote_addr - $remote_user [$time_local] $request '
		    '"$status" $body_bytes_sent "$http_referer" '
		    '"$http_user_agent" "$http_x_forwarded_for" "$gzip_ratio" '
		    '"$connection" "$connection_requests" "$request_time" "$sent_http_set_cookie"';

    access_log /var/log/nginx/access.log combined buffer=32k;
    error_log  /var/log/nginx/error.log warn;


    sendfile    on;

    tcp_nopush  on;  # off may be better for *some* Comet/long-poll stuff
    tcp_nodelay off; # on may be better for some Comet/long-poll stuff
    server_tokens off;
    server_name_in_redirect off;

    keepalive_disable msie6;
    keepalive_timeout  75s;
    keepalive_requests 100;
    lingering_time 20s;
    lingering_timeout 5s;


    gzip on;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";
    gzip_static on;
    gzip_min_length 1400;
    gzip_buffers 32 8k;
    gzip_http_version 1.0;
    gzip_comp_level 5;
    gzip_proxied any;
    gzip_types text/plain text/css text/xml application/javascript application/x-javascript application/xml application/xml+rss application/ecmascript application/json image/svg+xml;


    client_body_buffer_size 256k;
    client_body_in_file_only off;
    client_body_timeout 60s;
    client_header_buffer_size 64k;


    ## how long a connection has to complete sending 
    ## it's headers for request to be processed
    client_header_timeout  20s;
    client_max_body_size 10m; 
    connection_pool_size 256;
    directio  4m;
    ignore_invalid_headers on;
    large_client_header_buffers 8 64k;
    output_buffers   8 256k;
    postpone_output  1460;

    request_pool_size  32k;
    reset_timedout_connection on;
    send_timeout     60s;
    types_hash_max_size 2048;
    server_names_hash_bucket_size 64;

    # for nginx proxy backends to prevent redirects to backend port 
    # port_in_redirect off;


    open_file_cache max=10000 inactive=30s;
    open_file_cache_valid 120s;
    open_file_cache_min_uses 2;
    open_file_cache_errors off;
    open_log_file_cache max=4096 inactive=30s min_uses=2;

    server {
      location /elb-ht {
        access_log off;
        return 200 'A-OK!';
        add_header Content-Type text/plain;
      }
    }

    include /etc/nginx/conf.d/*.conf;
    
    
}
