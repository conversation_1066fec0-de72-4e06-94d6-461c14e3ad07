upstream backend-ws-services {
    server 127.0.0.1:9004;
}

server {

    listen   80;
    server_name  services2.wheel-size.com;

    root        /opt/driveate/wheel-size-services;

    access_log  /var/log/nginx/stg-ws-services-access.log  combined gzip flush=5m;
    error_log   /var/log/nginx/stg-ws-services-error.log   error;

    proxy_cache off;
    
    location ^~ /code {
      alias /opt/driveate/wheel-size-services/collected_static/widget/code/beta/;
      access_log off;
      expires 7d;
    }

    location / {
      proxy_pass http://backend-ws-services;

      proxy_redirect off;
      proxy_ignore_client_abort on;
      proxy_next_upstream error;

      proxy_set_header   Authorization "";
      proxy_set_header   Host             $http_host;
      proxy_set_header   X-Real-IP        $remote_addr;
      proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
    }

    location = /favicon.ico {
        access_log     off;
        log_not_found  off;
    }
}
