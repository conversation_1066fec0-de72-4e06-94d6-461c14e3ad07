[Unit]
Description=ws services
After=syslog.target network.target

[Service]
Type=simple
User=ec2-user
Group=ec2-user
RuntimeDirectory=gunicorn
WorkingDirectory=/opt/driveate/wheel-size-services
# ExecStart=/bin/sh -c 'cd /opt/driveate/wheel-size-services && source .venv/bin/activate && gunicorn -c deployment/stg/config.py --preload wsgi'
ExecStart=/opt/driveate/wheel-size-services/.venv/bin/gunicorn -c deployment/stg/config.py wsgi
ExecReload=/bin/kill -s HUP $MAINPID
Restart=on-abort

[Install]
WantedBy=multi-user.target
