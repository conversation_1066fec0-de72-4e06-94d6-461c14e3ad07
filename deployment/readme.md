How to section
==============


Installation prerequisites
--------------------------

1) Installing pg_config needed by psycopg2
yum install python-devel postgresql-devel postgresql-libs

check the pg_config location and add it to PATH if it would under path already covered by PATH
# whereis pg_config
pg_config: /usr/bin/pg_config


2) Installing PIL with jpeg, free type, littlecms and zlib support. ======
yum install freetype freetype-devel libjpeg libjpeg-devel libpng libpng-devel zlib zlib-devel libtiff libtiff-devel



Installing lessc
----------------
pip install nodeenv
nodeenv -p
npm install -g less

or with anaconda
conda install -c javascript nodejs


Installing libcouchbase
----------------
http://developer.couchbase.com/documentation/server/current/sdk/c/start-using-sdk.html

Follow these additional instructions on <PERSON>ora
https://forums.couchbase.com/t/failed-to-synchronize-cache-for-repo-couchbase-server-disabling-and-2-more-messages-on-fedora-24/9865/5