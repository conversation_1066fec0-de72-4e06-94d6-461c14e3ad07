# Docker Compose override for Poetry operations
# Usage: docker-compose -f docker-compose.yml -f docker-compose.poetry.yml run poetry [command]

version: '3.8'

services:
  poetry:
    image: ws_services
    container_name: ws_services_poetry
    working_dir: /code
    environment:
      - PATH=/root/.pyenv/versions/3.12.0/bin:/usr/pgsql-*/bin:/usr/bin:/root/.pyenv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
      - PYTHONPATH=/code:/code/src
    volumes:
      - .:/code
    entrypoint: []
    command: >
      bash -c "
        /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false &&
        /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false &&
        /root/.pyenv/versions/3.12.0/bin/poetry install --no-root
      "
    profiles:
      - poetry
