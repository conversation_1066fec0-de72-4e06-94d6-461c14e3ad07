#!/usr/bin/env python3
"""
Verification script to test that all TailwindCSS classes used in the finder-v2 
widget configuration template are properly available in the portal configuration.
"""

import requests
from bs4 import BeautifulSoup

def test_tailwind_classes():
    """Test that all required TailwindCSS classes are available."""
    
    print("🧪 Testing TailwindCSS Classes Availability")
    print("=" * 60)
    
    # Classes that were previously missing and should now be available
    required_classes = [
        # Modern size utilities
        'size-5', 'size-20',
        
        # Divide utilities
        'divide-y', 'divide-gray-200', 'divide-x', 'sm:divide-x', 'sm:divide-y-0',
        
        # Ring utilities
        'ring-1', 'ring-inset', 'ring-gray-300',
        
        # Screen reader utility
        'sr-only',
        
        # Shadow variant
        'shadow-xs',
        
        # Flexbox utility
        'shrink-0',
        
        # Responsive utilities
        'sm:grid-cols-3', 'sm:items-center', 'sm:justify-between', 'sm:space-x-5',
        'sm:pt-1', 'sm:mt-0', 'sm:text-left', 'sm:text-2xl',
        
        # Layout utilities
        'justify-center', 'items-center',
        
        # Success notification classes
        'rounded-md', 'bg-green-50', 'p-4', 'mb-6', 'flex', 'ml-3',
        'text-sm', 'font-medium', 'text-green-800', 'mt-2', 'text-green-700',
        'text-green-400'
    ]
    
    base_url = "http://development.local:8000"
    config_url = f"{base_url}/widget/fd77b77985314fbab6047e9fa420360d/config-demo/"
    
    print("1. Loading configuration page...")
    response = requests.get(config_url)
    
    if response.status_code != 200:
        print(f"❌ Failed to load page. Status: {response.status_code}")
        return False
    
    print(f"✅ Page loaded successfully (Status: {response.status_code})")
    
    # Check if the page contains the classes in the HTML
    html_content = response.text
    
    print("\n2. Verifying TailwindCSS classes in HTML...")
    
    missing_classes = []
    found_classes = []
    
    for class_name in required_classes:
        if class_name in html_content:
            found_classes.append(class_name)
            print(f"✅ {class_name}")
        else:
            missing_classes.append(class_name)
            print(f"❌ {class_name}")
    
    print(f"\n📊 Results:")
    print(f"✅ Found: {len(found_classes)}/{len(required_classes)} classes")
    print(f"❌ Missing: {len(missing_classes)} classes")
    
    if missing_classes:
        print(f"\n🚨 Missing classes:")
        for class_name in missing_classes:
            print(f"  - {class_name}")
        return False
    
    print(f"\n🎉 All required TailwindCSS classes are available!")
    return True

def test_success_notification_structure():
    """Test that the success notification HTML structure is present."""
    
    print("\n🧪 Testing Success Notification Structure")
    print("=" * 60)
    
    base_url = "http://development.local:8000"
    config_url = f"{base_url}/widget/fd77b77985314fbab6047e9fa420360d/config-demo/"
    
    response = requests.get(config_url)
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Check for the messages section structure
    messages_comment = soup.find(string=lambda text: text and 'Success/Error Messages' in text)
    
    if messages_comment:
        print("✅ Messages section comment found")
    else:
        print("❌ Messages section comment not found")
        return False
    
    # Check for the template structure that includes our classes
    html_content = response.text
    
    # Key structure elements that should be present
    structure_checks = [
        ('rounded-md bg-green-50 p-4 mb-6', 'Success notification container'),
        ('shrink-0', 'Icon container'),
        ('size-5 text-green-400', 'SVG icon sizing'),
        ('text-sm font-medium text-green-800', 'Success title styling'),
        ('mt-2 text-sm text-green-700', 'Success message styling'),
    ]
    
    all_found = True
    for structure, description in structure_checks:
        if structure in html_content:
            print(f"✅ {description}: {structure}")
        else:
            print(f"❌ {description}: {structure}")
            all_found = False
    
    return all_found

def main():
    """Run all TailwindCSS verification tests."""
    
    print("TailwindCSS Classes Verification for Finder-v2 Widget")
    print("=" * 70)
    
    tests = [
        ("TailwindCSS Classes Availability", test_tailwind_classes),
        ("Success Notification Structure", test_success_notification_structure),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed with error: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 70)
    print("FINAL RESULTS:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\nThe TailwindCSS configuration has been successfully updated.")
        print("All classes used in the finder-v2 widget template are now available.")
        print("\nThe success notification system will render correctly with proper styling!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the TailwindCSS configuration and rebuild the CSS.")

if __name__ == "__main__":
    main()
