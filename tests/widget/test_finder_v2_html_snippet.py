#!/usr/bin/env python3
"""
Test Finder-v2 HTML Snippet Section

This test verifies that the HTML installation code section is properly displayed
on the finder-v2 configuration page with modern Tailwind CSS styling.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings.test')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.test.utils import override_settings

from src.apps.widgets.common.models.config import WidgetConfig
from src.apps.widgets.common.models.subscription import WidgetSubscription


@override_settings(
    ALLOWED_HOSTS=['testserver', 'development.local', 'localhost'],
    DEBUG=True
)
class FinderV2HtmlSnippetTest(TestCase):
    """Test HTML snippet section display for finder-v2 widgets."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create test user with unique username to avoid conflicts
        import uuid
        unique_suffix = str(uuid.uuid4())[:8]
        self.username = f'testuser_{unique_suffix}'
        
        self.user = User.objects.create_user(
            username=self.username,
            email=f'test_{unique_suffix}@example.com',
            password='testpass123'
        )
        
        # Create finder-v2 widget config
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Finder V2 Widget',
            type='finder-v2',
            lang='en',
            is_default=False,
            raw_params={
                "interface": {
                    "dimensions": {
                        "width": 900,
                        "height": ""
                    },
                    "tabs": {
                        "visible": ["by_vehicle", "by_tire", "by_rim"],
                        "primary": "by_vehicle"
                    }
                },
                "theme": {
                    "active": {
                        "theme_name": "modern_blue",
                        "templates": {
                            "page": "widgets/finder_v2/iframe/page.html"
                        }
                    },
                    "inactive": None
                },
                "content": {
                    "regions": [],
                    "filter": {
                        "brands": [],
                        "brands_exclude": []
                    }
                },
                "search_history": {
                    "enabled": True,
                    "max_items": 10,
                    "display_items": 5
                },
                "permissions": {
                    "domains": ["example.com", "test.local"]
                }
            }
        )
        
        # Create subscription for the widget
        self.subscription = WidgetSubscription.objects.create(
            widget_config=self.widget_config,
            contact_email='<EMAIL>',
            client_name='Test Client',
            notes='Test subscription'
        )

    def test_html_snippet_section_displays_for_authenticated_user(self):
        """Test that HTML snippet section displays for authenticated users with non-default widgets."""
        # Login as the test user
        self.client.login(username=self.username, password='testpass123')
        
        # Get the widget configuration page
        url = reverse('widget:configure', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that the HTML snippet section is present
        content = response.content.decode('utf-8')
        
        # Test main container with Tailwind styling
        self.assertIn('bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-8', content)
        
        # Test section title
        self.assertIn('Install Widget On Your Site', content)
        
        # Test code icon (SVG)
        self.assertIn('<path stroke-linecap="round" stroke-linejoin="round" d="M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5"', content)
        
        # Test info alert with blue styling
        self.assertIn('rounded-md bg-blue-50 p-4 mb-6 border border-blue-200', content)
        
        # Test code block container with dark theme
        self.assertIn('bg-gray-900 rounded-lg overflow-hidden', content)
        
        # Test macOS-style window controls
        self.assertIn('bg-red-500', content)  # Red dot
        self.assertIn('bg-yellow-500', content)  # Yellow dot
        self.assertIn('bg-green-500', content)  # Green dot
        
        # Test copy button
        self.assertIn('onclick="copyToClipboard()"', content)
        self.assertIn('Copy', content)
        
        # Test installation steps
        self.assertIn('Installation Steps:', content)
        self.assertIn('bg-blue-100 text-blue-800', content)  # Step numbers
        
        # Test that the widget UUID appears in the HTML code
        self.assertIn(str(self.widget_config.uuid), content)
        
        # Test that the widget type is finder-v2
        self.assertIn("type: 'finder-v2'", content)

    def test_html_snippet_section_not_displayed_for_demo_widgets(self):
        """Test that HTML snippet section is not displayed for demo configuration."""
        # Get the demo configuration page (no login required)
        url = reverse('widget:configure-demo', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        content = response.content.decode('utf-8')
        
        # HTML snippet section should NOT be present for demo widgets
        self.assertNotIn('Install Widget On Your Site', content)
        self.assertNotIn('bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-8', content)

    def test_html_snippet_contains_correct_widget_data(self):
        """Test that the HTML snippet contains correct widget-specific data."""
        # Login as the test user
        self.client.login(username=self.username, password='testpass123')
        
        # Get the widget configuration page
        url = reverse('widget:configure', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Test that widget-specific data is correctly embedded
        self.assertIn(f'uuid: \'{self.widget_config.uuid}\'', content)
        self.assertIn('type: \'finder-v2\'', content)
        self.assertIn('width: \'900\'', content)  # From widget config
        
        # Test JavaScript widget creation call
        self.assertIn('WheelSizeWidgets.create', content)
        
        # Test widget container div
        expected_container_id = f'ws-widget-{str(self.widget_config.uuid)[:6]}'
        self.assertIn(expected_container_id, content)

    def test_copy_functionality_javascript_present(self):
        """Test that copy to clipboard JavaScript functionality is present."""
        # Login as the test user
        self.client.login(username=self.username, password='testpass123')
        
        # Get the widget configuration page
        url = reverse('widget:configure', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Test JavaScript copy function
        self.assertIn('function copyToClipboard()', content)
        self.assertIn('navigator.clipboard.writeText', content)
        self.assertIn('document.execCommand(\'copy\')', content)  # Fallback
        
        # Test copy button IDs and interactions
        self.assertIn('copy-button-text', content)
        self.assertIn('code-block', content)
        
        # Test translated text for copy states
        self.assertIn('Copied!', content)

    def test_html_snippet_section_responsive_design(self):
        """Test that the HTML snippet section uses responsive Tailwind classes."""
        # Login as the test user
        self.client.login(username=self.username, password='testpass123')
        
        # Get the widget configuration page
        url = reverse('widget:configure', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode('utf-8')
        
        # Test responsive classes
        self.assertIn('flex items-center', content)
        self.assertIn('text-sm', content)
        self.assertIn('text-lg', content)
        
        # Test spacing and layout classes
        self.assertIn('mt-8', content)  # Top margin
        self.assertIn('mb-4', content)  # Bottom margin
        self.assertIn('p-4', content)   # Padding
        self.assertIn('space-y-1', content)  # Vertical spacing
        
        # Test color scheme consistency
        self.assertIn('text-gray-900', content)
        self.assertIn('text-gray-600', content)
        self.assertIn('text-blue-600', content)


def run_test_suite():
    """Run the HTML snippet test suite."""
    import unittest
    
    print("\n🎨 Finder-v2 HTML Snippet UI Test Suite")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(FinderV2HtmlSnippetTest)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Report results
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 ALL TESTS PASSED - HTML snippet section is working correctly!")
        print(f"✅ Tests: {result.testsRun} passed, 0 failed")
        print("\nUI Features Validated:")
        print("✅ Modern Tailwind CSS styling")
        print("✅ Code block with syntax highlighting")
        print("✅ Copy to clipboard functionality")
        print("✅ Installation instructions")
        print("✅ Responsive design")
        print("✅ Proper authentication handling")
        return True
    else:
        print(f"❌ Tests: {result.testsRun - len(result.failures) - len(result.errors)} passed, "
              f"{len(result.failures)} failed, {len(result.errors)} errors")
        print("\nFailures and errors above show what needs to be fixed.")
        return False


if __name__ == '__main__':
    success = run_test_suite()
    sys.exit(0 if success else 1)