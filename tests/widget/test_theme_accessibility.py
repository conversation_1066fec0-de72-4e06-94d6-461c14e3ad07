"""
Accessibility tests for the finder-v2 theme system.

This test suite covers:
- WCAG 2.1 AA compliance
- Color contrast ratios
- Font size accessibility
- Keyboard navigation
- Screen reader compatibility
- Reduced motion support
- Focus management
"""

import pytest
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from axe_selenium_python import Axe

from src.apps.widgets.common.models import WidgetConfig, WidgetTheme
from src.apps.widgets.finder_v2.utils.theme_generator import ThemeGenerator


class ColorContrastAccessibilityTests(TestCase):
    """Test color contrast accessibility compliance."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Accessibility Test Widget'
        )

    def test_wcag_aa_contrast_compliance(self):
        """Test WCAG 2.1 AA contrast compliance (4.5:1 ratio)."""
        # Test high contrast theme
        high_contrast_theme = WidgetTheme(
            widget=self.widget_config,
            theme_name='High Contrast',
            background_color='#FFFFFF',
            text_color='#000000',  # 21:1 contrast ratio
            primary_color='#0066CC',  # 4.5:1 contrast ratio on white
            secondary_color='#666666'  # 4.5:1 contrast ratio on white
        )
        
        # Should pass validation
        high_contrast_theme.full_clean()
        
        # Verify contrast ratios
        contrast_ratio = high_contrast_theme._calculate_contrast_ratio(
            high_contrast_theme.background_color,
            high_contrast_theme.text_color
        )
        self.assertGreaterEqual(contrast_ratio, 4.5)

    def test_wcag_aaa_contrast_compliance(self):
        """Test WCAG 2.1 AAA contrast compliance (7:1 ratio)."""
        # Test AAA level contrast
        aaa_theme = WidgetTheme(
            widget=self.widget_config,
            theme_name='AAA Contrast',
            background_color='#FFFFFF',
            text_color='#000000',  # 21:1 contrast ratio
            primary_color='#003366',  # 7:1+ contrast ratio
            secondary_color='#333333'  # 7:1+ contrast ratio
        )
        
        aaa_theme.full_clean()
        
        # Verify AAA level contrast
        contrast_ratio = aaa_theme._calculate_contrast_ratio(
            aaa_theme.background_color,
            aaa_theme.text_color
        )
        self.assertGreaterEqual(contrast_ratio, 7.0)

    def test_dark_theme_contrast_compliance(self):
        """Test dark theme contrast compliance."""
        # Test dark theme
        dark_theme = WidgetTheme(
            widget=self.widget_config,
            theme_name='Dark Theme',
            background_color='#1A1A1A',
            text_color='#FFFFFF',  # High contrast on dark
            primary_color='#66B3FF',  # Light blue for dark backgrounds
            secondary_color='#CCCCCC'  # Light gray for dark backgrounds
        )
        
        dark_theme.full_clean()
        
        # Verify dark theme contrast
        contrast_ratio = dark_theme._calculate_contrast_ratio(
            dark_theme.background_color,
            dark_theme.text_color
        )
        self.assertGreaterEqual(contrast_ratio, 4.5)

    def test_color_blindness_accessibility(self):
        """Test color accessibility for color blindness."""
        # Test theme that doesn't rely solely on color for information
        accessible_theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Color Blind Friendly',
            primary_color='#0066CC',  # Blue (distinguishable)
            secondary_color='#666666',  # Gray (neutral)
            accent_color='#FF6600',  # Orange (distinguishable from blue)
            background_color='#FFFFFF',
            text_color='#000000'
        )
        
        generator = ThemeGenerator(accessible_theme)
        css = generator.generate_theme_css()
        
        # Should use distinguishable colors
        self.assertIn('--theme-primary: #0066CC', css)
        self.assertIn('--theme-accent: #FF6600', css)

    def test_focus_indicator_contrast(self):
        """Test focus indicator contrast accessibility."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Focus Test',
            primary_color='#0066CC',
            accent_color='#FF6600',  # High contrast focus color
            background_color='#FFFFFF'
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should include focus styles with high contrast
        self.assertIn(':focus', css)
        self.assertIn('border-color: var(--theme-accent)', css)
        self.assertIn('box-shadow:', css)


class FontAccessibilityTests(TestCase):
    """Test font accessibility compliance."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Font Accessibility Test Widget'
        )

    def test_minimum_font_size_compliance(self):
        """Test minimum font size compliance."""
        # Test minimum recommended font size (16px)
        accessible_theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Accessible Font Size',
            base_font_size='16px',  # Minimum recommended
            line_height='1.5',  # Recommended line height
            letter_spacing='0'  # Normal spacing
        )
        
        generator = ThemeGenerator(accessible_theme)
        css = generator.generate_theme_css()
        
        # Should include accessible font properties
        self.assertIn('--theme-font-size: 16px', css)
        self.assertIn('--theme-line-height: 1.5', css)

    def test_font_readability_properties(self):
        """Test font readability properties."""
        readable_theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Readable Font',
            font_family='system-ui',  # System fonts are typically readable
            font_weight='400',  # Normal weight
            line_height='1.6',  # Generous line height
            letter_spacing='0.025em'  # Slight letter spacing
        )
        
        generator = ThemeGenerator(readable_theme)
        css = generator.generate_theme_css()
        
        # Should include readability properties
        self.assertIn('--theme-font-weight: 400', css)
        self.assertIn('--theme-line-height: 1.6', css)
        self.assertIn('--theme-letter-spacing: 0.025em', css)

    def test_dyslexia_friendly_fonts(self):
        """Test dyslexia-friendly font configuration."""
        dyslexia_theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Dyslexia Friendly',
            font_family='Open Sans',  # Dyslexia-friendly font
            font_weight='400',
            line_height='1.8',  # Extra line height
            letter_spacing='0.05em'  # Extra letter spacing
        )
        
        generator = ThemeGenerator(dyslexia_theme)
        css = generator.generate_theme_css()
        
        # Should include dyslexia-friendly properties
        self.assertIn('\"Open Sans\"', css)
        self.assertIn('--theme-line-height: 1.8', css)
        self.assertIn('--theme-letter-spacing: 0.05em', css)

    def test_responsive_font_scaling(self):
        """Test responsive font scaling for accessibility."""
        responsive_theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Responsive Font',
            base_font_size='16px',
            advanced_config={
                'responsive_overrides': {
                    'mobile': {
                        'font_size': '14px',  # Slightly smaller on mobile
                        'line_height': '1.6'
                    },
                    'tablet': {
                        'font_size': '16px',
                        'line_height': '1.5'
                    }
                }
            }
        )
        
        generator = ThemeGenerator(responsive_theme)
        css = generator.generate_css_custom_properties()
        
        # Should include responsive font sizing
        self.assertIn('@media (max-width: 767px)', css)
        self.assertIn('--theme-font_size: 14px', css)


class KeyboardNavigationTests(TestCase):
    """Test keyboard navigation accessibility."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Keyboard Navigation Test Widget'
        )

    def test_focus_management_css(self):
        """Test focus management CSS generation."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Focus Management',
            primary_color='#0066CC',
            accent_color='#FF6600',
            animation_speed='0.2s'
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should include focus styles
        self.assertIn(':focus', css)
        self.assertIn('border-color: var(--theme-accent)', css)
        self.assertIn('transition:', css)

    def test_keyboard_interaction_styles(self):
        """Test keyboard interaction styles."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Keyboard Interaction',
            primary_color='#0066CC',
            hover_effect='scale',
            animation_speed='0.3s'
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should include hover and focus styles
        self.assertIn(':hover', css)
        self.assertIn('transform:', css)
        self.assertIn('transition:', css)

    def test_skip_link_styling(self):
        """Test skip link styling for keyboard navigation."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Skip Link Styling',
            primary_color='#0066CC',
            background_color='#FFFFFF',
            text_color='#000000'
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should include accessible colors for skip links
        self.assertIn('--theme-primary: #0066CC', css)
        self.assertIn('--theme-background: #FFFFFF', css)


class ReducedMotionTests(TestCase):
    """Test reduced motion accessibility support."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Reduced Motion Test Widget'
        )

    def test_reduced_motion_support(self):
        """Test reduced motion media query support."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Reduced Motion',
            animation_speed='0.3s',
            hover_effect='scale'
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should include reduced motion support
        self.assertIn('transition:', css)
        self.assertIn('0.3s', css)
        
        # Could add @media (prefers-reduced-motion) support in future
        # self.assertIn('@media (prefers-reduced-motion: reduce)', css)

    def test_animation_control_properties(self):
        """Test animation control properties."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Animation Control',
            animation_speed='0.2s',
            hover_effect='darken'
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should include animation control
        self.assertIn('--theme-animation-speed: 0.2s', css)
        self.assertIn('--theme-hover-effect: brightness(0.9)', css)


class ScreenReaderAccessibilityTests(TestCase):
    """Test screen reader accessibility."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Screen Reader Test Widget'
        )
        self.client = Client()

    def test_semantic_html_structure(self):
        """Test semantic HTML structure for screen readers."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Screen Reader Friendly',
            primary_color='#0066CC'
        )
        
        # Test configuration page
        self.client.login(username='testuser', password='testpass123')
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        response = self.client.get(url)
        
        # Should contain semantic HTML
        self.assertContains(response, '<h3>')  # Section headings
        self.assertContains(response, '<h4>')  # Subsection headings
        self.assertContains(response, '<label')  # Form labels
        self.assertContains(response, 'for=')  # Label associations

    def test_aria_labels_and_descriptions(self):
        """Test ARIA labels and descriptions."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='ARIA Labels',
            primary_color='#0066CC'
        )
        
        self.client.login(username='testuser', password='testpass123')
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        response = self.client.get(url)
        
        # Should contain ARIA attributes
        self.assertContains(response, 'aria-')  # ARIA attributes
        # Could add more specific ARIA testing

    def test_color_picker_accessibility(self):
        """Test color picker accessibility for screen readers."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Color Picker A11y',
            primary_color='#0066CC'
        )
        
        self.client.login(username='testuser', password='testpass123')
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        response = self.client.get(url)
        
        # Should contain accessible color inputs
        self.assertContains(response, 'type=\"color\"')
        self.assertContains(response, 'Primary Color')  # Label text


class AccessibilityIntegrationTests(TestCase):
    """Integration tests for overall accessibility."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Accessibility Integration Test Widget'
        )
        self.client = Client()

    def test_complete_accessibility_workflow(self):
        """Test complete accessibility workflow."""
        # 1. Create accessible theme
        accessible_theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Fully Accessible',
            primary_color='#0066CC',  # WCAG AA compliant
            secondary_color='#666666',
            accent_color='#FF6600',
            background_color='#FFFFFF',
            text_color='#000000',
            font_family='system-ui',
            base_font_size='16px',
            font_weight='400',
            line_height='1.6',
            letter_spacing='0.025em',
            animation_speed='0.3s',
            hover_effect='darken'
        )
        
        # 2. Validate theme accessibility
        accessible_theme.full_clean()  # Should pass validation
        
        # 3. Generate accessible CSS
        generator = ThemeGenerator(accessible_theme)
        css = generator.generate_theme_css()
        
        # 4. Validate CSS accessibility
        validation_result = ThemeGenerator.validate_theme_css(css)
        self.assertTrue(validation_result['valid'])
        
        # 5. Test in configuration interface
        self.client.login(username='testuser', password='testpass123')
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        response = self.client.get(url)
        
        # Should load successfully
        self.assertEqual(response.status_code, 200)
        
        # Should contain accessible theme values
        self.assertContains(response, 'Fully Accessible')
        self.assertContains(response, '#0066CC')

    def test_accessibility_error_handling(self):
        """Test accessibility error handling."""
        # Test theme with accessibility issues
        try:
            problematic_theme = WidgetTheme(
                widget=self.widget_config,
                theme_name='Problematic Theme',
                background_color='#FFFFFF',
                text_color='#CCCCCC',  # Poor contrast
                primary_color='#DDDDDD'  # Poor contrast
            )
            problematic_theme.full_clean()
            self.fail('Should have raised ValidationError for poor contrast')
        except ValidationError as e:
            # Should catch accessibility violations
            self.assertIn('contrast', str(e))

    def test_high_contrast_mode_support(self):
        """Test high contrast mode support."""
        # Create high contrast theme
        high_contrast_theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='High Contrast Mode',
            primary_color='#000000',
            secondary_color='#000000',
            accent_color='#0000FF',  # Blue for links
            background_color='#FFFFFF',
            text_color='#000000',
            border_width='2px'  # Stronger borders
        )
        
        generator = ThemeGenerator(high_contrast_theme)
        css = generator.generate_theme_css()
        
        # Should include high contrast properties
        self.assertIn('--theme-primary: #000000', css)
        self.assertIn('--theme-border-width: 2px', css)
        self.assertIn('--theme-accent: #0000FF', css)


class AccessibilityAxeTests(TestCase):
    """Automated accessibility testing using axe-core."""

    @classmethod
    def setUpClass(cls):
        """Set up Selenium WebDriver for axe testing."""
        super().setUpClass()
        
        # Only run if Chrome is available
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            cls.driver = webdriver.Chrome(options=chrome_options)
            cls.axe = Axe(cls.driver)
        except Exception:
            cls.driver = None
            cls.axe = None

    @classmethod
    def tearDownClass(cls):
        """Clean up Selenium WebDriver."""
        if cls.driver:
            cls.driver.quit()
        super().tearDownClass()

    def setUp(self):
        """Set up test data."""
        if not self.driver:
            self.skipTest('Chrome WebDriver not available')
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Axe Test Widget'
        )

    def test_theme_configuration_accessibility(self):
        """Test theme configuration page accessibility with axe."""
        if not self.driver or not self.axe:
            self.skipTest('Chrome WebDriver or axe-core not available')
        
        # Create theme
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Axe Test Theme',
            primary_color='#0066CC',
            background_color='#FFFFFF',
            text_color='#000000'
        )
        
        # Navigate to configuration page
        # Note: This would require a running Django server
        # self.driver.get('http://localhost:8000/login/')
        # ... login process ...
        # self.driver.get(f'http://localhost:8000/widget/{self.widget_config.id}/config/')
        
        # Inject axe-core
        # self.axe.inject()
        
        # Run accessibility scan
        # results = self.axe.run()
        
        # Assert no violations
        # self.assertEqual(len(results['violations']), 0, 
        #                 f'Accessibility violations found: {results["violations"]}')

    def test_widget_preview_accessibility(self):
        """Test widget preview accessibility with axe."""
        if not self.driver or not self.axe:
            self.skipTest('Chrome WebDriver or axe-core not available')
        
        # This would test the actual widget preview
        # Similar to above but for the widget preview iframe
        pass


if __name__ == '__main__':
    # Run accessibility tests
    import unittest
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add accessibility test classes
    suite.addTest(unittest.makeSuite(ColorContrastAccessibilityTests))
    suite.addTest(unittest.makeSuite(FontAccessibilityTests))
    suite.addTest(unittest.makeSuite(KeyboardNavigationTests))
    suite.addTest(unittest.makeSuite(ReducedMotionTests))
    suite.addTest(unittest.makeSuite(ScreenReaderAccessibilityTests))
    suite.addTest(unittest.makeSuite(AccessibilityIntegrationTests))
    
    # Optionally add axe tests (requires Chrome and axe-selenium-python)
    # suite.addTest(unittest.makeSuite(AccessibilityAxeTests))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f\"\\nAccessibility Tests Summary:\")
    print(f\"Tests run: {result.testsRun}\")
    print(f\"Failures: {len(result.failures)}\")
    print(f\"Errors: {len(result.errors)}\")
    
    if result.failures:
        print(\"\\nFailures:\")
        for test, traceback in result.failures:
            print(f\"- {test}: {traceback}\")
    
    if result.errors:
        print(\"\\nErrors:\")
        for test, traceback in result.errors:
            print(f\"- {test}: {traceback}\")