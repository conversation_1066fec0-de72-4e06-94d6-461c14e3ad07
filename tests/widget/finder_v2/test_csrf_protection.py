"""
Unit tests for Finder-v2 CSRF Protection
"""

import hashlib
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.middleware.csrf import get_token
from unittest.mock import patch

from src.apps.widgets.main.models import WidgetConfig, WidgetSubscription
from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType


class FinderV2CSRFProtectionTest(TestCase):
    """Test CSRF protection for finder-v2 widgets."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create finder-v2 widget configuration
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Finder-v2 Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        # Create subscription
        self.subscription = WidgetSubscription.objects.create(
            widget_config=self.widget_config,
            contact_email='<EMAIL>',
            client_name='Test Client'
        )
        
    def test_csrf_token_generation(self):
        """Test that CSRF tokens are properly generated for finder-v2 widgets."""
        # Get widget iframe page
        iframe_url = reverse('widget:iframe', args=[self.widget_config.slug])
        response = self.client.get(iframe_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check that CSRF token is in the response
        content = response.content.decode('utf-8')
        self.assertIn('csrfToken', content)
        self.assertIn('FinderV2Config', content)
        
    def test_legacy_csrf_algorithm_compatibility(self):
        """Test that legacy CSRF algorithm produces expected results."""
        # Test the legacy CSRF algorithm with known inputs
        test_cases = [
            {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'expected_pattern': r'^[a-f0-9]{32}$'  # 32-character hex string
            },
            {
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'expected_pattern': r'^[a-f0-9]{32}$'
            }
        ]
        
        for test_case in test_cases:
            with self.subTest(user_agent=test_case['user_agent']):
                # Simulate the legacy CSRF algorithm
                user_agent = test_case['user_agent']
                
                # Legacy algorithm: MD5 hash of user agent
                legacy_token = hashlib.md5(user_agent.encode('utf-8')).hexdigest()
                
                # Verify token format
                import re
                self.assertRegex(legacy_token, test_case['expected_pattern'])
                self.assertEqual(len(legacy_token), 32)
                
    def test_csrf_token_validation(self):
        """Test CSRF token validation for API requests."""
        # Get CSRF token from iframe page
        iframe_url = reverse('widget:iframe', args=[self.widget_config.slug])
        response = self.client.get(iframe_url)
        csrf_token = get_token(self.client.session)
        
        # Test API request with valid CSRF token
        api_url = reverse('widget-api:makes', args=[self.widget_config.slug])
        
        # GET requests should work without CSRF token
        get_response = self.client.get(api_url)
        self.assertNotEqual(get_response.status_code, 403)
        
        # POST requests would require CSRF token (if implemented)
        # This is a placeholder for future POST endpoint testing
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_cross_domain_csrf_protection(self, mock_live_settings):
        """Test CSRF protection for cross-domain widget embedding."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        # Test request from different domain
        api_url = reverse('widget-api:makes', args=[self.widget_config.slug])
        
        # Simulate cross-domain request
        response = self.client.get(
            api_url,
            HTTP_REFERER='https://client-website.com/page-with-widget/',
            HTTP_USER_AGENT='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        
        # Should work for GET requests (CSRF mainly for POST/PUT/DELETE)
        self.assertNotEqual(response.status_code, 403)
        
    def test_same_origin_requests(self):
        """Test that same-origin requests are properly handled."""
        # Test request from same origin
        api_url = reverse('widget-api:makes', args=[self.widget_config.slug])
        
        # Simulate same-origin request
        response = self.client.get(
            api_url,
            HTTP_REFERER='http://testserver/widget/try/',
            HTTP_HOST='testserver'
        )
        
        # Should work without issues
        self.assertNotEqual(response.status_code, 403)
        
    def test_port_aware_hostname_validation(self):
        """Test port-aware hostname validation for development environments."""
        # Test with port in hostname
        api_url = reverse('widget-api:makes', args=[self.widget_config.slug])
        
        # Simulate development environment with port
        response = self.client.get(
            api_url,
            HTTP_REFERER='http://development.local/widget/try/',
            HTTP_HOST='development.local:8000'
        )
        
        # Should work (port should be ignored in development)
        self.assertNotEqual(response.status_code, 403)
        
    def test_csrf_token_in_vue_config(self):
        """Test that CSRF token is properly passed to Vue 3 app."""
        iframe_url = reverse('widget:iframe', args=[self.widget_config.slug])
        response = self.client.get(iframe_url)
        
        content = response.content.decode('utf-8')
        
        # Check that Vue 3 config includes CSRF token
        self.assertIn('window.FinderV2Config', content)
        self.assertIn('csrfToken', content)
        
        # Check that config is properly structured
        self.assertIn('widgetResources', content)
        self.assertIn('flowType', content)
        self.assertIn('apiVersion', content)
        
    def test_no_trusted_hostnames_bypass(self):
        """Test that trusted_hostnames bypass is not implemented (security)."""
        # This test ensures that the security vulnerability mentioned in
        # the existing codebase is not present in finder-v2
        
        iframe_url = reverse('widget:iframe', args=[self.widget_config.slug])
        response = self.client.get(iframe_url)
        
        content = response.content.decode('utf-8')
        
        # Should not contain trusted_hostnames configuration
        self.assertNotIn('trusted_hostnames', content.lower())
        self.assertNotIn('trustedhostnames', content.lower())
        
    def test_csrf_protection_with_different_user_agents(self):
        """Test CSRF protection with various user agent strings."""
        test_user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
            'Mozilla/5.0 (Android 11; Mobile; rv:92.0) Gecko/92.0 Firefox/92.0'
        ]
        
        api_url = reverse('widget-api:makes', args=[self.widget_config.slug])
        
        for user_agent in test_user_agents:
            with self.subTest(user_agent=user_agent):
                response = self.client.get(
                    api_url,
                    HTTP_USER_AGENT=user_agent
                )
                
                # Should work regardless of user agent
                self.assertNotEqual(response.status_code, 403)
