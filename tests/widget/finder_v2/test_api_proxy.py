"""
Unit tests for Finder-v2 API Proxy functionality
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from unittest.mock import patch, MagicMock

from src.apps.widgets.main.models import WidgetConfig, WidgetSubscription
from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType


class FinderV2ApiProxyTest(TestCase):
    """Test finder-v2 API proxy functionality with v2 endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create finder-v2 widget configuration
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Finder-v2 Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        # Create subscription
        self.subscription = WidgetSubscription.objects.create(
            widget_config=self.widget_config,
            contact_email='<EMAIL>',
            client_name='Test Client'
        )
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_feature_flag_enabled(self, mock_live_settings):
        """Test API proxy when finder-v2 feature flag is enabled."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        # Test makes endpoint
        url = reverse('widget-api:makes', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        # Should not return 404 (feature flag check passed)
        self.assertNotEqual(response.status_code, 404)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_feature_flag_disabled(self, mock_live_settings):
        """Test API proxy when finder-v2 feature flag is disabled."""
        # Disable feature flag
        mock_live_settings.return_value = False
        
        # Test makes endpoint
        url = reverse('widget-api:makes', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        # Should return 404 (feature flag disabled)
        self.assertEqual(response.status_code, 404)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_v2_api_endpoints(self, mock_live_settings):
        """Test that finder-v2 uses v2 API endpoints."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        # Test various v2 endpoints
        endpoints = [
            'makes',
            'models', 
            'years',
            'modifications',
            'generations',
            'search-by-model'
        ]
        
        for endpoint in endpoints:
            with self.subTest(endpoint=endpoint):
                url = reverse(f'widget-api:{endpoint}', args=[self.widget_config.slug])
                response = self.client.get(url)
                
                # Should not return 404 (endpoint exists)
                self.assertNotEqual(response.status_code, 404)
                
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_flow_type_parameter(self, mock_live_settings):
        """Test that flow_type parameter is passed to API requests."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        # Test with primary flow
        self.widget_config.raw_params['flow_type'] = 'primary'
        self.widget_config.save()
        
        url = reverse('widget-api:makes', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        # Should not return 404
        self.assertNotEqual(response.status_code, 404)
        
        # Test with alternative flow
        self.widget_config.raw_params['flow_type'] = 'alternative'
        self.widget_config.save()
        
        response = self.client.get(url)
        
        # Should not return 404
        self.assertNotEqual(response.status_code, 404)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_csrf_protection(self, mock_live_settings):
        """Test that CSRF protection is maintained for finder-v2."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        # Test without CSRF token (should work for GET requests)
        url = reverse('widget-api:makes', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        # GET requests should work without CSRF token
        self.assertNotEqual(response.status_code, 403)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_throttling_protection(self, mock_live_settings):
        """Test that rate limiting is applied to finder-v2 API requests."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        url = reverse('widget-api:makes', args=[self.widget_config.slug])
        
        # Make multiple requests to test throttling
        responses = []
        for i in range(5):
            response = self.client.get(url)
            responses.append(response.status_code)
            
        # At least some requests should succeed
        success_count = sum(1 for status in responses if status == 200)
        self.assertGreater(success_count, 0)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_widget_isolation(self, mock_live_settings):
        """Test that finder-v2 widgets are isolated from legacy finder widgets."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        # Create legacy finder widget
        legacy_config = WidgetConfig.objects.create(
            user=self.user,
            name='Legacy Finder Widget',
            type='finder',
            raw_params={'test': 'legacy'},
            is_default=False
        )
        
        legacy_subscription = WidgetSubscription.objects.create(
            widget_config=legacy_config,
            contact_email='<EMAIL>',
            client_name='Legacy Client'
        )
        
        # Test finder-v2 endpoint
        finder_v2_url = reverse('widget-api:makes', args=[self.widget_config.slug])
        finder_v2_response = self.client.get(finder_v2_url)
        
        # Test legacy finder endpoint
        legacy_url = reverse('widget-api:makes', args=[legacy_config.slug])
        legacy_response = self.client.get(legacy_url)
        
        # Both should work but be independent
        self.assertNotEqual(finder_v2_response.status_code, 404)
        self.assertNotEqual(legacy_response.status_code, 404)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_new_v2_endpoints(self, mock_live_settings):
        """Test new v2-specific endpoints (modifications, generations)."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        # Test modifications endpoint (new in v2)
        modifications_url = reverse('widget-api:modifications', args=[self.widget_config.slug])
        modifications_response = self.client.get(modifications_url, {
            'make': 'toyota',
            'model': 'camry',
            'year': '2023'
        })
        
        # Should not return 404 (endpoint exists)
        self.assertNotEqual(modifications_response.status_code, 404)
        
        # Test generations endpoint (new in v2 alternative flow)
        generations_url = reverse('widget-api:generations', args=[self.widget_config.slug])
        generations_response = self.client.get(generations_url, {
            'make': 'toyota',
            'model': 'camry'
        })
        
        # Should not return 404 (endpoint exists)
        self.assertNotEqual(generations_response.status_code, 404)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_api_authentication_headers(self, mock_live_settings):
        """Test that proper authentication headers are included."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        # This test verifies that the proxy view includes proper headers
        # The actual header validation happens in the proxy implementation
        url = reverse('widget-api:makes', args=[self.widget_config.slug])
        response = self.client.get(url)
        
        # Should not return authentication error
        self.assertNotEqual(response.status_code, 401)
        self.assertNotEqual(response.status_code, 403)

    @patch('ws_live_settings.models.WsLiveSettings.get')
    @patch('src.apps.widgets.api_proxy.views.requests.get')
    def test_region_filtering_parameters(self, mock_requests_get, mock_live_settings):
        """Test that region parameters are correctly passed to API requests."""
        # Enable feature flag
        mock_live_settings.return_value = True

        # Mock successful API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'data': [{'slug': 'toyota', 'display': 'Toyota'}]}
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_requests_get.return_value = mock_response

        # Configure widget with regions
        self.widget_config.raw_params['content'] = {
            'regions': ['usdm', 'cdm'],
            'filter': {'by': None, 'brands': [], 'brands_exclude': []},
            'only_oem': False
        }
        self.widget_config.save()

        # Test makes endpoint (should include region parameters)
        url = reverse('widget-api:makes', args=[self.widget_config.slug])
        response = self.client.get(url)

        # Verify the request was made
        self.assertTrue(mock_requests_get.called)

        # Get the call arguments
        call_args = mock_requests_get.call_args

        # Check that region parameters were included
        if call_args and len(call_args) > 1 and 'params' in call_args[1]:
            params = call_args[1]['params']
            self.assertIn('region', params)
            # Region parameter should be a list with our configured regions
            self.assertEqual(params['region'], ['usdm', 'cdm'])

        # Response should be successful
        self.assertEqual(response.status_code, 200)

    @patch('ws_live_settings.models.WsLiveSettings.get')
    @patch('src.apps.widgets.api_proxy.views.requests.get')
    def test_region_filtering_search_endpoint_exclusion(self, mock_requests_get, mock_live_settings):
        """Test that region parameters are NOT passed to search endpoints."""
        # Enable feature flag
        mock_live_settings.return_value = True

        # Mock successful API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'data': []}
        mock_response.headers = {'Content-Type': 'application/json'}
        mock_requests_get.return_value = mock_response

        # Configure widget with regions
        self.widget_config.raw_params['content'] = {
            'regions': ['usdm', 'cdm'],
            'filter': {'by': None, 'brands': [], 'brands_exclude': []},
            'only_oem': False
        }
        self.widget_config.save()

        # Test search-by-model endpoint (should NOT include region parameters)
        url = reverse('widget-api:search-by-model', args=[self.widget_config.slug])
        response = self.client.get(url, {
            'make': 'toyota',
            'model': 'camry',
            'year': '2023'
        })

        # Verify the request was made
        self.assertTrue(mock_requests_get.called)

        # Get the call arguments
        call_args = mock_requests_get.call_args

        # Check that region parameters were NOT included
        if call_args and len(call_args) > 1 and 'params' in call_args[1]:
            params = call_args[1]['params']
            self.assertNotIn('region', params)

        # Response should be successful
        self.assertEqual(response.status_code, 200)
