"""
Browser tests for Finder-v2 Widget using Playwright

These tests verify that the widget works correctly in real browser environments
and test the Vue 3 app functionality, iframe embedding, and user interactions.
"""

import asyncio
import pytest
from playwright.async_api import async_playwright, <PERSON>, Browser
from django.test import LiveServerTestCase
from django.contrib.auth.models import User
from unittest.mock import patch

from src.apps.widgets.main.models import WidgetConfig, WidgetSubscription
from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType


class FinderV2BrowserTest(LiveServerTestCase):
    """Browser tests for finder-v2 widget functionality."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test class."""
        super().setUpClass()
        cls.browser = None
        cls.page = None
        
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create finder-v2 widget
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Browser Test Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        self.subscription = WidgetSubscription.objects.create(
            widget_config=self.widget_config,
            contact_email='<EMAIL>',
            client_name='Browser Test Client'
        )
        
    async def async_setUp(self):
        """Set up browser for async tests."""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=True)
        self.page = await self.browser.new_page()
        
    async def async_tearDown(self):
        """Clean up browser after async tests."""
        if self.page:
            await self.page.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
            
    def test_widget_iframe_loading(self):
        """Test that widget iframe loads correctly in browser."""
        async def run_test():
            await self.async_setUp()
            
            try:
                # Navigate to widget iframe
                widget_url = f"{self.live_server_url}/widget/{self.widget_config.slug}/"
                await self.page.goto(widget_url)
                
                # Wait for Vue app to mount
                await self.page.wait_for_selector('#finder-v2-app', timeout=5000)
                
                # Check that Vue app is present
                app_element = await self.page.query_selector('#finder-v2-app')
                self.assertIsNotNone(app_element)
                
                # Check that tabs are rendered
                tabs = await self.page.query_selector_all('.nav-link')
                self.assertGreater(len(tabs), 0)
                
                # Check that default tab is active
                active_tab = await self.page.query_selector('.nav-link.active')
                self.assertIsNotNone(active_tab)
                
            finally:
                await self.async_tearDown()
                
        asyncio.run(run_test())
        
    def test_tab_switching_functionality(self):
        """Test that tab switching works correctly."""
        async def run_test():
            await self.async_setUp()
            
            try:
                widget_url = f"{self.live_server_url}/widget/{self.widget_config.slug}/"
                await self.page.goto(widget_url)
                
                # Wait for Vue app
                await self.page.wait_for_selector('#finder-v2-app')
                
                # Get all tabs
                tabs = await self.page.query_selector_all('.nav-link')
                
                if len(tabs) > 1:
                    # Click second tab
                    await tabs[1].click()
                    
                    # Wait for tab content to change
                    await self.page.wait_for_timeout(500)
                    
                    # Check that second tab is now active
                    active_tabs = await self.page.query_selector_all('.nav-link.active')
                    self.assertEqual(len(active_tabs), 1)
                    
                    # Check that correct tab content is displayed
                    tab_content = await self.page.query_selector('.tab-pane')
                    self.assertIsNotNone(tab_content)
                    
            finally:
                await self.async_tearDown()
                
        asyncio.run(run_test())
        
    def test_dropdown_interactions(self):
        """Test that HeadlessUI dropdown components work correctly."""
        async def run_test():
            await self.async_setUp()
            
            try:
                widget_url = f"{self.live_server_url}/widget/{self.widget_config.slug}/"
                await self.page.goto(widget_url)
                
                # Wait for Vue app
                await self.page.wait_for_selector('#finder-v2-app')
                
                # Look for dropdown buttons (CustomSelector components)
                dropdown_buttons = await self.page.query_selector_all('[role="button"]')
                
                if len(dropdown_buttons) > 0:
                    # Click first dropdown
                    await dropdown_buttons[0].click()
                    
                    # Wait for dropdown to open
                    await self.page.wait_for_timeout(300)
                    
                    # Check if dropdown options are visible
                    dropdown_options = await self.page.query_selector_all('[role="option"]')
                    
                    # Note: In a real test, we'd mock the API responses
                    # For now, just verify the dropdown mechanism works
                    
            finally:
                await self.async_tearDown()
                
        asyncio.run(run_test())
        
    def test_responsive_design(self):
        """Test that widget is responsive across different screen sizes."""
        async def run_test():
            await self.async_setUp()
            
            try:
                widget_url = f"{self.live_server_url}/widget/{self.widget_config.slug}/"
                
                # Test different viewport sizes
                viewports = [
                    {'width': 320, 'height': 568},   # Mobile
                    {'width': 768, 'height': 1024},  # Tablet
                    {'width': 1200, 'height': 800},  # Desktop
                ]
                
                for viewport in viewports:
                    await self.page.set_viewport_size(viewport)
                    await self.page.goto(widget_url)
                    
                    # Wait for Vue app
                    await self.page.wait_for_selector('#finder-v2-app')
                    
                    # Check that widget is visible and properly sized
                    widget_element = await self.page.query_selector('.finder-v2-widget')
                    self.assertIsNotNone(widget_element)
                    
                    # Check that content doesn't overflow
                    bounding_box = await widget_element.bounding_box()
                    self.assertIsNotNone(bounding_box)
                    self.assertLessEqual(bounding_box['width'], viewport['width'])
                    
            finally:
                await self.async_tearDown()
                
        asyncio.run(run_test())
        
    def test_error_handling(self):
        """Test that widget handles errors gracefully."""
        async def run_test():
            await self.async_setUp()
            
            try:
                widget_url = f"{self.live_server_url}/widget/{self.widget_config.slug}/"
                await self.page.goto(widget_url)
                
                # Wait for Vue app
                await self.page.wait_for_selector('#finder-v2-app')
                
                # Check that no JavaScript errors occurred
                # (This would be captured by page.on('pageerror') if set up)
                
                # Verify that error states are handled properly
                # (This would require mocking API failures)
                
                # For now, just verify the app loaded without crashing
                app_element = await self.page.query_selector('#finder-v2-app')
                self.assertIsNotNone(app_element)
                
            finally:
                await self.async_tearDown()
                
        asyncio.run(run_test())
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_feature_flag_disabled_in_browser(self, mock_live_settings):
        """Test that widget shows 404 when feature flag is disabled."""
        # Disable feature flag
        mock_live_settings.return_value = False
        
        async def run_test():
            await self.async_setUp()
            
            try:
                widget_url = f"{self.live_server_url}/widget/{self.widget_config.slug}/"
                response = await self.page.goto(widget_url)
                
                # Should return 404
                self.assertEqual(response.status, 404)
                
            finally:
                await self.async_tearDown()
                
        asyncio.run(run_test())
        
    def test_iframe_height_management(self):
        """Test that iframe height is properly managed for embedding."""
        async def run_test():
            await self.async_setUp()
            
            try:
                # Create a test page that embeds the widget in an iframe
                test_html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Widget Embedding Test</title>
                </head>
                <body>
                    <h1>Test Page</h1>
                    <iframe id="widget-iframe" 
                            src="{self.live_server_url}/widget/{self.widget_config.slug}/"
                            width="600" 
                            height="400"
                            frameborder="0">
                    </iframe>
                    
                    <script>
                        // Listen for height update messages
                        window.addEventListener('message', function(event) {{
                            if (event.data.type === 'ready:document') {{
                                const iframe = document.getElementById('widget-iframe');
                                iframe.height = event.data.height;
                            }}
                        }});
                    </script>
                </body>
                </html>
                """
                
                # Set page content
                await self.page.set_content(test_html)
                
                # Wait for iframe to load
                await self.page.wait_for_selector('#widget-iframe')
                
                # Wait for potential height messages
                await self.page.wait_for_timeout(2000)
                
                # Check that iframe is present and has reasonable dimensions
                iframe = await self.page.query_selector('#widget-iframe')
                self.assertIsNotNone(iframe)
                
                # Get iframe height
                height = await iframe.get_attribute('height')
                self.assertIsNotNone(height)
                
            finally:
                await self.async_tearDown()
                
        asyncio.run(run_test())
        
    def test_cross_browser_compatibility(self):
        """Test widget in different browser engines."""
        async def run_test():
            browsers_to_test = ['chromium', 'firefox', 'webkit']
            
            for browser_name in browsers_to_test:
                playwright = await async_playwright().start()
                
                try:
                    if browser_name == 'chromium':
                        browser = await playwright.chromium.launch(headless=True)
                    elif browser_name == 'firefox':
                        browser = await playwright.firefox.launch(headless=True)
                    elif browser_name == 'webkit':
                        browser = await playwright.webkit.launch(headless=True)
                        
                    page = await browser.new_page()
                    
                    widget_url = f"{self.live_server_url}/widget/{self.widget_config.slug}/"
                    await page.goto(widget_url)
                    
                    # Wait for Vue app
                    await page.wait_for_selector('#finder-v2-app', timeout=10000)
                    
                    # Verify basic functionality
                    app_element = await page.query_selector('#finder-v2-app')
                    self.assertIsNotNone(app_element, f"Widget failed to load in {browser_name}")
                    
                    await page.close()
                    await browser.close()
                    
                except Exception as e:
                    # Some browsers might not be available in CI
                    print(f"Skipping {browser_name} test: {e}")
                    
                finally:
                    await playwright.stop()
                    
        asyncio.run(run_test())
