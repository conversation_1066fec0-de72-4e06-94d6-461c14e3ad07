from django.test import SimpleTestCase, RequestFactory
from rest_framework.request import Request

from src.apps.widgets.api_proxy.views import FinderV2WidgetProxyView


class _DummyWidgetType:
    """Minimal stub to satisfy FinderV2WidgetProxyView checks."""

    allow_api = True


class _DummyParams:
    """Return widget-side filter params that include an extra region (chdm)."""

    def get_filter_params(self):
        return {"region": ["chdm"]}


class _DummyConfig:
    """Stub object emulating the attributes used inside the proxy view."""

    is_default = False
    widget_type = _DummyWidgetType()
    params = _DummyParams()
    lang = "en"


class RegionParameterNormalisationTest(SimpleTestCase):
    """Ensure `region[]` keys are normalised to `region` lists inside the proxy view."""

    def setUp(self):
        self.factory = RequestFactory()
        self.view = FinderV2WidgetProxyView()
        # Provide a minimal proxy_settings with an empty DISALLOWED_PARAMS list
        self.view.proxy_settings = type(
            "ps", (), {"DISALLOWED_PARAMS": []}
        )

    def test_region_array_param_is_normalised_and_overrides_widget_config(self):
        """`region[]` should become `region` and override widget config regions."""

        http_request = self.factory.get(
            "/widget/finder-v2/api/mk",
            {"region[]": ["usdm", "cdm"]},
        )
        drf_request = Request(http_request)
        drf_request.config = _DummyConfig()

        params = self.view.get_request_params(drf_request)

        self.assertIn("region", params)
        self.assertIsInstance(params["region"], list)
        self.assertEqual(set(params["region"]), {"usdm", "cdm"})
        # Ensure the widget-config-only region "chdm" is NOT present
        self.assertNotIn("chdm", params["region"])

    def test_http_request_path(self):
        """Ensure plain Django HttpRequest (no DRF wrapper) is also handled."""

        http_request = self.factory.get(
            "/widget/finder-v2/api/mk",
            {"region[]": ["eudm", "usdm"]},
        )

        # Attach stub config directly on the HttpRequest object (ProxyView passes it this way)
        http_request.config = _DummyConfig()

        params = self.view.get_request_params(http_request)

        self.assertIn("region", params)
        self.assertEqual(set(params["region"]), {"eudm", "usdm"}) 