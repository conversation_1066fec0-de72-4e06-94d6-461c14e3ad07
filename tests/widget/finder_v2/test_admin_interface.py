"""
Unit tests for Finder-v2 Admin Interface functionality
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.contrib.admin.sites import AdminSite
from unittest.mock import patch, MagicMock

from src.apps.widgets.main.models import WidgetConfig, WidgetSubscription
from src.apps.widgets.main.admin import WidgetConfigurationAdmin
from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType


class FinderV2AdminInterfaceTest(TestCase):
    """Test admin interface functionality for finder-v2 widgets."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create superuser for admin access
        self.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        
        # Create regular user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Set up admin
        self.site = AdminSite()
        self.admin = WidgetConfigurationAdmin(WidgetConfig, self.site)
        
        # Login as admin
        self.client.login(username='admin', password='adminpass123')
        
    def test_finder_creation_blocked(self):
        """Test that creation of new 'finder' widgets is blocked."""
        # Create mock request for finder widget creation
        request = MagicMock()
        request.GET = {'type': 'finder'}
        request.resolver_match.url_name = 'widgets_widgetconfig_add'
        
        # Test has_add_permission
        has_permission = self.admin.has_add_permission(request)
        
        # Should be False (blocked)
        self.assertFalse(has_permission)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_finder_v2_creation_when_enabled(self, mock_live_settings):
        """Test that finder-v2 widgets can be created when feature flag is enabled."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        # Create mock request for finder-v2 widget creation
        request = MagicMock()
        request.GET = {'type': 'finder-v2'}
        request.resolver_match.url_name = 'widgets_widgetconfig_add'
        
        # Test has_add_permission
        has_permission = self.admin.has_add_permission(request)
        
        # Should be True (allowed)
        self.assertTrue(has_permission)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_finder_v2_creation_when_disabled(self, mock_live_settings):
        """Test that finder-v2 widgets cannot be created when feature flag is disabled."""
        # Disable feature flag
        mock_live_settings.return_value = False
        
        # Create mock request for finder-v2 widget creation
        request = MagicMock()
        request.GET = {'type': 'finder-v2'}
        request.resolver_match.url_name = 'widgets_widgetconfig_add'
        
        # Test has_add_permission
        has_permission = self.admin.has_add_permission(request)
        
        # Should be False (blocked)
        self.assertFalse(has_permission)
        
    def test_type_field_readonly(self):
        """Test that 'type' field is readonly after widget creation."""
        # Create widget
        widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        # Create mock request
        request = MagicMock()
        
        # Test get_readonly_fields with existing object
        readonly_fields = self.admin.get_readonly_fields(request, widget_config)
        
        # 'type' should be in readonly fields
        self.assertIn('type', readonly_fields)
        
    def test_migration_admin_action(self):
        """Test the duplicate_finder_to_finder_v2 admin action."""
        # Create legacy finder widget
        finder_config = WidgetConfig.objects.create(
            user=self.user,
            name='Legacy Finder Widget',
            type='finder',
            raw_params={'test': 'legacy'},
            is_default=False
        )
        
        # Create subscription for finder widget
        finder_subscription = WidgetSubscription.objects.create(
            widget_config=finder_config,
            contact_email='<EMAIL>',
            client_name='Test Client',
            notes='Original finder widget'
        )
        
        # Create mock request
        request = MagicMock()
        request._messages = MagicMock()
        
        # Create queryset with finder widget
        queryset = WidgetConfig.objects.filter(id=finder_config.id)
        
        # Execute migration action
        self.admin.duplicate_finder_to_finder_v2(request, queryset)
        
        # Check that new finder-v2 widget was created
        finder_v2_widgets = WidgetConfig.objects.filter(type='finder-v2')
        self.assertEqual(finder_v2_widgets.count(), 1)
        
        new_widget = finder_v2_widgets.first()
        self.assertEqual(new_widget.name, 'Legacy Finder Widget (Finder-v2)')
        self.assertEqual(new_widget.type, 'finder-v2')
        self.assertIn('flow_type', new_widget.raw_params)
        self.assertEqual(new_widget.raw_params['flow_type'], 'primary')
        
        # Check that subscription was copied
        new_subscription = WidgetSubscription.objects.get(widget_config=new_widget)
        self.assertEqual(new_subscription.contact_email, '<EMAIL>')
        self.assertEqual(new_subscription.client_name, 'Test Client (Finder-v2)')
        self.assertIn('Migrated from finder widget', new_subscription.notes)
        
    def test_migration_action_skips_non_finder(self):
        """Test that migration action skips non-finder widgets."""
        # Create calc widget
        calc_config = WidgetConfig.objects.create(
            user=self.user,
            name='Calc Widget',
            type='calc',
            raw_params={'test': 'calc'},
            is_default=False
        )
        
        # Create mock request
        request = MagicMock()
        request._messages = MagicMock()
        
        # Create queryset with calc widget
        queryset = WidgetConfig.objects.filter(id=calc_config.id)
        
        # Execute migration action
        self.admin.duplicate_finder_to_finder_v2(request, queryset)
        
        # Check that no finder-v2 widget was created
        finder_v2_widgets = WidgetConfig.objects.filter(type='finder-v2')
        self.assertEqual(finder_v2_widgets.count(), 0)
        
    def test_help_text_for_type_field(self):
        """Test that help text is added for type field restrictions."""
        # Create mock request
        request = MagicMock()
        
        # Get form class
        form_class = self.admin.get_form(request)
        
        # Create form instance
        form = form_class()
        
        # Check that type field has help text
        if 'type' in form.fields:
            help_text = form.fields['type'].help_text
            self.assertIn('cannot be changed after creation', help_text)
            self.assertIn('finder-v2', help_text)
            
    def test_admin_action_in_actions_list(self):
        """Test that migration action is in admin actions list."""
        actions = self.admin.actions
        self.assertIn('duplicate_finder_to_finder_v2', actions)
        
    def test_admin_changelist_access(self):
        """Test that admin changelist is accessible."""
        url = reverse('admin:widgets_widgetconfig_changelist')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        
    def test_admin_add_form_access(self):
        """Test that admin add form is accessible."""
        url = reverse('admin:widgets_widgetconfig_add')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_admin_add_finder_v2_when_enabled(self, mock_live_settings):
        """Test adding finder-v2 widget through admin when enabled."""
        # Enable feature flag
        mock_live_settings.return_value = True
        
        url = reverse('admin:widgets_widgetconfig_add')
        response = self.client.get(url, {'type': 'finder-v2'})
        
        # Should be accessible
        self.assertEqual(response.status_code, 200)
        
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_admin_add_finder_v2_when_disabled(self, mock_live_settings):
        """Test adding finder-v2 widget through admin when disabled."""
        # Disable feature flag
        mock_live_settings.return_value = False
        
        url = reverse('admin:widgets_widgetconfig_add')
        response = self.client.get(url, {'type': 'finder-v2'})
        
        # Should be forbidden or redirected
        self.assertIn(response.status_code, [403, 302])
        
    def test_widget_edit_preserves_type(self):
        """Test that editing a widget preserves the type field."""
        # Create finder-v2 widget
        widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Finder-v2 Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        # Access edit form
        url = reverse('admin:widgets_widgetconfig_change', args=[widget_config.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check that type field is readonly in the form
        content = response.content.decode('utf-8')
        # The exact check depends on how readonly fields are rendered
        # This is a basic check that the page loads correctly
