#!/usr/bin/env python3
"""
Test runner for Finder-v2 Widget Tests

This script runs all finder-v2 widget tests and provides a summary report.
Run with: python tests/widget/finder_v2/run_tests.py
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner

def setup_django():
    """Set up Django environment for testing."""
    # Add project root to Python path
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
    sys.path.insert(0, project_root)
    
    # Configure Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings.test')
    django.setup()

def run_finder_v2_tests():
    """Run all finder-v2 widget tests."""
    print("🧪 Running Finder-v2 Widget Tests")
    print("=" * 50)
    
    # Get Django test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # Define test modules to run
    test_modules = [
        'tests.widget.finder_v2.test_widget_type',
        'tests.widget.finder_v2.test_api_proxy',
        'tests.widget.finder_v2.test_csrf_protection',
        'tests.widget.finder_v2.test_admin_interface',
    ]
    
    print(f"Running {len(test_modules)} test modules:")
    for module in test_modules:
        print(f"  - {module}")
    print()
    
    # Run tests
    failures = test_runner.run_tests(test_modules)
    
    # Print summary
    print("\n" + "=" * 50)
    if failures:
        print(f"❌ Tests completed with {failures} failure(s)")
        print("Review the output above for details.")
        return False
    else:
        print("✅ All tests passed!")
        print("Finder-v2 widget implementation is working correctly.")
        return True

def run_security_tests():
    """Run security-specific tests."""
    print("\n🔒 Running Security Tests")
    print("=" * 30)
    
    # Run the comprehensive CSRF security test
    csrf_test_path = os.path.join(
        os.path.dirname(__file__), 
        '../../../tests/widget/test_widget_csrf_security.py'
    )
    
    if os.path.exists(csrf_test_path):
        print("Running comprehensive CSRF security test...")
        import subprocess
        result = subprocess.run([sys.executable, csrf_test_path], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
        return result.returncode == 0
    else:
        print("⚠️  CSRF security test not found")
        return True

def main():
    """Main test runner function."""
    print("Finder-v2 Widget Test Suite")
    print("=" * 50)
    
    try:
        # Set up Django
        setup_django()
        
        # Run unit tests
        unit_tests_passed = run_finder_v2_tests()
        
        # Run security tests
        security_tests_passed = run_security_tests()
        
        # Final summary
        print("\n" + "=" * 50)
        print("FINAL TEST SUMMARY")
        print("=" * 50)
        
        if unit_tests_passed and security_tests_passed:
            print("🎉 ALL TESTS PASSED!")
            print("\nFinder-v2 widget is ready for deployment:")
            print("✅ Unit tests passed")
            print("✅ Security tests passed")
            print("✅ Widget type registration working")
            print("✅ API proxy functionality verified")
            print("✅ CSRF protection validated")
            print("✅ Admin interface tested")
            
            print("\nNext steps:")
            print("1. Run integration tests")
            print("2. Test in browser environments")
            print("3. Validate performance benchmarks")
            
            return 0
        else:
            print("❌ SOME TESTS FAILED!")
            print("\nFailed test categories:")
            if not unit_tests_passed:
                print("❌ Unit tests")
            if not security_tests_passed:
                print("❌ Security tests")
                
            print("\nPlease fix the failing tests before proceeding.")
            return 1
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
