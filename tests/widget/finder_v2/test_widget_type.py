"""
Unit tests for Finder-v2 Widget Type Registration and Configuration
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse

from src.apps.widgets.common.models.config import WidgetConfig
from src.apps.widgets.common.models.subscription import WidgetSubscription
from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType
from src.apps.widgets.finder_v2.forms import FinderV2ConfigForm


class FinderV2WidgetTypeTest(TestCase):
    """Test finder-v2 widget type registration and basic functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
    def test_widget_type_registration(self):
        """Test that finder-v2 widget type is properly registered."""
        self.assertEqual(FinderV2WidgetType.type, 'finder-v2')
        self.assertEqual(FinderV2WidgetType.name, 'Finder-v2')
        self.assertIsNotNone(FinderV2WidgetType.default_config)
        
    def test_default_configuration(self):
        """Test that default configuration contains required fields."""
        config = FinderV2WidgetType.default_config
        
        # Check required configuration sections
        self.assertIn('flow_type', config)
        self.assertIn('theme', config)
        self.assertIn('interface_tabs', config)
        self.assertIn('interface_blocks', config)
        self.assertIn('content', config)
        self.assertIn('permissions', config)
        
        # Check default flow type
        self.assertEqual(config['flow_type'], 'primary')
        
        # Check interface tabs configuration
        self.assertIn('tabs', config['interface_tabs'])
        self.assertIn('primary', config['interface_tabs'])
        
    def test_widget_creation(self):
        """Test creating a finder-v2 widget configuration."""
        config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Finder-v2 Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        self.assertEqual(config.type, 'finder-v2')
        self.assertEqual(config.name, 'Test Finder-v2 Widget')
        self.assertFalse(config.is_default)
        self.assertEqual(config.raw_params['flow_type'], 'primary')
        
    def test_widget_with_subscription(self):
        """Test creating a finder-v2 widget with subscription."""
        config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Finder-v2 Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        subscription = WidgetSubscription.objects.create(
            widget_config=config,
            contact_email='<EMAIL>',
            client_name='Test Client',
            notes='Test subscription for finder-v2 widget'
        )
        
        self.assertEqual(subscription.widget_config, config)
        self.assertEqual(subscription.contact_email, '<EMAIL>')
        self.assertEqual(subscription.client_name, 'Test Client')
        
    def test_flow_type_configuration(self):
        """Test both primary and alternative flow type configurations."""
        # Test primary flow
        primary_config = FinderV2WidgetType.default_config.copy()
        primary_config['flow_type'] = 'primary'
        
        config_primary = WidgetConfig.objects.create(
            user=self.user,
            name='Primary Flow Widget',
            type=FinderV2WidgetType.type,
            raw_params=primary_config,
            is_default=False
        )
        
        self.assertEqual(config_primary.raw_params['flow_type'], 'primary')
        
        # Test alternative flow
        alternative_config = FinderV2WidgetType.default_config.copy()
        alternative_config['flow_type'] = 'alternative'
        
        config_alternative = WidgetConfig.objects.create(
            user=self.user,
            name='Alternative Flow Widget',
            type=FinderV2WidgetType.type,
            raw_params=alternative_config,
            is_default=False
        )
        
        self.assertEqual(config_alternative.raw_params['flow_type'], 'alternative')
        
    def test_widget_form_classes(self):
        """Test that widget form classes are properly configured."""
        form_classes = FinderV2WidgetType.form_classes
        
        self.assertIn('config', form_classes)
        self.assertIn('theme', form_classes)
        self.assertIn('content', form_classes)
        self.assertIn('interface', form_classes)
        self.assertIn('permissions', form_classes)
        
    def test_static_file_configuration(self):
        """Test that static file paths are properly configured."""
        static_config = FinderV2WidgetType.static
        
        self.assertIn('app_css_libs', static_config)
        self.assertIn('app_css', static_config)
        self.assertIn('app_js_libs', static_config)
        self.assertIn('app_js', static_config)
        
        # Check that paths point to finder_v2 directory
        for key, path in static_config.items():
            self.assertTrue(path.startswith('finder_v2/'))
            
    def test_widget_themes(self):
        """Test that widget themes are properly configured."""
        themes = FinderV2WidgetType.themes
        
        # Should have at least desktop theme
        self.assertIn('desktop', themes)
        
        # Check theme structure
        desktop_theme = themes['desktop']
        self.assertIn('templates', desktop_theme)
        self.assertIn('page', desktop_theme['templates'])
        
    def test_widget_isolation_from_finder(self):
        """Test that finder-v2 widgets are isolated from legacy finder widgets."""
        # Create both widget types
        finder_config = WidgetConfig.objects.create(
            user=self.user,
            name='Legacy Finder Widget',
            type='finder',
            raw_params={'test': 'finder'},
            is_default=False
        )
        
        finder_v2_config = WidgetConfig.objects.create(
            user=self.user,
            name='New Finder-v2 Widget',
            type='finder-v2',
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        # Verify they are different types
        self.assertNotEqual(finder_config.type, finder_v2_config.type)
        self.assertEqual(finder_config.type, 'finder')
        self.assertEqual(finder_v2_config.type, 'finder-v2')
        
        # Verify different configurations
        self.assertNotEqual(finder_config.raw_params, finder_v2_config.raw_params)
        self.assertIn('flow_type', finder_v2_config.raw_params)
        self.assertNotIn('flow_type', finder_config.raw_params)
