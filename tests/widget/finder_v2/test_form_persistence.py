"""
Test form data persistence for finder-v2 widget configuration.

This test specifically addresses the issue where the flow_type field value
is not being properly displayed after saving the configuration form.
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.contrib.messages import get_messages

from src.apps.widgets.common.models import WidgetConfig, WidgetSubscription
from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType


class FinderV2FormPersistenceTest(TestCase):
    """Test form data persistence for finder-v2 widget configuration."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create a test widget configuration
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Finder-v2 Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        # Create subscription
        self.subscription = WidgetSubscription.objects.create(
            widget_config=self.widget_config,
            contact_email='<EMAIL>',
            client_name='Test Client',
            notes='Test subscription'
        )
        
    def test_flow_type_persistence_demo_config(self):
        """
        Test that flow_type field value persists after form submission in demo config.
        
        This test reproduces the exact issue described:
        1. Load the demo configuration page
        2. Change flow_type from 'primary' to 'alternative'
        3. Submit the form
        4. Verify that the form shows 'alternative' as selected after reload
        """
        # Get the demo configuration URL
        demo_url = reverse('widget:configure-demo', kwargs={'widget_slug': self.widget_config.slug})
        
        # Step 1: Load the initial form (should show 'primary' as default)
        response = self.client.get(demo_url)
        self.assertEqual(response.status_code, 200)
        
        # Verify initial state - form should show 'primary' as selected
        form = response.context['form']
        self.assertEqual(form.interface.initial.get('flow_type'), 'primary')
        
        # Step 2: Submit form with flow_type changed to 'alternative'
        form_data = {
            # Config form data
            'config-name': 'Test Widget Updated',
            'config-lang': 'en',
            
            # Theme form data
            'theme-theme_name': 'default',
            
            # Content form data
            'content-regions': '',
            'content-only_oem': False,
            'content-by': '',
            'content-brands': '',
            'content-brands_exclude': '',
            
            # Interface form data (the critical part)
            'interface-width': 600,
            'interface-height': '',
            'interface-tabs': ['by_vehicle'],
            'interface-primary_tab': 'by_vehicle',
            'interface-button_to_ws': False,
            'interface-flow_type': 'alternative',  # This is the key change
        }
        
        # Submit the form
        response = self.client.post(demo_url, data=form_data, follow=True)
        
        # Step 3: Verify the form submission was successful
        self.assertEqual(response.status_code, 200)
        
        # Check that success message was added
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('saved successfully' in str(message) for message in messages))
        
        # Step 4: Verify that the configuration was saved to database
        self.widget_config.refresh_from_db()
        self.assertEqual(self.widget_config.raw_params['interface']['flow_type'], 'alternative')
        
        # Step 5: CRITICAL TEST - Verify that the form displays the saved value
        # This is the main issue being fixed - the form should show 'alternative' as selected
        form = response.context['form']
        self.assertEqual(form.interface.initial.get('flow_type'), 'alternative')
        
        # Additional verification: check that the form field value is correct
        flow_type_field = form.interface['flow_type']
        self.assertEqual(flow_type_field.value(), 'alternative')
        
    def test_flow_type_persistence_authenticated_config(self):
        """
        Test that flow_type field value persists in authenticated config view.
        """
        # Login as the user
        self.client.login(username='testuser', password='testpass123')
        
        # Get the authenticated configuration URL
        config_url = reverse('widget:configure', kwargs={'widget_slug': self.widget_config.slug})
        
        # Load the initial form
        response = self.client.get(config_url)
        self.assertEqual(response.status_code, 200)
        
        # Submit form with flow_type changed to 'alternative'
        form_data = {
            # Config form data
            'config-name': 'Test Widget Updated Auth',
            'config-lang': 'en',
            
            # Theme form data
            'theme-theme_name': 'default',
            
            # Content form data
            'content-regions': '',
            'content-only_oem': False,
            'content-by': '',
            'content-brands': '',
            'content-brands_exclude': '',
            
            # Interface form data
            'interface-width': 600,
            'interface-height': '',
            'interface-tabs': ['by_vehicle'],
            'interface-primary_tab': 'by_vehicle',
            'interface-button_to_ws': False,
            'interface-flow_type': 'alternative',
            
            # Permissions form data
            'permissions-domains': ['localhost', 'development.local'],
        }
        
        # Submit the form
        response = self.client.post(config_url, data=form_data, follow=True)
        self.assertEqual(response.status_code, 200)
        
        # Verify the configuration was saved
        self.widget_config.refresh_from_db()
        self.assertEqual(self.widget_config.raw_params['interface']['flow_type'], 'alternative')
        
        # Verify that the form displays the saved value
        form = response.context['form']
        self.assertEqual(form.interface.initial.get('flow_type'), 'alternative')
        
    def test_multiple_flow_type_changes(self):
        """
        Test multiple changes to flow_type to ensure persistence works consistently.
        """
        demo_url = reverse('widget:configure-demo', kwargs={'widget_slug': self.widget_config.slug})
        
        # Test sequence: primary -> alternative -> primary
        flow_types = ['alternative', 'primary', 'alternative']
        
        for expected_flow_type in flow_types:
            with self.subTest(flow_type=expected_flow_type):
                # Submit form with the new flow_type
                form_data = {
                    'config-name': f'Test Widget {expected_flow_type}',
                    'config-lang': 'en',
                    'theme-theme_name': 'default',
                    'content-regions': '',
                    'content-only_oem': False,
                    'content-by': '',
                    'content-brands': '',
                    'content-brands_exclude': '',
                    'interface-width': 600,
                    'interface-height': '',
                    'interface-tabs': ['by_vehicle'],
                    'interface-primary_tab': 'by_vehicle',
                    'interface-button_to_ws': False,
                    'interface-flow_type': expected_flow_type,
                }
                
                response = self.client.post(demo_url, data=form_data, follow=True)
                self.assertEqual(response.status_code, 200)
                
                # Verify database save
                self.widget_config.refresh_from_db()
                self.assertEqual(
                    self.widget_config.raw_params['interface']['flow_type'], 
                    expected_flow_type
                )
                
                # Verify form display
                form = response.context['form']
                self.assertEqual(form.interface.initial.get('flow_type'), expected_flow_type)
