"""
Test search history form persistence for finder-v2 widget configuration.

This test specifically addresses the issue where search history configuration settings
were not persisting after saving the configuration form due to the decompose_to_initial
method always returning default values instead of loading saved configuration.
"""

from django.test import TestCase
from django.contrib.auth.models import User

from src.apps.widgets.common.models import WidgetConfig, WidgetSubscription
from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType
from src.apps.widgets.finder_v2.forms import FinderV2SearchHistoryForm
from src.apps.widgets.common.json_wrapper import DefaultJson


class FinderV2SearchHistoryFormPersistenceTest(TestCase):
    """Test search history form data persistence for finder-v2 widget configuration."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create a test widget configuration
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Finder-v2 Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        # Create subscription
        self.subscription = WidgetSubscription.objects.create(
            widget_config=self.widget_config,
            contact_email='<EMAIL>',
            client_name='Test Client',
            notes='Test subscription'
        )
    
    def test_search_history_form_defaults(self):
        """Test that form returns correct defaults when no saved data exists."""
        form = FinderV2SearchHistoryForm(instance=None)
        initial = form.decompose_to_initial()
        
        expected_defaults = {
            'enabled': True,
            'max_items': 10,
            'display_items': 5,
            'auto_expand': False,
            'show_timestamps': True,
        }
        
        self.assertEqual(initial, expected_defaults)
    
    def test_search_history_form_loads_saved_data(self):
        """Test that form correctly loads previously saved configuration data."""
        # Simulate saved configuration data (camelCase format as stored by compose_to_save)
        saved_config = {
            'search_history': {
                'enabled': False,  # User disabled search history
                'maxItems': 25,    # User changed max items
                'displayItems': 8, # User changed display items
                'autoExpand': True, # User enabled auto expand
                'showTimestamps': False, # User disabled timestamps
            }
        }
        
        form = FinderV2SearchHistoryForm(instance=DefaultJson(saved_config))
        initial = form.decompose_to_initial()
        
        expected = {
            'enabled': False,
            'max_items': 25,
            'display_items': 8,
            'auto_expand': True,
            'show_timestamps': False,
        }
        
        self.assertEqual(initial, expected)
    
    def test_search_history_form_partial_saved_data(self):
        """Test that form handles partial saved data with defaults for missing fields."""
        partial_config = {
            'search_history': {
                'enabled': False,  # Only this field is saved
                'maxItems': 15,    # And this one
                # Other fields missing - should use defaults
            }
        }
        
        form = FinderV2SearchHistoryForm(instance=DefaultJson(partial_config))
        initial = form.decompose_to_initial()
        
        expected = {
            'enabled': False,      # From saved data
            'max_items': 15,       # From saved data
            'display_items': 5,    # Default (missing from saved)
            'auto_expand': False,  # Default (missing from saved)
            'show_timestamps': True, # Default (missing from saved)
        }
        
        self.assertEqual(initial, expected)
    
    def test_search_history_form_snake_case_compatibility(self):
        """Test backward compatibility with snake_case field names."""
        snake_case_config = {
            'search_history': {
                'enabled': True,
                'max_items': 20,        # snake_case instead of camelCase
                'display_items': 7,     # snake_case instead of camelCase
                'auto_expand': True,    # snake_case instead of camelCase
                'show_timestamps': False, # snake_case instead of camelCase
            }
        }
        
        form = FinderV2SearchHistoryForm(instance=DefaultJson(snake_case_config))
        initial = form.decompose_to_initial()
        
        expected = {
            'enabled': True,
            'max_items': 20,
            'display_items': 7,
            'auto_expand': True,
            'show_timestamps': False,
        }
        
        self.assertEqual(initial, expected)
    
    def test_search_history_form_compose_to_save(self):
        """Test that compose_to_save correctly formats data for storage."""
        form_data = {
            'enabled': False,
            'max_items': 30,
            'display_items': 12,
            'auto_expand': True,
            'show_timestamps': False,
        }
        
        form = FinderV2SearchHistoryForm()
        composed = form.compose_to_save(form_data)
        
        expected = {
            'enabled': False,
            'maxItems': 30,        # camelCase for frontend consumption
            'displayItems': 12,    # camelCase for frontend consumption
            'autoExpand': True,    # camelCase for frontend consumption
            'showTimestamps': False, # camelCase for frontend consumption
        }
        
        self.assertEqual(composed, expected)
    
    def test_search_history_form_round_trip_persistence(self):
        """Test complete round-trip: compose_to_save -> save -> decompose_to_initial."""
        # Step 1: Start with form data
        form_data = {
            'enabled': False,
            'max_items': 35,
            'display_items': 15,
            'auto_expand': True,
            'show_timestamps': False,
        }
        
        # Step 2: Compose for saving
        form = FinderV2SearchHistoryForm()
        composed = form.compose_to_save(form_data)
        
        # Step 3: Simulate saving to config (this would be done by the widget config system)
        config_data = {'search_history': composed}
        
        # Step 4: Load form with saved data
        form_loaded = FinderV2SearchHistoryForm(instance=DefaultJson(config_data))
        initial = form_loaded.decompose_to_initial()
        
        # Step 5: Verify round-trip integrity
        self.assertEqual(initial, form_data)
