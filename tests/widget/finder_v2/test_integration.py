"""
Integration tests for Finder-v2 Widget complete workflow
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from unittest.mock import patch

from src.apps.widgets.main.models import WidgetConfig, WidgetSubscription
from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType


class FinderV2IntegrationTest(TestCase):
    """Test complete finder-v2 widget workflow from creation to embedding."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
    def test_complete_widget_creation_workflow(self):
        """Test complete workflow: create widget → configure → embed → use."""
        # Step 1: Create widget configuration
        widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Integration Test Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        # Step 2: Create subscription
        subscription = WidgetSubscription.objects.create(
            widget_config=widget_config,
            contact_email='<EMAIL>',
            client_name='Integration Test Client',
            notes='Integration test subscription'
        )
        
        # Step 3: Test widget iframe loading
        iframe_url = reverse('widget:iframe', args=[widget_config.slug])
        iframe_response = self.client.get(iframe_url)
        
        self.assertEqual(iframe_response.status_code, 200)
        
        # Verify Vue 3 app configuration is present
        content = iframe_response.content.decode('utf-8')
        self.assertIn('FinderV2Config', content)
        self.assertIn('finder-v2-app', content)
        self.assertIn('flow_type', content)
        
        # Step 4: Test API endpoints
        api_endpoints = [
            'makes',
            'models',
            'years',
            'modifications',
            'generations'
        ]
        
        for endpoint in api_endpoints:
            with self.subTest(endpoint=endpoint):
                api_url = reverse(f'widget-api:{endpoint}', args=[widget_config.slug])
                api_response = self.client.get(api_url)
                
                # Should not return 404 (endpoint exists)
                self.assertNotEqual(api_response.status_code, 404)
                
    @patch('ws_live_settings.models.WsLiveSettings.get')
    def test_feature_flag_integration(self, mock_live_settings):
        """Test complete workflow with feature flag enabled/disabled."""
        # Create widget
        widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Feature Flag Test Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        subscription = WidgetSubscription.objects.create(
            widget_config=widget_config,
            contact_email='<EMAIL>',
            client_name='Feature Flag Test Client'
        )
        
        # Test with feature flag enabled
        mock_live_settings.return_value = True
        
        iframe_url = reverse('widget:iframe', args=[widget_config.slug])
        iframe_response = self.client.get(iframe_url)
        self.assertEqual(iframe_response.status_code, 200)
        
        api_url = reverse('widget-api:makes', args=[widget_config.slug])
        api_response = self.client.get(api_url)
        self.assertNotEqual(api_response.status_code, 404)
        
        # Test with feature flag disabled
        mock_live_settings.return_value = False
        
        iframe_response = self.client.get(iframe_url)
        self.assertEqual(iframe_response.status_code, 404)
        
        api_response = self.client.get(api_url)
        self.assertEqual(api_response.status_code, 404)
        
    def test_widget_isolation_from_legacy_finder(self):
        """Test that finder-v2 and legacy finder widgets operate independently."""
        # Create legacy finder widget
        legacy_config = WidgetConfig.objects.create(
            user=self.user,
            name='Legacy Finder Widget',
            type='finder',
            raw_params={'test': 'legacy'},
            is_default=False
        )
        
        legacy_subscription = WidgetSubscription.objects.create(
            widget_config=legacy_config,
            contact_email='<EMAIL>',
            client_name='Legacy Client'
        )
        
        # Create finder-v2 widget
        finder_v2_config = WidgetConfig.objects.create(
            user=self.user,
            name='Finder-v2 Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        finder_v2_subscription = WidgetSubscription.objects.create(
            widget_config=finder_v2_config,
            contact_email='<EMAIL>',
            client_name='Finder-v2 Client'
        )
        
        # Test that both widgets load independently
        legacy_iframe_url = reverse('widget:iframe', args=[legacy_config.slug])
        legacy_response = self.client.get(legacy_iframe_url)
        self.assertEqual(legacy_response.status_code, 200)
        
        finder_v2_iframe_url = reverse('widget:iframe', args=[finder_v2_config.slug])
        finder_v2_response = self.client.get(finder_v2_iframe_url)
        self.assertEqual(finder_v2_response.status_code, 200)
        
        # Verify different content
        legacy_content = legacy_response.content.decode('utf-8')
        finder_v2_content = finder_v2_response.content.decode('utf-8')
        
        # Legacy should not have Vue 3 config
        self.assertNotIn('FinderV2Config', legacy_content)
        
        # Finder-v2 should have Vue 3 config
        self.assertIn('FinderV2Config', finder_v2_content)
        self.assertIn('flow_type', finder_v2_content)
        
        # Test API endpoints work independently
        legacy_api_url = reverse('widget-api:makes', args=[legacy_config.slug])
        legacy_api_response = self.client.get(legacy_api_url)
        self.assertNotEqual(legacy_api_response.status_code, 404)
        
        finder_v2_api_url = reverse('widget-api:makes', args=[finder_v2_config.slug])
        finder_v2_api_response = self.client.get(finder_v2_api_url)
        self.assertNotEqual(finder_v2_api_response.status_code, 404)
        
    def test_cross_domain_embedding_scenario(self):
        """Test cross-domain widget embedding scenario."""
        # Create widget with domain permissions
        widget_config = WidgetConfig.objects.create(
            user=self.user,
            name='Cross-Domain Test Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        
        # Configure domain permissions
        widget_config.raw_params['permissions']['domains'] = [
            'client-website.com',
            '*.example.com'
        ]
        widget_config.save()
        
        subscription = WidgetSubscription.objects.create(
            widget_config=widget_config,
            contact_email='<EMAIL>',
            client_name='Cross-Domain Client'
        )
        
        # Test iframe loading from allowed domain
        iframe_url = reverse('widget:iframe', args=[widget_config.slug])
        response = self.client.get(
            iframe_url,
            HTTP_REFERER='https://client-website.com/page-with-widget/'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Test API access from allowed domain
        api_url = reverse('widget-api:makes', args=[widget_config.slug])
        api_response = self.client.get(
            api_url,
            HTTP_REFERER='https://client-website.com/page-with-widget/'
        )
        
        self.assertNotEqual(api_response.status_code, 403)
        
    def test_both_flow_types_integration(self):
        """Test both primary and alternative flow types work end-to-end."""
        # Test primary flow
        primary_config = WidgetConfig.objects.create(
            user=self.user,
            name='Primary Flow Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        primary_config.raw_params['flow_type'] = 'primary'
        primary_config.save()
        
        primary_subscription = WidgetSubscription.objects.create(
            widget_config=primary_config,
            contact_email='<EMAIL>',
            client_name='Primary Flow Client'
        )
        
        # Test alternative flow
        alternative_config = WidgetConfig.objects.create(
            user=self.user,
            name='Alternative Flow Widget',
            type=FinderV2WidgetType.type,
            raw_params=FinderV2WidgetType.default_config.copy(),
            is_default=False
        )
        alternative_config.raw_params['flow_type'] = 'alternative'
        alternative_config.save()
        
        alternative_subscription = WidgetSubscription.objects.create(
            widget_config=alternative_config,
            contact_email='<EMAIL>',
            client_name='Alternative Flow Client'
        )
        
        # Test both widgets load with correct flow configuration
        primary_iframe_url = reverse('widget:iframe', args=[primary_config.slug])
        primary_response = self.client.get(primary_iframe_url)
        self.assertEqual(primary_response.status_code, 200)
        
        primary_content = primary_response.content.decode('utf-8')
        self.assertIn('"primary"', primary_content)
        
        alternative_iframe_url = reverse('widget:iframe', args=[alternative_config.slug])
        alternative_response = self.client.get(alternative_iframe_url)
        self.assertEqual(alternative_response.status_code, 200)
        
        alternative_content = alternative_response.content.decode('utf-8')
        self.assertIn('"alternative"', alternative_content)
        
        # Test that both can access v2 API endpoints
        for config in [primary_config, alternative_config]:
            with self.subTest(flow_type=config.raw_params['flow_type']):
                api_url = reverse('widget-api:makes', args=[config.slug])
                api_response = self.client.get(api_url)
                self.assertNotEqual(api_response.status_code, 404)
                
    def test_side_by_side_operation(self):
        """Test that multiple finder-v2 widgets can operate simultaneously."""
        # Create multiple widgets
        widgets = []
        for i in range(3):
            config = WidgetConfig.objects.create(
                user=self.user,
                name=f'Widget {i+1}',
                type=FinderV2WidgetType.type,
                raw_params=FinderV2WidgetType.default_config.copy(),
                is_default=False
            )
            
            subscription = WidgetSubscription.objects.create(
                widget_config=config,
                contact_email=f'widget{i+1}@example.com',
                client_name=f'Client {i+1}'
            )
            
            widgets.append(config)
            
        # Test that all widgets work independently
        for i, widget in enumerate(widgets):
            with self.subTest(widget_number=i+1):
                iframe_url = reverse('widget:iframe', args=[widget.slug])
                iframe_response = self.client.get(iframe_url)
                self.assertEqual(iframe_response.status_code, 200)
                
                api_url = reverse('widget-api:makes', args=[widget.slug])
                api_response = self.client.get(api_url)
                self.assertNotEqual(api_response.status_code, 404)
