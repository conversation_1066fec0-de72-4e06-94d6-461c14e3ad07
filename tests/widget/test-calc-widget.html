<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Calc Widget - JavaScript Compatibility</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .widget-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <h1>🔧 Calc Widget JavaScript Compatibility Test</h1>

    <div class="test-container">
        <h2>Test Status</h2>
        <div id="test-status" class="status warning">
            🔄 Loading widget and monitoring for JavaScript errors...
        </div>

        <h3>Console Output Monitor</h3>
        <div id="console-output" class="console-output">
            Monitoring console for debug messages and errors...<br>
        </div>
    </div>

    <div class="test-container">
        <h2>Calc Widget</h2>
        <div class="widget-container">
            <div id="ws-widget"></div>
        </div>
    </div>

    <script src="http://development.local:8000/static/widget/code/beta/ws-widget.js"></script>
    <script>
        let errorCount = 0;
        let debugMessages = [];
        let testStartTime = Date.now();

        // Capture console messages
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function logToMonitor(type, message, color = '#00ff00') {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<span style="color: ${color}">[${timestamp}] ${type}: ${message}</span><br>`;
            output.scrollTop = output.scrollHeight;
        }

        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('🔧') || message.includes('🎯') || message.includes('✅') || message.includes('❌')) {
                debugMessages.push(message);
                logToMonitor('DEBUG', message, '#00ffff');
            }
            originalConsoleLog.apply(console, args);
        };

        console.error = function(...args) {
            const message = args.join(' ');
            errorCount++;
            logToMonitor('ERROR', message, '#ff4444');
            updateTestStatus();
            originalConsoleError.apply(console, args);
        };

        console.warn = function(...args) {
            const message = args.join(' ');
            if (!message.includes('Cross-Origin-Opener-Policy') && !message.includes('Less has finished')) {
                logToMonitor('WARN', message, '#ffaa00');
            }
            originalConsoleWarn.apply(console, args);
        };

        // Monitor for specific errors
        window.addEventListener('error', function(event) {
            if (event.message.includes('Cannot read properties of undefined (reading \'collapse\')')) {
                errorCount++;
                logToMonitor('CRITICAL', 'Bootstrap collapse error detected!', '#ff0000');
                updateTestStatus();
            } else if (event.message.includes('require is not defined')) {
                errorCount++;
                logToMonitor('CRITICAL', 'ACE editor require error detected!', '#ff0000');
                updateTestStatus();
            }
        });

        // Initialize the calc widget
        try {
            logToMonitor('INFO', 'Initializing calc widget...', '#00ff00');
            var widget = WheelSizeWidgets.create('#ws-widget', {
                uuid: 'calc',  // Use default calc widget
                type: 'calc'
            });

            widget.on('ready:window', function() {
                logToMonitor('SUCCESS', 'Widget loaded successfully!', '#00ff00');
                onWidgetReady();
            });

        } catch (error) {
            logToMonitor('ERROR', 'Failed to initialize widget: ' + error.message, '#ff4444');
            errorCount++;
            updateTestStatus();
        }

        function onWidgetReady() {
            logToMonitor('INFO', 'Widget ready, checking for errors...', '#00ff00');

            // Wait a bit for JavaScript to execute, then check status
            setTimeout(function() {
                updateTestStatus();

                // Check if we have any collapse-related errors
                const hasCollapseErrors = debugMessages.some(msg =>
                    msg.includes('Cannot read properties of undefined') && msg.includes('collapse')
                );

                if (!hasCollapseErrors && errorCount === 0) {
                    logToMonitor('SUCCESS', 'No collapse-related JavaScript errors detected!', '#00ff00');
                } else if (hasCollapseErrors) {
                    logToMonitor('ERROR', 'Collapse-related errors still present!', '#ff4444');
                }
            }, 3000);
        }

        function updateTestStatus() {
            const statusDiv = document.getElementById('test-status');
            const elapsed = Math.round((Date.now() - testStartTime) / 1000);

            if (errorCount === 0) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `✅ No JavaScript errors detected after ${elapsed} seconds!<br>
                    Debug messages captured: ${debugMessages.length}`;
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ ${errorCount} JavaScript error(s) detected after ${elapsed} seconds.<br>
                    Debug messages captured: ${debugMessages.length}`;
            }
        }

        // Initial status update
        setTimeout(updateTestStatus, 1000);

        logToMonitor('INFO', 'Test page loaded, monitoring started...', '#00ff00');
    </script>
</body>
</html>
