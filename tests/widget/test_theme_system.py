"""
Comprehensive test suite for the finder-v2 theme system.

This test suite covers:
- Theme model validation and functionality
- CSS generation and performance
- Theme form validation
- Template tag functionality
- Accessibility compliance
- Security measures
"""

import pytest
import json
import time
from unittest.mock import Mock, patch
from django.test import TestCase, RequestFactory
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
from django.template import Context, Template
from django.utils.safestring import SafeString

from src.apps.widgets.common.models import WidgetConfig, WidgetTheme
from src.apps.widgets.finder_v2.utils.theme_generator import ThemeGenerator, ThemeInjector
from src.apps.widgets.finder_v2.forms import FinderV2Form


class ThemeModelTests(TestCase):
    """Test suite for WidgetTheme model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )

    def test_theme_creation_with_defaults(self):
        """Test creating a theme with default values."""
        theme = WidgetTheme.objects.create(widget=self.widget_config)
        
        self.assertEqual(theme.theme_name, 'Custom')
        self.assertEqual(theme.primary_color, '#3B82F6')
        self.assertEqual(theme.secondary_color, '#6B7280')
        self.assertEqual(theme.accent_color, '#10B981')
        self.assertEqual(theme.background_color, '#FFFFFF')
        self.assertEqual(theme.text_color, '#1F2937')
        self.assertEqual(theme.font_family, 'system-ui')
        self.assertEqual(theme.base_font_size, '16px')
        self.assertEqual(theme.font_weight, '400')
        self.assertEqual(theme.line_height, '1.5')
        self.assertEqual(theme.letter_spacing, '0')
        self.assertEqual(theme.element_padding, '0.75rem')
        self.assertEqual(theme.element_margin, '0.5rem')
        self.assertEqual(theme.border_radius, '0.375rem')
        self.assertEqual(theme.border_width, '1px')
        self.assertEqual(theme.shadow_intensity, 'medium')
        self.assertEqual(theme.animation_speed, '0.3s')
        self.assertEqual(theme.hover_effect, 'darken')

    def test_theme_custom_values(self):
        """Test creating a theme with custom values."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Custom Blue Theme',
            primary_color='#2563EB',
            secondary_color='#64748B',
            accent_color='#0EA5E9',
            background_color='#F8FAFC',
            text_color='#1E293B',
            font_family='Inter',
            base_font_size='18px',
            font_weight='500',
            line_height='1.6',
            letter_spacing='0.025em',
            element_padding='1rem',
            element_margin='0.75rem',
            border_radius='0.5rem',
            border_width='2px',
            shadow_intensity='heavy',
            animation_speed='0.2s',
            hover_effect='scale'
        )
        
        self.assertEqual(theme.theme_name, 'Custom Blue Theme')
        self.assertEqual(theme.primary_color, '#2563EB')
        self.assertEqual(theme.hover_effect, 'scale')

    def test_hex_color_validation(self):
        """Test hex color validation."""
        # Valid hex colors
        theme = WidgetTheme(
            widget=self.widget_config,
            primary_color='#FF0000',
            secondary_color='#00FF00',
            accent_color='#0000FF'
        )
        theme.full_clean()  # Should not raise
        
        # Invalid hex colors
        with self.assertRaises(ValidationError):
            invalid_theme = WidgetTheme(
                widget=self.widget_config,
                primary_color='red'  # Invalid hex
            )
            invalid_theme.full_clean()

    def test_contrast_ratio_validation(self):
        """Test color contrast ratio validation."""
        # Good contrast (dark text on light background)
        theme = WidgetTheme(
            widget=self.widget_config,
            background_color='#FFFFFF',
            text_color='#000000'
        )
        theme.full_clean()  # Should not raise
        
        # Poor contrast (light text on light background)
        with self.assertRaises(ValidationError):
            poor_contrast_theme = WidgetTheme(
                widget=self.widget_config,
                background_color='#FFFFFF',
                text_color='#CCCCCC'
            )
            poor_contrast_theme.full_clean()

    def test_advanced_config_validation(self):
        """Test advanced configuration validation."""
        # Valid advanced config
        theme = WidgetTheme(
            widget=self.widget_config,
            advanced_config={
                'custom_font_family': 'Roboto',
                'responsive_overrides': {
                    'mobile': {'font_size': '14px'}
                }
            }
        )
        theme.full_clean()  # Should not raise
        
        # Invalid advanced config key
        with self.assertRaises(ValidationError):
            invalid_theme = WidgetTheme(
                widget=self.widget_config,
                advanced_config={
                    'malicious_script': '<script>alert("xss")</script>'
                }
            )
            invalid_theme.full_clean()

    def test_css_custom_properties_generation(self):
        """Test CSS custom properties generation."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            primary_color='#2563EB',
            font_family='Inter',
            base_font_size='18px',
            font_weight='500',
            line_height='1.6',
            border_radius='0.5rem',
            animation_speed='0.2s'
        )
        
        properties = theme.get_css_custom_properties()
        
        self.assertEqual(properties['--theme-primary'], '#2563EB')
        self.assertEqual(properties['--theme-font-size'], '18px')
        self.assertEqual(properties['--theme-font-weight'], '500')
        self.assertEqual(properties['--theme-line-height'], '1.6')
        self.assertEqual(properties['--theme-border-radius'], '0.5rem')
        self.assertEqual(properties['--theme-animation-speed'], '0.2s')
        self.assertIn('--theme-primary-rgb', properties)
        self.assertEqual(properties['--theme-primary-rgb'], '37, 99, 235')

    def test_theme_export_import(self):
        """Test theme export and import functionality."""
        # Create original theme
        original_theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Export Test',
            primary_color='#2563EB',
            font_family='Inter',
            advanced_config={'custom_setting': 'value'}
        )
        
        # Export to JSON
        exported_data = original_theme.export_to_json()
        
        # Create new widget config for import
        new_widget = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Import Test Widget'
        )
        
        # Import from JSON
        imported_theme = WidgetTheme.import_from_json(exported_data, new_widget)
        
        # Verify import
        self.assertEqual(imported_theme.theme_name, 'Export Test')
        self.assertEqual(imported_theme.primary_color, '#2563EB')
        self.assertEqual(imported_theme.font_family, 'Inter')
        self.assertEqual(imported_theme.advanced_config, {'custom_setting': 'value'})


class ThemeGeneratorTests(TestCase):
    """Test suite for ThemeGenerator class."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )
        self.theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            primary_color='#2563EB',
            font_family='Inter',
            base_font_size='18px'
        )

    def test_theme_generator_initialization(self):
        """Test ThemeGenerator initialization."""
        generator = ThemeGenerator(self.theme)
        self.assertEqual(generator.theme, self.theme)
        self.assertEqual(generator._css_cache, {})

    def test_css_generation_with_theme(self):
        """Test CSS generation with theme."""
        generator = ThemeGenerator(self.theme)
        css = generator.generate_css_custom_properties()
        
        self.assertIn(':root {', css)
        self.assertIn('--theme-primary: #2563EB;', css)
        self.assertIn('--theme-font-size: 18px;', css)
        self.assertIn('}', css)

    def test_css_generation_without_theme(self):
        """Test CSS generation without theme (default values)."""
        generator = ThemeGenerator(None)
        css = generator.generate_css_custom_properties()
        
        self.assertIn(':root {', css)
        self.assertIn('--theme-primary: #3B82F6;', css)
        self.assertIn('--theme-font-size: 16px;', css)

    def test_complete_theme_css_generation(self):
        """Test complete theme CSS generation."""
        generator = ThemeGenerator(self.theme)
        complete_css = generator.generate_theme_css()
        
        # Should contain custom properties
        self.assertIn('--theme-primary: #2563EB;', complete_css)
        
        # Should contain component styles
        self.assertIn('.finder-v2-widget', complete_css)
        self.assertIn('.btn-primary', complete_css)
        self.assertIn('.form-control', complete_css)
        
        # Should contain fallback styles
        self.assertIn('@supports not (--css: variables)', complete_css)

    def test_inline_style_tag_generation(self):
        """Test inline style tag generation."""
        generator = ThemeGenerator(self.theme)
        style_tag = generator.generate_inline_style_tag()
        
        self.assertIsInstance(style_tag, SafeString)
        self.assertIn('<style type=\"text/css\">', style_tag)
        self.assertIn('--theme-primary: #2563EB;', style_tag)
        self.assertIn('</style>', style_tag)

    def test_css_file_content_generation(self):
        """Test CSS file content generation."""
        generator = ThemeGenerator(self.theme)
        css_file = generator.generate_css_file_content()
        
        self.assertIn('/* Generated Theme CSS for Finder-v2 Widget */', css_file)
        self.assertIn('/* Theme: Custom */', css_file)
        self.assertIn('/* Generated:', css_file)
        self.assertIn('--theme-primary: #2563EB;', css_file)

    def test_theme_preview_data(self):
        """Test theme preview data generation."""
        generator = ThemeGenerator(self.theme)
        preview_data = generator.get_theme_preview_data()
        
        self.assertEqual(preview_data['theme_name'], 'Custom')
        self.assertEqual(preview_data['colors']['primary'], '#2563EB')
        self.assertEqual(preview_data['typography']['font_family'], 'Inter')
        self.assertEqual(preview_data['typography']['font_size'], '18px')

    def test_css_validation(self):
        """Test CSS validation functionality."""
        generator = ThemeGenerator(self.theme)
        css = generator.generate_theme_css()
        
        validation_result = ThemeGenerator.validate_theme_css(css)
        
        self.assertTrue(validation_result['valid'])
        self.assertEqual(validation_result['errors'], [])
        self.assertEqual(len(validation_result['warnings']), 0)

    def test_css_validation_with_errors(self):
        """Test CSS validation with invalid CSS."""
        invalid_css = \"\"\"
        :root {
            --theme-primary: #FF0000;
            /* Missing closing brace
        \"\"\"
        
        validation_result = ThemeGenerator.validate_theme_css(invalid_css)
        
        self.assertFalse(validation_result['valid'])
        self.assertIn('Unbalanced braces', validation_result['errors'][0])

    def test_performance_caching(self):
        """Test performance caching functionality."""
        generator = ThemeGenerator(self.theme)
        
        # First generation should be slower
        start_time = time.time()
        css1 = generator.generate_css_custom_properties()
        first_duration = time.time() - start_time
        
        # Second generation should be faster (cached)
        start_time = time.time()
        css2 = generator.generate_css_custom_properties()
        second_duration = time.time() - start_time
        
        # Results should be identical
        self.assertEqual(css1, css2)
        
        # Second call should be faster (though timing might vary)
        # We'll just check that cache is populated
        self.assertGreater(len(generator._css_cache), 0)

    def test_performance_metrics(self):
        """Test performance metrics collection."""
        generator = ThemeGenerator(self.theme)
        
        # Clear any existing metrics
        ThemeGenerator.clear_performance_metrics()
        
        # Generate CSS to collect metrics
        generator.generate_css_custom_properties()
        
        # Check metrics
        metrics = ThemeGenerator.get_performance_metrics()
        self.assertIn('css_generation', metrics)
        self.assertGreater(metrics['css_generation']['count'], 0)


class ThemeInjectorTests(TestCase):
    """Test suite for ThemeInjector class."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )
        self.theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            primary_color='#2563EB'
        )

    def test_theme_injector_initialization(self):
        """Test ThemeInjector initialization."""
        injector = ThemeInjector(self.widget_config)
        self.assertEqual(injector.widget_config, self.widget_config)
        self.assertIsInstance(injector.theme_generator, ThemeGenerator)

    def test_iframe_css_injection(self):
        """Test iframe CSS injection generation."""
        injector = ThemeInjector(self.widget_config)
        injection_code = injector.get_iframe_css_injection()
        
        self.assertIn('function()', injection_code)
        self.assertIn('createElement(\"style\")', injection_code)
        self.assertIn('--theme-primary: #2563EB', injection_code)

    def test_theme_data_json(self):
        """Test theme data JSON generation."""
        injector = ThemeInjector(self.widget_config)
        theme_json = injector.get_theme_data_json()
        
        theme_data = json.loads(theme_json)
        self.assertEqual(theme_data['colors']['primary'], '#2563EB')
        self.assertEqual(theme_data['theme_name'], 'Custom')

    def test_render_theme_style_tag(self):
        """Test theme style tag rendering."""
        injector = ThemeInjector(self.widget_config)
        style_tag = injector.render_theme_style_tag()
        
        self.assertIsInstance(style_tag, SafeString)
        self.assertIn('<style type=\"text/css\">', style_tag)
        self.assertIn('--theme-primary: #2563EB', style_tag)


class ThemeFormTests(TestCase):
    """Test suite for theme form functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )

    def test_theme_form_creation(self):
        """Test theme form creation with valid data."""
        form_data = {
            'theme-theme_name': 'Test Theme',
            'theme-primary_color': '#2563EB',
            'theme-secondary_color': '#64748B',
            'theme-accent_color': '#0EA5E9',
            'theme-background_color': '#FFFFFF',
            'theme-text_color': '#1E293B',
            'theme-font_family': 'Inter',
            'theme-base_font_size': '16px',
            'theme-font_weight': '400',
            'theme-line_height': '1.5',
            'theme-letter_spacing': '0',
            'theme-element_padding': '0.75rem',
            'theme-element_margin': '0.5rem',
            'theme-border_radius': '0.5rem',
            'theme-border_width': '1px',
            'theme-shadow_intensity': 'medium',
            'theme-animation_speed': '0.3s',
            'theme-hover_effect': 'darken'
        }
        
        form = FinderV2Form(data=form_data, instance=self.widget_config)
        self.assertTrue(form.is_valid(), form.errors)

    def test_predefined_theme_application(self):
        """Test predefined theme application."""
        form_data = {
            'theme-predefined_theme': 'modern-blue',
            'theme-theme_name': 'Modern Blue',
            'theme-primary_color': '#2563EB',  # Will be overridden
            'theme-secondary_color': '#64748B',
            'theme-accent_color': '#0EA5E9',
            'theme-background_color': '#FFFFFF',
            'theme-text_color': '#1E293B'
        }
        
        form = FinderV2Form(data=form_data, instance=self.widget_config)
        if form.is_valid():
            cleaned_data = form.cleaned_data
            # Predefined theme should override the colors
            self.assertEqual(cleaned_data.get('theme-primary_color'), '#2563EB')


class ThemeTemplateTagTests(TestCase):
    """Test suite for theme template tags."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )
        self.theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            primary_color='#2563EB'
        )

    def test_render_theme_css_tag(self):
        """Test render_theme_css template tag."""
        template = Template(
            \"\"\"
            {% load theme_tags %}
            {% render_theme_css theme %}
            \"\"\"
        )
        
        context = Context({'theme': self.theme})
        rendered = template.render(context)
        
        self.assertIn('<style type=\"text/css\">', rendered)
        self.assertIn('--theme-primary: #2563EB', rendered)

    def test_render_theme_css_tag_with_none(self):
        """Test render_theme_css template tag with None theme."""
        template = Template(
            \"\"\"
            {% load theme_tags %}
            {% render_theme_css theme %}
            \"\"\"
        )
        
        context = Context({'theme': None})
        rendered = template.render(context)
        
        # Should render fallback CSS
        self.assertIn('<style>:root', rendered)
        self.assertIn('--theme-primary: #3B82F6', rendered)


class ThemeAccessibilityTests(TestCase):
    """Test suite for theme accessibility compliance."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )

    def test_color_contrast_compliance(self):
        """Test color contrast compliance."""
        # Test high contrast theme
        high_contrast_theme = WidgetTheme(
            widget=self.widget_config,
            background_color='#FFFFFF',
            text_color='#000000'
        )
        high_contrast_theme.full_clean()  # Should pass
        
        # Test low contrast theme
        with self.assertRaises(ValidationError):
            low_contrast_theme = WidgetTheme(
                widget=self.widget_config,
                background_color='#FFFFFF',
                text_color='#DDDDDD'
            )
            low_contrast_theme.full_clean()

    def test_font_size_accessibility(self):
        """Test font size accessibility."""
        # Test minimum font size
        theme = WidgetTheme(
            widget=self.widget_config,
            base_font_size='14px'  # Should be accessible
        )
        theme.full_clean()  # Should pass
        
        # Very small font sizes should be discouraged but not blocked
        # (handled by choices in the model)

    def test_animation_accessibility(self):
        """Test animation accessibility considerations."""
        # Test reduced motion support
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            animation_speed='0.1s',  # Fast animation
            hover_effect='scale'
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should include transition properties
        self.assertIn('transition:', css)
        self.assertIn('0.1s', css)


class ThemeSecurityTests(TestCase):
    """Test suite for theme security measures."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )

    def test_xss_prevention_in_theme_name(self):
        """Test XSS prevention in theme name."""
        # Theme name should be escaped in output
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='<script>alert(\"xss\")</script>'
        )
        
        generator = ThemeGenerator(theme)
        css_file = generator.generate_css_file_content()
        
        # Script tags should be escaped or sanitized
        self.assertNotIn('<script>', css_file)

    def test_advanced_config_sanitization(self):
        """Test advanced configuration sanitization."""
        # Malicious advanced config should be rejected
        with self.assertRaises(ValidationError):
            theme = WidgetTheme(
                widget=self.widget_config,
                advanced_config={
                    'javascript_injection': 'javascript:alert(\"xss\")',
                    'css_injection': 'expression(alert(\"xss\"))'
                }
            )
            theme.full_clean()

    def test_css_injection_prevention(self):
        """Test CSS injection prevention."""
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            primary_color='#FF0000'
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Validate CSS for dangerous patterns
        validation_result = ThemeGenerator.validate_theme_css(css)
        
        # Should not contain dangerous patterns
        self.assertTrue(validation_result['valid'])
        self.assertEqual(len(validation_result['errors']), 0)


class ThemeIntegrationTests(TestCase):
    """Integration tests for the complete theme system."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )
        self.factory = RequestFactory()

    def test_end_to_end_theme_workflow(self):
        """Test complete theme workflow from creation to rendering."""
        # 1. Create theme
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Integration Test',
            primary_color='#2563EB',
            font_family='Inter',
            base_font_size='18px'
        )
        
        # 2. Generate CSS
        generator = ThemeGenerator(theme)
        css = generator.generate_css_custom_properties()
        
        # 3. Validate CSS
        validation_result = ThemeGenerator.validate_theme_css(css)
        self.assertTrue(validation_result['valid'])
        
        # 4. Create injector
        injector = ThemeInjector(self.widget_config)
        
        # 5. Generate injection code
        injection_code = injector.get_iframe_css_injection()
        self.assertIn('--theme-primary: #2563EB', injection_code)
        
        # 6. Export theme
        exported_data = theme.export_to_json()
        self.assertEqual(exported_data['theme_name'], 'Integration Test')
        
        # 7. Import theme to new widget
        new_widget = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Import Test'
        )
        imported_theme = WidgetTheme.import_from_json(exported_data, new_widget)
        
        # 8. Verify imported theme
        self.assertEqual(imported_theme.theme_name, 'Integration Test')
        self.assertEqual(imported_theme.primary_color, '#2563EB')

    def test_theme_system_performance(self):
        """Test theme system performance with multiple themes."""
        themes = []
        
        # Create multiple themes
        for i in range(10):
            widget = WidgetConfig.objects.create(
                user=self.user,
                type='finder-v2',
                name=f'Performance Test Widget {i}'
            )
            theme = WidgetTheme.objects.create(
                widget=widget,
                theme_name=f'Performance Test Theme {i}',
                primary_color=f'#FF{i:02d}{i:02d}{i:02d}'
            )
            themes.append(theme)
        
        # Test bulk CSS generation
        start_time = time.time()
        css_results = []
        for theme in themes:
            generator = ThemeGenerator(theme)
            css = generator.generate_css_custom_properties()
            css_results.append(css)
        
        total_time = time.time() - start_time
        
        # Should complete reasonably quickly
        self.assertLess(total_time, 5.0)  # 5 seconds max
        self.assertEqual(len(css_results), 10)
        
        # All results should be valid
        for css in css_results:
            self.assertIn(':root {', css)
            self.assertIn('--theme-primary:', css)


if __name__ == '__main__':
    pytest.main([__file__])