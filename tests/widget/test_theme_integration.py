"""
Integration tests for the finder-v2 theme system.

This test suite covers:
- Theme configuration interface integration
- Real-time preview functionality
- Theme persistence and loading
- Widget rendering with themes
- Cross-browser compatibility
"""

import json
import time
from unittest.mock import Mock, patch
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils.html import escape
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

from src.apps.widgets.common.models import WidgetConfig, WidgetTheme
from src.apps.widgets.finder_v2.utils.theme_generator import ThemeGenerator


class ThemeConfigurationIntegrationTests(TestCase):
    """Integration tests for theme configuration interface."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )
        self.client.login(username='testuser', password='testpass123')

    def test_theme_configuration_page_loads(self):
        """Test that theme configuration page loads correctly."""
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Theme & Styling')
        self.assertContains(response, 'Color Settings')
        self.assertContains(response, 'Typography Settings')
        self.assertContains(response, 'Visual Effects')
        self.assertContains(response, 'Spacing Controls')
        self.assertContains(response, 'Animation Controls')

    def test_theme_form_submission(self):
        """Test theme form submission."""
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        
        form_data = {
            'theme-theme_name': 'Integration Test Theme',
            'theme-primary_color': '#2563EB',
            'theme-secondary_color': '#64748B',
            'theme-accent_color': '#0EA5E9',
            'theme-background_color': '#FFFFFF',
            'theme-text_color': '#1E293B',
            'theme-font_family': 'Inter',
            'theme-base_font_size': '16px',
            'theme-font_weight': '500',
            'theme-line_height': '1.6',
            'theme-letter_spacing': '0.025em',
            'theme-element_padding': '1rem',
            'theme-element_margin': '0.75rem',
            'theme-border_radius': '0.5rem',
            'theme-border_width': '2px',
            'theme-shadow_intensity': 'heavy',
            'theme-animation_speed': '0.2s',
            'theme-hover_effect': 'scale'
        }
        
        response = self.client.post(url, form_data)
        
        # Should redirect on success
        self.assertEqual(response.status_code, 302)
        
        # Verify theme was created
        theme = WidgetTheme.objects.get(widget=self.widget_config)
        self.assertEqual(theme.theme_name, 'Integration Test Theme')
        self.assertEqual(theme.primary_color, '#2563EB')
        self.assertEqual(theme.font_weight, '500')
        self.assertEqual(theme.hover_effect, 'scale')

    def test_predefined_theme_selection(self):
        """Test predefined theme selection."""
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        
        form_data = {
            'theme-predefined_theme': 'modern-blue',
            'theme-theme_name': 'Modern Blue',
            'theme-primary_color': '#2563EB',  # Should match predefined
            'theme-secondary_color': '#64748B',
            'theme-accent_color': '#0EA5E9',
            'theme-background_color': '#FFFFFF',
            'theme-text_color': '#1E293B'
        }
        
        response = self.client.post(url, form_data)
        self.assertEqual(response.status_code, 302)
        
        # Verify theme was created with predefined values
        theme = WidgetTheme.objects.get(widget=self.widget_config)
        self.assertEqual(theme.theme_name, 'Modern Blue')
        self.assertEqual(theme.primary_color, '#2563EB')

    def test_theme_validation_errors(self):
        """Test theme validation error handling."""
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        
        # Submit invalid data
        form_data = {
            'theme-theme_name': 'Invalid Theme',
            'theme-primary_color': 'invalid-color',  # Invalid hex
            'theme-background_color': '#FFFFFF',
            'theme-text_color': '#CCCCCC'  # Poor contrast
        }
        
        response = self.client.post(url, form_data)
        
        # Should return form with errors
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Color must be a valid hex code')

    def test_theme_persistence_across_requests(self):
        """Test that theme settings persist across requests."""
        # Create theme
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Persistence Test',
            primary_color='#FF5733',
            font_family='Roboto'
        )
        
        # Load configuration page
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        response = self.client.get(url)
        
        # Verify theme values are loaded
        self.assertContains(response, 'Persistence Test')
        self.assertContains(response, '#FF5733')
        self.assertContains(response, 'Roboto')


class ThemeRenderingIntegrationTests(TestCase):
    """Integration tests for theme rendering in widgets."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )
        self.theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Rendering Test',
            primary_color='#2563EB',
            font_family='Inter',
            base_font_size='18px'
        )

    def test_widget_renders_with_theme(self):
        """Test that widget renders with applied theme."""
        client = Client()
        url = reverse('widget_view', kwargs={'widget_id': self.widget_config.id})
        response = client.get(url)
        
        self.assertEqual(response.status_code, 200)
        
        # Should contain theme CSS
        self.assertContains(response, '--theme-primary: #2563EB')
        self.assertContains(response, '--theme-font-size: 18px')

    def test_widget_iframe_theme_injection(self):
        """Test theme injection into widget iframe."""
        client = Client()
        url = reverse('widget_iframe', kwargs={'widget_id': self.widget_config.id})
        response = client.get(url)
        
        self.assertEqual(response.status_code, 200)
        
        # Should contain theme injection script
        self.assertContains(response, 'createElement(\"style\")')
        self.assertContains(response, '--theme-primary: #2563EB')

    def test_widget_api_includes_theme_data(self):
        """Test that widget API includes theme data."""
        client = Client()
        url = reverse('widget_api_config', kwargs={'widget_id': self.widget_config.id})
        response = client.get(url)
        
        self.assertEqual(response.status_code, 200)
        
        # Parse response as JSON
        data = json.loads(response.content)
        
        # Should include theme data
        self.assertIn('theme', data)
        self.assertEqual(data['theme']['colors']['primary'], '#2563EB')
        self.assertEqual(data['theme']['typography']['font_family'], 'Inter')

    def test_theme_css_generation_performance(self):
        """Test theme CSS generation performance."""
        generator = ThemeGenerator(self.theme)
        
        # Test multiple generations
        start_time = time.time()
        for _ in range(100):
            css = generator.generate_css_custom_properties()
        
        total_time = time.time() - start_time
        
        # Should complete quickly
        self.assertLess(total_time, 1.0)  # 1 second max for 100 generations
        self.assertIn('--theme-primary: #2563EB', css)

    def test_theme_fallback_css_generation(self):
        """Test theme fallback CSS generation."""
        generator = ThemeGenerator(self.theme)
        css = generator.generate_theme_css(include_fallbacks=True)
        
        # Should include fallback CSS
        self.assertIn('@supports not (--css: variables)', css)
        self.assertIn('background-color: #FFFFFF', css)
        self.assertIn('color: #1F2937', css)

    def test_responsive_theme_overrides(self):
        """Test responsive theme overrides."""
        # Create theme with responsive overrides
        self.theme.advanced_config = {
            'responsive_overrides': {
                'mobile': {
                    'font_size': '14px',
                    'padding': '0.5rem'
                },
                'tablet': {
                    'font_size': '16px',
                    'padding': '0.625rem'
                }
            }
        }
        self.theme.save()
        
        generator = ThemeGenerator(self.theme)
        css = generator.generate_css_custom_properties()
        
        # Should include responsive overrides
        self.assertIn('@media (max-width: 767px)', css)
        self.assertIn('--theme-font_size: 14px', css)
        self.assertIn('@media (min-width: 768px) and (max-width: 1023px)', css)
        self.assertIn('--theme-font_size: 16px', css)


class ThemeJavaScriptIntegrationTests(TestCase):
    """Integration tests for theme JavaScript functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )

    def test_theme_preview_javascript(self):
        """Test theme preview JavaScript functionality."""
        client = Client()
        client.login(username='testuser', password='testpass123')
        
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        response = client.get(url)
        
        self.assertEqual(response.status_code, 200)
        
        # Should include theme JavaScript
        self.assertContains(response, 'applyPredefinedTheme')
        self.assertContains(response, 'updatePreview')
        self.assertContains(response, 'predefinedThemes')

    def test_predefined_themes_javascript_data(self):
        """Test predefined themes JavaScript data structure."""
        client = Client()
        client.login(username='testuser', password='testpass123')
        
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        response = client.get(url)
        
        # Should include predefined theme data
        self.assertContains(response, 'modern-blue')
        self.assertContains(response, 'corporate-gray')
        self.assertContains(response, 'vibrant-green')
        self.assertContains(response, 'elegant-purple')

    def test_theme_form_event_handlers(self):
        """Test theme form event handlers."""
        client = Client()
        client.login(username='testuser', password='testpass123')
        
        url = reverse('widget_config', kwargs={'widget_id': self.widget_config.id})
        response = client.get(url)
        
        # Should include event handlers
        self.assertContains(response, 'addEventListener(\"change\"')
        self.assertContains(response, 'input[type=\"color\"]')
        self.assertContains(response, 'theme-predefined_theme')


class ThemeAccessibilityIntegrationTests(TestCase):
    """Integration tests for theme accessibility compliance."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget'
        )

    def test_high_contrast_theme_compliance(self):
        """Test high contrast theme compliance."""
        # Create high contrast theme
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='High Contrast',
            primary_color='#000000',
            secondary_color='#666666',
            accent_color='#0066CC',
            background_color='#FFFFFF',
            text_color='#000000'
        )
        
        # Theme should validate successfully
        theme.full_clean()
        
        # CSS should be accessible
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should contain high contrast colors
        self.assertIn('--theme-primary: #000000', css)
        self.assertIn('--theme-background: #FFFFFF', css)
        self.assertIn('--theme-text: #000000', css)

    def test_font_size_accessibility(self):
        """Test font size accessibility compliance."""
        # Create theme with accessible font sizes
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Accessible Fonts',
            base_font_size='16px'  # Minimum recommended
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should include accessible font size
        self.assertIn('--theme-font-size: 16px', css)

    def test_animation_accessibility(self):
        """Test animation accessibility (reduced motion)."""
        # Create theme with animations
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Animated Theme',
            animation_speed='0.3s',
            hover_effect='scale'
        )
        
        generator = ThemeGenerator(theme)
        css = generator.generate_theme_css()
        
        # Should include animation properties
        self.assertIn('--theme-animation-speed: 0.3s', css)
        self.assertIn('transition:', css)


class ThemePerformanceIntegrationTests(TestCase):
    """Integration tests for theme system performance."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_multiple_widgets_theme_performance(self):
        """Test performance with multiple widgets."""
        widgets = []
        themes = []
        
        # Create multiple widgets with themes
        for i in range(50):
            widget = WidgetConfig.objects.create(
                user=self.user,
                type='finder-v2',
                name=f'Performance Test Widget {i}'
            )
            theme = WidgetTheme.objects.create(
                widget=widget,
                theme_name=f'Performance Theme {i}',
                primary_color=f'#{i:02x}{i:02x}{i:02x}'
            )
            widgets.append(widget)
            themes.append(theme)
        
        # Test CSS generation performance
        start_time = time.time()
        
        for theme in themes:
            generator = ThemeGenerator(theme)
            css = generator.generate_css_custom_properties()
        
        total_time = time.time() - start_time
        
        # Should complete within reasonable time
        self.assertLess(total_time, 10.0)  # 10 seconds max for 50 themes

    def test_theme_caching_performance(self):
        """Test theme caching performance."""
        widget = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Caching Test Widget'
        )
        theme = WidgetTheme.objects.create(
            widget=widget,
            theme_name='Caching Test',
            primary_color='#FF0000'
        )
        
        generator = ThemeGenerator(theme)
        
        # First generation (no cache)
        start_time = time.time()
        css1 = generator.generate_css_custom_properties()
        first_time = time.time() - start_time
        
        # Second generation (with cache)
        start_time = time.time()
        css2 = generator.generate_css_custom_properties()
        second_time = time.time() - start_time
        
        # Results should be identical
        self.assertEqual(css1, css2)
        
        # Cache should improve performance
        self.assertLessEqual(second_time, first_time)

    def test_theme_memory_usage(self):
        """Test theme system memory usage."""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Create many themes
        widgets = []
        for i in range(100):
            widget = WidgetConfig.objects.create(
                user=self.user,
                type='finder-v2',
                name=f'Memory Test Widget {i}'
            )
            theme = WidgetTheme.objects.create(
                widget=widget,
                theme_name=f'Memory Test Theme {i}',
                primary_color=f'#{i:02x}{i:02x}{i:02x}'
            )
            widgets.append(widget)
        
        # Generate CSS for all themes
        for widget in widgets:
            generator = ThemeGenerator(widget.theme)
            css = generator.generate_css_custom_properties()
        
        # Get final memory usage
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB)
        self.assertLess(memory_increase, 100 * 1024 * 1024)


class ThemeSeleniumIntegrationTests(TestCase):
    """Selenium integration tests for theme system (requires Chrome)."""

    @classmethod
    def setUpClass(cls):
        """Set up Selenium WebDriver."""
        super().setUpClass()
        
        # Only run if Chrome is available
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            cls.driver = webdriver.Chrome(options=chrome_options)
        except Exception:
            cls.driver = None

    @classmethod
    def tearDownClass(cls):
        """Clean up Selenium WebDriver."""
        if cls.driver:
            cls.driver.quit()
        super().tearDownClass()

    def setUp(self):
        """Set up test data."""
        if not self.driver:
            self.skipTest('Chrome WebDriver not available')
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Selenium Test Widget'
        )

    def test_theme_configuration_ui(self):
        """Test theme configuration UI with Selenium."""
        if not self.driver:
            self.skipTest('Chrome WebDriver not available')
        
        # Login first
        self.driver.get(f'http://localhost:8000/login/')
        username_field = self.driver.find_element(By.NAME, 'username')
        password_field = self.driver.find_element(By.NAME, 'password')
        username_field.send_keys('testuser')
        password_field.send_keys('testpass123')
        self.driver.find_element(By.XPATH, '//button[@type=\"submit\"]').click()
        
        # Navigate to widget configuration
        config_url = f'http://localhost:8000/widget/{self.widget_config.id}/config/'
        self.driver.get(config_url)
        
        # Wait for theme section to load
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, '//h3[contains(text(), \"Theme\")]'))
        )
        
        # Test color picker interaction
        color_picker = self.driver.find_element(By.NAME, 'theme-primary_color')
        color_picker.clear()
        color_picker.send_keys('#FF0000')
        
        # Test predefined theme selection
        theme_select = self.driver.find_element(By.NAME, 'theme-predefined_theme')
        theme_select.click()
        
        # Select modern-blue theme
        modern_blue_option = self.driver.find_element(By.XPATH, '//option[@value=\"modern-blue\"]')
        modern_blue_option.click()
        
        # Verify theme values are applied
        WebDriverWait(self.driver, 5).until(
            lambda d: d.find_element(By.NAME, 'theme-primary_color').get_attribute('value') == '#2563EB'
        )

    def test_theme_preview_functionality(self):
        """Test theme preview functionality with Selenium."""
        if not self.driver:
            self.skipTest('Chrome WebDriver not available')
        
        # Create theme first
        theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Selenium Test',
            primary_color='#FF5733'
        )
        
        # Navigate to widget configuration
        config_url = f'http://localhost:8000/widget/{self.widget_config.id}/config/'
        self.driver.get(config_url)
        
        # Wait for preview iframe to load
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'iframe-preview'))
        )
        
        # Switch to preview iframe
        preview_iframe = self.driver.find_element(By.CSS_SELECTOR, '.iframe-preview iframe')
        self.driver.switch_to.frame(preview_iframe)
        
        # Check if theme styles are applied
        widget_element = self.driver.find_element(By.CLASS_NAME, 'finder-v2-widget')
        computed_style = self.driver.execute_script(
            \"return window.getComputedStyle(arguments[0]);\", widget_element
        )
        
        # Verify theme CSS variables are applied
        primary_color = self.driver.execute_script(
            \"return getComputedStyle(document.documentElement).getPropertyValue('--theme-primary');\".strip()
        )
        
        self.assertEqual(primary_color, '#FF5733')


if __name__ == '__main__':
    # Run specific test classes
    import unittest
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTest(unittest.makeSuite(ThemeConfigurationIntegrationTests))
    suite.addTest(unittest.makeSuite(ThemeRenderingIntegrationTests))
    suite.addTest(unittest.makeSuite(ThemeJavaScriptIntegrationTests))
    suite.addTest(unittest.makeSuite(ThemeAccessibilityIntegrationTests))
    suite.addTest(unittest.makeSuite(ThemePerformanceIntegrationTests))
    
    # Optionally add Selenium tests (requires Chrome)
    # suite.addTest(unittest.makeSuite(ThemeSeleniumIntegrationTests))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    runner.run(suite)