<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Copy Functionality Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Copy Functionality Test</h1>
        
        <!-- Test Code Block (similar to the widget implementation) -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="relative">
                <div class="absolute top-3 right-3 z-10">
                    <button onclick="copyToClipboard()" 
                            class="inline-flex items-center px-3 py-2 text-xs font-medium text-gray-600 bg-white rounded-md border border-gray-300 shadow-sm hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                            type="button">
                        <svg class="size-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.375a2.25 2.25 0 0 1-2.25-2.25V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
                        </svg>
                        <span id="copy-button-text">Copy</span>
                    </button>
                </div>
                
                <div class="bg-gray-900 rounded-lg overflow-hidden">
                    <div class="flex items-center justify-between px-4 py-3 bg-gray-800 border-b border-gray-700">
                        <div class="flex items-center space-x-2">
                            <div class="flex space-x-1">
                                <div class="w-2.5 h-2.5 rounded-full bg-red-500"></div>
                                <div class="w-2.5 h-2.5 rounded-full bg-yellow-500"></div>
                                <div class="w-2.5 h-2.5 rounded-full bg-green-500"></div>
                            </div>
                            <span class="text-sm text-gray-300 font-mono">widget-embed.html</span>
                        </div>
                        <span class="text-xs text-gray-400">HTML</span>
                    </div>
                    <pre id="code-block" class="p-4 text-sm text-gray-100 overflow-x-auto"><code class="language-html">&lt;!-- The Link to Wheel-Size.com. It is required for free usage of services. --&gt;
&lt;h3&gt;&lt;a title="Wheel fitment and tire size guide" href="https://www.wheel-size.com"&gt;Wheel-Size.com&lt;/a&gt;&lt;/h3&gt;

&lt;div id="ws-widget-test123"&gt;&lt;/div&gt;

&lt;script src="//services.wheel-size.com/code/ws-widget.js"&gt;&lt;/script&gt;
&lt;script&gt;
  var widget = WheelSizeWidgets.create('#ws-widget-test123', {
    uuid: 'test-uuid-123',
    type: 'finder-v2',
    width: '900',
    height: '500'
  });
&lt;/script&gt;</code></pre>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 class="font-semibold text-blue-900 mb-2">Test Instructions:</h3>
            <ol class="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                <li>Click the "Copy" button above</li>
                <li>Paste the content somewhere (text editor, email, etc.)</li>
                <li>Verify that the HTML code was copied correctly</li>
                <li>Check that the button shows "Copied!" feedback</li>
            </ol>
        </div>

        <!-- Debug Info -->
        <div class="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h3 class="font-semibold text-gray-900 mb-2">Debug Information:</h3>
            <ul class="text-sm text-gray-700 space-y-1">
                <li id="clipboard-api-status">Clipboard API: <span class="font-mono">checking...</span></li>
                <li id="secure-context-status">Secure Context: <span class="font-mono">checking...</span></li>
                <li id="fallback-status">Fallback Available: <span class="font-mono">checking...</span></li>
            </ul>
        </div>
    </div>

    <!-- Copy functionality (same as widget implementation) -->
    <script>
        // Check capabilities on page load
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('clipboard-api-status').innerHTML = 
                'Clipboard API: <span class="font-mono">' + (navigator.clipboard ? 'Available' : 'Not Available') + '</span>';
            
            document.getElementById('secure-context-status').innerHTML = 
                'Secure Context: <span class="font-mono">' + (window.isSecureContext ? 'Yes' : 'No') + '</span>';
            
            document.getElementById('fallback-status').innerHTML = 
                'Fallback Available: <span class="font-mono">' + (document.execCommand ? 'Yes' : 'No') + '</span>';
        });

        function copyToClipboard() {
            const codeBlock = document.getElementById('code-block');
            const copyButton = document.getElementById('copy-button-text');
            const codeText = codeBlock.textContent;
            
            console.log('Copy attempt started');
            console.log('Text to copy:', codeText.substring(0, 100) + '...');
            
            // Check if modern clipboard API is available
            if (navigator.clipboard && window.isSecureContext) {
                console.log('Using modern Clipboard API');
                // Modern browsers with HTTPS
                navigator.clipboard.writeText(codeText).then(function() {
                    console.log('Clipboard API success');
                    showCopySuccess(copyButton);
                }).catch(function(err) {
                    console.warn('Clipboard API failed, falling back to execCommand:', err);
                    fallbackCopy(codeText, copyButton);
                });
            } else {
                console.log('Using fallback method');
                // Fallback for older browsers or non-HTTPS contexts
                fallbackCopy(codeText, copyButton);
            }
        }
        
        function fallbackCopy(text, copyButton) {
            try {
                console.log('Attempting fallback copy');
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed';
                textarea.style.left = '-999999px';
                textarea.style.top = '-999999px';
                document.body.appendChild(textarea);
                textarea.focus();
                textarea.select();
                
                const successful = document.execCommand('copy');
                document.body.removeChild(textarea);
                
                console.log('Fallback copy result:', successful);
                
                if (successful) {
                    showCopySuccess(copyButton);
                } else {
                    showCopyError(copyButton);
                }
            } catch (err) {
                console.error('Fallback copy failed:', err);
                showCopyError(copyButton);
            }
        }
        
        function showCopySuccess(copyButton) {
            console.log('Showing copy success');
            copyButton.textContent = 'Copied!';
            copyButton.style.color = '#059669'; // green-600
            setTimeout(() => {
                copyButton.textContent = 'Copy';
                copyButton.style.color = '';
            }, 2000);
        }
        
        function showCopyError(copyButton) {
            console.log('Showing copy error');
            copyButton.textContent = 'Copy Failed';
            copyButton.style.color = '#dc2626'; // red-600
            setTimeout(() => {
                copyButton.textContent = 'Copy';
                copyButton.style.color = '';
            }, 3000);
        }
    </script>
</body>
</html>