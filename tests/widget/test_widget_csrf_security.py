#!/usr/bin/env python3
"""
Comprehensive Widget CSRF Security Test

This test validates that the widget CSRF protection:
1. Allows legitimate cross-domain widget requests with valid tokens
2. Blocks malicious requests without proper tokens
3. Maintains security without relying on trusted_hostnames bypass

Run with: python test_widget_csrf_security.py
"""

import base64
import requests
import sys
from urllib.parse import urlparse

# Test configuration
BASE_URL = "http://development.local:8000"
API_ENDPOINT = f"{BASE_URL}/widget/finder/api/mk"

def generate_csrf_token(user_agent):
    """Generate CSRF token using the widget algorithm"""
    token = base64.b64encode(user_agent.encode('utf-8')).decode('utf-8')
    token_slice = token[:32]
    
    result = []
    for i in range(len(token_slice)):
        index = (27 + 11 - (7 + i * 11) % 39) % len(token)
        result.append(token[index])
    
    return ''.join(result)

def test_request(referer, csrf_token, user_agent, description):
    """Test a single API request and return result"""
    headers = {
        'Referer': referer,
        'User-Agent': user_agent
    }
    
    if csrf_token:
        headers['X-Csrf-Token'] = csrf_token
    
    try:
        response = requests.get(API_ENDPOINT, headers=headers, timeout=10)
        
        # Check if response is JSON (success) or HTML (404/error)
        is_json = response.headers.get('content-type', '').startswith('application/json')
        
        return {
            'status_code': response.status_code,
            'is_json': is_json,
            'success': response.status_code == 200 and is_json,
            'content_preview': response.text[:100]
        }
    except Exception as e:
        return {
            'status_code': 0,
            'is_json': False,
            'success': False,
            'error': str(e)
        }

def run_security_tests():
    """Run comprehensive widget CSRF security tests"""
    
    print("🔒 Widget CSRF Security Test Suite")
    print("=" * 50)
    
    # Test configuration
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    valid_token = generate_csrf_token(user_agent)
    invalid_token = "invalid_token_here"
    
    print(f"Generated CSRF Token: {valid_token}")
    print(f"User-Agent: {user_agent}")
    print()
    
    # Test cases
    test_cases = [
        # ✅ SHOULD SUCCEED - Legitimate requests
        {
            'referer': f'{BASE_URL}/widget/finder/try/',
            'token': valid_token,
            'user_agent': user_agent,
            'description': '✅ Same-origin with valid token',
            'should_succeed': True
        },
        {
            'referer': 'https://client-website.com/page-with-widget/',
            'token': valid_token,
            'user_agent': user_agent,
            'description': '✅ Cross-origin widget with valid token',
            'should_succeed': True
        },
        {
            'referer': 'https://another-client.com/products/',
            'token': valid_token,
            'user_agent': user_agent,
            'description': '✅ Different client domain with valid token',
            'should_succeed': True
        },
        
        # ❌ SHOULD FAIL - Security attacks
        {
            'referer': f'{BASE_URL}/widget/finder/try/',
            'token': invalid_token,
            'user_agent': user_agent,
            'description': '❌ Same-origin with invalid token',
            'should_succeed': False
        },
        {
            'referer': 'https://client-website.com/page-with-widget/',
            'token': invalid_token,
            'user_agent': user_agent,
            'description': '❌ Cross-origin with invalid token',
            'should_succeed': False
        },
        {
            'referer': 'https://client-website.com/page-with-widget/',
            'token': None,
            'user_agent': user_agent,
            'description': '❌ Cross-origin without token',
            'should_succeed': False
        },
        {
            'referer': 'https://malicious-site.com/attack/',
            'token': valid_token,
            'user_agent': user_agent,
            'description': '❌ Malicious domain with valid token',
            'should_succeed': True  # Should succeed - widgets allow cross-domain
        },
        {
            'referer': 'https://malicious-site.com/attack/',
            'token': invalid_token,
            'user_agent': user_agent,
            'description': '❌ Malicious domain with invalid token',
            'should_succeed': False
        },
        {
            'referer': 'https://malicious-site.com/attack/',
            'token': None,
            'user_agent': user_agent,
            'description': '❌ Malicious domain without token',
            'should_succeed': False
        },
        
        # 🔍 EDGE CASES
        {
            'referer': f'http://development.local/widget/finder/try/',  # No port
            'token': valid_token,
            'user_agent': user_agent,
            'description': '🔍 Same domain without port (port ignore test)',
            'should_succeed': True
        }
    ]
    
    # Run tests
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['description']}")
        print(f"  Referer: {test_case['referer']}")
        print(f"  Token: {test_case['token'] or 'None'}")
        
        result = test_request(
            test_case['referer'],
            test_case['token'],
            test_case['user_agent'],
            test_case['description']
        )
        
        expected = test_case['should_succeed']
        actual = result['success']
        
        if actual == expected:
            print(f"  ✅ PASS - Expected: {expected}, Got: {actual}")
            passed += 1
        else:
            print(f"  ❌ FAIL - Expected: {expected}, Got: {actual}")
            print(f"     Status: {result['status_code']}, JSON: {result['is_json']}")
            print(f"     Content: {result.get('content_preview', 'N/A')}")
            failed += 1
        
        print()
    
    # Summary
    print("=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED - Widget CSRF security is working correctly!")
        print()
        print("Security Model Validated:")
        print("✅ Cross-origin widget requests allowed with valid tokens")
        print("✅ Invalid/missing tokens blocked")
        print("✅ No security bypass through hostname spoofing")
        return True
    else:
        print("🚨 SECURITY ISSUES DETECTED - Some tests failed!")
        print("Review the failed tests and fix security configuration.")
        return False

def test_trusted_hostnames_vulnerability():
    """
    Test to demonstrate why trusted_hostnames is a security risk.
    This test should be run with trusted_hostnames enabled to show the vulnerability.
    """
    print("\n🚨 Trusted Hostnames Vulnerability Test")
    print("=" * 50)
    print("This test demonstrates why 'trusted_hostnames' creates security risks.")
    print()
    
    # Simulate an attack where malicious site spoofs trusted hostname
    user_agent = "Mozilla/5.0 (compatible; AttackBot/1.0)"
    
    # Test with spoofed referer matching trusted hostname
    spoofed_referer = "http://development.local/fake-page/"
    
    print(f"Simulating attack:")
    print(f"  Malicious site spoofs referer: {spoofed_referer}")
    print(f"  No CSRF token provided")
    print(f"  User-Agent: {user_agent}")
    
    result = test_request(spoofed_referer, None, user_agent, "Spoofed referer attack")
    
    if result['success']:
        print("  🚨 VULNERABILITY: Attack succeeded! trusted_hostnames bypassed security!")
        print("  This proves why trusted_hostnames should not be used.")
    else:
        print("  ✅ SECURE: Attack blocked. Current configuration is safe.")
    
    print()

if __name__ == '__main__':
    print("Starting Widget CSRF Security Tests...")
    print(f"Testing against: {BASE_URL}")
    print()
    
    # Run main security tests
    success = run_security_tests()
    
    # Run vulnerability demonstration
    test_trusted_hostnames_vulnerability()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
