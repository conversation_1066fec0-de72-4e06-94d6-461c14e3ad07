#!/usr/bin/env python3
"""
Production CSRF Configuration Verification Script

This script verifies that the CSRF hostname validation will work correctly
in production environment by simulating production settings and testing
various hostname scenarios.
"""

import os
import sys
from urllib.parse import urlparse

# Simulate production settings
PRODUCTION_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': False,  # Strict hostname checking in production
    'trusted_hostnames': ['services.wheel-size.com'],  # Only trust production domain
    'debug_csrf_validation': False,  # No debug logging in production
}

def validate_hostname_production(referer_url, request_host, csrf_settings):
    """
    Simulate the production hostname validation logic.
    
    Args:
        referer_url: The referer URL from the request
        request_host: The Host header value
        csrf_settings: CSRF settings dictionary
        
    Returns:
        tuple: (is_valid, reason)
    """
    ignore_port = csrf_settings.get('ignore_port_in_hostname_check', False)
    trusted_hostnames = csrf_settings.get('trusted_hostnames', [])
    
    # Parse hostnames
    referer_hostname = urlparse(referer_url).hostname
    
    # Extract request hostname (with or without port based on settings)
    if ignore_port and ':' in request_host:
        request_hostname = request_host.split(':')[0]
    else:
        request_hostname = request_host
        
    # Check direct hostname match
    if referer_hostname == request_hostname:
        return True, f"Direct match: {referer_hostname} == {request_hostname}"
        
    # Check against trusted hostnames list
    if referer_hostname in trusted_hostnames:
        return True, f"Trusted hostname: {referer_hostname} in {trusted_hostnames}"
        
    # If ignore_port is enabled, also check if referer matches request hostname without port
    if ignore_port and ':' in request_host:
        base_request_hostname = request_host.split(':')[0]
        if referer_hostname == base_request_hostname:
            return True, f"Port-ignored match: {referer_hostname} == {base_request_hostname}"
            
    return False, f"No match: {referer_hostname} not allowed for {request_hostname}"

def test_production_scenarios():
    """Test various production scenarios."""
    
    print("🔍 Production CSRF Hostname Validation Test")
    print("=" * 50)
    print(f"Settings: {PRODUCTION_CSRF_SETTINGS}")
    print()
    
    # Test scenarios for production
    test_cases = [
        # Valid production scenarios
        {
            'name': 'Production widget iframe',
            'referer': 'https://services.wheel-size.com/widget/finder/try/',
            'host': 'services.wheel-size.com',
            'expected': True
        },
        {
            'name': 'Production widget API call',
            'referer': 'https://services.wheel-size.com/widget/finder/iframe/',
            'host': 'services.wheel-size.com',
            'expected': True
        },
        {
            'name': 'Production with HTTPS',
            'referer': 'https://services.wheel-size.com/widget/calc/try/',
            'host': 'services.wheel-size.com',
            'expected': True
        },
        
        # Invalid scenarios that should be blocked
        {
            'name': 'Different subdomain attack',
            'referer': 'https://malicious.wheel-size.com/widget/finder/try/',
            'host': 'services.wheel-size.com',
            'expected': False
        },
        {
            'name': 'Different domain attack',
            'referer': 'https://evil.com/widget/finder/try/',
            'host': 'services.wheel-size.com',
            'expected': False
        },
        {
            'name': 'Development domain in production',
            'referer': 'http://development.local:8000/widget/finder/try/',
            'host': 'services.wheel-size.com',
            'expected': False
        },
        {
            'name': 'Staging domain in production',
            'referer': 'https://services2.wheel-size.com/widget/finder/try/',
            'host': 'services.wheel-size.com',
            'expected': False
        },
        
        # Edge cases
        {
            'name': 'Production with port (should work)',
            'referer': 'https://services.wheel-size.com/widget/finder/try/',
            'host': 'services.wheel-size.com:443',
            'expected': True  # Should work because referer matches host without port
        },
        {
            'name': 'HTTP vs HTTPS mismatch',
            'referer': 'http://services.wheel-size.com/widget/finder/try/',
            'host': 'services.wheel-size.com',
            'expected': True  # Should work - we only check hostname, not protocol
        }
    ]
    
    passed = 0
    failed = 0
    
    for test_case in test_cases:
        is_valid, reason = validate_hostname_production(
            test_case['referer'], 
            test_case['host'], 
            PRODUCTION_CSRF_SETTINGS
        )
        
        status = "✅ PASS" if is_valid == test_case['expected'] else "❌ FAIL"
        if is_valid == test_case['expected']:
            passed += 1
        else:
            failed += 1
            
        print(f"{status} {test_case['name']}")
        print(f"     Referer: {test_case['referer']}")
        print(f"     Host: {test_case['host']}")
        print(f"     Expected: {test_case['expected']}, Got: {is_valid}")
        print(f"     Reason: {reason}")
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Production configuration is secure.")
        return True
    else:
        print("⚠️  Some tests failed. Review the configuration.")
        return False

def test_development_scenarios():
    """Test development scenarios with development settings."""
    
    dev_settings = {
        'ignore_port_in_hostname_check': True,
        'trusted_hostnames': ['development.local', 'localhost', '127.0.0.1'],
        'debug_csrf_validation': True,
    }
    
    print("\n🔍 Development CSRF Hostname Validation Test")
    print("=" * 50)
    print(f"Settings: {dev_settings}")
    print()
    
    test_cases = [
        {
            'name': 'Development with port',
            'referer': 'http://development.local/widget/finder/try/',
            'host': 'development.local:8000',
            'expected': True
        },
        {
            'name': 'Localhost with port',
            'referer': 'http://localhost/widget/finder/try/',
            'host': 'localhost:8000',
            'expected': True
        },
        {
            'name': 'Trusted hostname',
            'referer': 'http://127.0.0.1/widget/finder/try/',
            'host': '127.0.0.1:8000',
            'expected': True
        }
    ]
    
    for test_case in test_cases:
        is_valid, reason = validate_hostname_production(
            test_case['referer'], 
            test_case['host'], 
            dev_settings
        )
        
        status = "✅ PASS" if is_valid == test_case['expected'] else "❌ FAIL"
        print(f"{status} {test_case['name']}")
        print(f"     Reason: {reason}")

if __name__ == '__main__':
    success = test_production_scenarios()
    test_development_scenarios()
    
    if success:
        print("\n🚀 Ready for production deployment!")
        sys.exit(0)
    else:
        print("\n🛑 Configuration needs review before production deployment.")
        sys.exit(1)
