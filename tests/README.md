# Test Suite Documentation
Last Modified: 2025-01-28 15:30 UTC+6

This directory contains the test suite for the wheel-size-services project, organized by functionality and component.

## Directory Structure

```
tests/
├── README.md                           # This file - comprehensive test documentation
├── pytest-quick-start.md              # Quick start guide for pytest testing
├── pytest-usage-guide.md              # Comprehensive pytest usage documentation
├── verify_production_csrf.py           # Production CSRF configuration verification
├── test_tailwind_classes_verification.py # TailwindCSS class verification
└── widget/
    ├── test_widget_csrf_security.py    # Widget CSRF security validation
    ├── test-calc-widget.html           # Calc widget JavaScript compatibility test
    ├── test-calc-widget-direct.html    # Direct calc widget JavaScript fix verification
    ├── test_copy_functionality.html    # Copy functionality testing
    ├── test_finder_v2_html_snippet.py  # Finder v2 HTML snippet tests
    ├── test_theme_accessibility.py     # Theme accessibility tests (requires selenium)
    ├── test_theme_integration.py       # Theme integration tests (requires selenium)
    ├── test_theme_system.py            # Theme system tests
    └── finder_v2/                      # Finder v2 specific tests
        ├── __init__.py                 # Package initialization
        ├── run_tests.py                # Test runner for finder v2 tests
        ├── test_admin_interface.py     # Admin interface tests
        ├── test_api_proxy.py           # API proxy functionality tests
        ├── test_browser.py             # Browser-based tests
        ├── test_csrf_protection.py     # CSRF protection tests
        ├── test_form_persistence.py    # Form persistence tests
        ├── test_integration.py         # Integration tests
        ├── test_region_filtering.py    # Region filtering tests
        ├── test_search_history_form_persistence.py # Search history tests
        └── test_widget_type.py         # Widget type registration tests
```

## Pytest Documentation

This directory includes comprehensive pytest documentation:

- **[pytest-quick-start.md](pytest-quick-start.md)** - Quick start guide for immediate testing of region filtering functionality
- **[pytest-usage-guide.md](pytest-usage-guide.md)** - Comprehensive guide covering pytest usage in the Docker environment, test structure patterns, and best practices

## Test Categories

### Root Level Tests (`tests/`)

**Purpose:** Core functionality validation and production readiness checks.

**Key Tests:**
- **Production CSRF Verification** (`verify_production_csrf.py`) - Validate CSRF settings for production deployment
- **TailwindCSS Class Verification** (`test_tailwind_classes_verification.py`) - Verify CSS classes are properly available in templates

### Widget Tests (`widget/`)

**Purpose:** Validate widget functionality, security, theming, and cross-domain operation.

**Key Test Categories:**

#### Security & CSRF Tests
- **Widget CSRF Security** (`test_widget_csrf_security.py`) - Comprehensive security validation for cross-domain widget operation

#### Interactive HTML Tests
- **Calc Widget Compatibility** (`test-calc-widget.html`, `test-calc-widget-direct.html`) - JavaScript compatibility and error detection
- **Copy Functionality** (`test_copy_functionality.html`) - Copy-to-clipboard functionality testing

#### Theme System Tests
- **Theme System Core** (`test_theme_system.py`) - Theme model validation, CSS generation, form validation
- **Theme Integration** (`test_theme_integration.py`) - Real-time preview, cross-browser compatibility (requires selenium)
- **Theme Accessibility** (`test_theme_accessibility.py`) - WCAG 2.1 AA compliance, color contrast testing (requires selenium)

#### Finder V2 Widget Tests
- **HTML Snippet Generation** (`test_finder_v2_html_snippet.py`) - Widget installation code display validation

### Finder V2 Specific Tests (`widget/finder_v2/`)

**Purpose:** Comprehensive testing of the finder-v2 widget implementation.

**Test Coverage:**
- **Widget Type Registration** (`test_widget_type.py`) - Widget type system integration
- **API Proxy Functionality** (`test_api_proxy.py`) - API request proxying and parameter handling
- **CSRF Protection** (`test_csrf_protection.py`) - Security validation for API endpoints
- **Admin Interface** (`test_admin_interface.py`) - Widget management interface testing
- **Form Persistence** (`test_form_persistence.py`) - Form state management and persistence
- **Integration Testing** (`test_integration.py`) - End-to-end workflow validation
- **Region Filtering** (`test_region_filtering.py`) - Geographic region parameter handling
- **Search History** (`test_search_history_form_persistence.py`) - Search history functionality
- **Browser Testing** (`test_browser.py`) - Browser-specific functionality validation

## Running Tests

### Quick Start with Pytest

For comprehensive pytest testing, see the dedicated documentation:
- **[pytest-quick-start.md](pytest-quick-start.md)** - Immediate testing setup
- **[pytest-usage-guide.md](pytest-usage-guide.md)** - Comprehensive pytest usage

### Individual Test Files

**Production CSRF Verification:**
```bash
python tests/verify_production_csrf.py
```

**TailwindCSS Class Verification:**
```bash
python tests/test_tailwind_classes_verification.py
```

**Widget CSRF Security Tests:**
```bash
python tests/widget/test_widget_csrf_security.py
```

**Finder V2 HTML Snippet Tests:**
```bash
python tests/widget/test_finder_v2_html_snippet.py
```

**Interactive HTML Tests:**
```bash
# Open in browser for interactive testing
open tests/widget/test-calc-widget.html
open tests/widget/test-calc-widget-direct.html
open tests/widget/test_copy_functionality.html
```

**Finder V2 Test Suite:**
```bash
# Run all finder-v2 tests
python tests/widget/finder_v2/run_tests.py

# Run specific finder-v2 tests
python -m pytest tests/widget/finder_v2/test_integration.py -v
python -m pytest tests/widget/finder_v2/test_api_proxy.py -v
```

**Theme System Tests (requires selenium):**
```bash
# Note: These tests require selenium and additional dependencies
python -m pytest tests/widget/test_theme_system.py -v
python -m pytest tests/widget/test_theme_integration.py -v
python -m pytest tests/widget/test_theme_accessibility.py -v
```

**Expected Output:**
```
🔒 Widget CSRF Security Test Suite
==================================================
Test Results: 10 passed, 0 failed
🎉 ALL TESTS PASSED - Widget CSRF security is working correctly!

Security Model Validated:
✅ Cross-origin widget requests allowed with valid tokens
✅ Invalid/missing tokens blocked
✅ No security bypass through hostname spoofing
```

## Test Naming Conventions

### File Naming
- `test_[component]_[functionality].py` - Main test files
- Use lowercase with underscores
- Descriptive names indicating what is being tested

### Test Function Naming
- `test_[specific_scenario]()` - Individual test functions
- `run_[test_category]_tests()` - Test suite runners
- Clear, descriptive names explaining the test scenario

### Test Class Naming (if used)
- `Test[Component][Functionality]` - Test classes
- PascalCase naming convention
- Group related tests logically

## Test Coverage

### Widget Security Tests (`test_widget_csrf_security.py`)

**Test Scenarios:**
1. ✅ Same-origin requests with valid tokens
2. ✅ Cross-origin widget requests with valid tokens
3. ✅ Different client domains with valid tokens
4. ❌ Same-origin requests with invalid tokens (blocked)
5. ❌ Cross-origin requests with invalid tokens (blocked)
6. ❌ Cross-origin requests without tokens (blocked)
7. ✅ Malicious domains with valid tokens (allowed - widgets work cross-domain)
8. ❌ Malicious domains with invalid tokens (blocked)
9. ❌ Malicious domains without tokens (blocked)
10. 🔍 Port mismatch handling (development environment)

**Security Validation:**
- CSRF token generation and validation
- Cross-domain request handling
- Security blocking of unauthorized requests
- Port mismatch handling in development
- trusted_hostnames vulnerability demonstration

## Adding New Tests

### Creating Widget Tests

1. **Create test file in appropriate subdirectory:**
   ```bash
   touch tests/widget/test_new_functionality.py
   ```

2. **Follow the established pattern:**
   ```python
   #!/usr/bin/env python3
   """
   Test description and purpose
   """

   import sys
   import requests
   # Other imports

   def test_specific_functionality():
       """Test specific functionality with clear description"""
       # Test implementation
       pass

   def run_test_suite():
       """Main test runner"""
       # Run all tests and report results
       pass

   if __name__ == '__main__':
       success = run_test_suite()
       sys.exit(0 if success else 1)
   ```

3. **Include comprehensive documentation:**
   - Clear test descriptions
   - Expected outcomes
   - Error handling
   - Result reporting

### Test Requirements

**All tests should:**
- Be self-contained and runnable independently
- Include clear success/failure reporting
- Handle errors gracefully
- Provide meaningful output for debugging
- Follow the established naming conventions
- Include documentation explaining what is being tested

**Security tests should:**
- Test both positive and negative scenarios
- Validate security boundaries
- Include attack simulation where appropriate
- Clearly document security implications
- Provide evidence of security model validation

## Test Environment

### Prerequisites
- Docker services running (`docker-compose up -d`)
- Application accessible at `http://development.local:8000`
- Database populated with test data

### Environment Variables
Tests use the development environment configuration by default.

### Dependencies

#### Core Dependencies (Available)
- `requests` library for HTTP testing
- `base64` for CSRF token generation
- `BeautifulSoup4` for HTML parsing
- Standard Python libraries
- Django test framework

#### Optional Dependencies (For Advanced Tests)
- `pytest` - For pytest-based test execution
- `selenium` - For browser automation tests (theme system tests)
- `axe-selenium-python` - For accessibility testing
- Chrome/Firefox WebDriver - For browser tests

#### Installing Optional Dependencies
```bash
# For pytest support
pip install pytest pytest-django

# For selenium-based tests
pip install selenium axe-selenium-python

# Install WebDriver (Chrome example)
# Download from https://chromedriver.chromium.org/
```

## Continuous Integration

### Local Testing
Run all tests before committing:
```bash
# Widget security tests
python tests/widget/test_widget_csrf_security.py
```

### Pre-Deployment Testing
Before deploying to production:
1. Run full test suite
2. Verify all security tests pass
3. Test against staging environment
4. Validate production configuration

## Troubleshooting

### Common Issues

**Test Connection Errors:**
- Ensure Docker services are running
- Check that `development.local:8000` is accessible
- Verify database is populated

**CSRF Token Failures:**
- Check User-Agent string matches between test and server
- Verify CSRF algorithm implementation
- Enable debug logging: `WIDGET_CSRF_SETTINGS['debug_csrf_validation'] = True`

**Cross-Domain Test Failures:**
- Verify CSRF settings allow cross-domain requests
- Check that `trusted_hostnames` is not configured (security risk)
- Ensure proper token validation is enabled

### Debug Mode

Enable detailed logging for troubleshooting:
```python
# In settings
WIDGET_CSRF_SETTINGS = {
    'debug_csrf_validation': True,
}
```

View debug output:
```bash
docker logs -f ws_services 2>&1 | grep "WIDGET CSRF DEBUG"
```

## Contributing

### Adding Tests
1. Create test file in appropriate subdirectory
2. Follow naming conventions and patterns
3. Include comprehensive documentation
4. Test both success and failure scenarios
5. Update this README if adding new test categories

### Test Review Checklist
- [ ] Test file follows naming conventions
- [ ] Tests are self-contained and runnable
- [ ] Clear success/failure reporting
- [ ] Comprehensive scenario coverage
- [ ] Documentation explains test purpose
- [ ] Error handling implemented
- [ ] Security implications considered (for security tests)

## Test Directory Summary

This test directory has been comprehensively cleaned and organized as of 2025-01-28. The structure includes:

### ✅ Active Test Files (15 files)
- **4 Root level tests** - Production verification and core functionality
- **5 Widget tests** - Security, HTML functionality, and theme system
- **6 Finder V2 tests** - Comprehensive widget-specific testing

### 📚 Documentation Files (3 files)
- **README.md** - This comprehensive test directory guide
- **pytest-quick-start.md** - Quick pytest setup guide
- **pytest-usage-guide.md** - Detailed pytest usage documentation

### 🧹 Cleanup Completed
- **Removed 9 obsolete files** - Temporary debug scripts, outdated tests, duplicates
- **Removed 3 cache directories** - Python `__pycache__` directories
- **Consolidated pytest documentation** - Moved from `docs/development/` to `tests/`

### 🎯 Test Coverage
- **Security Testing** - CSRF protection, cross-domain validation
- **UI/UX Testing** - Theme system, accessibility, copy functionality
- **Integration Testing** - End-to-end widget workflows
- **API Testing** - Proxy functionality, parameter handling
- **Production Readiness** - Configuration validation

---

**For more information about the project and its architecture, see [docs/README.md](../docs/README.md)**
