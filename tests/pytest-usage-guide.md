# Pytest Usage Guide for wheel-size-services
Last Modified: 2025-01-28 16:45 UTC+6

This guide covers how to use pytest for testing in the wheel-size-services Docker environment.

## Docker Environment Overview

### Container Configuration
- **Container Name**: `ws_services` (Django application)
- **Base Image**: Amazon Linux with Python 3.12.0 via pyenv
- **Working Directory**: `/code`
- **Python Path**: `/root/.pyenv/versions/3.12.0/bin/python`
- **Package Manager**: Poetry (system-wide installation)

### Available Test Runners

#### 1. Django Test Runner (Recommended for Django tests)
```bash
# Run specific test file
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py

# Run all tests in directory
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/

# Run root level tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/verify_production_csrf.py
```

#### 2. Pytest Runner (For pytest-specific features)
```bash
# Run with pytest (requires pytest installation)
docker exec ws_services bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py

# Run with pytest markers
docker exec ws_services bash /code/scripts/test_runner.sh -m "not slow" tests/

# Run with verbose output
docker exec ws_services bash /code/scripts/test_runner.sh -v tests/widget/finder_v2/
```

#### 3. Direct Python Execution (For standalone scripts)
```bash
# Run standalone test scripts directly
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python tests/verify_production_csrf.py
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python tests/test_tailwind_classes_verification.py

# Interactive HTML tests (open in browser)
open tests/widget/test-calc-widget.html
open tests/widget/test-calc-widget-direct.html
open tests/widget/test_copy_functionality.html
```

## Test Environment Setup

### Django Settings Configuration

The project uses multiple Django settings modules for different environments:

```bash
# Development settings (default for Docker)
DJANGO_SETTINGS_MODULE=src.settings.dev_docker

# Test-optimized settings (for pytest)
DJANGO_SETTINGS_MODULE=src.settings.test

# Production settings
DJANGO_SETTINGS_MODULE=src.settings.production
```

### Test Settings Features (`src.settings.test`)
- **Database**: SQLite in-memory for speed (can switch to PostgreSQL for integration tests)
- **Cache**: Local memory cache
- **Logging**: Error-level only during tests
- **Migrations**: Disabled for faster test execution
- **Static Files**: Simplified storage backend
- **CSRF**: Disabled for API testing

### Environment Variables
```bash
# Automatically set by test runners
export DJANGO_SETTINGS_MODULE=src.settings.test  # or dev_docker
export PYTHONPATH=/code:/code/src
export DISABLE_LESS_COMPILATION=true
```

## Test Structure

### Example Test File Structure

```python
"""
Unit tests for Finder-v2 Region Filtering
"""

from django.test import TestCase
from django.http import HttpRequest
from rest_framework.request import Request

from src.apps.widgets.api_proxy.views import FinderV2WidgetProxyView


class RegionParameterNormalisationTest(TestCase):
    """Test region parameter normalization in API proxy."""

    def test_region_array_param_is_normalised_and_overrides_widget_config(self):
        """`region[]` should become `region` and override widget config regions."""
        # Test implementation here
        pass
```

### Key Testing Patterns

#### 1. Django TestCase for Database Tests
```python
from django.test import TestCase
from django.contrib.auth.models import User
from src.apps.widgets.common.models.config import WidgetConfig

class WidgetConfigTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser_unique',  # Use unique names
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_widget_creation(self):
        config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Widget',
            type='finder-v2',
            raw_params={'test': 'data'}
        )
        self.assertEqual(config.type, 'finder-v2')
```

#### 2. SimpleTestCase for Non-Database Tests
```python
from django.test import SimpleTestCase
from django.http import HttpRequest

class ParameterNormalizationTest(SimpleTestCase):
    """Use SimpleTestCase for tests that don't need database."""

    def test_parameter_normalization(self):
        request = HttpRequest()
        request.GET = {'region[]': ['usdm', 'cdm']}
        # Test logic here
```

#### 3. API Testing Pattern
```python
from rest_framework.test import APITestCase
from rest_framework.request import Request
from django.http import HttpRequest

class APIProxyTest(APITestCase):
    def test_api_proxy_parameter_handling(self):
        # Create mock request
        http_request = HttpRequest()
        http_request.GET = {'region[]': ['usdm', 'cdm']}
        drf_request = Request(http_request)

        # Test API proxy logic
        view = FinderV2WidgetProxyView()
        params = view.get_request_params(drf_request, {})

        self.assertIn('region', params)
        self.assertEqual(params['region'], ['usdm', 'cdm'])
```

#### 4. Pytest-Style Tests (Optional)
```python
import pytest
from django.test import RequestFactory
from src.apps.widgets.api_proxy.views import FinderV2WidgetProxyView

@pytest.mark.django_db
def test_widget_config_creation():
    """Pytest-style test with database access."""
    from django.contrib.auth.models import User
    from src.apps.widgets.common.models.config import WidgetConfig

    user = User.objects.create_user(username='testuser', password='pass')
    config = WidgetConfig.objects.create(
        user=user,
        name='Test Widget',
        type='finder-v2'
    )
    assert config.type == 'finder-v2'

@pytest.mark.unit
def test_parameter_processing():
    """Unit test without database."""
    factory = RequestFactory()
    request = factory.get('/?region[]=usdm&region[]=cdm')

    # Test parameter processing logic
    assert 'region[]' in request.GET
    assert request.GET.getlist('region[]') == ['usdm', 'cdm']
```

## Docker Commands Reference

### Container Management
```bash
# Check container status
docker ps --filter name=ws_services

# View container logs
docker logs ws_services --tail 50 -f

# Access container shell
docker exec -it ws_services bash

# Restart container
docker-compose restart web
```

### Test Execution Commands
```bash
# Using container name (recommended)
docker exec ws_services bash /code/scripts/run_tests.sh <test_path>

# Using docker-compose (alternative)
docker-compose exec web bash /code/scripts/run_tests.sh <test_path>

# Direct Python execution (advanced)
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python -m pytest <test_path>
```

### File System Paths in Container
```bash
# Application code
/code/src/

# Test files
/code/tests/

# Python packages
/root/.pyenv/versions/3.12.0/lib/python3.12/site-packages/

# Scripts
/code/scripts/

# Static files
/code/src/static/
```

## Running Specific Tests

### Current Active Test Files

```bash
# Root level tests (standalone scripts - run directly)
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python tests/verify_production_csrf.py
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python tests/test_tailwind_classes_verification.py

# Widget security tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/test_widget_csrf_security.py

# Finder-v2 specific tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_integration.py
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_api_proxy.py

# Theme system tests (requires selenium dependencies)
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/test_theme_system.py

# All tests in a directory
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/
```

### Pytest-Specific Commands

```bash
# Run with pytest markers
docker exec ws_services bash /code/scripts/test_runner.sh -m "unit" tests/

# Run with coverage (if coverage is installed)
docker exec ws_services bash /code/scripts/test_runner.sh --cov=src tests/

# Run specific test methods
docker exec ws_services bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_integration.py::FinderV2IntegrationTest::test_complete_widget_creation_workflow
```

### Expected Output

#### Django Test Runner Output
```
🧪 Running tests using dev_docker settings...
📍 Running: tests/widget/finder_v2/test_region_filtering.py
📍 Settings: src.settings.dev_docker
📍 Python path: /code:/code/src

🔍 Running RegionParameterNormalisationTest...
test_http_request_path ... ok
test_region_array_param_is_normalised_and_overrides_widget_config ... ok

----------------------------------------------------------------------
Ran 2 tests in 0.001s

OK

============================================================
📊 SUMMARY
Tests run: 2
Failures: 0
Errors: 0
Success: True
============================================================
```

#### Pytest Runner Output
```
📍 Running tests with settings: src.settings.test
📍 Python path: /code:/code/src
📍 Working directory: /code

========================= test session starts =========================
platform linux -- Python 3.12.0, pytest-7.4.0, pluggy-1.3.0
django: version 4.2.21, settings: src.settings.test
rootdir: /code
configfile: pytest.ini
plugins: django-4.5.2
collected 2 items

tests/widget/finder_v2/test_region_filtering.py::RegionParameterNormalisationTest::test_region_array_param_is_normalised_and_overrides_widget_config PASSED [50%]
tests/widget/finder_v2/test_region_filtering.py::RegionParameterNormalisationTest::test_http_request_path PASSED [100%]

========================= 2 passed in 0.12s =========================
```

## Writing New Tests

### 1. Create Test File

```bash
# Create new test file
touch tests/widget/finder_v2/test_new_feature.py
```

### 2. Basic Test Template

```python
"""
Unit tests for New Feature
"""

from django.test import SimpleTestCase  # or TestCase for database tests
from src.apps.widgets.finder_v2.some_module import SomeClass


class NewFeatureTest(SimpleTestCase):
    """Test new feature functionality."""
    
    def setUp(self):
        """Set up test data."""
        # Initialize test data here
        pass
    
    def test_basic_functionality(self):
        """Test basic functionality works."""
        # Arrange
        test_input = "test_data"
        
        # Act
        result = SomeClass.process(test_input)
        
        # Assert
        self.assertEqual(result, "expected_output")
    
    def test_edge_case(self):
        """Test edge case handling."""
        # Test edge cases
        pass
```

### 3. Run Your New Test

```bash
# Using Django test runner
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_new_feature.py

# Using pytest runner
docker exec ws_services bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_new_feature.py
```

## Best Practices

### 1. Test Naming
- Use descriptive test method names: `test_region_array_param_is_normalised`
- Use descriptive test class names: `RegionParameterNormalisationTest`

### 2. Test Organization
- Group related tests in the same class
- Use `setUp()` for common test data
- Use `SimpleTestCase` when database not needed

### 3. Assertions
```python
# Use specific assertions
self.assertEqual(actual, expected)
self.assertIn(item, container)
self.assertTrue(condition)
self.assertIsNone(value)
self.assertRaises(Exception, callable)
```

### 4. Database Tests
```python
# For database tests, use unique identifiers
def setUp(self):
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    self.user = User.objects.create_user(
        username=f'testuser_{unique_id}',
        email=f'test_{unique_id}@example.com',
        password='testpass123'
    )
```

## Common Test Patterns

### 1. API Parameter Testing
```python
def test_api_parameter_normalization(self):
    """Test API parameter normalization."""
    # Create request with array parameters
    request = HttpRequest()
    request.GET = {'region[]': ['usdm', 'cdm'], 'make[]': ['bmw']}
    
    # Test normalization
    view = FinderV2WidgetProxyView()
    params = view.get_request_params(Request(request), {})
    
    # Verify normalization
    self.assertIn('region', params)
    self.assertNotIn('region[]', params)
    self.assertEqual(params['region'], ['usdm', 'cdm'])
```

### 2. Widget Configuration Testing
```python
def test_widget_config_override(self):
    """Test widget configuration override behavior."""
    widget_config = {'regions': ['eudm']}
    request_params = {'region': ['usdm', 'cdm']}
    
    # Test that request params override widget config
    final_params = merge_params(widget_config, request_params)
    
    self.assertEqual(final_params['region'], ['usdm', 'cdm'])
```

### 3. Form Validation Testing
```python
def test_form_validation(self):
    """Test form validation logic."""
    form_data = {
        'flow_type': 'primary',
        'regions': ['usdm', 'cdm']
    }
    
    form = FinderV2ConfigForm(data=form_data)
    self.assertTrue(form.is_valid())
```

## Troubleshooting

### Common Issues

#### 1. Container Connection Issues
```bash
# Error: Cannot connect to Docker daemon
# Solution: Ensure Docker is running and container exists
docker ps --filter name=ws_services
docker-compose up -d web

# Error: Container not found
# Solution: Check container name and status
docker ps -a | grep ws_services
```

#### 2. Import Errors
```bash
# Error: ModuleNotFoundError: No module named 'src.apps...'
# Solution: Check import paths match actual file structure
from src.apps.widgets.common.models.config import WidgetConfig  # Correct
from src.apps.widgets.main.models import WidgetConfig  # Incorrect

# Error: Module not found in container
# Solution: Verify PYTHONPATH is set correctly
docker exec ws_services echo $PYTHONPATH
# Should show: /code:/code/src
```

#### 3. Database Errors
```bash
# Error: IntegrityError: duplicate key value violates unique constraint
# Solution: Use unique identifiers in test data
import uuid
username=f'testuser_{uuid.uuid4().hex[:8]}'

# Error: Database connection issues
# Solution: Check if using correct settings module
# For integration tests, use dev_docker settings
# For unit tests, use test settings (SQLite in-memory)
```

#### 4. Settings Issues
```bash
# Error: Django settings not configured
# Solution: Our test runners automatically set DJANGO_SETTINGS_MODULE
# Verify the setting is correct:
docker exec ws_services echo $DJANGO_SETTINGS_MODULE

# Error: Missing dependencies in container
# Solution: Rebuild container after dependency changes
docker-compose build web
docker-compose up -d
```

#### 5. Permission Issues
```bash
# Error: Permission denied accessing files
# Solution: Check file permissions and ownership
docker exec ws_services ls -la /code/tests/

# Error: Cannot write to directory
# Solution: Ensure proper volume mounts
docker exec ws_services ls -la /code/
```

#### 6. Missing Dependencies
```bash
# Error: ModuleNotFoundError: No module named 'bs4'
# Solution: Install missing dependencies
docker exec ws_services pip install beautifulsoup4

# Error: ModuleNotFoundError: No module named 'pytest'
# Solution: Install pytest and related packages
docker exec ws_services pip install pytest pytest-django

# Error: ModuleNotFoundError: No module named 'selenium'
# Solution: Install selenium for browser tests
docker exec ws_services pip install selenium

# Permanent solution: Add to pyproject.toml and rebuild container
# Add to [tool.poetry.group.dev.dependencies] section:
# beautifulsoup4 = "^4.12.0"
# pytest = "^7.4.0"
# pytest-django = "^4.5.2"
# selenium = "^4.15.0"
```

### Debug Tips

1. **Add print statements** for debugging:
```python
def test_debug_example(self):
    result = some_function()
    print(f"Debug: result = {result}")  # Will show in test output
    self.assertEqual(result, expected)
```

2. **Use verbose output** (already enabled in our runners):
```bash
# Django test runner includes verbose output automatically
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py

# Pytest runner with extra verbosity
docker exec ws_services bash /code/scripts/test_runner.sh -vv tests/widget/finder_v2/test_region_filtering.py
```

3. **Check test file structure**:
```bash
# List all test files
docker exec ws_services find /code/tests -name "test_*.py" -type f

# Check specific test directory
docker exec ws_services ls -la /code/tests/widget/finder_v2/

# Verify test file syntax
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python -m py_compile /code/tests/widget/finder_v2/test_region_filtering.py
```

4. **Environment debugging**:
```bash
# Check Python version and path
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python --version
docker exec ws_services which python

# Check Django installation
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python -c "import django; print(django.VERSION)"

# Check available packages
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python -c "import sys; print('\\n'.join(sys.path))"
```

## Integration with Development

### 1. Before Committing Code
```bash
# Run relevant tests for your changes
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/

# Run security tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/test_widget_csrf_security.py

# Run production verification
docker exec ws_services bash /code/scripts/run_tests.sh tests/verify_production_csrf.py
```

### 2. After Bug Fixes
```bash
# Run specific regression tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py

# Run integration tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_integration.py
```

### 3. Before Deployment
```bash
# Run all active tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/

# Run critical security tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/test_widget_csrf_security.py

# Verify TailwindCSS classes
docker exec ws_services bash /code/scripts/run_tests.sh tests/test_tailwind_classes_verification.py
```

### 4. Continuous Integration Setup
```bash
# Example CI script for automated testing
#!/bin/bash
set -e

echo "Starting CI test suite..."

# Ensure container is running
docker-compose up -d web

# Wait for container to be ready
sleep 10

# Run test suite
docker exec ws_services bash /code/scripts/run_tests.sh tests/

echo "CI test suite completed successfully"
```

## Advanced Usage

### 1. Test Markers (Future Enhancement)
```python
# Mark tests with categories
import pytest

@pytest.mark.region_filtering
def test_region_filtering_logic(self):
    pass

@pytest.mark.slow
def test_expensive_operation(self):
    pass
```

### 2. Test Fixtures (Future Enhancement)
```python
# Create reusable test data
@pytest.fixture
def sample_widget_config():
    return {
        'flow_type': 'primary',
        'regions': ['usdm', 'cdm']
    }
```

### 3. Parameterized Tests (Future Enhancement)
```python
# Test multiple scenarios
@pytest.mark.parametrize("input,expected", [
    (['usdm'], ['usdm']),
    (['usdm', 'cdm'], ['usdm', 'cdm']),
    (['eudm'], ['eudm']),
])
def test_region_normalization(self, input, expected):
    result = normalize_regions(input)
    assert result == expected
```

## Files and Directories

### Test Infrastructure
- **`scripts/run_tests.sh`** - Django test runner (uses dev_docker settings)
- **`scripts/test_runner.sh`** - Pytest runner (uses test settings)
- **`src/settings/test.py`** - Test-optimized Django settings
- **`src/settings/dev_docker.py`** - Development Docker settings
- **`pytest.ini`** - Pytest configuration with markers and options
- **`docker-compose.yml`** - Container configuration

### Container Paths
- **Application code**: `/code/src/`
- **Test files**: `/code/tests/`
- **Python executable**: `/root/.pyenv/versions/3.12.0/bin/python`
- **Scripts**: `/code/scripts/`
- **Static files**: `/code/src/static/`

### Active Test Files (Post-Cleanup)
```bash
# Root level tests (2 files)
tests/verify_production_csrf.py                    # Production CSRF verification
tests/test_tailwind_classes_verification.py        # TailwindCSS class verification

# Widget tests (5 files)
tests/widget/test_widget_csrf_security.py          # Widget CSRF security
tests/widget/test_finder_v2_html_snippet.py        # HTML snippet generation
tests/widget/test_theme_system.py                  # Theme system core
tests/widget/test_theme_integration.py             # Theme integration (selenium)
tests/widget/test_theme_accessibility.py           # Theme accessibility (selenium)

# Finder V2 tests (8 files)
tests/widget/finder_v2/test_integration.py         # End-to-end integration
tests/widget/finder_v2/test_api_proxy.py           # API proxy functionality
tests/widget/finder_v2/test_region_filtering.py    # Region parameter handling
tests/widget/finder_v2/test_csrf_protection.py     # CSRF protection
tests/widget/finder_v2/test_admin_interface.py     # Admin interface
tests/widget/finder_v2/test_form_persistence.py    # Form state management
tests/widget/finder_v2/test_browser.py             # Browser-specific tests
tests/widget/finder_v2/test_widget_type.py         # Widget type registration

# Interactive HTML tests (3 files)
tests/widget/test-calc-widget.html                 # Calc widget compatibility
tests/widget/test-calc-widget-direct.html          # Direct calc widget test
tests/widget/test_copy_functionality.html          # Copy functionality test
```

### Documentation Files
- **`tests/README.md`** - Comprehensive test directory documentation
- **`tests/pytest-quick-start.md`** - Quick pytest setup guide
- **`tests/pytest-usage-guide.md`** - This comprehensive usage guide

## Summary

The pytest setup is **ready for daily use** in the wheel-size-services Docker environment! Key points:

### ✅ **Ready-to-Use Commands**
```bash
# Primary test runner (Django-based)
docker exec ws_services bash /code/scripts/run_tests.sh <test_path>

# Pytest runner (for advanced features)
docker exec ws_services bash /code/scripts/test_runner.sh <test_path>
```

### ✅ **Current Environment**
- **Container**: `ws_services` (Amazon Linux + Python 3.12.0)
- **Settings**: `src.settings.dev_docker` (integration) or `src.settings.test` (unit)
- **Database**: PostgreSQL (integration) or SQLite in-memory (unit)
- **Dependencies**: All WS packages and Django 4.2 ready

### ✅ **Required Dependencies**
```bash
# Install missing dependencies in container
docker exec -it ws_services bash
pip install beautifulsoup4 pytest pytest-django selenium

# Or using Poetry
poetry add --dev beautifulsoup4 pytest pytest-django selenium
```

### ✅ **Test Coverage**
- **15 active test files** covering security, widgets, themes, and integration
- **3 documentation files** with comprehensive guides
- **Clean structure** after recent cleanup and reorganization

### ✅ **Best Practices**
- Use descriptive test names and proper assertions
- Use unique identifiers for database tests
- Follow Django TestCase patterns for consistency
- Run tests before commits and deployments

### ✅ **Docker Integration**
- Fully containerized testing environment
- Consistent Python/Django versions with production
- Volume mounts for live code changes
- Proper environment variable configuration

This setup provides a **solid foundation for test-driven development** in the wheel-size-services project with full Docker integration and comprehensive documentation.