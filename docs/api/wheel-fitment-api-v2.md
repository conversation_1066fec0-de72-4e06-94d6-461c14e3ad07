# Wheel Size API v2 Reference

A fully structured reference for every current endpoint, field, and example in the Wheel Size API.

---

## Table of Contents

1. [General Notes](#general-notes)
2. [/makes/](#makes)
3. [/models/](#models)
4. [/years/](#years)
5. [/generations/](#generations)
6. [/modifications/](#modifications)
7. [/search/by\_model/](#searchbymodel)
8. [Rim-Based Search Suite](#rim-based-search-suite)

   * [/by\_rim/rd/](#by_rimrd)
   * [/by\_rim/rw/](#by_rimrw)
   * [/by\_rim/of/](#by_rimof)
   * [/by\_rim/cb/](#by_rimcb)
   * [/by\_rim/bp/](#by_rimbp)
   * [/by\_rim/search/](#by_rimsearch)
   * [/by\_rim/search/modifications/](#by_rimsearchmodifications)
9. [Tire Search Endpoints](#tire-search-endpoints)

   * [/tires/search/advanced/](#tiressearchadvanced)
   * [/tires/catalog/{brand}/{model}/](#tirescatalogbrandmodel)
10. [Region List](#region-list)

---

## General Notes

* **Slug fields** are URL-safe unique identifiers.
* Arrays are shown with square brackets `[]`; objects with curly braces `{}`.
* Image URLs are guaranteed to be transparent PNG (makes/logos) or optimized JPEG/WebP (bodies).
* Power values appear in **kW**, **PS** (metric hp) and **hp** (mechanical hp).

---

## /makes/ <span id="makes"></span>

| Field     | Type     | Description                       | Example                                                                           |
| --------- | -------- | --------------------------------- | --------------------------------------------------------------------------------- |
| `slug`    | *string* | Unique make ID                    | `"acura"`                                                                         |
| `name`    | *string* | Make name (local language)        | `"Acura"`                                                                         |
| `name_en` | *string* | Make name (English)               | `"Acura"`                                                                         |
| `regions` | *array*  | Regions where the make is present | `["cdm","chdm","eudm","medm","mxndm","usdm"]`                                     |
| `logo`    | *string* | 240 × 160 px transparent PNG      | `https://cdn.wheel-size.com/automobile/manufacturer/acura-1622187471.8597765.png` |

---

## /models/ <span id="models"></span>

| Field         | Type     | Description          | Example         |
| ------------- | -------- | -------------------- | --------------- |
| `slug`        | *string* | Unique model ID      | `"corolla"`     |
| `name`        | *string* | Model name (local)   | `"Corolla"`     |
| `name_en`     | *string* | Model name (English) | `"Corolla"`     |
| `regions`     | *array*  | Available regions    | `["eudm"]`      |
| `year_ranges` | *array*  | Production spans     | `["2016-2022"]` |

---

## /years/ <span id="years"></span>

| Field  | Type     | Description | Example  |
| ------ | -------- | ----------- | -------- |
| `slug` | *string* | Year slug   | `"2024"` |
| `name` | *string* | Year label  | `"2024"` |

---

## /generations/ <span id="generations"></span>

| Field         | Type     | Description                  | Example                          |
| ------------- | -------- | ---------------------------- | -------------------------------- |
| `slug`        | *string* | Generation ID                | `"35613dde17"`                   |
| `name`        | *string* | Generation name              | `"IV (GN)"`                      |
| `platform`    | *string* | Platform code (may be empty) | `"CMF-CD"`                       |
| `start`       | *int*    | Start year                   | `2021`                           |
| `end`         | *int*    | End year (or final planned)  | `2026`                           |
| `bodies`      | *array*  | Body objects (see below)     | —                                |
| `regions`     | *array*  | Region list                  | `["audm","cdm", ...]`            |
| `years`       | *array*  | Individual years             | `[2021, 2022, 2023, 2024, 2025]` |
| `year_ranges` | *array*  | Span strings                 | `["2021-2025"]`                  |

**Body object**

```json
{
  "slug": "ea415b5f70",
  "name": "SUV, 5d",
  "image": "https://cdn.wheel-size.com/automobile/body/mitsubishi-outlander-2021-2023-1651194609.7068112.jpg"
}
```

---

## /modifications/ <span id="modifications"></span>

Every modification inherits `make`, `model`, and `generation`.

| Field                     | Type     | Description       | Example                         |
| ------------------------- | -------- | ----------------- | ------------------------------- |
| `slug`                    | *string* | Modification ID   | `"58f170c366"`                  |
| `name`                    | *string* | Trim + body       | `"2.4i (DBA-GF8W)"`             |
| `body`                    | *string* | JDM body code     | `"DBA-GF8W"`                    |
| `trim`                    | *string* | Trim label        | `"2.4i"`                        |
| `trim_attributes`         | *array*  | Attribute flags   | `["AWD","DRW"]`                 |
| `trim_body_types`         | *array*  | Body types        | `["Convertible"]`               |
| `trim_levels`             | *array*  | Levels            | `["Basis","Advanced",...]`      |
| `start_year` / `end_year` | *int*    | Production span   | `2013` / `2015`                 |
| `engine`                  | *object* | See example below | —                               |
| `regions`                 | *array*  | Region list       | `["usdm","cdm","mxndm","chdm"]` |

**Engine example**

```json
{
  "fuel": "Petrol",
  "capacity": 3.0,
  "type": "I4",
  "power": { "kW": 124, "PS": 169, "hp": 166 },
  "code": "CCSA"
}
```

---

## /search/by\_model/ <span id="searchbymodel"></span>

Everything from **/modifications/** **plus** full wheel & tire tech.

### Additional Fields

| Field       | Type     | Description                | Example |
| ----------- | -------- | -------------------------- | ------- |
| `technical` | *object* | Wheel hub data (see below) | —       |
| `tire_type` | *string* | Recommended tire class     | `"SUV"` |
| `wheels`    | *array*  | Stock / optional setups    | —       |

**Technical block**

```json
{
  "wheel_fasteners": { "type": "Lug nuts", "thread_size": "M12 x 1.5" },
  "wheel_tightening_torque": "88 - 108 Nm",
  "stud_holes": 5,
  "pcd": 114.3,
  "centre_bore": "67.1",
  "bolt_pattern": "5x114.3",
  "rear_axis_stud_holes": null,
  "rear_axis_pcd": null,
  "rear_axis_centre_bore": null,
  "rear_axis_bolt_pattern": "N/A"
}
```

**Wheel-set array element (abridged)**

```json
{
  "is_stock": true,
  "showing_fp_only": true,
  "front": {
    "rim": "6.5Jx16 ET38",
    "rim_diameter": 16,
    "rim_width": 6.5,
    "rim_offset": 38,
    "tire_full": "215/70R16 100H",
    "tire_pressure": { "bar": 2.4, "psi": 35, "kPa": 240 }
  },
  "rear": { "rim": "", "tire_full": "" }
}
```

---

## Rim-Based Search Suite <span id="rim-based-search-suite"></span>

### /by\_rim/rd/ <span id="by_rimrd"></span>

| Field   | Type               | Example |
| ------- | ------------------ | ------- |
| `value` | *int* – rim Ø in ″ | `13`    |
| `total` | *int* occurrences  | `53`    |

### /by\_rim/rw/ <span id="by_rimrw"></span>

| `value` | `total` |
| ------- | ------- |
| `6.5`   | `4`     |

### /by\_rim/of/ <span id="by_rimof"></span>

| `value` | `total` |
| ------- | ------- |
| `30`    | `16`    |

### /by\_rim/cb/ <span id="by_rimcb"></span>

| `value` | `total` |
| ------- | ------- |
| `60.1`  | `6`     |

### /by\_rim/bp/ <span id="by_rimbp"></span>

| `value`   | `total` |
| --------- | ------- |
| `"5x115"` | `46`    |

### /by\_rim/search/ <span id="by_rimsearch"></span>

Returns **models** that match rim parameters.

```json
{
  "make": { "slug": "alfa-romeo", "name": "Alfa Romeo", "name_en": "Alfa Romeo" },
  "slug": "147",
  "name": "147",
  "name_en": "147",
  "regions": ["eudm","medm","russia"],
  "year_ranges": ["2000-2010"]
}
```

### /by\_rim/search/modifications/ <span id="by_rimsearchmodifications"></span>

Returns **modifications** for a matching model.

```json
{
  "slug": "a4396f6116",
  "name": "2.0i",
  "model": {
    "make": { "slug": "mitsubishi", "name": "Mitsubishi", "name_en": "Mitsubishi" },
    "slug": "outlander",
    "name": "Outlander",
    "name_en": "Outlander"
  },
  "generation": { "slug": "7949e45600", "name": "I (CU)", "start": 2003, "end": 2006 },
  "start_year": 2003,
  "end_year": 2006,
  "engine": {
    "fuel": "Petrol",
    "capacity": "2.0",
    "type": "I4",
    "power": { "kW": 100, "PS": 136, "hp": 134 },
    "code": "4G63"
  },
  "regions": ["eudm","russia"]
}
```

---

## Tire Search Endpoints <span id="tire-search-endpoints"></span>

### /tires/search/advanced/ <span id="tiressearchadvanced"></span>

| Field                     | Type     | Example                                    |
| ------------------------- | -------- | ------------------------------------------ |
| `slug`                    | *string* | `"victra-sport-5"`                         |
| `display`                 | *string* | `"Victra Sport 5 VS5"`                     |
| `brand.slug`              | *string* | `"maxxis"`                                 |
| `brand.display`           | *string* | `"Maxxis"`                                 |
| `price_segment`           | *string* | `"Mid-Range"`                              |
| `canonical_link`          | *string* | `https://tiresvote.com/...`                |
| `season.display`          | *string* | `"Summer"`                                 |
| `automobile_type.display` | *string* | `"Passenger"`                              |
| `year`                    | *int*    | `2018`                                     |
| `discontinued`            | *bool*   | `false`                                    |
| `is_runflat`              | *bool*   | `false`                                    |
| `thumbnail`               | *string* | `https://ws-tires.s3.amazonaws.com/...jpg` |
| `counters.modes`          | *int*    | `67`                                       |
| `regions[0].slug`         | *string* | `"cdm"`                                    |
| `has_modes`               | *object* | `{"235/45ZR19": ["235/45 R19 99Y XL"]}`    |
| `rating.score`            | *int*    | `74`                                       |
| `rating.tags[0].display`  | *string* | `"Price/Quality"`                          |

### /tires/catalog/{brand}/{model}/ <span id="tirescatalogbrandmodel"></span>

Extends the advanced search object with:

* `performance_category`
* `studded`
* `for_nordic_winter`
* `is_oe_model`
* `manufacturer_page_link`
* `description` (Markdown text)
* `tags` (array of feature keywords)
* `has_bnb_reasons`
* `image`
* `meta.last_update` (`YYYY-MM-DD HH:MM:SS.mmmmmm`)

Example snippet:

```json
{
  "slug": "premium-contact-6",
  "display": "PremiumContact 6",
  "brand": { "slug": "continental", "display": "Continental", "price_segment": "Premium" },
  "performance_category": { "slug": "car-grand-touring-summer", "display": "Premium Touring Summer" },
  "year": 2016,
  "is_oe_model": false,
  "manufacturer_page_link": "https://www.continental"
}
```

---

## /regions/ <span id="region-list"></span>

Returns a list of all available regions. The response is an object with a `data` field containing an array of region objects, and a `meta` object with the total count.

Each object in the `data` array has the following fields:

| Field       | Type                | Description                      | Example                             |
| ----------- | ------------------- | -------------------------------- | ----------------------------------- |
| `slug`      | *string*            | Unique region ID                 | `"usdm"`                            |
| `display`   | *string*            | Region name for display          | `"USA+"`                            |
| `abbr`      | *string*            | Abbreviation for the region      | `"USDM"`                            |
| `countries` | *array of strings*  | List of countries in the region  | `["United States of America", ...]` |

**Example Response**

This is an abridged example showing the first region.

```json
{
  "data": [
    {
      "slug": "usdm",
      "display": "USA+",
      "abbr": "USDM",
      "countries": [
        "Antigua and Barbuda",
        "Anguilla",
        "Barbados",
        "Saint Barthélemy",
        "Bermuda",
        "Bahamas",
        "Cuba",
        "Dominica",
        "Dominican Republic",
        "Grenada",
        "Guadeloupe",
        "Haiti",
        "Jamaica",
        "Saint Kitts and Nevis",
        "Cayman Islands",
        "Saint Lucia",
        "Saint Martin (French part)",
        "Martinique",
        "Montserrat",
        "Puerto Rico",
        "Sint Maarten (Dutch part)",
        "Turks and Caicos Islands",
        "United States of America",
        "Saint Vincent and the Grenadines",
        "Virgin Islands (British)",
        "Virgin Islands (U.S.)"
      ]
    }
  ],
  "meta": {
    "count": 14
  }
}
```
