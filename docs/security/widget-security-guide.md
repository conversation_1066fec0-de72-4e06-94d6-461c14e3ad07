# Widget Security Guide

This guide documents the security model for wheel-size widgets, including CSRF protection, cross-domain considerations, and security testing procedures.

## Overview

Wheel-size widgets are designed to be embedded on client websites via JavaScript, creating unique security challenges:

1. **Cross-Domain Operation**: Widgets must work when embedded on any client domain
2. **API Protection**: Widget API endpoints need protection from unauthorized access
3. **CSRF Prevention**: Must prevent cross-site request forgery attacks
4. **Client Installation**: Widgets are installed via simple JavaScript embed code

## Widget Installation Model

### Client Installation Code
```html
<!-- Client website: https://client-website.com -->
<script src="//services.wheel-size.com/static/widget/code/local/ws-widget.js"></script>
<script>
  var widget = WheelSizeWidgets.create('#ws-widget-676be0', {
    uuid: '676be06961a74399b5f17303c179874c',
    type: 'finder',
    width: '600',
    height: '400'
  });
</script>
```

### Security Flow
1. **Widget JavaScript loads** from wheel-size server
2. **Widget generates CSRF token** based on User-Agent
3. **API calls include token** in `X-Csrf-Token` header
4. **Server validates token** regardless of origin domain
5. **Cross-domain request succeeds** with proper authentication

## CSRF Protection Model

### Traditional CSRF vs Widget CSRF

**Traditional Web CSRF:**
- Same-origin policy enforced
- Referer must match server domain
- Designed for same-domain applications

**Widget CSRF (Our Model):**
- Cross-origin requests allowed
- Security through token validation
- Designed for cross-domain embedding

### Current Implementation

**Server-Side Protection (`WsProtectMixin`):**
```python
def is_allowed(self, request):
    # 1. Check referer exists
    referer = request.META.get('HTTP_REFERER')
    if not referer:
        return False

    # 2. Allow cross-origin requests (widgets work cross-domain)
    # Hostname validation passes for any domain

    # 3. Validate CSRF token (main security)
    token = self.get_token(request)
    client_token = request.META.get('HTTP_X_CSRF_TOKEN')
    return client_token == token
```

**Client-Side Token Generation:**
```javascript
// Generate token from User-Agent
var token = btoa(window.navigator.userAgent);
token = token.split('').slice(0, 32).map(function(value, i) {
    return token[27 + 11 - (7 + i * 11) % 39];
}).join('');

// Include in API requests
headers['X-Csrf-Token'] = token;
```

## Security Configuration

### Safe Configuration (Current)
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': True,  # Handle development ports
    'debug_csrf_validation': True,  # Enable debug logging
    # 'trusted_hostnames': [],  # NOT USED - Security risk
}
```

### ⚠️ Dangerous Configuration (Avoid)
```python
WIDGET_CSRF_SETTINGS = {
    'trusted_hostnames': ['development.local'],  # 🚨 SECURITY BYPASS
}
```

**Why `trusted_hostnames` is dangerous:**
- Any domain can spoof `Referer: development.local` header
- CSRF protection completely bypassed for spoofed requests
- No token validation required
- Creates vulnerability for all client widget installations

## Security Threats and Mitigations

### Threat 1: Cross-Site Request Forgery

**Attack Scenario:**
```html
<!-- Malicious site: https://evil.com -->
<form action="http://services.wheel-size.com/widget/finder/api/mk" method="POST">
  <input type="hidden" name="malicious" value="data">
</form>
<script>document.forms[0].submit();</script>
```

**Mitigation:**
- CSRF token validation required for all requests
- Token generated from User-Agent (difficult to predict)
- No token = request blocked

### Threat 2: Referer Spoofing

**Attack Scenario:**
```bash
curl "http://services.wheel-size.com/widget/finder/api/mk" \
  -H "Referer: http://development.local/fake-page/" \
  # Attacker spoofs trusted referer
```

**Mitigation:**
- No reliance on referer for security decisions
- `trusted_hostnames` removed from configuration
- Security through token validation only

### Threat 3: Token Prediction

**Attack Scenario:**
- Attacker tries to predict CSRF tokens
- Uses common User-Agent strings
- Attempts brute force attacks

**Mitigation:**
- Complex token generation algorithm
- User-Agent based tokens (vary by client)
- Rate limiting on API endpoints

## Testing Security

### Automated Security Tests

Run comprehensive security validation:
```bash
python tests/widget/test_widget_csrf_security.py
```

**Test Coverage:**
- ✅ Legitimate cross-domain widget requests
- ❌ Requests with invalid tokens (blocked)
- ❌ Requests without tokens (blocked)
- 🔍 Port mismatch handling
- 🚨 trusted_hostnames vulnerability demonstration

### Manual Security Testing

**Test 1: Valid Cross-Domain Request**
```bash
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: https://client-website.com/page/" \
  -H "X-Csrf-Token: gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
# Expected: JSON response with data
```

**Test 2: Invalid Token Attack**
```bash
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: https://client-website.com/page/" \
  -H "X-Csrf-Token: invalid_token" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
# Expected: 404 error page (blocked)
```

**Test 3: Missing Token Attack**
```bash
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: https://client-website.com/page/" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
# Expected: 404 error page (blocked)
```

## Production Security Checklist

### Environment Configuration

| Environment | ignore_port | trusted_hostnames | debug_csrf |
|-------------|-------------|-------------------|------------|
| **Development** | `True` | `[]` (empty) | `True` |
| **Staging** | `False` | `[]` (empty) | `False` |
| **Production** | `False` | `[]` (empty) | `False` |

### Pre-Deployment Validation

- [ ] **No trusted_hostnames configured** (security risk)
- [ ] **CSRF token validation working** for all API endpoints
- [ ] **Cross-domain requests allowed** with valid tokens
- [ ] **Invalid tokens blocked** consistently
- [ ] **Debug logging disabled** in production
- [ ] **Rate limiting enabled** on API endpoints
- [ ] **Security tests passing** (run `tests/widget/test_widget_csrf_security.py`)

### Monitoring and Alerting

**Security Metrics to Monitor:**
- High rate of 404 responses (potential attacks)
- Unusual User-Agent patterns
- Requests without referer headers
- Geographic anomalies in API usage

**Alert Conditions:**
- 404 rate > 10% of total requests
- Requests from unexpected geographic regions
- Sudden spikes in API usage
- Repeated requests with invalid tokens

## Security Best Practices

### For Developers

1. **Never use trusted_hostnames** in production
2. **Always validate CSRF tokens** for API endpoints
3. **Test cross-domain functionality** during development
4. **Monitor security metrics** in production
5. **Keep token algorithm secure** (don't expose in client code)

### For Operations

1. **Regular security testing** with automated test suite
2. **Monitor API access patterns** for anomalies
3. **Keep rate limiting configured** appropriately
4. **Review security logs** regularly
5. **Update security documentation** when changes are made

## Incident Response

### If Security Breach Detected

1. **Immediate Actions:**
   - Block suspicious IP addresses
   - Increase rate limiting temporarily
   - Enable debug logging to trace attacks

2. **Investigation:**
   - Review access logs for attack patterns
   - Check if CSRF protection was bypassed
   - Identify affected client widgets

3. **Remediation:**
   - Fix any configuration vulnerabilities
   - Update security measures
   - Notify affected clients if necessary

4. **Prevention:**
   - Update security tests to cover new attack vectors
   - Review and update security documentation
   - Implement additional monitoring if needed

---

**This security guide ensures that widget installations remain secure while maintaining cross-domain functionality for legitimate client use cases.**
