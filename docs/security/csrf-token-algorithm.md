# CSRF Token Algorithm Documentation

**⚠️ CONFIDENTIAL - INTERNAL DEVELOPMENT USE ONLY**

This document contains sensitive security implementation details for the finder widget CSRF token generation algorithm. Do not share outside the development team.

## Overview

The finder widget uses a custom CSRF token generation algorithm that must match exactly between client-side JavaScript and server-side Python implementations.

## Algorithm Details

### Client-Side Implementation (JavaScript)
**File**: `app-config-token.js`

### Server-Side Implementation (Python)
**File**: `src/apps/widgets/api_proxy/views.py` - `WsProtectMixin.get_token()`

### Algorithm Steps

1. **Input**: User-Agent string from browser
2. **Base64 Encoding**: Encode the User-Agent string
3. **Substring Extraction**: Take first 39 characters
4. **String Reversal**: Reverse the 39-character substring
5. **String Repetition**: Repeat the reversed string 9 times
6. **Character Extraction**: Extract every 11th character starting from position 7
7. **Token Assembly**: Join the extracted characters to form the final token

### Mathematical Formula
```
token = (base64(userAgent)[:39][::-1] * 9)[7::11]
```

### Implementation Requirements

#### Critical Matching Points
- Both implementations must use identical User-Agent string
- Base64 encoding must be consistent
- Character extraction pattern must be exact: `[7::11]`
- String repetition count must be exactly 9

#### Security Considerations
- Algorithm relies on User-Agent uniqueness
- Token changes with different browsers/versions
- No cryptographic randomness - relies on obscurity
- Vulnerable if algorithm is reverse-engineered

### Testing Verification

#### Test Cases
```javascript
// Test User-Agent: "Mozilla/5.0 (compatible; test)"
// Expected base64: "TW96aWxsYS81LjAgKGNvbXBhdGlibGU7IHRlc3Qp"
// First 39 chars: "TW96aWxsYS81LjAgKGNvbXBhdGlibGU7IHRlc3"
// Reversed: "3sZXQgO2VsYml0YXBtb2MgKDAuNS9hbGxpem9X"
// Repeated 9x: "3sZXQgO2VsYml0YXBtb2MgKDAuNS9hbGxpem9X..." (351 chars)
// Extract [7::11]: positions 7, 18, 29, 40, 51, 62, 73, 84, 95...
```

#### Validation Commands
```bash
# Test server-side generation
python manage.py shell -c "
from src.apps.widgets.api_proxy.views import WsProtectMixin
import base64
from unittest.mock import Mock

request = Mock()
request.META = {'HTTP_USER_AGENT': 'Mozilla/5.0 (compatible; test)'}
mixin = WsProtectMixin()
token = mixin.get_token(request)
print(f'Server token: {token}')
"

# Test client-side generation (in browser console)
# userAgent = 'Mozilla/5.0 (compatible; test)'
# token = (btoa(userAgent).substring(0, 39).split('').reverse().join('').repeat(9))
# finalToken = []
# for (var j = 7; j < token.length; j += 11) { finalToken.push(token[j]); }
# console.log('Client token:', finalToken.join(''))
```

### Maintenance Notes

#### When to Update Algorithm
- If security vulnerabilities are discovered
- If User-Agent spoofing becomes common
- If algorithm is reverse-engineered by attackers

#### Deployment Considerations
- Both client and server must be updated simultaneously
- JavaScript bundles must be rebuilt after changes
- Cache invalidation required for widget JavaScript

#### Security Improvements (Future)
- Consider adding cryptographic randomness
- Implement time-based token expiration
- Add additional entropy sources beyond User-Agent
- Consider using standard CSRF token approaches

### Related Files

- `src/apps/widgets/finder/app/js/app/app-config-token.js` - Client-side implementation
- `src/apps/widgets/api_proxy/views.py` - Server-side implementation
- `../widget-finder-api-analysis.md` - High-level documentation
- `src/apps/widgets/finder/app/Gruntfile.js` - Build configuration for JavaScript compilation
- `widget-api-testing-guide.md` - Comprehensive testing procedures

### Testing Commands

**Quick CSRF Token Generation:**
```bash
python3 -c "
import base64
user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
token = base64.b64encode(user_agent.encode('utf-8')).decode('utf-8')
token_slice = token[:32]
result = []
for i in range(len(token_slice)):
    index = (27 + 11 - (7 + i * 11) % 39) % len(token)
    result.append(token[index])
print('CSRF Token:', ''.join(result))
"
```

**Test API with Generated Token:**
```bash
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: http://development.local:8000/widget/finder/try/" \
  -H "X-Csrf-Token: gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" | head -c 200
```

## Investigation Findings (December 2024)

### ✅ **Root Cause of Widget Failures After Django Upgrade**

The widget API failures after Django 4.2 upgrade were **NOT caused by algorithm issues**. Investigation revealed:

**Real Issue: Port Mismatch in CSRF Hostname Validation**
- **Browser Referer**: `http://development.local/` (no port)
- **Server Host**: `development.local:8000` (with port)
- **Result**: CSRF validation failed due to hostname mismatch

**Evidence:**
```
🔍 WIDGET CSRF DEBUG: Hostname validation: referer='development.local' request='development.local:8000' ignore_port=False
🔍 WIDGET CSRF DEBUG: Hostname validation failed: 'development.local' not allowed for 'development.local:8000'
```

**Solution:**
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': True,  # Key fix for development
}
```

### ❌ **What Was NOT the Issue**

1. **Algorithm Differences**: GitHub master and minified versions use identical algorithms
2. **Build Process**: Gruntfile.js configuration was not the cause
3. **File Versions**: Source and minified files are functionally equivalent
4. **JavaScript Errors**: No syntax issues in source files

### 🎯 **Key Lesson**

**Occam's Razor applies to debugging.** The simplest explanation (port mismatch) was correct, while complex theories about algorithm differences were red herrings.

### Security Warning

**This algorithm provides security through obscurity, not cryptographic strength. It should be considered a temporary solution until a more robust CSRF protection mechanism can be implemented.**

---

**Last Updated**: Current implementation
**Review Required**: When security requirements change
**Access Level**: Development team only
