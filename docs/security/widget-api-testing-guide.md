# Widget API Testing Guide

This guide provides comprehensive instructions for testing widget API endpoints with proper CSRF token validation across different environments.

## Overview

The widget API uses a custom CSRF protection system that requires:
1. **Proper Referer header** (same-origin policy)
2. **Valid CSRF token** generated from User-Agent
3. **Correct User-Agent header** (must match token generation)

## CSRF Token Generation

### Algorithm
The CSRF token is generated using a legacy algorithm that must match between client and server:

```python
import base64

def generate_csrf_token(user_agent):
    """Generate CSRF token for given User-Agent"""
    token = base64.b64encode(user_agent.encode('utf-8')).decode('utf-8')
    token_slice = token[:32]  # First 32 characters

    result = []
    for i in range(len(token_slice)):
        # Legacy algorithm: token[27 + 11 - (7 + i * 11) % 39]
        index = (27 + 11 - (7 + i * 11) % 39) % len(token)
        result.append(token[index])

    return ''.join(result)
```

### Quick Token Generation

**For Development Testing:**
```bash
python3 -c "
import base64
user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
token = base64.b64encode(user_agent.encode('utf-8')).decode('utf-8')
token_slice = token[:32]
result = []
for i in range(len(token_slice)):
    index = (27 + 11 - (7 + i * 11) % 39) % len(token)
    result.append(token[index])
print('CSRF Token:', ''.join(result))
print('User-Agent:', user_agent)
"
```

**For Production Testing:**
```bash
python3 -c "
import base64
user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
token = base64.b64encode(user_agent.encode('utf-8')).decode('utf-8')
token_slice = token[:32]
result = []
for i in range(len(token_slice)):
    index = (27 + 11 - (7 + i * 11) % 39) % len(token)
    result.append(token[index])
print('CSRF Token:', ''.join(result))
print('User-Agent:', user_agent)
"
```

## Environment-Specific Testing

### Development Environment

**Base URL:** `http://development.local:8000`

**Test All Finder API Endpoints:**
```bash
# Generate token for development
CSRF_TOKEN="gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9"
USER_AGENT="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
BASE_URL="http://development.local:8000"
REFERER="http://development.local:8000/widget/finder/try/"

# Test car makes
curl -s "${BASE_URL}/widget/finder/api/mk" \
  -H "Referer: ${REFERER}" \
  -H "X-Csrf-Token: ${CSRF_TOKEN}" \
  -H "User-Agent: ${USER_AGENT}" | head -c 200

# Test tire widths
curl -s "${BASE_URL}/widget/finder/api/tw" \
  -H "Referer: ${REFERER}" \
  -H "X-Csrf-Token: ${CSRF_TOKEN}" \
  -H "User-Agent: ${USER_AGENT}" | head -c 200

# Test rim diameters
curl -s "${BASE_URL}/widget/finder/api/rd" \
  -H "Referer: ${REFERER}" \
  -H "X-Csrf-Token: ${CSRF_TOKEN}" \
  -H "User-Agent: ${USER_AGENT}" | head -c 200

# Test models (requires make parameter)
curl -s "${BASE_URL}/widget/finder/api/ml?make=bmw" \
  -H "Referer: ${REFERER}" \
  -H "X-Csrf-Token: ${CSRF_TOKEN}" \
  -H "User-Agent: ${USER_AGENT}" | head -c 200

# Test years (requires make and model parameters)
curl -s "${BASE_URL}/widget/finder/api/yr?make=bmw&model=3-series" \
  -H "Referer: ${REFERER}" \
  -H "X-Csrf-Token: ${CSRF_TOKEN}" \
  -H "User-Agent: ${USER_AGENT}" | head -c 200
```

### Production Environment

**Base URL:** `https://services.wheel-size.com`

**Test Production API Endpoints:**
```bash
# Generate token for production
CSRF_TOKEN="gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9"
USER_AGENT="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
BASE_URL="https://services.wheel-size.com"
REFERER="https://services.wheel-size.com/widget/finder/try/"

# Test car makes
curl -s "${BASE_URL}/widget/finder/api/mk" \
  -H "Referer: ${REFERER}" \
  -H "X-Csrf-Token: ${CSRF_TOKEN}" \
  -H "User-Agent: ${USER_AGENT}" | head -c 200

# Test tire widths
curl -s "${BASE_URL}/widget/finder/api/tw" \
  -H "Referer: ${REFERER}" \
  -H "X-Csrf-Token: ${CSRF_TOKEN}" \
  -H "User-Agent: ${USER_AGENT}" | head -c 200

# Test rim diameters
curl -s "${BASE_URL}/widget/finder/api/rd" \
  -H "Referer: ${REFERER}" \
  -H "X-Csrf-Token: ${CSRF_TOKEN}" \
  -H "User-Agent: ${USER_AGENT}" | head -c 200
```

## Security Testing

### Test CSRF Protection (Should Return 404)

**Missing CSRF Token:**
```bash
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: http://development.local:8000/widget/finder/try/"
# Expected: 404 Not Found
```

**Invalid CSRF Token:**
```bash
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: http://development.local:8000/widget/finder/try/" \
  -H "X-Csrf-Token: invalid_token_here" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
# Expected: 404 Not Found
```

**Wrong Referer Domain:**
```bash
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: http://evil.com/" \
  -H "X-Csrf-Token: gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
# Expected: 404 Not Found
```

**Missing Referer:**
```bash
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "X-Csrf-Token: gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
# Expected: 404 Not Found
```

## Expected Responses

### Successful API Response
```json
[
  {
    "slug": "acura",
    "name": "Acura",
    "name_en": "Acura"
  },
  {
    "slug": "aion",
    "name": "Aion",
    "name_en": "Aion"
  }
]
```

### Failed Security Check
```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <title>Page not found at /widget/finder/api/mk</title>
```

## Complete API Endpoint Reference

### Finder Widget Endpoints

| Endpoint | Description | Parameters |
|----------|-------------|------------|
| `/widget/finder/api/mk` | Car makes | None |
| `/widget/finder/api/ml` | Car models | `make` (required) |
| `/widget/finder/api/yr` | Car years | `make`, `model` (required) |
| `/widget/finder/api/tw` | Tire widths | None |
| `/widget/finder/api/ar` | Aspect ratios | `tire_width` (optional) |
| `/widget/finder/api/rd` | Rim diameters | `tire_width`, `aspect_ratio` (optional) |
| `/widget/finder/api/rw` | Rim widths | None |
| `/widget/finder/api/bp` | Bolt patterns | None |
| `/widget/finder/api/sm` | Search by model | `make`, `model`, `year` |
| `/widget/finder/api/st` | Search by tire | `tire_width`, `aspect_ratio`, `rim_diameter` |
| `/widget/finder/api/sr` | Search by rim | `rim_diameter`, `rim_width`, `bolt_pattern` |

## Troubleshooting

### Common Issues

1. **404 Errors**: Usually CSRF validation failure
   - Check CSRF token generation
   - Verify User-Agent matches token generation
   - Ensure correct Referer header
   - **Most Common**: Port mismatch in hostname validation

2. **Empty Responses**: API might be working but no data
   - Check if parameters are required
   - Verify API endpoint is correct

3. **CORS Issues**: Browser-specific
   - Use curl for testing instead of browser fetch
   - Check if widget is loaded in iframe

### Root Cause Analysis (December 2024)

**The Primary Issue After Django Upgrade: Port Mismatch**

After extensive investigation, the main cause of widget API failures was identified as **port mismatch in CSRF hostname validation**:

**Problem:**
- Browser Referer: `http://development.local/` (no port)
- Server Host: `development.local:8000` (with port)
- Result: CSRF validation failed

**Solution:**
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': True,  # Key fix for development
}
```

**What Was NOT the Issue:**
- ❌ Algorithm differences between files
- ❌ Build process or Gruntfile.js configuration
- ❌ JavaScript syntax errors
- ❌ File version mismatches

### 🚨 **Critical Security Discovery: trusted_hostnames Vulnerability**

During investigation, a critical security vulnerability was discovered:

**The Problem:**
```python
WIDGET_CSRF_SETTINGS = {
    'trusted_hostnames': ['development.local'],  # ⚠️ SECURITY BYPASS
}
```

**Security Risk:**
- Any domain could spoof `Referer: development.local` header
- CSRF protection would be completely bypassed
- Widgets on client websites would be vulnerable to attacks

**The Solution:**
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': True,  # Port handling only
    # 'trusted_hostnames': [],  # REMOVED: Security risk
}
```

**New Security Model:**
- Cross-origin requests allowed (widgets work on client domains)
- CSRF token validation enforced for all requests
- No hostname bypass (maintains security)

### Debug Mode

Enable CSRF debug logging in development:
```python
# In settings/dev_docker.py
WIDGET_CSRF_SETTINGS = {
    'debug_csrf_validation': True,
    # ... other settings
}
```

Check logs for debug output:
```bash
docker logs -f ws_services 2>&1 | grep "WIDGET CSRF DEBUG"
```

## Security Testing

### Comprehensive Security Test Suite

A dedicated security test script validates the widget CSRF protection:

**Run Security Tests:**
```bash
python tests/widget/test_widget_csrf_security.py
```

**Test Coverage:**
- ✅ Same-origin requests with valid tokens
- ✅ Cross-origin widget requests with valid tokens
- ❌ Requests with invalid/missing tokens (should be blocked)
- ❌ Malicious domain attacks (should be blocked)
- 🔍 Port mismatch handling
- 🚨 trusted_hostnames vulnerability demonstration

**Expected Results:**
```
🔒 Widget CSRF Security Test Suite
==================================================
Test Results: 10 passed, 0 failed
🎉 ALL TESTS PASSED - Widget CSRF security is working correctly!

Security Model Validated:
✅ Cross-origin widget requests allowed with valid tokens
✅ Invalid/missing tokens blocked
✅ No security bypass through hostname spoofing
```

### Manual Security Testing

**Test Cross-Domain Widget Functionality:**
```bash
# Simulate widget request from client website
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: https://client-website.com/page-with-widget/" \
  -H "X-Csrf-Token: gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
# Expected: JSON response with car makes
```

**Test Security Blocking:**
```bash
# Test invalid token (should be blocked)
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: https://client-website.com/page-with-widget/" \
  -H "X-Csrf-Token: invalid_token" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
# Expected: 404 HTML error page

# Test missing token (should be blocked)
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: https://client-website.com/page-with-widget/" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
# Expected: 404 HTML error page
```

---

**This testing guide provides comprehensive instructions for validating widget API functionality and security across all environments.**
