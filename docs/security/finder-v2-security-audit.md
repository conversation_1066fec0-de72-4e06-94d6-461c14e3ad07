# Finder-v2 Widget Security Audit & Compliance

## Overview

This document outlines the security measures implemented in the finder-v2 widget to ensure safe deployment and operation in production environments.

## Security Features

### 1. CSRF Protection

**Implementation:**
- Uses <PERSON>jan<PERSON>'s CSRF token system with legacy algorithm compatibility
- CSRF token generated server-side and passed to Vue 3 app via template
- Axios interceptors automatically include CSRF token in all API requests
- Port-aware hostname validation for development environments

**Configuration:**
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': True,  # Development only
    'debug_csrf_validation': True,          # Development only
}
```

**Security Level:** ✅ **High** - Prevents cross-site request forgery attacks

### 2. Domain Validation

**Implementation:**
- Same-origin iframe requests always allowed (widget portal previews)
- Cross-domain embedding controlled by authorized domains list
- Wildcard domain support (*.example.com)
- Domain blacklist via WS_DOMAINS_BLACK_LIST live setting

**Security Level:** ✅ **High** - Prevents unauthorized widget embedding

### 3. API Authentication

**Implementation:**
- All API requests use X-WS-API-SECRET-TOKEN header authentication
- API proxy routes through secure backend (no direct client API access)
- Rate limiting via WidgetProxyDayThrottle and WidgetProxyMinuteThrottle
- Request throttling prevents API abuse

**Security Level:** ✅ **High** - Protects API endpoints from unauthorized access

### 4. Feature Flag Control

**Implementation:**
- FINDER_V2_ENABLED live setting controls widget availability
- Feature flag checked in:
  - API proxy view (returns 404 if disabled)
  - Admin interface (blocks widget creation)
  - Iframe view (prevents widget loading)

**Security Level:** ✅ **Medium** - Allows emergency disable of widget functionality

### 5. Input Validation & Sanitization

**Implementation:**
- Vue 3 components use v-model with proper data binding
- API parameters validated on backend before proxy
- HeadlessUI components provide XSS protection
- No direct HTML injection in widget templates

**Security Level:** ✅ **High** - Prevents XSS and injection attacks

### 6. Content Security Policy (CSP)

**Implementation:**
- Widget operates within iframe sandbox
- No external script loading (all assets bundled)
- Vue 3 app uses safe template compilation
- TailwindCSS styles are pre-compiled (no runtime CSS injection)

**Security Level:** ✅ **High** - Prevents code injection and data exfiltration

## Production Deployment Security Checklist

### Pre-Deployment

- [ ] **CSRF Settings:** Remove debug flags from production settings
- [ ] **Feature Flag:** Ensure the finder-v2 widget is enabled.
- [ ] **Domain Validation:** Configure authorized domains for each widget
- [ ] **API Authentication:** Verify X-WS-API-SECRET-TOKEN is configured
- [ ] **Rate Limiting:** Confirm throttling settings are appropriate
- [ ] **Dependencies:** Ensure `vue` and `tailwindcss` are properly installed and configured in `package.json`.
- [ ] **Static Files:** Verify that the `finder_v2` static files are correctly collected by Django's `collectstatic`.
- [ ] **CSRF Protection:** Confirm that the widget's API requests include the necessary CSRF tokens.

### Post-Deployment

- [ ] **Widget Embedding:** Test cross-domain embedding functionality
- [ ] **CSRF Protection:** Verify CSRF tokens are working correctly
- [ ] **API Proxy:** Test all v2 API endpoints through proxy
- [ ] **Error Handling:** Confirm proper error responses for unauthorized access
- [ ] **Performance:** Monitor API response times and throttling effectiveness

## Security Monitoring

### Metrics to Monitor

1. **Failed CSRF Validations:** Indicates potential attack attempts
2. **Blocked Domain Requests:** Shows unauthorized embedding attempts
3. **API Rate Limit Hits:** Monitors for abuse patterns
4. **Feature Flag Toggles:** Tracks emergency disables

### Alert Thresholds

- CSRF failures > 100/hour per widget
- Blocked domains > 50/hour per widget
- Rate limit hits > 1000/hour per widget

## Compliance Standards

### GDPR Compliance
- ✅ No personal data collection in widget
- ✅ No cookies or local storage usage
- ✅ No user tracking or analytics

### Security Standards
- ✅ OWASP Top 10 protection implemented
- ✅ CSP headers configured
- ✅ XSS prevention measures in place
- ✅ CSRF protection enabled

## Incident Response

### Security Incident Procedures

1. **Immediate Response:**
   - Set FINDER_V2_ENABLED=False to disable all finder-v2 widgets
   - Monitor logs for attack patterns
   - Document incident details

2. **Investigation:**
   - Analyze failed requests and error logs
   - Check for unauthorized domain access attempts
   - Review API usage patterns

3. **Resolution:**
   - Apply security patches if needed
   - Update domain blacklists
   - Re-enable widgets after verification

## Security Contact

For security issues related to finder-v2 widget:
- **Internal:** Development team via issue tracker
- **External:** <EMAIL>

---

**Last Updated:** December 2024  
**Next Review:** March 2025  
**Document Version:** 1.0
