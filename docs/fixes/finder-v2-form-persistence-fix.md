# Finder-v2 Form Persistence Fix

## Issue Description

The finder-v2 widget configuration page had a form persistence issue where:

1. When selecting "Alternative Flow (Make → Model → Generation → Modifications)" from the flow_type dropdown and saving the configuration, the save operation appeared successful
2. However, after saving, the dropdown still showed "Primary Flow (Year → Make → Model → Modifications)" as selected in the HTML
3. When attempting to save the configuration again without changes, the flow_type would incorrectly reset to "primary" instead of maintaining the saved "alternative" value

## Root Cause

The issue was in the `FinderV2InterfaceForm.decompose_to_initial()` method in `src/apps/widgets/finder_v2/forms.py`. 

**Problem**: The form was correctly saving the `flow_type` and `button_to_ws` values in the `compose_to_save()` method, but was not loading these saved values back into the form's initial data in the `decompose_to_initial()` method.

**Effect**: The form would always display the default values instead of the actual saved configuration, leading to:
- Incorrect visual state in the form
- Subsequent saves reverting to default values

## Technical Details

### Before the Fix

The `decompose_to_initial()` method only loaded:
- `width` from `self.instance['dimensions']['width']`
- `output_template` from `self.instance['display']['output_template']`

But was missing:
- `flow_type` from `self.instance['flow_type']`
- `button_to_ws` from `self.instance['blocks']['button_to_ws']['hide']` (inverted)

### After the Fix

The `decompose_to_initial()` method now correctly loads all form fields:

```python
def decompose_to_initial(self):
    # ... existing width and template logic ...

    # Get flow_type from saved configuration
    try:
        flow_type_val = self.instance['flow_type']
    except (KeyError, TypeError):
        flow_type_val = 'primary'  # Default flow type

    # Get button_to_ws from saved configuration
    # Note: The stored value is 'hide' but the form field is the inverse (show)
    try:
        button_hidden = self.instance['blocks']['button_to_ws']['hide']
        button_to_ws_val = not button_hidden  # Form field is inverse of stored value
    except (KeyError, TypeError):
        button_to_ws_val = False  # Default to not showing the button

    initial = {
        'width': width_val,
        'output_template': template_val,
        'flow_type': flow_type_val,           # ← FIXED: Now loaded
        'button_to_ws': button_to_ws_val,     # ← FIXED: Now loaded
        # no height field
    }

    return initial
```

## Files Modified

- `src/apps/widgets/finder_v2/forms.py` - Enhanced `FinderV2InterfaceForm.decompose_to_initial()` method

## Testing the Fix

### Manual Testing Steps

1. Navigate to the problematic widget configuration page:
   ```
   http://development.local/widget/788889ba96ed4dcfa8f54a2f0e54f83a/config-demo/
   ```

2. **Test Initial Load**:
   - If the widget previously had "alternative" flow_type saved, the dropdown should now show "Alternative Flow" as selected
   - If it was "primary", it should show "Primary Flow"
   - If it was "year_select", it should show "Year Selection Flow"

3. **Test Save and Reload**:
   - Change the flow_type to "Alternative Flow (Make → Model → Generation → Modifications)"
   - Click "Update Demo Configuration"
   - The page should reload and the dropdown should still show "Alternative Flow" selected

4. **Test Persistence**:
   - Without changing anything, click "Update Demo Configuration" again
   - The flow_type should remain "alternative" (not revert to "primary")
   - The dropdown should continue showing "Alternative Flow" selected

5. **Test Other Flow Types**:
   - Test the same save/reload/persistence behavior with:
     - "Primary Flow (Year → Make → Model → Modifications)"
     - "Year Selection Flow (Make → Model → Years → Modifications)"

### Expected Behavior (After Fix)

- ✅ Form displays the correct saved flow_type value in the dropdown
- ✅ Saving a flow_type keeps it selected after page reload
- ✅ Subsequent saves maintain the selected flow_type
- ✅ All three flow types work correctly
- ✅ The button_to_ws field also persists correctly

### Verification

The fix ensures that:

1. **Form Loading**: `decompose_to_initial()` correctly loads saved values
2. **Form Display**: The HTML dropdown shows the correct selected option
3. **Form Saving**: `compose_to_save()` continues to work correctly (was already working)
4. **Form Persistence**: Subsequent loads maintain the saved state

## Impact

This fix resolves the form persistence issue for all finder-v2 widgets and ensures that:
- Widget configurations are displayed accurately
- User changes are preserved correctly
- The configuration interface behaves as expected

The fix is backward compatible and does not affect existing widget functionality or API behavior.
