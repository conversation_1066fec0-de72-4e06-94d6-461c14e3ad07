# Widget Iframe Authorization Fix

## Issue Description

After the Django 4.2 upgrade, widget configuration iframe previews were displaying the error message:
> "This site is not authorized for widget embedding"

This affected widget configuration pages at URLs like:
`http://development.local:8000/widget/676be06961a74399b5f17303c179874c/config/`

## Root Cause Analysis

The issue was caused by a **port mismatch in hostname validation** within the `WidgetView.check_permissions()` method:

### The Problem Flow

1. **Widget Configuration Page**: User visits `http://development.local:8000/widget/{uuid}/config/`
2. **Iframe Preview**: Page loads iframe with `src="http://development.local:8000/widget/{uuid}/?config"`
3. **Referer Header**: Iframe request has referer `http://development.local:8000/widget/{uuid}/config/`
4. **Hostname Extraction**:
   - `referer.hostname` = `development.local` (no port)
   - `request.META['HTTP_HOST']` = `development.local:8000` (with port)
5. **Same-Origin Check Fails**: `development.local` != `development.local:8000`
6. **Domain Validation**: Falls through to check widget's configured domains (`['*.luxoft.com']`)
7. **Authorization Error**: `development.local` doesn't match `*.luxoft.com`

### Why This Wasn't an Issue Before

The `WsProtectMixin` (used for API proxy views) already had port-ignoring logic implemented during the Django 4.2 upgrade, but the `WidgetView.check_permissions()` method was not updated with the same logic.

## Solution Implemented

### 1. Added Port-Aware Hostname Comparison

Updated `src/apps/widgets/main/views/iframe.py` to implement the same port-ignoring logic as `WsProtectMixin`:

```python
def _is_same_origin_request(self, referer_hostname):
    """
    Check if the request is from the same origin, with port-aware hostname comparison.

    This method implements the same port-ignoring logic as WsProtectMixin to handle
    development environments where referer might be 'development.local' but request
    host is 'development.local:8000'.
    """
    from django.conf import settings

    # Get CSRF settings for port handling configuration
    csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
    ignore_port = csrf_settings.get('ignore_port_in_hostname_check', False)

    # Get request hostname
    request_host = self.request.META.get('HTTP_HOST', '')

    # Extract request hostname (with or without port based on settings)
    if ignore_port and ':' in request_host:
        request_hostname = request_host.split(':')[0]
    else:
        request_hostname = request_host

    # Check direct hostname match
    if referer_hostname == request_hostname:
        return True

    # If port ignoring is enabled, also check without port
    if ignore_port and ':' in request_host:
        base_request_hostname = request_host.split(':')[0]
        if referer_hostname == base_request_hostname:
            return True

    return False
```

### 2. Added Debug Logging

Implemented debug logging to help troubleshoot iframe permission issues:

```python
def _debug_log(self, message):
    """Log debug messages for widget iframe permission checking if debug mode is enabled."""
    from django.conf import settings

    csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
    if csrf_settings.get('debug_csrf_validation', False):
        print(f"🔍 WIDGET IFRAME DEBUG: {message}")
```

### 3. Updated Permission Check Logic

Modified `check_permissions()` to use the new same-origin check method:

```python
def check_permissions(self, config):
    # ... existing code ...

    referer = urlparse(referer)

    # Same-origin check with port-aware hostname comparison
    if self._is_same_origin_request(referer.hostname):
        return True

    # ... rest of permission checks ...
```

## Configuration

The fix uses the existing `WIDGET_CSRF_SETTINGS` configuration:

### Development Environment (`dev_docker.py`)
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': True,  # Enable port ignoring for Docker development
    'debug_csrf_validation': True,  # Enable debug logging
}
```

### Production Environment (`aws_prod.py`)
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': False,  # Strict hostname checking in production
    'debug_csrf_validation': False,  # No debug logging in production
}
```

## Testing

Created comprehensive unit tests in `tests/test_widget_iframe_permissions.py` to verify all widget portal security scenarios:

### ✅ **Same-Origin Widget Portal Previews**
1. Same-origin requests work with port ignoring enabled (development)
2. Same-origin requests fail with port ignoring disabled (production)
3. Exact hostname matches work regardless of port settings

### ✅ **Cross-Domain Security Validation**
4. Cross-origin requests work for authorized domains (`*.luxoft.com`)
5. Cross-origin requests are blocked for unauthorized domains (`evil.com`)
6. Wildcard domain matching works correctly (`client.luxoft.com` matches `*.luxoft.com`)
7. Subdomain spoofing is prevented (`fake-luxoft.com` doesn't match `*.luxoft.com`)

### ✅ **Production Security Enforcement**
8. Production environment enforces strict hostname checking
9. Port-ignoring logic only affects same-origin checks, not cross-domain validation
10. Debug logging works when enabled and is silent when disabled

### ✅ **Real-World Verification**
Tested with actual widget configuration `676be06961a74399b5f17303c179874c`:
- **Widget Portal Preview**: ✅ Same-origin iframe preview allowed
- **Authorized Domain**: ✅ `client.luxoft.com` would be allowed (matches `*.luxoft.com`)
- **Unauthorized Domain**: ✅ `evil.com` would be blocked (security working)

## Verification

### Debug Logs Show Success
```
🔍 WIDGET IFRAME DEBUG: Same-origin check: referer='development.local' request='development.local' ignore_port=True
🔍 WIDGET IFRAME DEBUG: Same-origin request allowed
```

### Widget Configuration Pages Work
- ✅ Iframe preview displays correctly at `http://development.local:8000/widget/{uuid}/config/`
- ✅ Direct iframe access works at `http://development.local:8000/widget/{uuid}/`
- ✅ API calls continue to work with existing CSRF protection

## Impact

- ✅ **Fixed**: Widget configuration iframe previews now work correctly in development
- ✅ **Maintained**: Same security model as API proxy views
- ✅ **Preserved**: Production security with strict hostname checking
- ✅ **Enhanced**: Debug logging for iframe permission troubleshooting
- ✅ **Consistent**: Unified port-handling logic across widget system

## Related Files

- `src/apps/widgets/main/views/iframe.py` - Main fix implementation
- `src/apps/widgets/api_proxy/views.py` - Reference implementation (WsProtectMixin)
- `src/settings/dev_docker.py` - Development configuration
- `src/settings/aws_prod.py` - Production configuration
- `tests/test_widget_iframe_permissions.py` - Unit tests
- `docs/upgrade/production-deployment-guide.md` - Updated documentation
