# 🚀 PRODUCTION DEPLOYMENT GUIDE - Django 4.2 Upgrade

## 📋 **OVERVIEW**

This guide provides comprehensive instructions for deploying the Django 4.2 upgrade to production for the wheel-size-services project. The upgrade includes:

- **Django**: 2.2.25 → 4.2.21 LTS
- **Python**: 3.8+ → 3.9+
- **WS Packages**: v1.x → v2.0.0 (5 packages)
- **Admin Interface**: django-suit → django-admin-interface v0.30.0
- **reCAPTCHA**: nocaptcha_recaptcha → django-recaptcha v4.1.0
- **✅ CALC WIDGET**: LESS compilation issues resolved - all 24 demo pages working
- **✅ WS-DJANGO-BETTERFORMS**: v2.0.0 fork published to private PyPI - proper package management

**⚠️ CRITICAL**: This deployment affects **41,878 production users** and **5,568 widget configurations**. Follow this guide exactly to ensure zero data loss.

---

## 🔍 **PRE-DEPLOYMENT CHECKLIST**

### **1. Environment Verification**
- [ ] **Production database backup** completed and verified
- [ ] **Development environment** fully tested with production data copy
- [ ] **All admin pages** working in development (especially `/admin/widgets_demo/demopage/`)
- [ ] **WS packages v2.0.0** published to private PyPI (pypi.wheel-size.com)
- [ ] **Server resources** adequate (Python 3.9+, sufficient memory)
- [ ] **Maintenance window** scheduled and communicated
- [ ] **✅ NEW: Widget CSRF protection** tested and verified with `verify_production_csrf.py`

### **2. Code Verification**
- [ ] **Git repository** up to date with all Django 4.2 changes
- [ ] **pyproject.toml** updated with correct dependency versions
- [ ] **Django settings** configured for production
- [ ] **Static files** compilation tested
- [ ] **All tests** passing in development environment
- [ ] **✅ NEW: WIDGET_CSRF_SETTINGS** configured for production environment
- [ ] **✅ NEW: Widget API endpoints** tested with proper CSRF token validation

### **3. Infrastructure Readiness**
- [ ] **Python 3.9+** installed on production servers
- [ ] **PostgreSQL** version compatibility verified
- [ ] **Redis/Memcached** (if used) compatibility checked
- [ ] **Web server** (nginx/Apache) configuration reviewed
- [ ] **SSL certificates** valid and up to date

---

## 💾 **DATABASE MIGRATION STRATEGY**

### **Critical Database Backup**
```bash
# 1. Create full database backup
pg_dump -h production_host -U production_user -d production_db > backup_pre_django42_$(date +%Y%m%d_%H%M%S).sql

# 2. Verify backup integrity
pg_restore --list backup_pre_django42_*.sql | head -20

# 3. Test restore on staging environment
createdb staging_test_db
psql staging_test_db < backup_pre_django42_*.sql
```

### **Django Migration Commands**
```bash
# 1. Check for migration conflicts (run in maintenance mode)
python manage.py showmigrations --plan

# 2. Run migrations with verbose output
python manage.py migrate --verbosity=2

# 3. Verify migration status
python manage.py showmigrations

# 4. Check for any migration issues
python manage.py check --deploy
```

### **Expected Migration Behavior**
- **No destructive migrations** - Django 4.2 upgrade is backward compatible
- **New Django internal migrations** may run (auth, contenttypes, sessions)
- **Custom app migrations** should be minimal
- **WS package migrations** already applied in v2.0.0

---

## 📦 **DEPENDENCY UPDATES**

### **1. WS Packages Update (v1.x → v2.0.0)**
```bash
# Update pyproject.toml to use PyPI versions instead of local paths
[tool.poetry.dependencies]
# Replace local paths with PyPI versions
ws-django-helpers = "^2.0.0"
ws-django-fields = "^2.0.0"
ws-django-live-settings = "^2.0.0"
ws-django-rest-framework-proxy = "^2.0.0"
ws-django-tire-calc = "^2.0.0"

# Install updated dependencies
poetry install --only=main
```

### **2. Critical Package Updates**
```bash
# Key package version updates in pyproject.toml
django = "^4.2.21"
django-admin-interface = "^0.30.0"  # Critical for Django 4.2 compatibility
django-recaptcha = "^4.1.0"
django-registration-redux = "^2.13"
```

### **3. Python Requirements**
```bash
# Verify Python version
python --version  # Must be 3.9+

# Update system packages if needed
pip install --upgrade pip setuptools wheel
```

---

## ⚡ **DEPLOYMENT SEQUENCE**

### **Phase 1: Preparation (5 minutes)**
```bash
# 1. Enable maintenance mode
python manage.py maintenance_mode on

# 2. Stop application servers
sudo systemctl stop gunicorn
sudo systemctl stop celery  # if using Celery

# 3. Final database backup
pg_dump production_db > final_backup_$(date +%Y%m%d_%H%M%S).sql
```

### **Phase 2: Code Deployment (10 minutes)**
```bash
# 1. Pull latest code
git fetch origin
git checkout production-django-4.2  # or your production branch
git pull origin production-django-4.2

# 2. Update dependencies
poetry install --only=main --no-dev

# 3. Verify WS packages installation
python -c "import ws_fields, ws_django_helpers, ws_live_settings, ws_calc; print('✅ All WS packages loaded')"

# 4. Verify finder-v2 Vue.js build configuration for production
# Ensure `drop_console: true` in `src/apps/widgets/finder_v2/app/vite.config.js` 
# to prevent debug logs in production.
# ( cat src/apps/widgets/finder_v2/app/vite.config.js | grep 'drop_console' )
```

### **Phase 3: Database Migration (5 minutes)**
```bash
# 1. Run Django system checks
python manage.py check --deploy

# 2. Check current migration status
python manage.py showmigrations

# Expected unapplied migrations after Django 4.2 upgrade:
# auth
#  [ ] 0012_alter_user_first_name_max_length
# portal
#  [ ] 0002_auto_20250526_0101
# ws_live_settings
#  [ ] 0002_alter_wslivesettings_json_value

# 3. Apply migrations
python manage.py migrate --verbosity=2

# Expected migration output:
# Running migrations:
#   Applying auth.0012_alter_user_first_name_max_length... OK
#   Applying portal.0002_auto_20250526_0101... OK
#   Applying ws_live_settings.0002_alter_wslivesettings_json_value... OK

# 4. Verify migration success
python manage.py showmigrations | grep -E '\[ \]'  # Should show no unapplied migrations
```

### **Migration Details**
- **auth.0012_alter_user_first_name_max_length**: Django 3.1+ migration expanding User.first_name from 30 to 150 characters (safe, non-destructive)
- **portal.0002_auto_20250526_0101**: Empty migration for Django 4.2 compatibility (no schema changes)
- **ws_live_settings.0002_alter_wslivesettings_json_value**: Updates JSON field for ws-django-live-settings v2.0.0 compatibility (safe, preserves data)

### **Phase 4: Static Files & Services (5 minutes)**
```bash
# 1. Collect static files
python manage.py collectstatic --noinput

# 1a. Special step for finder-v2 widget (Vue 3 based):
# Ensure finder-v2 static assets are built and copied before running collectstatic.
# This is typically done during the code deployment phase or as a pre-collectstatic step.
# NOTE: For production, ensure `drop_console: true` is set in 
# `src/apps/widgets/finder_v2/app/vite.config.js` before building.
# Example commands (run from project root, adjust paths as necessary):
docker exec ws_services bash -c "cd src/apps/widgets/finder_v2/app && npm run build"
cp -R src/apps/widgets/finder_v2/app/dist/* src/apps/widgets/finder_v2/static/finder_v2/

# 2. Start application servers
sudo systemctl start gunicorn
sudo systemctl start celery  # if using Celery

# 3. Verify services are running
sudo systemctl status gunicorn
sudo systemctl status celery
```

### **Phase 5: Verification & Go-Live (5 minutes)**
```bash
# 1. Test critical endpoints
curl -I https://your-domain.com/admin/
curl -I https://your-domain.com/

# 2. Disable maintenance mode
python manage.py maintenance_mode off

# 3. Monitor logs
tail -f /var/log/gunicorn/error.log
tail -f /var/log/nginx/error.log
```

**Total Estimated Downtime: 30 minutes**

---

## 🔄 **ROLLBACK PROCEDURES**

### **Emergency Rollback (if issues occur)**
```bash
# 1. Enable maintenance mode immediately
python manage.py maintenance_mode on

# 2. Stop services
sudo systemctl stop gunicorn
sudo systemctl stop celery

# 3. Restore previous code version
git checkout previous-production-tag
poetry install --only=main

# 4. Restore database (if migrations caused issues)
dropdb production_db
createdb production_db
psql production_db < final_backup_*.sql

# 5. Restart services
sudo systemctl start gunicorn
sudo systemctl start celery
python manage.py maintenance_mode off
```

### **Partial Rollback (specific issues)**
```bash
# If only WS packages are problematic, downgrade them
poetry add ws-django-helpers@^1.0.2
poetry add ws-django-fields@^1.1.1
poetry add ws-django-live-settings@^1.0.9
poetry add ws-django-rest-framework-proxy@^0.2.0
poetry add ws-django-tire-calc@^0.4.7

# Restart services
sudo systemctl restart gunicorn
```

---

## ✅ **POST-DEPLOYMENT VERIFICATION**

### **1. Critical Functionality Tests**
```bash
# Test admin interface (the main issue we fixed)
curl -s https://your-domain.com/admin/widgets_demo/demopage/ | grep -q "200 OK"
curl -s https://your-domain.com/admin/widgets_main/widgetconfig/ | grep -q "200 OK"
curl -s https://your-domain.com/admin/portal/userprofile/ | grep -q "200 OK"

# Test main site functionality
curl -s https://your-domain.com/ | grep -q "200 OK"

# ✅ NEW: Test calc widget functionality (restored in this upgrade)
curl -I https://your-domain.com/widget/demo/calc/default-calc/ | grep -q "200 OK"
curl -I https://your-domain.com/widget/demo/calc/ | grep -q "200 OK"
curl -I https://your-domain.com/widget/demo/calc/color-dark/ | grep -q "200 OK"

# ✅ NEW: Test widget configuration pages (django-betterforms fix)
curl -I https://your-domain.com/widget/finder/try/ | grep -q "200 OK"
curl -I https://your-domain.com/widget/calc/try/ | grep -q "200 OK"

# ✅ CRITICAL: Test widget API CSRF protection (new security system)
curl -H "Referer: https://your-domain.com/widget/finder/try/" \
     -H "X-Csrf-Token: test" -H "User-Agent: ProductionTest/1.0" \
     https://your-domain.com/widget/finder/api/mk | grep -q "slug"

# ✅ CRITICAL: Verify CSRF security blocks unauthorized requests
if curl -s -H "Referer: https://evil.com/" \
   https://your-domain.com/widget/finder/api/mk | grep -q "slug"; then
    echo "❌ SECURITY ALERT: CSRF protection not working!"
    exit 1
else
    echo "✅ CSRF protection working correctly"
fi
```

### **2. Database Integrity Verification**
```bash
# Verify user count (should be 41,878)
python manage.py shell -c "from django.contrib.auth.models import User; print(f'Users: {User.objects.count()}')"

# Verify widget configurations (should be 5,568)
python manage.py shell -c "from src.apps.widgets.common.models import WidgetConfig; print(f'Configs: {WidgetConfig.objects.count()}')"

# Test AlwaysHexUUIDField functionality and verify demo page coverage
python manage.py shell -c "
from src.apps.widgets.demo.models import DemoPage
total = DemoPage.objects.count()
finder = DemoPage.objects.filter(type='finder').count()
calc = DemoPage.objects.filter(type='calc').count()
print(f'DemoPages Total: {total} (Expected: 24)')
print(f'Finder Pages: {finder} (Expected: 17)')
print(f'Calc Pages: {calc} (Expected: 7)')
print(f'✅ Demo Coverage: {total}/24 = {(total/24)*100:.1f}%')
"
```

### **3. Performance Monitoring**
```bash
# Monitor response times
curl -w "@curl-format.txt" -s -o /dev/null https://your-domain.com/admin/

# Check memory usage
free -h
ps aux | grep gunicorn | head -5

# Monitor error logs for 30 minutes
tail -f /var/log/gunicorn/error.log | grep -E "(ERROR|CRITICAL)"
```

---

## ⚠️ **POTENTIAL ISSUES AND SOLUTIONS**

### **1. AlwaysHexUUIDField Error (RESOLVED)**
**Issue**: `TypeError: AlwaysHexUUIDField.from_db_value() missing 1 required positional argument: 'context'`

**Root Cause**: django-admin-interface v0.28.8 incompatibility

**Solution**: ✅ **Already fixed** - django-admin-interface updated to v0.30.0

### **2. Import Errors for WS Packages**
**Issue**: `ModuleNotFoundError: No module named 'ws_fields'`

**Solution**:
```bash
# Verify PyPI access
poetry config repositories.ws https://pypi.wheel-size.com/
poetry config http-basic.ws repo-user Ojc3ZSPwIBEEisX

# Reinstall packages
poetry install --only=main
```

### **3. Static Files Issues**
**Issue**: CSS/JS files not loading correctly

**Solution**:
```bash
# Clear static files and regenerate
rm -rf /path/to/static/collected/
python manage.py collectstatic --noinput --clear
```

### **4. Database Connection Issues**
**Issue**: Database connection errors after upgrade

**Solution**:
```bash
# Check database settings
python manage.py dbshell  # Should connect successfully

# Verify database encoding
psql -c "SHOW server_encoding;" production_db
```

### **5. Memory Issues**
**Issue**: Increased memory usage with Django 4.2

**Solution**:
```bash
# Monitor and adjust gunicorn workers
# In gunicorn.conf.py:
workers = 2  # Reduce if memory constrained
max_requests = 1000
max_requests_jitter = 100
```

---

## 📞 **EMERGENCY CONTACTS & RESOURCES**

### **Key Personnel**
- **Lead Developer**: [Contact Info]
- **DevOps Engineer**: [Contact Info]
- **Database Administrator**: [Contact Info]

### **Critical Resources**
- **Backup Location**: `/backups/django-upgrade/`
- **Log Files**: `/var/log/gunicorn/`, `/var/log/nginx/`
- **Monitoring**: [Your monitoring dashboard URL]
- **Private PyPI**: https://pypi.wheel-size.com/

### **Rollback Decision Matrix**
| Issue Severity | Action | Timeline |
|---------------|--------|----------|
| **Critical** (site down) | Immediate rollback | < 5 minutes |
| **High** (admin broken) | Investigate 15 min, then rollback | < 20 minutes |
| **Medium** (performance) | Monitor and fix post-deployment | Next business day |
| **Low** (cosmetic) | Schedule fix | Next release cycle |

---

---

## 🎯 **WIDGET SYSTEM FIXES (CRITICAL)**

### **Widget JavaScript Functionality Fixes**
The following critical widget system issues have been resolved in this upgrade:

#### **1. JavaScript Dependencies & RequireJS**
**Issue**: `Uncaught ReferenceError: require is not defined at apply_widget (widget.js:105:24)`

**Solution**: Added RequireJS to widget templates
```html
<!-- Added to src/templates/widgets/demo/base.html -->
<!-- Added to src/templates/widgets/common/config/page.html -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js"></script>
```

#### **2. LESS Compilation Modernization**
**Issue**: `Less has finished and no sheets were loaded` - Modern LESS.js incompatibility

**Solution**: Updated LESS configuration for modern compatibility
```javascript
// Updated in widget templates
less = {
    env: 'development',
    async: false,
    fileAsync: false,
    poll: 1000,
    functions: {},
    modifyVars: {...}
};

// Changed type attribute from 'text/ws-less' to 'text/less'
<style type="text/less" data-name="widget-original">
```

#### **3. X-Frame-Options Configuration**
**Issue**: `Refused to display in a frame because it set 'X-Frame-Options' to 'deny'`

**Solution**: Updated development settings
```python
# src/settings/dev_docker.py
X_FRAME_OPTIONS = 'SAMEORIGIN'  # Allows widget iframe embedding
```

#### **4. Widget URL Resolution**
**Issue**: Hardcoded production URLs in development environment

**Solution**: Dynamic URL detection in widget JavaScript
```javascript
// Updated src/apps/widgets/embed/static/widget/code/local/ws-widget.js
iframeUrl: (function() {
    var host = window.location.host;
    if (host.indexOf('development.local') !== -1 || host.indexOf('localhost') !== -1) {
        return '//' + host + '/widget/__uuid__/';
    }
    return '//services.ws.com/widget/__uuid__/';
})(),
```

### **Widget Authorization System Fixes**

#### **Issue 1: Empty Domain Permission Lists**
**Issue**: "This site is not authorized for widget embedding inside the widget"

**Root Cause**
Default widget configurations had empty domain permission lists, preventing any domain from embedding widgets.

#### **Solution Applied**
```python
# Updated src/apps/widgets/finder/default_config/config.py
# Updated src/apps/widgets/calc/default_config/config.py
"permissions": {
    "domains": [
        "localhost",
        "development.local",
        "127.0.0.1",
        "*.localhost",
        "*.development.local"
    ]
}
```

#### **Issue 2: Widget Iframe Preview Authorization Errors**
**Issue**: Widget configuration iframe preview shows "This site is not authorized for widget embedding" after Django 4.2 upgrade

**Root Cause**
The `WidgetView.check_permissions()` method was performing same-origin checks without the port-ignoring logic that was implemented in `WsProtectMixin` for API proxy views. This caused iframe previews to fail in development environments where:
- Referer hostname: `development.local` (from config page URL)
- Request hostname: `development.local:8000` (with port)
- Result: Same-origin check failed, falling through to domain validation

**Solution Applied**
```python
# Updated src/apps/widgets/main/views/iframe.py
def _is_same_origin_request(self, referer_hostname):
    """Port-aware hostname comparison for iframe permission checking"""
    from django.conf import settings

    csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
    ignore_port = csrf_settings.get('ignore_port_in_hostname_check', False)

    request_host = self.request.META.get('HTTP_HOST', '')

    if ignore_port and ':' in request_host:
        request_hostname = request_host.split(':')[0]
    else:
        request_hostname = request_host

    return referer_hostname == request_hostname
```

**Impact**
- ✅ **Fixed**: Widget configuration iframe previews now work correctly in development
- ✅ **Maintained**: Same security model as API proxy views
- ✅ **Preserved**: Production security with strict hostname checking
- ✅ **Enhanced**: Debug logging for iframe permission troubleshooting
```

#### **Database Configuration Reload**
```python
# Required after updating default configurations
from src.apps.widgets.common.config_loader import DefaultConfigLoader
from src.apps.widgets.finder.widget_type import FinderWidgetType
from src.apps.widgets.calc.widget_type import CalcWidgetType

finder_loader = DefaultConfigLoader(widget_type=FinderWidgetType)
finder_loader.load_config()

calc_loader = DefaultConfigLoader(widget_type=CalcWidgetType)
calc_loader.load_config()
```

#### **Language Activation Fix**
**Issue**: `AttributeError: 'WidgetConfig' object has no attribute 'language'`

**Solution**: Temporary fix for Django 4.2 compatibility
```python
# Updated src/apps/widgets/main/views/iframe.py
# Temporarily use config.lang directly due to Django 4.2 upgrade
translation.activate(self.config.lang)
```

### **Production Deployment Considerations**

#### **Widget System Verification Commands**
```bash
# 1. Test widget authorization
curl -I -H "Referer: https://your-domain.com/widget/finder/try/" \
     https://your-domain.com/widget/finder/

# 2. Verify widget demo pages
curl -I https://your-domain.com/widget/finder/try/
curl -I https://your-domain.com/widget/calc/try/

# 3. Check widget JavaScript loading
curl -I https://your-domain.com/static/widget/code/local/ws-widget.js

# 4. Verify default widget configurations
python manage.py shell -c "
from src.apps.widgets.common.models import WidgetConfig
finder_config = WidgetConfig.get_default('finder')
calc_config = WidgetConfig.get_default('calc')
print('Finder domains:', finder_config.params['permissions']['domains'])
print('Calc domains:', calc_config.params['permissions']['domains'])
"
```

#### **Widget System Health Check**
```bash
#!/bin/bash
# widget_health_check.sh - Add to monitoring

# Test widget iframe loading
if ! curl -s -f -H "Referer: https://your-domain.com/widget/finder/try/" \
     https://your-domain.com/widget/finder/ > /dev/null; then
    echo "ALERT: Widget iframe authorization failed"
    exit 1
fi

# Test widget demo pages
if ! curl -s -f https://your-domain.com/widget/finder/try/ | grep -q "WheelSizeWidgets"; then
    echo "ALERT: Widget JavaScript not loading"
    exit 1
fi

# ✅ NEW: Test widget API endpoints (CSRF protection)
if ! curl -s -f -H "Referer: https://your-domain.com/widget/finder/try/" \
     -H "X-Csrf-Token: test" -H "User-Agent: HealthCheck/1.0" \
     https://your-domain.com/widget/finder/api/mk > /dev/null; then
    echo "ALERT: Widget API CSRF protection failed"
    exit 1
fi

echo "✅ Widget system health check passed"
```

---

## 🛡️ **WIDGET CSRF PROTECTION SYSTEM (CRITICAL UPDATE)**

### **New Settings-Based CSRF Hostname Validation**
A robust, environment-specific CSRF protection system has been implemented to resolve hostname validation issues across development, staging, and production environments.

#### **Root Issue Resolved**
**Problem**: CSRF validation was failing due to hostname comparison mismatch:
- **Referer hostname**: `development.local` (without port)
- **Request hostname**: `development.local:8000` (with port)

**Solution**: Implemented flexible, settings-based hostname validation that works correctly in all environments while maintaining security.

#### **Configuration Structure**
```python
# Base configuration (src/settings/base.py)
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': get_env_bool('WIDGET_CSRF_IGNORE_PORT', default=False),
    'trusted_hostnames': get_env_list('WIDGET_CSRF_TRUSTED_HOSTS', default=[]),
    'debug_csrf_validation': get_env_bool('WIDGET_CSRF_DEBUG', default=False),
}
```

#### **Environment-Specific Overrides**

**Production Settings** (`src/settings/aws_prod.py`):
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': False,  # Strict hostname checking
    'trusted_hostnames': ['services.wheel-size.com'],  # Only production domain
    'debug_csrf_validation': False,  # No debug logging for security
}
```

**Development Settings** (`src/settings/dev.py`):
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': True,  # Allow port differences
    'trusted_hostnames': ['development.local', 'localhost', '127.0.0.1'],
    'debug_csrf_validation': True,  # Enable debug logging
}
```

**Staging Settings** (`src/settings/aws_stg.py`):
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': False,  # Strict hostname checking
    'trusted_hostnames': ['services2.wheel-size.com'],  # Only staging domain
    'debug_csrf_validation': False,  # No debug logging
}
```

#### **Security Verification Matrix**

| Environment | Hostname Check | Trusted Domains | Port Handling | Debug Logs |
|-------------|---------------|-----------------|---------------|------------|
| **Production** | Strict | `services.wheel-size.com` only | Exact match required | Disabled |
| **Staging** | Strict | `services2.wheel-size.com` only | Exact match required | Disabled |
| **Development** | Flexible | Multiple dev domains | Ignores port differences | Enabled |

#### **Production Security Validation**
```bash
# Run the comprehensive CSRF security test
python3 verify_production_csrf.py

# Expected output:
# 🔍 Production CSRF Hostname Validation Test
# ✅ PASS Production widget iframe
# ✅ PASS Different subdomain attack (blocked)
# ✅ PASS Different domain attack (blocked)
# 🎉 All tests passed! Production configuration is secure.
# 🚀 Ready for production deployment!
```

#### **Widget API Endpoint Testing**
```bash
# Test finder widget API with proper CSRF token
curl -H "Referer: https://services.wheel-size.com/widget/finder/try/" \
     -H "X-Csrf-Token: [generated_token]" \
     -H "User-Agent: Mozilla/5.0" \
     https://services.wheel-size.com/widget/finder/api/mk

# IMPORTANT FOR FINDER-V2 WIDGET:
# The finder-v2 widget (Vue 3 based) requires the CSRF header as X-CSRF-TOKEN (all uppercase).
# Ensure frontend JavaScript for finder-v2 sends this header name.
# Browser cache may need to be cleared (Cmd+Shift+R or Ctrl+Shift+R) after frontend updates.
# Example for testing finder-v2 API (replace [generated_token] with a valid one):
curl -H "Referer: https://services.wheel-size.com/widget/finder-v2/try/" \
     -H "X-CSRF-TOKEN: [generated_token]" \
     -H "User-Agent: Mozilla/5.0" \
     https://services.wheel-size.com/widget/finder-v2/api/yr

# Expected: JSON response with car makes data
# Blocked scenarios (should return 404):
# - Different referer domain
# - Missing CSRF token
# - Invalid CSRF token
```

#### **Deployment Verification Commands**
```bash
# 1. Verify CSRF settings are loaded correctly
python manage.py shell -c "
from django.conf import settings
csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
print('CSRF Settings:', csrf_settings)
print('✅ Production ready:', not csrf_settings.get('debug_csrf_validation', True))
"

# 2. Generate CSRF token for testing
python3 -c "
import base64
user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
token = base64.b64encode(user_agent.encode('utf-8')).decode('utf-8')
token_slice = token[:32]
result = []
for i in range(len(token_slice)):
    index = (27 + 11 - (7 + i * 11) % 39) % len(token)
    result.append(token[index])
print('Production CSRF Token:', ''.join(result))
print('User-Agent:', user_agent)
"

# 3. Test widget API endpoints with proper CSRF tokens
curl -s "https://services.wheel-size.com/widget/finder/api/mk" \
     -H "Referer: https://services.wheel-size.com/widget/finder/try/" \
     -H "X-Csrf-Token: gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9" \
     -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" | head -c 200

# 4. Test other finder API endpoints
curl -s "https://services.wheel-size.com/widget/finder/api/tw" \
     -H "Referer: https://services.wheel-size.com/widget/finder/try/" \
     -H "X-Csrf-Token: gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9" \
     -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" | head -c 200

curl -s "https://services.wheel-size.com/widget/finder/api/rd" \
     -H "Referer: https://services.wheel-size.com/widget/finder/try/" \
     -H "X-Csrf-Token: gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9" \
     -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" | head -c 200

# Expected: JSON data with array of objects containing 'slug', 'name', and 'name_en' fields

# 5. Verify security blocks malicious requests (should return 404)
curl -s "https://services.wheel-size.com/widget/finder/api/mk" \
     -H "Referer: https://evil.com/attack/"

curl -s "https://services.wheel-size.com/widget/finder/api/mk" \
     -H "Referer: https://services.wheel-size.com/widget/finder/try/" \
     -H "X-Csrf-Token: invalid_token" \
     -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
```

---

## 🔧 **ENVIRONMENT-SPECIFIC CONFIGURATIONS**

### **Production Settings Verification**
```python
# src/settings/production.py - Key settings to verify

# Database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'production_db_name',
        'USER': 'production_user',
        'HOST': 'production_host',
        'PORT': '5432',
        'OPTIONS': {
            'sslmode': 'require',  # Ensure SSL is enabled
        }
    }
}

# Django 4.2 specific settings
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
USE_TZ = True
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY')

# Updated admin interface settings
INSTALLED_APPS = [
    'admin_interface',  # Must be before django.contrib.admin
    'colorfield',
    'django.contrib.admin',
    # ... rest of apps
]

# Security settings for Django 4.2
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
```

### **Web Server Configuration**
```nginx
# nginx configuration updates for Django 4.2
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # Updated for Django 4.2 static files
    location /static/ {
        alias /path/to/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        alias /path/to/media/;
        expires 1y;
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increased timeouts for Django 4.2
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

---

## 📊 **MONITORING AND ALERTING**

### **Key Metrics to Monitor**
```bash
# 1. Application Performance
# Response time should remain < 500ms for admin pages
curl -w "%{time_total}\n" -s -o /dev/null https://your-domain.com/admin/

# 2. Database Performance
# Query time monitoring
tail -f /var/log/postgresql/postgresql.log | grep "duration:"

# 3. Memory Usage
# Should not exceed 80% of available memory
free -h | awk 'NR==2{printf "Memory Usage: %s/%s (%.2f%%)\n", $3,$2,$3*100/$2 }'

# 4. Error Rate
# Should be < 1% of total requests
tail -f /var/log/nginx/access.log | awk '$9 >= 400 {print $0}'
```

### **Automated Health Checks**
```bash
#!/bin/bash
# health_check.sh - Run every 5 minutes post-deployment

# Check admin interface (the critical fix we implemented)
if ! curl -s -f https://your-domain.com/admin/widgets_demo/demopage/ > /dev/null; then
    echo "ALERT: Admin interface down - AlwaysHexUUIDField issue may have returned"
    exit 1
fi

# Check main site
if ! curl -s -f https://your-domain.com/ > /dev/null; then
    echo "ALERT: Main site down"
    exit 1
fi

# Check database connectivity
if ! python manage.py dbshell -c "SELECT 1;" > /dev/null 2>&1; then
    echo "ALERT: Database connection failed"
    exit 1
fi

# ✅ NEW: Check widget API CSRF protection
if ! curl -s -f -H "Referer: https://your-domain.com/widget/finder/try/" \
     -H "X-Csrf-Token: health-check" -H "User-Agent: HealthCheck/1.0" \
     https://your-domain.com/widget/finder/api/mk > /dev/null; then
    echo "ALERT: Widget API CSRF protection failed"
    exit 1
fi

# ✅ NEW: Verify CSRF security blocks unauthorized requests
if curl -s -H "Referer: https://unauthorized.com/" \
   https://your-domain.com/widget/finder/api/mk | grep -q "slug"; then
    echo "CRITICAL ALERT: CSRF protection bypassed - security breach!"
    exit 1
fi

echo "✅ All health checks passed"
```

---

## 🧪 **TESTING PROCEDURES**

### **Pre-Deployment Testing Checklist**
```bash
# 1. Run full test suite
python manage.py test --verbosity=2

# 2. Test WS packages functionality
python manage.py shell -c "
import ws_fields, ws_django_helpers, ws_live_settings, ws_calc
from src.apps.widgets.common.fields import AlwaysHexUUIDField
from src.apps.widgets.demo.models import DemoPage
print('✅ All imports successful')
print(f'✅ DemoPage count: {DemoPage.objects.count()}')
"

# 3. Test admin interface with production data
python manage.py shell -c "
from django.contrib.admin.sites import site
from src.apps.widgets.demo.models import DemoPage
from src.apps.widgets.common.models import WidgetConfig
print(f'✅ Admin models registered: {len(site._registry)}')
print(f'✅ WidgetConfig count: {WidgetConfig.objects.count()}')
print(f'✅ DemoPage count: {DemoPage.objects.count()}')
"

# 4. Performance baseline test
python manage.py shell -c "
import time
from src.apps.widgets.demo.models import DemoPage
start = time.time()
list(DemoPage.objects.all()[:100])
end = time.time()
print(f'✅ Query performance: {end-start:.3f}s for 100 records')
"
```

### **Load Testing**
```bash
# Use Apache Bench to test under load
ab -n 1000 -c 10 https://your-domain.com/
ab -n 500 -c 5 https://your-domain.com/admin/

# Monitor during load test
watch -n 1 'ps aux | grep gunicorn | wc -l'
watch -n 1 'free -h'
```

---

## 📋 **DEPLOYMENT TIMELINE**

### **Week Before Deployment**
- [ ] **Monday**: Final code review and testing
- [ ] **Tuesday**: Staging environment deployment and testing
- [ ] **Wednesday**: Performance testing and optimization
- [ ] **Thursday**: Documentation review and team briefing
- [ ] **Friday**: Final backup verification and rollback testing

### **Day of Deployment**
- [ ] **T-2 hours**: Final staging verification
- [ ] **T-1 hour**: Team standup and final go/no-go decision
- [ ] **T-0**: Begin deployment sequence
- [ ] **T+30 min**: Deployment complete, monitoring begins
- [ ] **T+2 hours**: Extended monitoring and performance verification
- [ ] **T+24 hours**: Post-deployment review and documentation

### **Post-Deployment**
- [ ] **Day 1**: Intensive monitoring and issue resolution
- [ ] **Day 3**: Performance analysis and optimization
- [ ] **Week 1**: User feedback collection and minor fixes
- [ ] **Month 1**: Long-term stability assessment

---

## 🔐 **SECURITY CONSIDERATIONS**

### **Django 4.2 Security Updates**
```python
# Updated security settings for Django 4.2
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# New Django 4.2 security features
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_SECURE = True
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
```

### **Package Security Verification**
```bash
# Verify WS packages integrity
poetry show ws-django-fields | grep "version"
poetry show ws-django-helpers | grep "version"

# Check for security vulnerabilities
poetry audit  # If available
pip-audit  # Alternative security scanner
```

---

## 📚 **DOCUMENTATION UPDATES**

### **Post-Deployment Documentation**
1. **Update deployment logs** with actual timings and issues encountered
2. **Document any deviations** from this guide
3. **Update monitoring dashboards** with new Django 4.2 metrics
4. **Create incident response playbook** based on lessons learned
5. **Update team knowledge base** with Django 4.2 specific procedures

### **User Communication**
```markdown
# Sample user communication template

Subject: System Upgrade Complete - Enhanced Performance and Security

Dear Users,

We have successfully completed our system upgrade to Django 4.2, bringing you:
- ✅ Enhanced security and performance
- ✅ Improved admin interface experience
- ✅ Better long-term stability (LTS support until 2026)

All your data has been preserved during the upgrade. If you experience any issues, please contact support.

Thank you for your patience during the maintenance window.
```

---

**🎯 This comprehensive deployment guide ensures a safe, tested upgrade path for the Django 4.2 modernization while preserving all production data and minimizing downtime. The guide accounts for the specific fixes implemented (AlwaysHexUUIDField compatibility) and provides detailed procedures for every aspect of the deployment process.**
