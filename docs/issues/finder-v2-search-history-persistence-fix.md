# Finder-v2 Search History Configuration Persistence Issue

Last Modified: 2025-07-27 16:15 UTC+6

## Issue Summary

**Problem**: Search history configuration settings in the finder-v2 widget were not persisting after form save and page reload. Users would modify settings like "Enable Search History", "Maximum Stored Searches", and "Default Display Items", save the form, but the settings would revert to default values when the page was reloaded.

**Status**: ✅ **RESOLVED** (2025-07-27)  
**Severity**: High - Core functionality broken  
**Impact**: All search history configuration changes were lost after form submission

## Affected Components

- **Widget Type**: finder-v2
- **Configuration Section**: Search History settings
- **Files Affected**:
  - `src/apps/widgets/finder_v2/forms.py` - Form data handling
  - `src/apps/widgets/common/forms/main.py` - Form instance preparation
- **UI Elements**:
  - Enable Search History checkbox (`id_search_history-enabled`)
  - Maximum Stored Searches field
  - Default Display Items field
  - Auto-expand Panel checkbox
  - Show Timestamps checkbox

## Symptoms

### User Experience
1. User opens finder-v2 widget configuration page
2. User modifies search history settings (e.g., unchecks "Enable Search History")
3. User clicks "Save" button
4. Success message appears: "Configuration widget has been saved successfully"
5. **BUG**: After page reload, all search history settings revert to default values
6. Changes appear to be lost despite successful save confirmation

### Technical Symptoms
- Form validation passes and save operation completes successfully
- Success messages display correctly
- Database contains the correct saved values in `raw_params`
- However, form fields always display default values on page reload
- No error messages or obvious failures in the UI

## Root Cause Analysis

The issue had **three interconnected problems**:

### Problem 1: Form Method Always Returned Defaults
**Location**: `src/apps/widgets/finder_v2/forms.py` - `FinderV2SearchHistoryForm.decompose_to_initial()`

```python
def decompose_to_initial(self):
    # PROBLEM: Always returned defaults instead of loading saved data
    # For search history, we always return defaults for now since this is a new feature
    logger.debug("🔍 SEARCH_HISTORY DECOMPOSE: Returning defaults for new feature")
    return default_initial  # ← This was the first bug!
```

**Issue**: The method was hardcoded to always return default values with a comment indicating it was a "new feature", preventing the form from loading previously saved configuration data.

### Problem 2: DefaultJson.get() Method Incompatibility
**Location**: `src/apps/widgets/common/forms/main.py` - `prepare_instances()` method

```python
# PROBLEM: DefaultJson.get() has different signature than dict.get()
value = config.params.get(key)  # ← This was the second bug!
# DefaultJson.get() returns None instead of the expected data
```

**Issue**: The `DefaultJson.get()` method has a different signature than standard `dict.get()` and was returning `None` instead of the saved configuration data.

### Problem 3: Form Data Access Pattern Mismatch
**Location**: `src/apps/widgets/finder_v2/forms.py` - `FinderV2SearchHistoryForm.compose_to_save()`

```python
# PROBLEM: Using .get() method on DefaultJson objects in compose_to_save
enabled = data.get('enabled', True)  # ← This was the third bug!
# DefaultJson.get() has different signature, causing wrong values to be saved
```

**Issue**: The `compose_to_save()` method was using `.get()` on `DefaultJson` objects, causing incorrect values to be saved to the database.

## Solution Implementation

### Fix 1: Corrected Form Data Extraction Logic
**File**: `src/apps/widgets/finder_v2/forms.py`  
**Method**: `FinderV2SearchHistoryForm.decompose_to_initial()`

```python
def decompose_to_initial(self):
    """Extract search history configuration for form initialization"""
    # Note: self.instance IS the search_history section, not the full config
    try:
        enabled_val = self.instance['enabled']  # Direct access, not nested
        logger.debug(f"🔍 Found saved enabled: {enabled_val}")
    except (KeyError, TypeError):
        enabled_val = default_initial['enabled']
    
    # Similar pattern for all other fields with camelCase/snake_case compatibility
    try:
        max_items_val = self.instance.get('maxItems') or self.instance.get('max_items')
        if max_items_val is not None:
            max_items_val = int(max_items_val)
        else:
            raise KeyError("No max_items found")
    except (KeyError, TypeError, ValueError):
        max_items_val = default_initial['max_items']
    
    # ... similar for all fields
    return final_initial
```

**Key Changes**:
- Replaced hardcoded default returns with proper data extraction
- Fixed field access pattern (direct access instead of nested)
- Added backward compatibility for both camelCase and snake_case field names
- Added robust error handling with graceful fallbacks

### Fix 2: Corrected DefaultJson Access Pattern
**File**: `src/apps/widgets/common/forms/main.py`  
**Method**: `prepare_instances()`

```python
# CRITICAL FIX: Use bracket notation for DefaultJson objects
# The DefaultJson.get() method has a different signature than dict.get()
try:
    value = config.params[key]  # Use bracket notation instead of .get()
except (KeyError, IndexError):
    value = None
```

**Key Changes**:
- Changed from `config.params.get(key)` to `config.params[key]` with try/except
- Resolved signature incompatibility between `DefaultJson.get()` and `dict.get()`

### Fix 3: Corrected Form Data Saving Logic
**File**: `src/apps/widgets/finder_v2/forms.py`  
**Method**: `FinderV2SearchHistoryForm.compose_to_save()`

```python
def compose_to_save(self, data):
    """Compose search history configuration for saving"""
    # Handle both DefaultJson objects and regular dicts
    try:
        enabled = data['enabled'] if 'enabled' in data else True
    except (KeyError, TypeError):
        enabled = True
    
    try:
        max_items = int(data['max_items']) if 'max_items' in data and data['max_items'] else 10
    except (KeyError, TypeError, ValueError):
        max_items = 10
    
    # ... similar for all fields
    return final_data
```

**Key Changes**:
- Changed from using `.get()` to using bracket notation with try/except
- Ensured correct value saving for all form fields
- Added proper type conversion and error handling

## Testing & Verification

### Test Coverage
1. **Form loads saved data correctly** ✅
2. **Form saves data correctly** ✅  
3. **Complete round-trip persistence (UI simulation)** ✅
4. **Edge cases and backward compatibility** ✅

### Verification Steps
1. Modify search history settings in widget configuration
2. Save the form
3. Reload the page
4. Verify all settings persist correctly
5. Test with various combinations of enabled/disabled settings
6. Verify backward compatibility with existing configurations

## Resolution Status

**Status**: ✅ **COMPLETELY RESOLVED**  
**Date**: 2025-07-27  
**Verification**: All tests pass, manual testing confirms fix works

### What Now Works
- ✅ **Enable Search History checkbox**: Properly saves enabled/disabled state
- ✅ **Maximum Stored Searches**: Numeric values persist correctly  
- ✅ **Default Display Items**: Values save and load properly
- ✅ **Auto-expand Panel**: Boolean setting persists correctly
- ✅ **Show Timestamps**: Boolean setting persists correctly
- ✅ **UI shows correct values**: Form fields display actual saved values
- ✅ **Complete round-trip**: Save → database → reload → display cycle works perfectly

## Prevention

### Code Review Guidelines
1. **Always test form persistence** when implementing new configuration sections
2. **Be aware of DefaultJson vs dict differences** when accessing configuration data
3. **Use bracket notation** instead of `.get()` for DefaultJson objects
4. **Test round-trip persistence** (save → reload → verify) for all form changes

### Related Documentation
- [Finder-v2 Knowledge Transfer Guide](../development/finder-v2-knowledge-transfer.md#search-history-configuration-persistence-fix)
- [Widget Configuration Form Patterns](../development/widget-form-patterns.md)

## Impact Assessment

**Before Fix**:
- Search history configuration was completely non-functional
- User frustration due to lost settings
- Potential support tickets and confusion

**After Fix**:
- Full search history configuration functionality restored
- Improved user experience and confidence in the system
- Reduced potential support burden
