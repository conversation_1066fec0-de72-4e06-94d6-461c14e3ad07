# Finder-v2 Widget Output Template Saving Bug - Complete Resolution

## Issue Summary

**Bug:** The finder-v2 widget's custom output template field was not saving to the database after creating or editing a widget configuration. Users could enter template content in the textarea, but upon saving and reloading, the field would revert to empty/default, with no changes persisted in `WidgetConfig.raw_params['interface']['display']['output_template']`.

**Status:** ✅ **RESOLVED**

**Affected Components:**
- Form handling in `src/apps/widgets/finder_v2/forms.py` (FinderV2InterfaceForm)
- Multi-form orchestration in `src/apps/widgets/common/forms/main.py` (WidgetConfigForm, MultiModelForm)
- View saving logic in `src/apps/widgets/main/views/config.py` (WidgetConfigView)

**Discovered During:** Follow-up to regions multi-selection bug fix, where changes inadvertently affected template persistence.

## Symptoms

- Template textarea shows default/pre-populated value on initial load.
- User modifications to the template are visible in POST data (e.g., 'interface-output_template').
- After save, reloading the config page shows empty/blank template field.
- Database inspection shows `raw_params['interface']['display']['output_template']` as empty string.
- No errors in logs; save appears successful (success message shown).
- Other form fields (e.g., flow_type, width) save correctly.

## Root Causes

Multiple interconnected issues in the form data flow:

1. **Double Processing in Multi-Form Save:** `WidgetConfigForm.prepare_to_save()` was redundantly calling `compose_to_save()` on already composed sub-form data, causing the template value to be dropped during re-processing (composed dict lacked expected keys).

2. **Prefixed Keys in Multi-Form Context:** Form data keys are prefixed (e.g., 'interface-output_template'), but `compose_to_save()` was looking for unprefixed 'output_template', missing the value in some flows.

3. **Decompose Logic Always Falling Back to Default:** `decompose_to_initial()` was not properly extracting stored template, always returning default even when value existed.

4. **Caching Interference:** Widget params caching prevented fresh data reload after save, showing stale (empty) values on redirect.

5. **JSON Wrapper Mismatches:** Inconsistent use of `DefaultJson` vs raw dicts led to KeyErrors during access.

These combined to allow initial form population but fail persistence.

## Investigation Steps

1. **Reproduced Issue:** Confirmed template not saving via admin interface and direct DB checks.

2. **Added Logging:** Instrumented key methods (`__init__`, `decompose_to_initial`, `compose_to_save`, `form_valid`, `save`) with detailed logs to trace data flow.

3. **Traced Data Path:**
   - Verified POST data contains prefixed keys with template content.
   - Saw data reach sub-form `clean()` but drop in `compose_to_save()` due to prefix mismatch.
   - Identified double `compose_to_save()` call clearing values.

4. **Codebase Search:** Queried for form initialization, saving logic, and JSON structures to map full pipeline.

5. **Iterative Fixes:** Applied targeted patches, tested via logs/config reloads.

## Fixes Applied

1. **Removed Redundant compose_to_save Call** (src/apps/widgets/common/forms/main.py):
   - In `prepare_to_save()`, use already-composed sub-form data directly instead of re-calling `compose_to_save()`.
   - Prevents data loss from double-processing.

2. **Prefix-Aware Key Lookup** (src/apps/widgets/finder_v2/forms.py):
   - In `compose_to_save()`, added fallback to check 'interface-<key>' if unprefixed missing.
   - Handles MultiModelForm prefixing.

3. **Improved Decompose Logic** (src/apps/widgets/finder_v2/forms.py):
   - Ensure `decompose_to_initial()` reads stored 'output_template' before default fallback.
   - Only use default if stored value is truly empty/missing.

4. **Cache Clearing Post-Save** (src/apps/widgets/main/views/config.py):
   - Reload config from DB after save and delete cached '_params'/'_widget_type'.
   - Ensures fresh data on redirect.

5. **JSON Wrapper Fixes:** Adjusted DefaultJson handling to avoid KeyErrors on empty sections.

All fixes preserve backward compatibility and don't affect other fields/forms.

## Verification & Testing

**Steps to Verify:**
1. Restart server: `docker-compose restart web`.
2. Create new finder-v2 config at http://development.local/widget/finder-v2/config-demo/.
3. Edit output template, save.
4. Reload page – template should persist.
5. Check DB: `WidgetConfig.raw_params['interface']['display']['output_template']` matches submitted value.
6. Test other fields (e.g., flow_type) still save correctly.

**Logs to Monitor:** Look for '🎯 COMPOSE_TO_SAVE' and 'FORM_VALID CALLED' entries confirming data flow.

**Edge Cases Tested:**
- Empty template (falls back to default).
- Default vs custom configs.
- Authenticated vs demo modes.
- Large templates (no truncation).

## Lessons Learned

- Multi-form systems require careful prefix handling.
- Avoid redundant method calls in save pipelines.
- Always clear caches after DB writes.
- Comprehensive logging is key for debugging data flows.
- Test persistence end-to-end (POST → DB → Reload).

## Related Files

- `src/apps/widgets/finder_v2/forms.py`
- `src/apps/widgets/common/forms/main.py`
- `src/apps/widgets/main/views/config.py`
- `src/apps/widgets/common/json_wrapper.py`

**Fixed In:** Conversation dated [insert date].

For similar issues, start by logging the full data path from POST to DB save. 