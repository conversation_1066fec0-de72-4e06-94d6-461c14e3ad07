# Widget Finder API Routing Fix

## Issue Description

The finder widget iframe at `http://development.local:8000/widget/676be06961a74399b5f17303c179874c/` was experiencing API routing issues with 404 errors for multiple endpoints:

- `http://development.local:8000/widget/676be06961a74399b5f17303c179874c/api/rd?bolt_pattern=&rim_diameter=&rim_width=`
- `http://development.local:8000/widget/676be06961a74399b5f17303c179874c/api/mk?make=&model=&year=`
- `http://development.local:8000/widget/676be06961a74399b5f17303c179874c/api/tw?aspect_ratio=&rim_diameter=&tire_width=`

## Root Cause Analysis

The issue was in the URL routing configuration in `src/apps/widgets/api_proxy/urls.py`:

1. **Incorrect API Version Routing**: UUID widgets (like `676be06961a74399b5f17303c179874c`) were being routed to `WidgetProxyView` (v2 API) regardless of their actual widget type

2. **Missing Widget Type Detection**: The routing logic assumed all UUID widgets use v2 API, but finder widgets specifically require v1 API endpoints

3. **API Version Mismatch**: 
   - Finder widgets need **v1 API endpoints** (`/v1/__tw/`, `/v1/makes/`, `/v1/__rd/`)
   - The routing was sending them to **v2 API endpoints** (`/v2/__tw/`, `/v2/makes/`, `/v2/__rd/`)

## Solution Implemented

### 1. Created Dynamic Widget API View

**File**: `src/apps/widgets/api_proxy/urls.py`

**Added `dynamic_widget_api_view()` function**:
- Detects widget type by querying the database using the widget UUID
- Routes finder widgets to `FinderWidgetProxyView` (v1 API)
- Routes other widgets to `WidgetProxyView` (v2 API)
- Maintains backward compatibility for all widget types

### 2. Updated URL Routing Configuration

**Before**:
```python
# UUID widget specific API URLs (use default v2 API endpoints)
# Pattern: /widget/abc123.../api/mk → WidgetProxyView
re_path(r'^(?P<widget_slug>[a-z0-9]{32})/api/', include(api_urlpatterns)),
```

**After**:
```python
# UUID widget specific API URLs (dynamic routing based on widget type)
# Pattern: /widget/abc123.../api/mk → dynamic_widget_api_view → FinderWidgetProxyView or WidgetProxyView
re_path(r'^(?P<widget_slug>[a-z0-9]{32})/api/', include(dynamic_uuid_api_urlpatterns)),
```

### 3. Dynamic Routing Logic

```python
def dynamic_widget_api_view(request, widget_slug, **kwargs):
    # Check if this is a UUID widget (32 hex characters)
    if len(widget_slug) == 32 and all(c in '0123456789abcdef' for c in widget_slug):
        try:
            # Get widget configuration from database
            widget_config = WidgetConfig.objects.get(uuid=widget_slug)
            
            # Route finder widgets to FinderWidgetProxyView (v1 API)
            if widget_config.type == 'finder':
                view = FinderWidgetProxyView.as_view(source=source_mapping[source])
                return view(request, widget_slug=widget_slug, **kwargs)
                
        except WidgetConfig.DoesNotExist:
            pass
    
    # Default to WidgetProxyView for non-finder widgets (v2 API)
    view = WidgetProxyView.as_view(source=source_mapping[source])
    return view(request, widget_slug=widget_slug, **kwargs)
```

## Verification Results

### Widget Type Confirmation
```python
widget_config = WidgetConfig.objects.get(uuid='676be06961a74399b5f17303c179874c')
# Result: Widget type: finder ✅
```

### API Routing Testing
- ✅ **CSRF Validation**: All API calls now pass CSRF protection
- ✅ **Dynamic Routing**: Finder widgets correctly route to `FinderWidgetProxyView`
- ✅ **API Version**: v1 API endpoints are used for finder widgets
- ✅ **Backward Compatibility**: Non-finder widgets still use v2 API

### Browser Testing
- ✅ **Widget Iframe**: http://development.local:8000/widget/676be06961a74399b5f17303c179874c/ loads correctly
- ✅ **API Endpoints**: All finder widget API calls return data instead of 404 errors
- ✅ **JavaScript Functionality**: Widget JavaScript can successfully fetch data

## Technical Details

### API Version Architecture
- **FinderWidgetProxyView**: Uses v1 API (`https://api3.wheel-size.com/v1/...`)
- **WidgetProxyView**: Uses v2 API (`https://api3.wheel-size.com/v2/...`)
- **Dynamic Routing**: Database lookup determines which proxy view to use

### Endpoint Mapping
```python
source_mapping = {
    'tire-widths': '__tw/',      # /v1/__tw/ for finder, /v2/__tw/ for others
    'makes': 'makes/',           # /v1/makes/ for finder, /v2/makes/ for others
    'rim-diameters': '__rd/',    # /v1/__rd/ for finder, /v2/__rd/ for others
    # ... etc
}
```

## Files Modified

1. **src/apps/widgets/api_proxy/urls.py**
   - Added `dynamic_widget_api_view()` function
   - Created `dynamic_uuid_api_urlpatterns` URL patterns
   - Updated main `urlpatterns` to use dynamic routing for UUID widgets

## Impact

- ✅ **Fixed**: Finder widget iframe API routing issues
- ✅ **Maintained**: Backward compatibility for calc widgets and other widget types
- ✅ **Enhanced**: Automatic widget type detection and routing
- ✅ **Preserved**: Security through CSRF protection

## Related Issues

This fix resolves the same v1/v2 API version issue that was previously fixed for widget configuration forms in `src/apps/widgets/finder/forms.py`. The complete solution now covers both:

1. **Widget Configuration Forms**: Use `FinderWidgetProxyView` for form initialization
2. **Widget Iframe API Calls**: Use dynamic routing to detect widget type and route accordingly

## Related Documentation

- [Widget Configuration Forms Fix](docs/widget-finder-brands-fix.md)
- [Widget API Proxy Views](src/apps/widgets/api_proxy/views.py)
- [Widget Security Guide](docs/security/widget-security-guide.md)
