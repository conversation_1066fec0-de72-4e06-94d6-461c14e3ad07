# Wheel-Size Services Documentation Hub
**Last Modified:** 2025-06-12 15:11 UTC+6

This is the main documentation hub for the wheel-size-services project, a Django-based platform providing wheel and tire size widgets for client websites.

## Table of Contents

- [Project Overview](#project-overview)
- [Architecture](#architecture)
- [Quick Start](#quick-start)
- [Documentation Index](#documentation-index)
- [Testing](#testing)
- [Security](#security)
- [Django 4.2 Upgrade](#django-42-upgrade)
- [Development](#development)

## Project Overview

Wheel-Size Services is a Django 4.2 application that provides embeddable widgets for client websites to help users find compatible wheel and tire sizes for their vehicles. The system includes:

- **Widget System**: Embeddable JavaScript widgets for client websites
- **API Proxy**: Secure API endpoints with CSRF protection
- **Admin Interface**: Django admin for widget configuration and management
- **Cross-Domain Security**: Secure widget operation across different client domains

### How the Widget System Works

```html
<!-- Client website embeds widget with simple JavaScript -->
<script src="//services.wheel-size.com/static/widget/code/local/ws-widget.js"></script>
<script>
  var widget = WheelSizeWidgets.create('#ws-widget-container', {
    uuid: 'widget-uuid-here',
    type: 'finder',
    width: '600',
    height: '400'
  });
</script>
```

**Security Flow:**
1. Widget JavaScript loads from wheel-size server
2. Widget generates CSRF token based on User-Agent
3. API calls include token in `X-Csrf-Token` header
4. Server validates token regardless of origin domain
5. Cross-domain request succeeds with proper authentication

## Architecture

```
wheel-size-services/
├── src/
│   ├── apps/
│   │   └── widgets/           # Widget application
│   │       ├── api_proxy/     # API proxy with CSRF protection
│   │       ├── finder/        # Finder widget implementation
│   │       └── common/        # Shared widget components
│   ├── settings/              # Environment-specific settings
│   └── static/                # Static files and built widgets
├── tests/                     # Test suite
│   └── widget/               # Widget-specific tests
├── docs/                     # Documentation
│   ├── security/             # Security documentation
│   └── upgrade/              # Django upgrade documentation
└── docker/                  # Docker configuration
```

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.9+ (for local development)
- Node.js (for widget JavaScript building)

### Development Setup

1. **Clone and start services:**
   ```bash
   git clone <repository-url>
   cd wheel-size-services
   docker-compose up -d
   ```

2. **Access the application:**
   - Main application: http://development.local:8000
   - Admin interface: http://development.local:8000/admin/
   - Widget test page: http://development.local:8000/widget/finder/try/

3. **Run security tests:**
   ```bash
   python tests/widget/test_widget_csrf_security.py
   ```

### Database Setup
The project uses PostgreSQL 15 with a local database copied from production:
- Database: `ws_services_db`
- User: `ws_services_user`
- Host: `postgres15` (Docker) / `development.local` (local)

## Documentation Index

### General Documentation
- **[Widget Finder API Analysis](widget-finder-api-analysis.md)** - Complete analysis of widget API implementation and CSRF protection
- **[Docker Project Configuration](settings/docker-project-configuration.md)** - Comprehensive guide for Docker development and deployment
- **[Docker Container Troubleshooting](update/docker-container-troubleshooting.md)** - Detailed Docker troubleshooting procedures
- **[Finder-v2 Implementation Plan](development/finder-v2-master-implementation-plan.md)** - Detailed plan for creating new finder-v2 widget with API v2 support

### Security Documentation
- **[Widget Security Guide](security/widget-security-guide.md)** - Comprehensive security model for cross-domain widgets
- **[Widget API Testing Guide](security/widget-api-testing-guide.md)** - Security testing procedures and commands
- **[CSRF Token Algorithm](security/csrf-token-algorithm.md)** - Internal CSRF algorithm documentation and implementation details

### Django 4.2 Upgrade Documentation
- **[Upgrade Plan](upgrade/upgrade-plan.md)** - Original Django 4.2 upgrade planning and phases
- **[Production Deployment Guide](upgrade/production-deployment-guide.md)** - Comprehensive production deployment procedures
- **[Upgrade Complete](upgrade/upgrade-complete.md)** - Final upgrade status and achievements
- **[WS Packages Upgrade Roadmap](upgrade/ws-packages-upgrade-roadmap.md)** - Complete roadmap for upgrading all WS packages to Django 4.2
- **[Remaining Components Upgrade Plan](upgrade/remaining-components-upgrade-plan.md)** - Plan for upgrading remaining third-party components
- **[Django Admin Interface Bootstrap Theme Installation](upgrade/django-admin-interface-bootstrap-theme-installation.md)** - Complete guide for Bootstrap theme installation and configuration

## Testing

### Test Structure
```
tests/
├── README.md                 # Testing documentation
└── widget/
    └── test_widget_csrf_security.py  # Comprehensive widget security tests
```

### Running Tests

**Widget Security Tests:**
```bash
# Run comprehensive security validation
python tests/widget/test_widget_csrf_security.py

# Expected output:
# 🎉 ALL TESTS PASSED - Widget CSRF security is working correctly!
```

**Manual API Testing:**
```bash
# Generate CSRF token
python -c "
import base64
user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
token = base64.b64encode(user_agent.encode('utf-8')).decode('utf-8')
token_slice = token[:32]
result = []
for i in range(len(token_slice)):
    index = (27 + 11 - (7 + i * 11) % 39) % len(token)
    result.append(token[index])
print('CSRF Token:', ''.join(result))
"

# Test API endpoint
curl -s "http://development.local:8000/widget/finder/api/mk" \
  -H "Referer: http://development.local:8000/widget/finder/try/" \
  -H "X-Csrf-Token: gbSDMgaMm8sgKWTR1TTFxAvLWldsudj9" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
```

## Security

### CSRF Protection Model

The widget system uses a custom CSRF protection designed for cross-domain operation:

**Key Features:**
- ✅ Cross-origin requests allowed (widgets work on client domains)
- ✅ CSRF token validation enforced for all requests
- ✅ No hostname bypass vulnerabilities
- ✅ Secure token generation based on User-Agent

**Security Configuration:**
```python
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': True,  # Development port handling
    'debug_csrf_validation': True,         # Debug logging
    # 'trusted_hostnames': [],             # NOT USED - Security risk
}
```

### Critical Security Findings

During the Django 4.2 upgrade, a critical security vulnerability was discovered and fixed:

**❌ Vulnerability:** `trusted_hostnames` setting created a security bypass
**✅ Fix:** Removed trusted_hostnames, rely on CSRF token validation only
**📋 Documentation:** See [Widget Security Guide](security/widget-security-guide.md)

## Django 4.2 Upgrade

### Upgrade Status: ✅ COMPLETE

The project has been successfully upgraded from Django 3.2 to Django 4.2 LTS with:

**Key Achievements:**
- ✅ Django 4.2 LTS compatibility (supported until 2026)
- ✅ Python 3.9-3.12 support
- ✅ All WS packages upgraded to v2.0.0
- ✅ Widget CSRF security vulnerabilities fixed
- ✅ Production deployment procedures documented
- ✅ Comprehensive testing suite implemented

**Root Cause Analysis:**
The main issue after upgrade was **port mismatch in CSRF hostname validation**:
- Browser referer: `development.local` (no port)
- Server host: `development.local:8000` (with port)
- Solution: `ignore_port_in_hostname_check: True`

### Upgrade Documentation
- **[Upgrade Plan](upgrade/upgrade-plan.md)** - Original planning and phases
- **[Production Deployment Guide](upgrade/production-deployment-guide.md)** - Deployment procedures
- **[Upgrade Complete](upgrade/upgrade-complete.md)** - Final status and achievements

## Development

### Widget Development

**Building JavaScript:**
```bash
# Navigate to widget app directory
cd src/apps/widgets/finder/app

# Build widget JavaScript
grunt app --force

# Files are automatically copied to static directories
```

**Key Development Files:**
- `src/apps/widgets/api_proxy/views.py` - CSRF protection and API proxy
- `src/apps/widgets/finder/app/js/app/app-config-token.js` - CSRF token generation
- `src/apps/widgets/finder/widget_type.py` - Widget type configuration

### Environment Configuration

| Environment | ignore_port | debug_csrf | Notes |
|-------------|-------------|------------|-------|
| **Development** | `True` | `True` | Port ignoring enabled |
| **Staging** | `False` | `False` | Strict validation |
| **Production** | `False` | `False` | Strict validation |

### Debugging

**Enable CSRF Debug Logging:**
```python
WIDGET_CSRF_SETTINGS = {
    'debug_csrf_validation': True,
}
```

**View Debug Logs:**
```bash
docker logs -f ws_services 2>&1 | grep "WIDGET CSRF DEBUG"
```

---

**For detailed information on any topic, see the linked documentation files above.**
