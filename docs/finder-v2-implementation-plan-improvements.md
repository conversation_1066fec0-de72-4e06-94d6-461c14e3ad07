# Finder-v2 Implementation Plan – Proposed Enhancements
_(complements **finder-v2-implementation-plan.md**)_

> All items below **extend** the original plan; nothing is removed.
> ☑︎ = new task to add  🛈 = explanation / reference

---

## 0. Project & Git Workflow
- ☑︎ **Create feature branch** `feature/finder-v2-widget` (follows upgrade conventions – see augment-memory-28-05-2025.md).
- ☑︎ Enable **progress tracking**: keep a running checklist in
  `docs/development/progress-finder-v2.md` (mirrors upgrade-plan style).
- ☑︎ After each major checklist section → commit + push (no fast-forward) for code-review visibility.
- ☑︎ Final merge via PR after QA approval.

## 1. Poetry / Dependency Management
- ☑︎ **Pin WS packages** versions exactly to `2.0.0` (user prefers 2.0.0, not 2.0.2).
  🛈 Edit `pyproject.toml` + regenerate lock.
- ☑︎ Publish any modified WS forks to private PyPI (`poetry publish -r ws --build`) – no site-packages patching.
- ☑︎ Consolidate docker and root `pyproject.toml` if still duplicated (see README "Poetry consolidation" note).

## 2. Docker & Build Pipeline
- ☑︎ **Node layer**: add Node 20 LTS stage to `Dockerfile` so Vue build can run in CI & local compose.
- ☑︎ Add `RUN npm ci && npm run build` for finder-v2 inside Docker compose override, but mount volume in dev for hot-reload.
- ☑︎ Cache `node_modules` in GitHub Actions to speed up CI.

## 3. CI / GitHub Actions
- ☑︎ Extend existing workflow:
  `matrix["widget"] = ["finder", "finder_v2", "calc"]`
- ☑︎ Add step `npm ci && npm run lint && npm run test && npm run build` in `src/apps/widgets/finder_v2/app`.
- ☑︎ Upload built static artefacts as workflow artifact for review.

## 4. Live-Settings & Feature Flags
- ☑︎ Create LiveSetting `widgets.FINDER_V2_ENABLED` (boolean, default *False*).
  - If False → proxy view returns 404 for finder-v2 calls, admin hides creation form.
  - Toggle to True during staging → production rollout.
- ☑︎ Document rollout toggle in `docs/upgrade/remaining-components-upgrade-plan.md`.

## 5. Public Widget Creation Interface
- ☑︎ Implement public widget creation interface for finder-v2 widgets at `/widget/finder-v2/config/`
- ☑︎ Allow anonymous users to create finder-v2 widget configurations in public mode
- ☑︎ Provide same functionality as finder v1 public creation page but for finder-v2 widgets
- ☑︎ Ensure both anonymous and registered users can access the public creation interface
- ☑︎ Configure widgets with finder-v2 default settings and flow type options
- ☑︎ Follow existing patterns from finder v1 public creation functionality

## 5. Security & CSRF
- ☑︎ Re-use **legacy CSRF algorithm** path (`app-config-token.min.js`) for compatibility; unit-test equality with new TS implementation.
- ☑︎ Confirm `trusted_hostnames` is NOT re-introduced; rely on token + port rule only.
- ☑︎ Add regression test (pytest) verifying port-mismatch bypass is impossible in finder-v2.

## 6. Internationalisation
- ☑︎ Duplicate PO/POT files from `widgets/finder/translations/` to `finder_v2/translations/`.
- ☑︎ Run `django-admin makemessages -a` after JS extraction to capture Vue i18n strings.

## 7. Admin Interface Refinements
- ☑︎ In addition to `has_add_permission`, enforce read-only "type" field via `get_readonly_fields` to stop post-creation changes.
- ☑︎ Provide **migration assistant** action in admin list: duplicate an existing *finder* config into *finder-v2* for quick pilot tests.

## 8. Front-End Performance & Accessibility
- ☑︎ Hard bundle budget: < 90 KB gzipped (original plan says 100 KB).
- ☑︎ Add **Lighthouse CI** check in build pipeline (scores ≥90 for PWA, accessibility).
- ☑︎ Use Tailwind v4 **JIT** with purge to satisfy budget.

## 9. Monitoring & Metrics
- ☑︎ Emit Prometheus metric `ws_widgets_finder_v2_render_seconds` in iframe view.
- ☑︎ Add Grafana dashboard section "Finder-v2 adoption".

## 10. Documentation & Testing Additions
- ☑︎ Place *all new docs* under `docs/development/finder-v2/…` per project structure guideline.
- ☑︎ Add **cURL cheat-sheet** for v2 endpoints in `docs/api/`.
- ☑︎ Browser test matrix expanded to include **mobile Safari iOS** (historically problematic).
- ☑︎ Unit-test `WidgetCSRFSettings.ignore_port_in_hostname_check` obeyed in finder-v2 proxies.

---

### Summary Table of Added Tasks

| Section | New Tasks | Reference |
|---------|-----------|-----------|
| Git workflow | feature branch, progress md | augment-memory |
| Poetry | version pinning, private PyPI | augment-memory |
| Docker | node layer, cache | README |
| CI | Vue build, artefacts | upgrade-plan |
| Live-Settings | rollout toggle | augment-memory |
| Security | legacy algorithm tests | widget-finder-api-analysis |
| I18N | translations duplication | general pattern |
| Admin | readonly type, migration action | new-finder-v2-creation |
| Perf | bundle < 90 KB, Lighthouse | internal standards |
| Monitoring | Prom metrics, Grafana | current Prom setup |
| Docs / Tests | folder location, cheat-sheet | docs conventions |

> Implement these enhancements in addition to the existing roadmap to fully satisfy all stakeholder requirements and minimize production risk.