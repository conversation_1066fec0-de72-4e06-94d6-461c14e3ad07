# Widget Finder Brands Unavailable Fix

## Issue Description

The widget finder configuration pages were displaying the error message:
> "Sorry, list of brands is currently unavailable because of some server problems"

This affected both:
- New widget creation: `http://development.local:8000/widget/finder/config/`
- Existing widget editing: `http://development.local:8000/widget/676be06961a74399b5f17303c179874c/config/`

## Root Cause Analysis

The issue was caused by a mismatch between API versions in the widget configuration system:

1. **REST_PROXY Configuration Change**: The `REST_PROXY.HOST` setting was changed from `https://api3.wheel-size.com/v1` to `https://api3.wheel-size.com` (without `/v1`)

2. **Incorrect API View Usage**: The `ContentFilterForm.choices` property in `src/apps/widgets/finder/forms.py` was using `WidgetProxyView` (which uses v2 API) instead of `FinderWidgetProxyView` (which uses v1 API)

3. **API Version Mismatch**: 
   - Finder widgets require **v1 API endpoints** (`/v1/makes/`, `/v1/__cc/`, `/v1/__mr/`)
   - Regular widgets use **v2 API endpoints** (`/v2/makes/`, `/v2/__cc/`, `/v2/__mr/`)

## Solution Implemented

### 1. Updated Form to Use Correct API View

**File**: `src/apps/widgets/finder/forms.py`

**Changes**:
- Changed import from `WidgetProxyView` to `FinderWidgetProxyView`
- Updated `choices` property to use `FinderWidgetProxyView.as_view()` for all endpoints
- Enhanced `get_choices()` method with proper error handling and debugging

**Before**:
```python
from src.apps.widgets.api_proxy.views import WidgetProxyView

@cached_property
def choices(self):
    return {
        'countries': self.get_choices(reverse('widget-api:countries'),
                                      WidgetProxyView.as_view(source='__cc/')),
        'brands': self.get_choices(reverse('widget-api:makes'),
                                   WidgetProxyView.as_view(source='makes/')),
        'markets': self.get_choices(reverse('widget-api:markets'),
                                    WidgetProxyView.as_view(source='__mr/'))
    }
```

**After**:
```python
from src.apps.widgets.api_proxy.views import FinderWidgetProxyView

@cached_property
def choices(self):
    """
    Get choices data for finder widget configuration.
    
    Uses FinderWidgetProxyView to ensure v1 API endpoints are used,
    which are required for the finder widget functionality.
    """
    return {
        'countries': self.get_choices(reverse('widget-api:countries'),
                                      FinderWidgetProxyView.as_view(source='__cc/')),
        'brands': self.get_choices(reverse('widget-api:makes'),
                                   FinderWidgetProxyView.as_view(source='makes/')),
        'markets': self.get_choices(reverse('widget-api:markets'),
                                    FinderWidgetProxyView.as_view(source='__mr/'))
    }
```

### 2. Enhanced Error Handling

**Improved `get_choices()` method**:
- Added proper request headers for internal API calls
- Enhanced error logging for debugging
- Added exception handling for API call failures

## Verification Results

### API Call Testing
```python
# Test with FinderWidgetProxyView (v1 API)
response = FinderWidgetProxyView.as_view(source='makes/')(request)
# Result: Status code: 200, Content length: 13179 ✅

# Form choices testing
form = ContentFilterForm(instance=mock_instance)
choices = form.choices
# Results:
# - Brands choices length: 13179 ✅
# - Countries choices length: 767 ✅  
# - Markets choices length: 1229 ✅
```

### Web Interface Testing
- ✅ Widget finder configuration pages now load successfully
- ✅ Brand selection interface displays available brands
- ✅ Country and market selection interfaces work correctly
- ✅ No more "brands unavailable" error messages

## Technical Details

### API Version Architecture
- **FinderWidgetProxyView**: Automatically appends `/v1` to `REST_PROXY.HOST` via `get_proxy_host()` method
- **WidgetProxyView**: Uses `REST_PROXY.HOST` directly (for v2 API)

### CSRF Protection
- Both views use `WsProtectMixin` for security
- Internal form calls bypass CSRF via `request._dont_enforce_csrf_checks = True`
- Proper headers added for hostname validation

## Files Modified

1. **src/apps/widgets/finder/forms.py**
   - Changed API view from `WidgetProxyView` to `FinderWidgetProxyView`
   - Enhanced error handling in `get_choices()` method
   - Added documentation for API version requirements

## Impact

- ✅ **Fixed**: Widget finder configuration pages now work correctly
- ✅ **Maintained**: Backward compatibility with existing widgets
- ✅ **Preserved**: Security through CSRF protection
- ✅ **Enhanced**: Error logging for future debugging

## Related Documentation

- [Widget API Proxy Views](src/apps/widgets/api_proxy/views.py)
- [Widget Security Guide](docs/security/widget-security-guide.md)
- [API Testing Guide](docs/security/widget-api-testing-guide.md)
