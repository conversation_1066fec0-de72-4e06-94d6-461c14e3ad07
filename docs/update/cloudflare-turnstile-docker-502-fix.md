# Cloudflare Turnstile Docker 502 Bad Gateway Fix

Last Modified: 2025-01-21 18:00 UTC+6

## Issue Summary

**Problem**: Cloudflare Turnstile configuration in Django settings caused 502 Bad Gateway errors after restarting the web service with `docker-compose restart web`. The issue required rebuilding the Docker image (`docker-compose up --build web`) rather than just restarting the service.

**Root Cause**: Python package installation mismatch in Docker container where `django-turnstile` was installed in a Poetry virtual environment instead of the system Python environment that Django was using.

## Symptoms

- ✅ `docker-compose up --build web` (rebuild) works
- ❌ `docker-compose restart web` (restart only) fails with 502 Bad Gateway
- ❌ Django logs show: `ModuleNotFoundError: No module named 'turnstile'`
- ❌ Service becomes inaccessible at `http://development.local/`

## Root Cause Analysis

### 1. Package Location Mismatch

The `django-turnstile` package was installed in the wrong location:

```bash
# Package was installed here (virtual environment):
/code/.venv/lib/python3.12/site-packages/turnstile/

# But Django was looking here (system Python):
/root/.pyenv/versions/3.12.0/lib/python3.12/site-packages/
```

### 2. Poetry Configuration Issues

Despite Dockerfile setting `virtualenvs.create false`, a virtual environment was still created:

```dockerfile
# Dockerfile configuration (correct):
RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false

# But .venv directory still existed in container
```

### 3. Django Import Failure

Django's `INSTALLED_APPS` includes `'turnstile'` but the module couldn't be imported:

```python
# src/settings/base.py
INSTALLED_APPS = [
    # ...
    'turnstile',  # ← This caused ModuleNotFoundError
    # ...
]
```

### 4. Container Restart vs Rebuild Behavior

- **Container Restart**: Preserves file system state, including incorrect `.venv` directory
- **Container Rebuild**: Creates fresh environment with proper package installation

## Solution Implementation

### Step 1: Remove Virtual Environment

```bash
docker exec ws_services bash -c "cd /code && rm -rf .venv"
```

### Step 2: Configure Poetry Correctly

```bash
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false"
```

### Step 3: Reinstall Packages to System Python

```bash
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry install --no-root"
```

### Step 4: Add Missing Environment Variables

Updated `.env.local`:
```bash
# Cloudflare Turnstile Configuration (Replaces reCAPTCHA)
TURNSTILE_SITEKEY=0x4AAAAAABkh7r6su2_ky-7R
TURNSTILE_SECRET=0x4AAAAAABkh7mVABDG6dVVD9vUJJ_Z1bUE
```

### Step 5: Enhanced Dockerfile

Updated `docker/Dockerfile` to prevent future issues:
```dockerfile
# Use Poetry to install all dependencies (including WS packages from private PyPI)
RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false
RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false
# Install dependencies from Docker-specific pyproject.toml
RUN /root/.pyenv/versions/3.12.0/bin/poetry install --no-root
# Verify that django-turnstile is installed correctly
RUN /root/.pyenv/versions/3.12.0/bin/python -c "import turnstile; print('Turnstile module imported successfully')"
```

## Verification Steps

### 1. Test Module Import
```bash
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/python -c 'import turnstile; print(\"Turnstile imported successfully\")'"
```

### 2. Test Service Restart
```bash
docker-compose restart web
```

### 3. Verify Web Service
```bash
curl -I http://development.local/
```

### 4. Check Django Logs
```bash
docker logs ws_services --tail 10
```

## Expected Results After Fix

✅ **Service Restart Works**: `docker-compose restart web` works without 502 errors  
✅ **Module Import Success**: `import turnstile` works correctly  
✅ **Django Startup Clean**: No `ModuleNotFoundError` in logs  
✅ **Web Service Accessible**: HTTP 200 responses from `http://development.local/`  

## Prevention Measures

### 1. Dockerfile Best Practices
- Always set both `virtualenvs.create false` and `virtualenvs.in-project false`
- Add import verification for critical packages
- Use consistent Poetry configuration

### 2. Environment Variable Management
- Ensure all required environment variables are defined
- Use `.env.local` for development-specific values
- Document required variables in settings files

### 3. Container Health Checks
- Add health checks to detect import failures early
- Monitor Django startup logs for module errors
- Test both restart and rebuild scenarios

## Troubleshooting Commands

### Check Package Installation Location
```bash
# Check if package is in virtual environment
docker exec ws_services bash -c "ls -la /code/.venv/lib/python3.12/site-packages/ | grep turnstile"

# Check if package is in system Python
docker exec ws_services bash -c "ls -la /root/.pyenv/versions/3.12.0/lib/python3.12/site-packages/ | grep turnstile"
```

### Check Poetry Configuration
```bash
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create"
```

### Check Poetry Environment Info
```bash
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry env info"
```

## Related Documentation

- [Docker Project Configuration](../settings/docker-project-configuration.md) - Comprehensive Docker setup guide
- [Docker Container Troubleshooting](./docker-container-troubleshooting.md) - Detailed troubleshooting procedures
- [Cloudflare Turnstile Migration Guide](../development/features/recaptcha-to-turnstile-migration.md) - Migration process documentation

## Key Takeaways

1. **Docker State Persistence**: Containers preserve file system changes between restarts
2. **Poetry Virtual Environments**: Can be created even with `virtualenvs.create false` if configuration isn't persistent
3. **Django Module Loading**: Fails fast on missing modules in `INSTALLED_APPS`
4. **Environment Variables**: Missing variables don't always cause immediate failures due to defaults
5. **Package Installation**: Always verify packages are installed in the correct Python environment
