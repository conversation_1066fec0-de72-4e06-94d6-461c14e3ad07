<script setup>
import {ref, watch, onMounted} from "vue";
import axios from 'axios';
import BaseListbox from "../form/CustomSelector.vue";
import {
	API_KEY,
	WS_WHEEL_API_BASE_URL
} from '../../config.js';

const emit = defineEmits(['dataConfiguratorReceived', 'dataFitmentReceived', 'reset-complete', 'resetState'])

const props = defineProps({
	reset: Boolean
});

const isResetting = ref(true);  // Start as true since initially all fields are empty


// Loading states, responsible for showing preloader for every ajax request
const loadingYears = ref(true);
const loadingBrands = ref(false);
const loadingModels = ref(false);
const loadingModifications = ref(false);

// shows that data was received and 'select' can be activated via bolded text
const stateLoadedYears = ref(false);
const stateLoadedBrands = ref(false);
const stateLoadedModels = ref(false);
const stateLoadedModifications = ref(false);

// Data for the dropdowns
const years = ref([]);
const brands = ref([]);
const models = ref([]);
const modifications = ref([]);
const generations = ref([]);
const configurator = ref([]);
const fitmentData = ref(null);


// State and reactive variables
const selectedYear = ref(null);
const selectedBrand = ref(null);
const selectedModel = ref(null);
const selectedModification = ref(null);

let requestErrorYears = "";
let requestErrorBrands = "";
let requestErrorModels = "";
let requestErrorModifications = "";
let requestErrorGenerations = "";
let requestErrorFitmentData = "";

// Reset functions
const resetYear = () => {
	selectedYear.value = null;
	resetBrand();
	updateResetState();
};

const resetBrand = () => {
	selectedBrand.value = null;
	brands.value = [];
	stateLoadedBrands.value = false;
	resetModel();
	updateResetState();
};

const resetModel = () => {
	selectedModel.value = null;
	models.value = [];
	stateLoadedModels.value = false;
	resetModification();
	updateResetState();
};

const resetModification = () => {
	selectedModification.value = null;
	modifications.value = [];
	stateLoadedModifications.value = false;
	updateResetState();
};

// Function to update reset state and emit it
const updateResetState = () => {
	isResetting.value = !selectedYear.value ||
			!selectedBrand.value ||
			!selectedModel.value ||
			!selectedModification.value;
	emit('resetState', isResetting.value);
};

// Watch for changes in the reset prop
watch(() => props.reset, (newValue) => {
	if (newValue) {
		resetYear();
		emit('reset-complete');
	}
});

// Watch for changes in selections and update reset state
watch([selectedYear, selectedBrand, selectedModel, selectedModification], () => {
	updateResetState();
});

// Watch for changes in the selected Year
watch(selectedYear, async (newYear) => {
	if (newYear) {
		loadingBrands.value = true;  // Start loading
		try {
			const response = await axios.get(`${WS_WHEEL_API_BASE_URL}/makes/?user_key=${API_KEY}&year=${newYear.slug}`);
			brands.value = response.data.data;
		} catch (error) {
			requestErrorBrands = 'Error fetching Brands: ' + error;
		} finally {
			loadingBrands.value = false;  // End loading
			stateLoadedBrands.value = true;  // Data is received
			resetModel();
		}
	}
});

// Watch for changes in the selected Brand
watch(selectedBrand, async (newBrand) => {
	if (newBrand) {
		loadingModels.value = true;  // Start loading
		try {
			const response = await axios.get(`${WS_WHEEL_API_BASE_URL}/models/?user_key=${API_KEY}&make=${newBrand.slug}&year=${selectedYear.value.slug}`);
			models.value = response.data.data;
		} catch (error) {
			requestErrorModels = 'Error fetching Models: ' + error;
		} finally {
			loadingModels.value = false;  // End loading
			stateLoadedModels.value = true;   // Data is received
			resetModification();
		}
	}
});

// Watch for changes in the selected Model
watch(selectedModel, async (newModel) => {
	if (newModel) {
		loadingModifications.value = true;  // Start loading
		try {
			const response = await axios.get(`${WS_WHEEL_API_BASE_URL}/modifications/?user_key=${API_KEY}&make=${selectedBrand.value.slug}&year=${selectedYear.value.slug}&model=${newModel.slug}`);
			modifications.value = response.data.data;
		} catch (error) {
			requestErrorModifications = 'Error fetching Modifications: ' + error;
		} finally {
			loadingModifications.value = false;  // End loading
			stateLoadedModifications.value = true;  // Data is received

			//Get related configurator generations if any
			try {
				const responseGenerations = await axios.get(`/ws_web/configurator/data/resource/?make=${selectedBrand.value.slug}&model=${newModel.slug}&resource=generation`);
				// example: https://www.wheel-size.com/configurator/data/resource/?make=audi&model=a1&resource=generation
				if (responseGenerations.data && responseGenerations.data.length > 0) {
					// temporary solution
					// TODO redo this
					// request should get actual generation by year make model
					generations.slug = responseGenerations.data[0].id;
					console.log(responseGenerations.data);
					console.log(generations.slug);
					if (generations.slug) {
						try {
							// example: https://www.wheel-size.com/configurator/data/audi/a1/gb-2018-now/
							const responseConfigurator = await axios.get(`/ws_web/configurator/data/${selectedBrand.value.slug}/${newModel.slug}/${generations.slug}/`);
							configurator.value = responseConfigurator.data;
							emit('dataConfiguratorReceived', responseConfigurator.data)
							console.log(responseConfigurator.data);
							console.log(configurator.value);
						} catch (error) {
							console.error('Error fetching configurator data:', error);
							// Handle configurator data error (e.g., show a message to the user)
						}
					}
				} else {
					console.log('No generations data available');
					// Handle case when no generations data is available
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					console.log('Generations data not found for this model');
					// Handle 404 error (e.g., show a message to the user or use default values)
				} else {
					console.error('Error fetching Generations:', error);
					requestErrorGenerations = 'Error fetching Generations: ' + error;
				}
				// Optionally, you can still emit an event or update the UI to reflect that no data is available
				// emit('dataReceived', null);
			}
		}
	}
});

// Watch for changes in the selected Modification
watch(selectedModification, async (newModification) => {

	if (newModification) {
		//loadingModels.value = true;  // Start loading
		try {
			const response = await axios.get(`${WS_WHEEL_API_BASE_URL}/search/by_model/?user_key=${API_KEY}&make=${selectedBrand.value.slug}&year=${selectedYear.value.slug}&model=${selectedModel.value.slug}&modification=${newModification.slug}`);

			fitmentData.value = response.data.data;
			emit('dataFitmentReceived', response.data.data);
			console.log(response.data.data);

		} catch (error) {
			requestErrorFitmentData = 'Error fetching Fitment data: ' + error;
		} finally {
			//loadingModels.value = false;  // End loading
			//stateLoadedModels.value = true;   // Data is received
		}
	}
});

// Fetch years on mount
onMounted(async () => {
	try {
		const response = await axios.get(`${WS_WHEEL_API_BASE_URL}/years/?user_key=${API_KEY}`);
		years.value = response.data.data;
	} catch (error) {
		requestErrorYears = 'Error fetching years: ' + error;
	} finally {
		loadingYears.value = false;
		stateLoadedYears.value = true;
		updateResetState();
	}
});
</script>

<template>
	<form @submit.prevent>
		<label for="vehicle" class="p-1 block text-sm font-medium leading-6 text-gray-900">Select Vehicle</label>
		<div class="my-1">
			<div class="px-1 mx-auto max-w-2xl">
				<KeepAlive>
					<BaseListbox
							placeholder="Year"
							v-model="selectedYear"
							:options="years"
							:error="requestErrorYears"
							:preloader="loadingYears"
							:state-loaded="stateLoadedYears"
							:reset="reset"
							@reset="resetYear"
					/>
				</KeepAlive>

				<BaseListbox
						placeholder="Make"
						v-model="selectedBrand"
						:options="brands"
						:error="requestErrorBrands"
						:preloader="loadingBrands"
						:state-loaded="stateLoadedBrands"
						:reset="reset"
						@reset="resetBrand"
				/>

				<BaseListbox
						placeholder="Model"
						v-model="selectedModel"
						:options="models"
						:error="requestErrorModels"
						:preloader="loadingModels"
						:state-loaded="stateLoadedModels"
						:reset="reset"
						@reset="resetModel"
				/>

				<BaseListbox
						placeholder="Modification"
						v-model="selectedModification"
						:options="modifications"
						:error="requestErrorModifications"
						:preloader="loadingModifications"
						:state-loaded="stateLoadedModifications"
						:reset="reset"
						@reset="resetModification"
				/>
			</div>

<!--			<h3 class="text-sm font-medium text-red-800">{{ fitmentData }}</h3>-->

		</div>
	</form>
</template>