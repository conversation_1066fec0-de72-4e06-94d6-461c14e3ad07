<script setup>
import {computed, watch} from "vue";
import {
	Listbox,
	ListboxButton, ListboxLabel,
	ListboxOption,
	ListboxOptions,
} from "@headlessui/vue";
import {CheckIcon, ChevronUpDownIcon, XCircleIcon} from '@heroicons/vue/20/solid'


// Define properties for this component
const props = defineProps({
	options: Array,  // Options to be displayed
	modelValue: [String, Number, Array, Object],  // Value that's currently selected
	placeholder: {
		type: String,
		default: "Select option",  // Default placeholder text
	},
	multiple: <PERSON><PERSON><PERSON>,  // Whether multiple selections are allowed
	error: String,  // Error message
	preloader: <PERSON><PERSON><PERSON>, // shows preloader for ajax requests
	stateLoaded: <PERSON><PERSON><PERSON>, // shows that data was received and 'select' can be activated
	reset: <PERSON><PERSON><PERSON>,
});

// Define events that this component emits
const emit = defineEmits(["update:modelValue", "reset"]);

// Compute the label to be displayed based on the currently selected value(s)
const label = computed(() => {
	if (props.options)
		return props.options
				.filter(option => {
					if (Array.isArray(props.modelValue)) {
						return props.modelValue.includes(option.slug);
					}

					return props.modelValue === option;
				})
				.map(option => option.name)
				.join(", ");
});

// Watch for changes in the reset prop
watch(() => props.reset, (newValue) => {
	if (newValue) {
		emit('update:modelValue', null);
		emit('reset');
	}
});
</script>
<template>
	<Listbox
			:model-value="props.modelValue"
			:multiple="props.multiple"
			@update:modelValue="value => emit('update:modelValue', value)"
	>
		<ListboxLabel class="block text-sm font-medium leading-6 text-gray-900"></ListboxLabel>
		<div class="flex mb-3">
			<div class="relative mt-1 flex-1">
				<ListboxButton
						class="relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 sm:text-sm sm:leading-6">
					<span class="flex items-center">
					  <!--<img :src="innerSelected.avatar" alt="" class="h-5 w-5 flex-shrink-0 rounded-full" />-->
					  <span v-if="label" class="ml-3 block truncate">{{ label }}</span>
					  <span v-else class="text-gray-500 ml-3 block" :class="{ 'font-bold': props.stateLoaded }">{{
							  props.placeholder
						  }}
					  </span>
					</span>
					<span class="pointer-events-none absolute inset-y-0 right-0 ml-3 flex items-center pr-2">
					  <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true"/>
					</span>
				</ListboxButton>

				<transition leave-active-class="transition ease-in duration-100" leave-from-class="opacity-100"
							leave-to-class="opacity-0">

					<ListboxOptions
							class="absolute z-10 mt-1 max-h-56 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
					>
						<ListboxOption
								v-for="option in props.options"
								:key="option.name"
								v-slot="{active, selected}"
								:value="option"
								as="template"
						>
							<li :class="[active ? 'bg-indigo-600 text-white' : 'text-gray-900', 'relative cursor-default select-none py-2 pl-3 pr-9']">
								<div class="flex items-center">
									<!--<img :src="innerSelected.avatar" alt="" class="h-5 w-5 flex-shrink-0 rounded-full" />-->
									<span :class="[selected ? 'font-semibold' : 'font-normal', 'ml-3 block truncate']">{{
											option.name
										}}</span>
								</div>
								<span v-if="selected"
									  :class="[active ? 'text-white' : 'text-indigo-600', 'absolute inset-y-0 right-0 flex items-center pr-4']">
                <CheckIcon class="h-5 w-5" aria-hidden="true"/>
              </span>
							</li>
						</ListboxOption>
					</ListboxOptions>
				</transition>

				<!-- if error was received -->
				<div class="rounded-md bg-red-50 p-4 mt-1" v-if="props.error">
					<div class="flex">
						<div class="flex-shrink-0">
							<XCircleIcon class="h-5 w-5 text-red-400" aria-hidden="true"/>
						</div>
						<div class="ml-3">
							<h3 class="text-sm font-medium text-red-800">{{ props.error }}</h3>
						</div>
					</div>
				</div>

			</div>
			<div class="min-w-[32px] mt-2 ml-1">
				<div class="spinner" v-if="props.preloader">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
					</svg>
				</div>
			</div>

		</div>
	</Listbox>
</template>


