# TailwindCSS Development Workflow Guide
## "Add Class → See Result" Development Process

### 📋 **Overview**
This guide provides a step-by-step workflow for developing with TailwindCSS v4.1 in the wheel-size-services project, focusing on efficient class usage, validation, and real-time development.

**Last Updated**: 2025-06-19 - Updated for current project configuration

---

## 🏗️ **Project Configuration Overview**

The wheel-size-services project uses **three separate TailwindCSS configurations**:

1. **Portal Management Area** (Django templates)
   - **Production**: `src/apps/portal/static/portal/css/tailwind.prod.js` → `tailwind.min.css`
   - **Development**: `src/apps/portal/static/portal/css/tailwind.dev.js` → `tailwind-debug.css`

2. **Vue.js Widget (Finder-v2)**
   - **Configuration**: `src/apps/widgets/finder_v2/app/tailwind.config.js`
   - **Output**: Built into widget bundle

3. **Legacy Configuration** (Reference only)
   - **File**: `src/apps/portal/static/portal/css/tailwind.config.js`
   - **Status**: Not used in builds, kept for reference

---

## 🚀 **Quick Start: "Add Class → See Result" Workflow**

### **Step 1: Start Watch Mode**
```bash
# From project root - Portal development
npm run dev:watch

# For finder-v2 widget development
npm run tailwind:finder-v2:dev
```

### **Step 2: Edit Templates**
Add TailwindCSS classes to Django templates or Vue components:
```html
<!-- Django Template Example: Success notifications -->
<div class="rounded-md bg-green-50 p-4 mb-6">
    <div class="flex">
        <div class="shrink-0">
            <svg class="size-5 text-green-400" viewBox="0 0 20 20">
                <!-- SVG content -->
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-green-800">Success!</p>
        </div>
    </div>
</div>

<!-- Vue Component Example: Modern utilities -->
<div class="grid sm:grid-cols-3 divide-y sm:divide-y-0 sm:divide-x divide-gray-200">
    <div class="p-6">Content</div>
</div>
```

### **Step 3: See Results Instantly**
- Save the template/component file
- Watch mode automatically rebuilds CSS (~200ms)
- Refresh browser to see changes
- No manual build steps needed!

### **Step 4: Validate Classes (Optional)**
```bash
# Check if specific classes are generated in portal CSS
grep "size-5\|shrink-0\|ring-1" src/apps/portal/static/portal/css/tailwind-debug.css
```

---

## 🛠️ **Development Commands Reference**

### **NPM Scripts (Root Level)**
```bash
# Portal Development (Django templates)
npm run dev:watch                       # Watch mode - builds tailwind-debug.css
npm run tailwind:portal:build           # Production build - builds tailwind.min.css

# Widget Development (Vue.js)
npm run tailwind:finder-v2:dev          # Finder-v2 development server
npm run tailwind:finder-v2:build        # Finder-v2 production build

# Combined Builds
npm run build:all                       # Build both portal and finder-v2
```

### **Manual Build Commands**
```bash
# Portal CSS builds (using npx)
npx tailwindcss -c src/apps/portal/static/portal/css/tailwind.dev.js \
  -i src/apps/portal/static/portal/css/tailwind.css \
  -o src/apps/portal/static/portal/css/tailwind-debug.css

npx tailwindcss -c src/apps/portal/static/portal/css/tailwind.prod.js \
  -i src/apps/portal/static/portal/css/tailwind.css \
  -o src/apps/portal/static/portal/css/tailwind.min.css --minify

# Widget builds (from widget directory)
cd src/apps/widgets/finder_v2/app && npm run dev
cd src/apps/widgets/finder_v2/app && npm run build
```

### **Validation Commands**
```bash
# Check if classes are available in generated CSS
grep "size-5\|shrink-0\|ring-1" src/apps/portal/static/portal/css/tailwind-debug.css
grep "bg-green-50\|text-green-800" src/apps/portal/static/portal/css/tailwind.min.css

# Verify file sizes
ls -la src/apps/portal/static/portal/css/tailwind*.css
```

---

## 📁 **Template File Locations & Content Scanning**

### **Portal Configuration Scanning**
The portal TailwindCSS configurations scan these directories:

**Production & Development Configs**:
```
"../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}"     # Portal templates
"../../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}"  # All templates
"../js/**/*.{js,ts}"                                       # Portal JavaScript
"../../../**/*.{html,js,ts,jsx,tsx,vue}"                   # All project files
"./**/*.{html,js,ts,jsx,tsx,vue,php,twig,erb,blade}"       # Wide pattern
```

**Actual Directory Structure**:
```
src/templates/                          # Main Django templates
├── portal/                            # Portal-specific templates
├── admin/                             # Django admin templates
├── widgets/                           # Widget templates (including finder_v2)
└── registration/                      # User registration templates

src/apps/widgets/*/templates/           # Individual widget templates
├── finder/templates/                  # Finder v1 widget
├── finder_v2/templates/               # Finder v2 widget (Django config pages)
├── calc/templates/                    # Calculator widget
└── embed/templates/                   # Embed widget

src/apps/portal/templates/              # Portal app templates
```

### **Widget Configuration Scanning**
**Finder-v2 Widget** (`src/apps/widgets/finder_v2/app/tailwind.config.js`):
```
"./index.html"                         # Widget HTML entry point
"./src/**/*.{vue,js,ts,jsx,tsx}"       # Vue components and JavaScript
```

### **Supported File Types**
**Portal Configurations**:
- `.html` files (Django templates)
- `.js`, `.ts` files (JavaScript/TypeScript)
- `.jsx`, `.tsx` files (React components)
- `.vue` files (Vue components)
- `.php`, `.md`, `.twig`, `.erb`, `.blade` files (Additional templates)

**Widget Configuration**:
- `.vue` files (Vue 3 components)
- `.js`, `.ts`, `.jsx`, `.tsx` files (JavaScript/TypeScript)

---

## 🎨 **Available Utility Classes**

### **Modern TailwindCSS v4.1 Utilities (Recently Added)**
```html
<!-- Modern Size Utilities -->
<div class="size-5">Square 5x5</div>
<div class="size-20">Square 20x20</div>

<!-- Flexbox Utilities -->
<div class="shrink-0">No shrinking</div>

<!-- Ring Utilities -->
<button class="ring-1 ring-inset ring-gray-300">Button with ring</button>

<!-- Divide Utilities -->
<div class="divide-y divide-gray-200">
    <div>Item 1</div>
    <div>Item 2</div>
</div>

<!-- Responsive Divide -->
<div class="grid sm:grid-cols-3 divide-y sm:divide-y-0 sm:divide-x divide-gray-200">
    <div>Column 1</div>
    <div>Column 2</div>
    <div>Column 3</div>
</div>

<!-- Accessibility -->
<span class="sr-only">Screen reader only text</span>

<!-- Shadow Variants -->
<div class="shadow-xs">Extra small shadow</div>
```

### **Success Notification Classes (Finder-v2 Specific)**
```html
<!-- Success Notification -->
<div class="rounded-md bg-green-50 p-4 mb-6">
    <div class="flex">
        <div class="shrink-0">
            <svg class="size-5 text-green-400"><!-- icon --></svg>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-green-800">Success title</p>
            <div class="mt-2 text-sm text-green-700">Success message</div>
        </div>
    </div>
</div>

<!-- Error/Warning/Info variants -->
<div class="bg-red-50 text-red-800">Error notification</div>
<div class="bg-yellow-50 text-yellow-800">Warning notification</div>
<div class="bg-blue-50 text-blue-800">Info notification</div>
```

### **Brand Colors (Auto-Generated)**
```html
<!-- Primary Colors (Red/Orange Brand) -->
<div class="bg-ws-primary-50">Lightest</div>
<div class="bg-ws-primary-500">Medium</div>
<div class="bg-ws-primary-700">Dark (Main brand)</div>

<!-- Secondary Colors (Blue/Gray Brand) -->
<div class="bg-ws-secondary-50">Lightest</div>
<div class="bg-ws-secondary-500">Medium</div>
<div class="bg-ws-secondary-700">Dark</div>

<!-- Text and Border variants -->
<span class="text-ws-primary-700 border-ws-primary-300">Brand styling</span>
```

### **State Colors (Auto-Generated)**
```html
<!-- Success, Warning, Danger states -->
<div class="bg-success-50 text-success-700">Success</div>
<div class="bg-warning-50 text-warning-700">Warning</div>
<div class="bg-danger-50 text-danger-700">Error</div>
```

---

## 🔍 **Class Validation and Debugging**

### **Validate Utility Class Generation**
```bash
# Check if modern utilities are available
grep "size-5\|shrink-0\|ring-1\|divide-y" src/apps/portal/static/portal/css/tailwind-debug.css

# Check success notification colors
grep "bg-green-50\|text-green-800" src/apps/portal/static/portal/css/tailwind.min.css

# Verify responsive utilities
grep "sm:grid-cols-3\|sm:divide-x" src/apps/portal/static/portal/css/tailwind-debug.css
```

### **Check Build Statistics**
```bash
# Check file sizes
ls -la src/apps/portal/static/portal/css/tailwind*.css

# Expected output:
# tailwind-debug.css: ~150K (development, unminified)
# tailwind.min.css: ~45K (production, minified)
# tailwind.css: ~10K (source file)

# Count available classes
grep -o '\.[a-zA-Z0-9_-]*{' src/apps/portal/static/portal/css/tailwind-debug.css | wc -l
```

### **Debug Missing Classes**

**Problem**: Class doesn't appear in generated CSS

**Solution Steps**:

1. **Identify which configuration to check**:
   ```bash
   # For portal/Django templates - check these configs:
   # - src/apps/portal/static/portal/css/tailwind.prod.js (production)
   # - src/apps/portal/static/portal/css/tailwind.dev.js (development)

   # For finder-v2 widget - check:
   # - src/apps/widgets/finder_v2/app/tailwind.config.js
   ```

2. **Check template usage**:
   ```bash
   grep -r "your-missing-class" src/templates/
   grep -r "your-missing-class" src/apps/widgets/finder_v2/app/src/
   ```

3. **Verify safelist in correct config**:
   ```bash
   # For portal classes
   grep "your-missing-class" src/apps/portal/static/portal/css/tailwind.prod.js
   grep "your-missing-class" src/apps/portal/static/portal/css/tailwind.dev.js
   ```

4. **Add to safelist if needed**:
   ```javascript
   // In tailwind.prod.js or tailwind.dev.js
   safelist: [
     'your-missing-class',
     // ... existing classes
   ]

   // OR add to raw patterns:
   { raw: 'your-missing-class other-class' }
   ```

5. **Rebuild and verify**:
   ```bash
   # Rebuild development CSS
   npx tailwindcss -c src/apps/portal/static/portal/css/tailwind.dev.js \
     -i src/apps/portal/static/portal/css/tailwind.css \
     -o src/apps/portal/static/portal/css/tailwind-debug.css

   # Rebuild production CSS
   npm run tailwind:portal:build

   # Verify class is now available
   grep "your-missing-class" src/apps/portal/static/portal/css/tailwind-debug.css
   ```

---

## 🏗️ **Production Deployment Workflow**

### **Pre-Deployment Checklist**
- [ ] Build production CSS: `npm run tailwind:portal:build`
- [ ] Build widget assets: `npm run tailwind:finder-v2:build`
- [ ] Verify critical classes: `grep "size-5\|bg-green-50" src/apps/portal/static/portal/css/tailwind.min.css`
- [ ] Check file sizes: `ls -la src/apps/portal/static/portal/css/tailwind*.css`
- [ ] Test in staging environment
- [ ] Run Django collectstatic: `python manage.py collectstatic`

### **Production Build Commands**
```bash
# Build all production assets
npm run build:all

# Or build individually:
npm run tailwind:portal:build        # Portal CSS
npm run tailwind:finder-v2:build     # Widget assets

# Verify production builds
ls -la src/apps/portal/static/portal/css/tailwind.min.css
ls -la src/apps/widgets/finder_v2/app/dist/

# Expected sizes:
# tailwind.min.css: ~45K (minified portal CSS)
# finder_v2/app/dist/: Widget bundle files
```

### **Django Integration**
The Django context processor automatically selects the correct CSS file:

```python
# In src/apps/portal/context_processors.py
def tailwind_css(request):
    use_debug_css = getattr(settings, 'USE_TAILWIND_DEBUG_CSS', False)
    return {
        'tailwind_css_file': 'portal/css/tailwind-debug.css' if use_debug_css else 'portal/css/tailwind.min.css',
        'use_tailwind_debug': use_debug_css,
    }
```

```html
<!-- In Django templates -->
{% load static %}
<link href="{% static tailwind_css_file %}" rel="stylesheet">
<!-- Automatically uses tailwind.min.css in production, tailwind-debug.css in development -->
```

---

## ⚡ **Performance Optimization**

### **Build Time Optimization**
- **Watch mode**: ~140ms rebuild time
- **Full build**: ~200ms for production CSS
- **Content scanning**: Optimized paths for fast template detection

### **CSS Bundle Optimization**
- **Auto-purging**: Only used classes included
- **Minification**: Production CSS compressed (~41K)
- **Safelist approach**: Predictable utility generation

### **Development Speed**
- **Real-time feedback**: Instant CSS updates in watch mode
- **Class validation**: Quick verification of utility generation
- **Debugging tools**: Fast identification of build issues

---

## 🐛 **Troubleshooting Guide**

### **Common Issues**

**Issue**: "Watch mode not detecting changes"
```bash
# Solution: Restart watch mode
# Ctrl+C to stop, then:
npm run dev:watch
```

**Issue**: "Class not generated despite being in templates"
```bash
# Solution: Check correct configuration and rebuild
# For portal classes:
grep "missing-class" src/apps/portal/static/portal/css/tailwind.prod.js
npm run tailwind:portal:build

# For widget classes:
grep "missing-class" src/apps/widgets/finder_v2/app/tailwind.config.js
npm run tailwind:finder-v2:build
```

**Issue**: "CSS file not updating in browser"
```bash
# Solution: Hard refresh browser and check file timestamps
# Ctrl+F5 (or Cmd+Shift+R on Mac)
ls -la src/apps/portal/static/portal/css/tailwind*.css
```

**Issue**: "Modern utilities (size-5, shrink-0) not working"
```bash
# Solution: These were recently added - rebuild CSS
npm run tailwind:portal:build
npx tailwindcss -c src/apps/portal/static/portal/css/tailwind.dev.js \
  -i src/apps/portal/static/portal/css/tailwind.css \
  -o src/apps/portal/static/portal/css/tailwind-debug.css

# Verify they're now available:
grep "size-5\|shrink-0" src/apps/portal/static/portal/css/tailwind-debug.css
```

**Issue**: "Success notification colors not appearing"
```bash
# Solution: These classes only appear when Django messages are active
# Check if they're in the CSS:
grep "bg-green-50\|text-green-800" src/apps/portal/static/portal/css/tailwind.min.css

# If missing, they're in the safelist - rebuild:
npm run tailwind:portal:build
```

**Issue**: "Wrong configuration file being used"
```bash
# Solution: Verify which config is used for builds
# Development uses: tailwind.dev.js → tailwind-debug.css
# Production uses: tailwind.prod.js → tailwind.min.css
# Widget uses: finder_v2/app/tailwind.config.js

# Check package.json for exact commands:
cat package.json | grep tailwind
```

### **Verification Commands**
```bash
# Check if TailwindCSS is properly installed
npm list tailwindcss

# Verify all config files exist
ls -la src/apps/portal/static/portal/css/tailwind.*.js
ls -la src/apps/widgets/finder_v2/app/tailwind.config.js

# Check source CSS exists
ls -la src/apps/portal/static/portal/css/tailwind.css

# Test builds manually
npm run tailwind:portal:build
npm run tailwind:finder-v2:build

# Verify output files
ls -la src/apps/portal/static/portal/css/tailwind*.css
ls -la src/apps/widgets/finder_v2/app/dist/
```

---

## 📚 **Best Practices**

### **Class Naming Conventions**
- **Brand colors**: Use `ws-primary-*` and `ws-secondary-*`
- **State colors**: Use `success-*`, `warning-*`, `danger-*`
- **Descriptive patterns**: `bg-`, `text-`, `border-` prefixes
- **Consistent spacing**: Use standard scale (50, 100, 200, 300, etc.)

### **Development Workflow**
1. **Start with watch mode**: Always use `npm run dev:watch` during development
2. **Use debug CSS**: Develop with unminified CSS for easier debugging
3. **Validate regularly**: Check class generation with validation tools
4. **Build production last**: Only build minified CSS for deployment

### **Template Organization**
```html
<!-- Good: Organized by purpose -->
<div class="container mx-auto py-8">              <!-- Layout -->
  <div class="bg-white border border-gray-200     <!-- Colors -->
              rounded-lg shadow-sm                 <!-- Effects -->
              p-6 mb-4">                          <!-- Spacing -->
    Content here
  </div>
</div>

<!-- Avoid: Random order -->
<div class="py-8 bg-white container border p-6 mx-auto">
  Content here
</div>
```

### **Performance Considerations**
- **Use safelist wisely**: Only include classes you actually need
- **Monitor bundle size**: Check with `./scripts/tailwind-dev.sh stats`
- **Test purging**: Ensure unused classes are removed in production
- **Optimize content paths**: Include only relevant template directories

---

## 🔄 **Integration with Existing Workflow**

### **Django Development**
```bash
# Standard Django development with TailwindCSS
# Terminal 1: Start TailwindCSS watch mode
npm run dev:watch

# Terminal 2: Start Django development server
docker-compose up web
# OR
python manage.py runserver

# Terminal 3: Make changes and test
vim src/templates/portal/base.html
# Save file → CSS rebuilds automatically → Refresh browser
```

### **Widget Development**
```bash
# For widget-specific development
# Portal CSS (always running)
npm run dev:watch

# Finder-v2 widget (if needed)
npm run dev:finder-v2

# Or use the deployment script for finder-v2
./deploy-finder-v2.sh
```

### **Git Workflow**
```bash
# Before committing changes
npm run tailwind:portal:build      # Build production CSS
./scripts/tailwind-dev.sh stats    # Verify bundle size
git add .
git commit -m "feat: update portal styling with TailwindCSS"
```

---

## 📖 **References**

### **Internal Documentation**
- `docs/development/tailwindcss-v4-roadmap.md` - Implementation roadmap
- `docs/development/tailwindcss-v4-implementation-guide.md` - Technical details
- `docs/development/tailwindcss-v4-implementation-summary-updated.md` - Progress summary

### **Configuration Files**
- `src/apps/portal/static/portal/css/tailwind.prod.js` - Production configuration (builds tailwind.min.css)
- `src/apps/portal/static/portal/css/tailwind.dev.js` - Development configuration (builds tailwind-debug.css)
- `src/apps/widgets/finder_v2/app/tailwind.config.js` - Widget configuration (Vue.js components)
- `src/apps/portal/static/portal/css/tailwind.config.js` - Legacy configuration (reference only)
- `src/apps/portal/static/portal/css/tailwind.css` - Source CSS with theme variables
- `package.json` - NPM scripts for builds

### **External Resources**
- [TailwindCSS v4.1 Documentation](https://tailwindcss.com/docs)
- [TailwindCSS Utility-First Fundamentals](https://tailwindcss.com/docs/utility-first)
- [TailwindCSS Configuration](https://tailwindcss.com/docs/configuration)

---

## 📝 **Recent Updates (2025-06-19)**

### **Configuration Changes**
- ✅ **Fixed build configuration mismatch**: Updated production (`tailwind.prod.js`) and development (`tailwind.dev.js`) configs
- ✅ **Added modern TailwindCSS v4.1 utilities**: `size-*`, `shrink-0`, `ring-*`, `divide-*`, `sr-only`, `shadow-xs`
- ✅ **Added finder-v2 specific classes**: Success notification colors, responsive utilities
- ✅ **Verified content scanning patterns**: All template directories properly included

### **Build Process Updates**
- ✅ **Corrected build commands**: `npm run tailwind:portal:build` uses `tailwind.prod.js`
- ✅ **Fixed development workflow**: `npm run dev:watch` uses `tailwind.dev.js`
- ✅ **Updated file outputs**: Production → `tailwind.min.css`, Development → `tailwind-debug.css`

### **Class Availability**
- ✅ **Modern utilities working**: `size-5`, `shrink-0`, `ring-1`, `divide-y`, `sm:grid-cols-3`
- ✅ **Success notifications ready**: `bg-green-50`, `text-green-800`, `rounded-md`, `mb-6`
- ✅ **Responsive design enabled**: All `sm:*` variants available

---

**Status**: ✅ **Updated for Current Configuration** | 🔄 **Active Development Tool**
**Last Updated**: 2025-06-19 - Comprehensive configuration review and update
**Next Steps**: Continue development with verified TailwindCSS setup