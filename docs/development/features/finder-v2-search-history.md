# Finder V2 Search History Feature

## Overview

The search history feature for the Finder V2 widget provides users with the ability to store, view, and re-execute their recent vehicle searches. This feature enhances user experience by eliminating the need to repeatedly enter search parameters for frequently used searches.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Core Components](#core-components)
- [Configuration](#configuration)
- [Usage Examples](#usage-examples)
- [API Reference](#api-reference)
- [Storage Schema](#storage-schema)
- [Testing](#testing)
- [Accessibility](#accessibility)
- [Error Handling](#error-handling)
- [Performance Considerations](#performance-considerations)
- [Troubleshooting](#troubleshooting)

## Architecture

### System Design

The search history feature is implemented as a modular Vue.js composable that integrates seamlessly with the existing Finder V2 widget architecture. The feature leverages browser localStorage for persistence and provides a reactive user interface for managing search history.

```mermaid
graph TD
    A[User Submits Search] --> B[Finder Store searchByVehicle]
    B --> C[API Call Executed]
    C --> D[Search Success]
    D --> E[Search History Composable]
    E --> F[Store in localStorage]
    F --> G[Update UI State]
    
    H[User Clicks History Item] --> I[Search History Panel]
    I --> J[Populate Form Fields]
    J --> K[Trigger Search Execution]
    K --> B
```

### Data Flow

1. **Search Capture**: Successful searches are automatically captured by the finder store
2. **Storage**: Search parameters are serialized and stored in browser localStorage
3. **Display**: Recent searches are displayed in a collapsible panel above the search form
4. **Execution**: Users can click on history items to re-execute searches
5. **Management**: Users can remove individual searches or clear all history

## Core Components

### 1. Search History Composable (`useSearchHistory.js`)

The core composable that manages localStorage operations and provides reactive state management.

**Key Features:**
- localStorage operations with error handling
- Reactive search history state
- Search serialization/deserialization
- Storage limits and cleanup
- Configuration management

**Location:** `src/apps/widgets/finder_v2/app/src/composables/useSearchHistory.js`

### 2. Finder Store Integration

The existing finder store has been extended to include search history functionality.

**Key Additions:**
- `executeSearchFromHistory(searchId)` - Execute a search from history
- `getSearchHistory()` - Get the search history instance
- `populateFormFromSearch(searchItem)` - Populate form fields from history

**Location:** `src/apps/widgets/finder_v2/app/src/stores/finder.js`

### 3. Search History Panel Component (`SearchHistoryPanel.vue`)

A Vue component that provides the user interface for viewing and managing search history.

**Key Features:**
- Collapsible panel interface
- Individual search item display
- Remove individual searches
- Clear all functionality
- Show more/less options
- Accessibility support

**Location:** `src/apps/widgets/finder_v2/app/src/components/SearchHistoryPanel.vue`

### 4. Main Widget Integration

The `VehicleSearch.vue` component has been updated to include the search history panel.

**Integration Points:**
- Import and register `SearchHistoryPanel` component
- Render panel above search form
- Conditional display based on history availability

**Location:** `src/apps/widgets/finder_v2/app/src/components/VehicleSearch.vue`

## Configuration

### Widget Configuration

Search history can be configured through the widget configuration system:

```javascript
{
  searchHistory: {
    enabled: true,           // Enable/disable feature
    maxItems: 10,           // Maximum stored searches
    displayItems: 5,        // Items shown by default
    autoExpand: false,      // Auto-expand panel on load
    showTimestamps: true    // Show relative timestamps
  }
}
```

### Default Configuration

If no configuration is provided, the following defaults are used:

```javascript
{
  enabled: true,
  maxItems: 10,
  displayItems: 5,
  autoExpand: false,
  showTimestamps: true
}
```

### Dynamic Configuration

Configuration can be updated at runtime:

```javascript
const searchHistory = useSearchHistory('widget-id')
searchHistory.configure({
  maxItems: 15,
  displayItems: 7,
  enabled: true
})
```

## Usage Examples

### Basic Implementation

```vue
<template>
  <div class="widget">
    <!-- Search History Panel -->
    <SearchHistoryPanel />
    
    <!-- Search Form -->
    <VehicleSearch />
  </div>
</template>

<script>
import SearchHistoryPanel from './components/SearchHistoryPanel.vue'
import VehicleSearch from './components/VehicleSearch.vue'

export default {
  components: {
    SearchHistoryPanel,
    VehicleSearch
  }
}
</script>
```

### Programmatic Usage

```javascript
import { useSearchHistory } from './composables/useSearchHistory.js'

// Initialize search history
const searchHistory = useSearchHistory('my-widget-id')

// Add a search
searchHistory.addSearch({
  flowType: 'primary',
  year: '2021',
  make: 'Toyota',
  model: 'Camry',
  modification: 'LE'
})

// Remove a search
searchHistory.removeSearch('search-id')

// Clear all history
searchHistory.clearHistory()

// Configure settings
searchHistory.configure({
  maxItems: 20,
  displayItems: 10
})
```

### Store Integration

```javascript
import { useFinderStore } from './stores/finder.js'

const finderStore = useFinderStore()

// Execute search from history
await finderStore.executeSearchFromHistory('search-id')

// Get search history instance
const searchHistory = finderStore.getSearchHistory()
```

## API Reference

### useSearchHistory(widgetId)

**Parameters:**
- `widgetId` (string): Unique identifier for the widget instance

**Returns:** Search history composable with the following interface:

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `searches` | `Ref<Array>` | Reactive array of search items (readonly) |
| `isEnabled` | `Ref<boolean>` | Whether search history is enabled (readonly) |
| `displaySearches` | `ComputedRef<Array>` | Searches limited by displayItems |
| `hasMoreSearches` | `ComputedRef<boolean>` | True if more searches exist beyond display limit |
| `maxItems` | `Ref<number>` | Maximum number of stored searches (readonly) |
| `displayItems` | `Ref<number>` | Number of searches shown by default (readonly) |
| `isLocalStorageAvailable` | `ComputedRef<boolean>` | Whether localStorage is available |

#### Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `addSearch(searchParams)` | `searchParams: Object` | Add a new search to history |
| `removeSearch(searchId)` | `searchId: string` | Remove a specific search |
| `getSearch(searchId)` | `searchId: string` | Get a search by ID |
| `updateSearchTimestamp(searchId)` | `searchId: string` | Move search to top of list |
| `clearHistory()` | - | Clear all search history |
| `setEnabled(enabled)` | `enabled: boolean` | Enable/disable search history |
| `configure(config)` | `config: Object` | Update configuration |
| `getRelativeTime(timestamp)` | `timestamp: number` | Get human-readable relative time |

### Search Parameters Object

```typescript
interface SearchParams {
  flowType: 'primary' | 'alternative' | 'year_select'
  year?: string
  make: string
  model: string
  modification: string
  generation?: string
}
```

### Search History Item

```typescript
interface SearchHistoryItem {
  id: string              // Unique identifier
  timestamp: number       // Unix timestamp
  description: string     // Human-readable description
  flowType: string        // Search flow type
  parameters: {
    year?: string
    make: string
    model: string
    modification: string
    generation?: string
  }
}
```

## Storage Schema

### localStorage Key Format

```
finder_v2_search_history_${widgetId}
```

### Storage Structure

```json
{
  "version": "1.0",
  "enabled": true,
  "searches": [
    {
      "id": "1642678800000_abc123",
      "timestamp": 1642678800000,
      "description": "2020 BMW X5 xDrive40i",
      "flowType": "primary",
      "parameters": {
        "year": "2020",
        "make": "BMW",
        "model": "X5",
        "modification": "xDrive40i",
        "generation": ""
      }
    }
  ]
}
```

### Data Validation

The system validates stored data on retrieval:

1. **Structure Validation**: Ensures data has correct format
2. **Item Validation**: Validates individual search items
3. **Corruption Recovery**: Removes invalid entries automatically
4. **Version Compatibility**: Handles version differences gracefully

## Testing

### Unit Tests

Comprehensive unit tests are provided for the search history composable:

**Location:** `src/apps/widgets/finder_v2/app/src/composables/__tests__/useSearchHistory.test.js`

**Test Coverage:**
- Initialization with/without stored data
- Adding and removing searches
- Storage limits and cleanup
- Error handling scenarios
- Configuration changes
- localStorage unavailability
- Duplicate search handling
- Relative time formatting

### Running Tests

```bash
cd src/apps/widgets/finder_v2/app
npm run test
```

### Integration Testing

Test the complete search history workflow:

1. Perform a vehicle search
2. Verify search appears in history
3. Click on history item
4. Verify form is populated and search executes
5. Test removal and clear functionality

### Browser Testing

Test across different browsers and localStorage scenarios:

- Chrome, Firefox, Safari, Edge
- Private/incognito mode
- localStorage disabled
- Storage quota exceeded
- Corrupted localStorage data

## Accessibility

### Keyboard Navigation

Full keyboard navigation is supported:

- **Tab**: Navigate between elements
- **Enter/Space**: Activate buttons and links
- **Escape**: Close confirmation modals
- **Arrow Keys**: Navigate within lists (future enhancement)

### Screen Reader Support

Comprehensive screen reader compatibility:

- **ARIA Labels**: Descriptive labels for all interactive elements
- **Role Attributes**: Proper semantic roles (button, list, listitem)
- **Live Regions**: Announce dynamic content changes
- **Focus Management**: Proper focus handling and restoration

### Accessibility Features

- High contrast support
- Proper color contrast ratios
- Focus indicators
- Keyboard-only operation
- Screen reader announcements
- Semantic HTML structure

## Error Handling

### localStorage Errors

**Scenario**: localStorage not available or disabled

**Handling**: 
- Feature gracefully degrades
- No errors thrown to user
- Console warnings for debugging
- Search functionality continues normally

### Storage Quota Exceeded

**Scenario**: localStorage quota limit reached

**Handling**:
- Automatic cleanup of oldest entries
- Retry mechanism with reduced data
- Fallback to session-only storage
- User notification (optional)

### Data Corruption

**Scenario**: Invalid or corrupted localStorage data

**Handling**:
- Automatic data validation
- Removal of invalid entries
- Complete reset if corruption is severe
- Continuation of normal operation

### API Integration Errors

**Scenario**: Search execution from history fails

**Handling**:
- Error display to user
- History operations continue normally
- Graceful degradation
- Retry mechanisms

## Performance Considerations

### Memory Management

- **Lazy Loading**: History panel only renders when expanded
- **Memory Limits**: Configurable maximum items in memory
- **Efficient Updates**: Minimal DOM manipulation
- **Event Cleanup**: Proper event listener management

### Storage Optimization

- **Debounced Writes**: Prevent excessive localStorage operations
- **Data Compression**: Minimal data structure
- **Cleanup Strategies**: Automatic removal of old entries
- **Batch Operations**: Group related localStorage operations

### Rendering Performance

- **Virtual Scrolling**: For large history lists (future enhancement)
- **Lazy Components**: Load components on demand
- **Optimized Re-renders**: Use Vue's reactivity efficiently
- **CSS Animations**: GPU-accelerated transitions

## Troubleshooting

### Common Issues

#### Search History Not Appearing

**Symptoms**: History panel doesn't show despite having searches

**Causes**:
- Search history disabled in configuration
- localStorage not available
- No successful searches yet
- Data corruption

**Solutions**:
1. Check widget configuration: `searchHistory.enabled`
2. Verify localStorage availability in browser console
3. Perform a successful search to populate history
4. Clear localStorage data to reset: `localStorage.removeItem('finder_v2_search_history_...')`

#### Searches Not Being Saved

**Symptoms**: Performed searches don't appear in history

**Causes**:
- API search failed (only successful searches are saved)
- localStorage quota exceeded
- Feature disabled
- JavaScript errors

**Solutions**:
1. Check browser console for errors
2. Verify search completes successfully with results
3. Check localStorage quota in DevTools
4. Ensure feature is enabled in configuration

#### History Panel Not Expanding

**Symptoms**: Clicking header doesn't expand panel

**Causes**:
- JavaScript errors
- Event listeners not attached
- CSS conflicts
- Component not properly mounted

**Solutions**:
1. Check browser console for JavaScript errors
2. Verify component is properly imported and registered
3. Check for CSS conflicts affecting clickability
4. Ensure Vue component is mounted correctly

#### Performance Issues

**Symptoms**: Widget becomes slow with search history

**Causes**:
- Too many searches in history
- Memory leaks
- Inefficient rendering
- Large localStorage data

**Solutions**:
1. Reduce `maxItems` configuration
2. Clear history periodically
3. Check for memory leaks in DevTools
4. Monitor localStorage size

### Debugging

#### Enable Debug Mode

Add to browser console:

```javascript
localStorage.setItem('finder_v2_debug', 'true')
```

#### Check Search History Data

```javascript
// Get raw localStorage data
const data = localStorage.getItem('finder_v2_search_history_your-widget-id')
console.log(JSON.parse(data))

// Get composable state
const searchHistory = useSearchHistory('your-widget-id')
console.log(searchHistory.searches.value)
```

#### Monitor localStorage Events

```javascript
// Listen for localStorage changes
window.addEventListener('storage', (e) => {
  if (e.key && e.key.includes('finder_v2_search_history')) {
    console.log('Search history changed:', e)
  }
})
```

### Browser DevTools

#### Application Tab
- Check localStorage entries under `finder_v2_search_history_*`
- Monitor storage quota usage
- Clear specific entries for testing

#### Console Tab
- Look for search history related warnings/errors
- Test composable methods directly
- Monitor API calls and responses

#### Network Tab
- Verify API calls complete successfully
- Check for failed requests that might prevent history saving

## Migration and Upgrades

### Version Compatibility

The storage schema includes a version field for future compatibility:

```json
{
  "version": "1.0",
  // ... other data
}
```

### Data Migration

When upgrading storage schema:

1. **Backward Compatibility**: Support old format during transition
2. **Automatic Migration**: Convert data on first load
3. **Fallback Strategy**: Reset if migration fails
4. **User Notification**: Inform users of data changes if needed

### Breaking Changes

Document any breaking changes in widget configuration or API:

- Configuration property renames
- Method signature changes
- Storage format updates
- Required dependency updates

## Security Considerations

### Data Sanitization

- **Input Validation**: Validate all search parameters
- **XSS Prevention**: Escape user-generated descriptions
- **Data Limits**: Enforce reasonable data size limits

### Privacy Compliance

- **Local Storage Only**: No server-side storage of search history
- **User Control**: Clear functionality for privacy compliance
- **Data Retention**: Configurable retention policies
- **Opt-out Support**: Ability to disable feature entirely

### Storage Isolation

- **Widget Scoping**: Data isolated per widget instance
- **Domain Isolation**: localStorage scoped to domain
- **No Cross-Widget Access**: Widgets cannot access each other's history

## Future Enhancements

### Planned Features

1. **Search Categories**: Group searches by vehicle type or brand
2. **Export/Import**: Allow users to backup/restore search history
3. **Search Analytics**: Basic usage statistics for administrators
4. **Cloud Sync**: Optional cloud storage for cross-device sync
5. **Advanced Filtering**: Filter history by date, make, model, etc.
6. **Search Suggestions**: Autocomplete based on history
7. **Batch Operations**: Select multiple searches for bulk actions

### Performance Improvements

1. **Virtual Scrolling**: Handle large search histories efficiently
2. **Background Cleanup**: Automatic maintenance of storage
3. **Compression**: Compress stored data to save space
4. **Indexing**: Add search indexing for faster lookups

### UX Enhancements

1. **Drag and Drop**: Reorder search history
2. **Search Filtering**: Filter displayed history
3. **Grouping Options**: Group by date, vehicle type, etc.
4. **Visual Indicators**: Show search freshness, popularity
5. **Quick Actions**: Pin favorite searches, add notes

## Contributing

### Code Style

Follow the existing Vue.js and JavaScript patterns:

- Use Composition API for new components
- Follow ESLint configuration
- Write comprehensive tests for new features
- Document all public APIs

### Testing Requirements

- Unit tests for all new composables
- Component tests for UI interactions
- Integration tests for store methods
- Accessibility tests for new UI elements

### Documentation

- Update this documentation for new features
- Add JSDoc comments for all public methods
- Include usage examples for complex features
- Document breaking changes in migration guide

---

**Last Updated**: July 19, 2025
**Version**: 1.0.0
**Maintainer**: Development Team