# Testing Procedures for Wheel-Size Services

This document outlines testing procedures and tools for the wheel-size-services Django application, including automated testing scripts for authenticated endpoints.

## Authenticated Testing Script

### Overview

The `scripts/test_authenticated_requests.py` script provides automated testing capabilities for authenticated endpoints in the wheel-size-services application. It handles user creation, authentication, CSRF token management, and session handling automatically.

### Features

- **Automatic User Management**: Creates test users with appropriate permissions
- **Session Management**: Handles authentication and maintains session cookies
- **CSRF Protection**: Automatically extracts and includes CSRF tokens
- **Error Handling**: Comprehensive error reporting and debugging output
- **Reusable Classes**: Can be imported and used in other test scripts

### Quick Start

#### Run All Tests
```bash
# From project root
python scripts/test_authenticated_requests.py
```

#### Use as a Module
```python
from scripts.test_authenticated_requests import AuthenticatedTester

# Create tester instance
tester = AuthenticatedTester()

# Setup test user and login
tester.setup_test_user()
tester.login()

# Test GET request
response = tester.get('/widget/finder-v2/config/')
print(f"Status: {response.status_code}")

# Test form submission
form_data = {
    'config-name': 'My Test Config',
    'config-lang': 'en',
    'theme-theme_name': 'light'
}
response = tester.post_form('/widget/finder-v2/config/', form_data)
```

### Common Testing Scenarios

#### Test Widget Configuration Access
```python
from scripts.test_authenticated_requests import AuthenticatedTester

tester = AuthenticatedTester()
tester.setup_test_user()
tester.login()

# Test different widget config pages
widgets = ['finder-v2', 'calc', 'finder']
for widget in widgets:
    success = tester.test_endpoint_access(f'/widget/{widget}/config/')
    print(f"{widget} config: {'✅' if success else '❌'}")
```

#### Test Form Submissions
```python
# Test finder-v2 configuration form
form_data = {
    'config-name': 'Test Config',
    'config-lang': 'en',
    'theme-theme_name': 'light',
    'content-by': '',
    'content-regions': '[]',
    'content-brands': '[]',
    'content-brands_exclude': '[]',
    'content-only_oem': '',
    'interface-width': '600',
    'interface-height': '',
    'interface-tabs': 'by_vehicle',
    'interface-primary_tab': 'by_vehicle',
    'interface-button_to_ws': 'on',
    'interface-flow_type': 'primary',
    'permissions-domains': '["localhost", "development.local"]',
}

response = tester.post_form('/widget/finder-v2/config/', form_data, allow_redirects=False)
if response.status_code == 302:
    print("✅ Form submission successful")
else:
    print(f"❌ Form submission failed: {response.status_code}")
```

#### Test Admin Access
```python
# Test admin pages
admin_pages = [
    '/admin/',
    '/admin/widgets/',
    '/admin/auth/user/',
]

for page in admin_pages:
    tester.test_endpoint_access(page)
```

### Extending for New Widget Types

To add testing support for new widget types, follow this pattern:

#### 1. Add Endpoint Tests
```python
def test_new_widget_endpoints():
    tester = AuthenticatedTester()
    tester.setup_test_user()
    tester.login()
    
    # Test new widget endpoints
    endpoints = [
        "/widget/new-widget/config/",
        "/widget/new-widget/config-demo/",
    ]
    
    for endpoint in endpoints:
        tester.test_endpoint_access(endpoint)
```

#### 2. Add Form Submission Tests
```python
def test_new_widget_form():
    tester = AuthenticatedTester()
    tester.setup_test_user()
    tester.login()
    
    # Define form data specific to new widget
    form_data = {
        'config-name': 'Test New Widget',
        'config-lang': 'en',
        # Add widget-specific fields here
        'new-widget-specific-field': 'value',
    }
    
    response = tester.post_form('/widget/new-widget/config/', form_data)
    return response.status_code == 302
```

#### 3. Update Main Test Function
```python
def main():
    # Add new test to main function
    new_widget_test = test_new_widget_form()
    
    print(f"New Widget Tests: {'✅ PASS' if new_widget_test else '❌ FAIL'}")
```

### Configuration

#### Base URL
Change the base URL for different environments:
```python
# Local development (default)
tester = AuthenticatedTester("http://development.local:8000")

# Production testing
tester = AuthenticatedTester("https://services.wheel-size.com")
```

#### Test User Credentials
Default test user credentials:
- **Username**: `testuser`
- **Password**: `testpass123`
- **Email**: `<EMAIL>`
- **Permissions**: Superuser, Staff, `widgets.can_edit`

To use different credentials:
```python
tester = AuthenticatedTester()
tester.username = 'custom_user'
tester.password = 'custom_pass'
tester.email = '<EMAIL>'
```

### Troubleshooting

#### Common Issues

**1. Docker Container Not Running**
```bash
# Ensure Docker containers are running
docker-compose up -d
```

**2. Database Not Migrated**
```bash
# Run migrations
docker exec ws_services bash -c "cd /code && python manage.py migrate"
```

**3. CSRF Token Issues**
- The script automatically handles CSRF tokens
- If issues persist, check Django CSRF middleware configuration

**4. Permission Errors**
- The script creates superuser accounts automatically
- For custom permissions, modify the `setup_test_user()` method

#### Debug Mode
Enable verbose output by modifying the script:
```python
# Add debug prints
response = tester.get('/some/endpoint/')
print(f"Response headers: {response.headers}")
print(f"Response content: {response.text[:500]}...")
```

### Integration with CI/CD

The script can be integrated into CI/CD pipelines:

```bash
#!/bin/bash
# ci-test-authenticated.sh

# Start services
docker-compose up -d

# Wait for services to be ready
sleep 10

# Run authenticated tests
python scripts/test_authenticated_requests.py

# Capture exit code
TEST_RESULT=$?

# Cleanup
docker-compose down

# Exit with test result
exit $TEST_RESULT
```

### Example Scripts

#### Complete Widget Testing Example
See `scripts/examples/test_widget_example.py` for a comprehensive example that demonstrates:

- Widget creation flow testing
- Demo vs authenticated endpoint comparison
- Admin functionality verification
- Custom test scenarios

Run the example:
```bash
python scripts/examples/test_widget_example.py
```

### Related Documentation

- [Django Upgrade Plan](../upgrade/upgrade-plan.md) - Overall upgrade documentation
- [Finder-v2 Implementation](finder-v2-master-implementation-plan.md) - Finder-v2 specific details
- [Production Deployment Guide](../upgrade/production-deployment-guide.md) - Production considerations

### Security Considerations

- Test users are created with superuser privileges for testing purposes
- **Never use test credentials in production environments**
- Test users should be removed or disabled in production deployments
- The script is designed for development and testing environments only
