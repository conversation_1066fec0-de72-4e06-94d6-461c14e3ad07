I need to create a new widget type called 'finder-v2' for the wheel-size-services Django 4.2 project that will use the Wheel Fitment API v2 instead of the current API v1. This should be implemented as a complete clone/copy of the existing 'finder' widget functionality with zero impact on existing finder widgets.

**Project Context:**
- Django 4.2 project with Docker + Poetry setup
- Current widget types: 'finder' (API v1) and 'calc' (API v2)
- Widget system uses proxy views, CSRF protection, domain validation
- JavaScript build process uses Grunt for concatenation/minification
- Widgets embedded via JavaScript on client websites
- Admin interface manages widget configurations and domain permissions

**Core Requirements:**

1. **Complete Functional Isolation**: 
   - Create full copy of existing 'finder' widget under new 'finder-v2' type
   - Ensure zero impact on existing finder widgets (separate URLs, views, templates, JavaScript, models)
   - Maintain complete backward compatibility for existing finder widget installations

2. **API Migration Strategy**:
   - **Current finder (v1) flow**: makes → years → models → search/by_model
   - **New finder-v2 (v2) primary flow**: years → makes → models → modifications → search/by_model
   - **New finder-v2 (v2) alternative flow**: makes → models → generations → modifications → search/by_model
   - Support configurable flow selection via widget settings

3. **Widget Management Policy**:
   - Block creation of new 'finder' widget configurations (legacy deprecation)
   - Allow editing of existing 'finder' configurations by original creators
   - Enable full creation/editing of new 'finder-v2' configurations
   - Implement this through Django admin interface modifications

4. **Technology Stack Upgrade**:
   - Replace current finder JavaScript with Vue 3 + TailwindCSS + TailwindUI
   - Integrate with existing Grunt build process
   - Maintain widget embedding compatibility and CSRF token handling

**API Endpoint Specifications:**

**V1 (current finder) endpoints:**
1. `/v1/makes/` - get all makes
2. `/v1/years/?make=mitsubishi` - get years by make
3. `/v1/models/?make=mitsubishi&year=2024` - get models by make+year
4. `/v1/search/by_model/?make=mitsubishi&model=outlander&year=2015` - get fitment data

**V2 (new finder-v2) primary flow:**
1. `/v2/years/` - get all available years
2. `/v2/makes/?year=2025` - get makes by year
3. `/v2/models/?make=mitsubishi&year=2025` - get models by make+year
4. `/v2/modifications/?make=mitsubishi&model=outlander&year=2025` - get modifications
5. `/v2/search/by_model/?make=mitsubishi&model=outlander&year=2025&modification=7bb1166e91` - get fitment data

**V2 (new finder-v2) alternative flow:**
1. `/v2/makes/?year=2025` - get makes by year
2. `/v2/models/?make=mitsubishi&year=2025` - get models by make+year
3. `/v2/generations/?make=mitsubishi&model=outlander` - get generations
4. `/v2/modifications/?make=mitsubishi&model=outlander&year=2025` - get modifications
5. `/v2/search/by_model/?make=mitsubishi&model=outlander&generation=18d10dc6e6&modification=7bb1166e91` - get fitment data

**Required Implementation Plan:**

Provide a comprehensive, step-by-step implementation plan covering:

1. **Database Schema Changes**: Model modifications for finder-v2 widget type, configuration fields for flow selection
2. **URL Routing Strategy**: Complete duplication of finder URLs under finder-v2 namespace
   - `/widget/finder-v2/config/`: Configuration interface, require authentication (login_required)
   - `/widget/finder-v2/try/`: Configuration interface, allow public access for testing widget settings
   - Both endpoints show the same configuration interface but with different access control
3. **View Layer Duplication**: Copy and modify proxy views for v2 API endpoints
4. **Template Organization**: Separate template hierarchy for finder-v2 widgets
5. **JavaScript Architecture**: Vue 3 integration strategy with existing Grunt build process
6. **Admin Interface Updates**: Widget management policy implementation (creation restrictions, editing permissions)
7. **API Integration**: Proxy view modifications for v2 endpoints and configurable flow logic
8. **Testing Strategy**: Isolation verification, backward compatibility validation, cross-domain embedding tests
9. **Deployment Considerations**: Production rollout strategy, client migration path, monitoring approach

**Success Criteria:**
- Existing finder widgets continue working without any changes
- New finder-v2 widgets can be created and embedded successfully
- Admin interface enforces widget management policies correctly
- Vue 3 + TailwindCSS frontend integrates seamlessly with existing infrastructure
- Both v2 API flows work correctly based on widget configuration
- Zero downtime deployment possible for existing client installations

The implementation should follow the project's established patterns for widget development, maintain security standards (CSRF protection, domain validation), and ensure production deployment compatibility with services.wheel-size.com.