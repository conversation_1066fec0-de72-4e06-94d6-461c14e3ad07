# Finder-v2 Vue.js Development Deployment

This document provides commands for quickly deploying Vue.js changes to the finder-v2 widget during development.

## Overview

The finder-v2 widget uses Vue 3 with Vite as the build tool. During development, changes to Vue.js source files need to be built and deployed to Django's static file system for testing. This document provides automated workflows to streamline this process.

**Build Process:**
1. **Vite Build** - Compiles Vue.js source files using `vite.config.js` configuration
2. **Static File Copy** - Moves built files from `dist/` to Django's static directory
3. **Server Restart** - Restarts Django to pick up new static files

## Full Deployment Script (Recommended)

**Complete automated deployment with error handling and status reporting:**

```bash
./deploy-finder-v2.sh
```

This script performs all deployment steps with detailed output and error handling:
- ✅ Verifies project directory and Docker containers
- ✅ Builds Vue.js application in Docker container
- ✅ Copies static files to Django directory
- ✅ Restarts Django development server
- ✅ Verifies server is responding
- ✅ Provides clear success/failure feedback

## One-Liner Commands

**Quick deployment (single command):**
```bash
docker compose exec web bash -c "cd src/apps/widgets/finder_v2/app && npm run build" && cp -R src/apps/widgets/finder_v2/app/dist/* src/apps/widgets/finder_v2/static/finder_v2/ && docker compose restart web && echo "✅ Deployment complete! Widget: http://development.local:8000/widget/finder-v2/?config"
```

**Compact version:**
```bash
docker compose exec web bash -c "cd src/apps/widgets/finder_v2/app && npm run build" && cp -R src/apps/widgets/finder_v2/app/dist/* src/apps/widgets/finder_v2/static/finder_v2/ && docker compose restart web
```

## Individual Steps

If you need to run steps individually:

**1. Build Vue.js application:**
```bash
docker compose exec web bash -c "cd src/apps/widgets/finder_v2/app && npm run build"
```

**2. Copy static files:**
```bash
cp -R src/apps/widgets/finder_v2/app/dist/* src/apps/widgets/finder_v2/static/finder_v2/
```

**3. Restart Django server:**
```bash
docker compose restart web
```

## Development Workflow

1. **Edit Vue.js files** in `src/apps/widgets/finder_v2/app/src/`
2. **Run deployment script**: `./deploy-finder-v2.sh`
3. **Test changes** at: http://development.local:8000/widget/finder-v2/?config
4. **Repeat** as needed during development

## Build Configuration

The Vue.js build process is configured via `vite.config.js` in the widget app directory:
- **Entry Point**: `src/main.js` - Vue 3 application initialization
- **Output**: `dist/` directory with optimized JavaScript and CSS files
- **Build Tool**: Vite v5.4.19 with Vue 3 plugin
- **Static Assets**: Automatically processed and optimized during build

## Troubleshooting

**If deployment fails:**
- Check Docker containers are running: `docker compose ps`
- Verify you're in project root directory (should contain `docker-compose.yml`)
- Check build errors in the script output
- Manually run individual steps to isolate issues
- Verify Node.js/npm are available in Docker container

**If widget doesn't update:**
- Clear browser cache (Cmd+Shift+R / Ctrl+Shift+R)
- Check browser console for JavaScript errors
- Verify static files were copied with current timestamps
- Check Django server logs: `docker compose logs web --tail=20`
- Ensure vite.config.js build settings are correct

**Common Issues:**
- **Build fails**: Check Vue.js syntax errors in source files
- **Files not copying**: Verify dist/ directory exists after build
- **Widget not loading**: Check Django static file serving configuration
- **API calls failing**: Verify CSRF token and API endpoint configuration

## File Locations

- **Vue.js source**: `src/apps/widgets/finder_v2/app/src/`
- **Build output**: `src/apps/widgets/finder_v2/app/dist/`
- **Django static**: `src/apps/widgets/finder_v2/static/finder_v2/`
- **Widget URL**: http://development.local:8000/widget/finder-v2/?config

## Quick Reference

| Command | Purpose |
|---------|---------|
| `./deploy-finder-v2.sh` | Full deployment with error handling |
| `docker compose exec web bash -c "cd src/apps/widgets/finder_v2/app && npm run build" && cp -R src/apps/widgets/finder_v2/app/dist/* src/apps/widgets/finder_v2/static/finder_v2/ && docker compose restart web` | One-liner deployment |
| `docker compose logs web --tail=20` | Check Django server logs |
| `docker compose ps` | Check container status |
