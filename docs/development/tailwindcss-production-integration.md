 # TailwindCSS Production Integration Guide
## CI/CD Pipeline and Deployment Integration

### 📋 **Overview**
This guide covers integrating TailwindCSS v4.1 build processes with production deployment workflows, Docker builds, and CI/CD pipelines for the wheel-size-services project.

---

## 🚀 **Production Build Commands**

### **NPM Scripts (Production)**
```bash
# Production builds with minification
npm run build:portal:prod         # Build portal CSS for production
npm run build:finder-v2:prod      # Build finder-v2 for production  
npm run build:all:prod            # Build everything for production

# Development builds (debug/unminified)
npm run tailwind:portal:build     # Build portal CSS (debug)
npm run build:all                 # Build everything (debug)
```

### **Shell Scripts (Docker Compatible)**
```bash
# Production builds
./scripts/build-tailwind.sh portal --production      # Portal only (production)
./scripts/build-tailwind.sh finder-v2 --production   # Finder-v2 only (production)
./scripts/build-tailwind.sh all --production         # Everything (production)

# Development builds
./scripts/build-tailwind.sh portal                   # Portal only (debug)
./scripts/build-tailwind.sh all                      # Everything (debug)
```

---

## 🐳 **Docker Integration**

### **Docker Build Integration**
Add TailwindCSS build steps to Dockerfile or docker-compose:

```dockerfile
# In Dockerfile (if building assets in container)
COPY package*.json ./
RUN npm install

# Copy source files
COPY src/ ./src/
COPY scripts/ ./scripts/

# Build TailwindCSS assets for production
RUN chmod +x scripts/build-tailwind.sh
RUN ./scripts/build-tailwind.sh all --production

# Continue with Django setup...
```

### **Docker Compose Development**
```yaml
# In docker-compose.yml
services:
  web:
    # ... existing config ...
    volumes:
      - .:/app
      - node_modules:/app/node_modules
    command: >
      bash -c "
        ./scripts/build-tailwind.sh all --production &&
        python manage.py collectstatic --noinput &&
        python manage.py runserver 0.0.0.0:8000
      "
```

### **Docker Commands**
```bash
# Build assets inside Docker container
docker exec ws_services ./scripts/build-tailwind.sh all --production

# Watch mode for development
docker exec ws_services npm run dev:watch

# Validate classes
docker exec ws_services ./scripts/tailwind-dev.sh validate "bg-ws-primary"
```

---

## 🏗️ **CI/CD Pipeline Integration**

### **GitHub Actions Example**
```yaml
# .github/workflows/build.yml
name: Build and Deploy

on:
  push:
    branches: [ main, production ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm install
        cd src/apps/portal && npm install
        cd ../../widgets/finder_v2/app && npm install
    
    - name: Build TailwindCSS assets
      run: |
        chmod +x scripts/build-tailwind.sh
        ./scripts/build-tailwind.sh all --production
    
    - name: Verify build outputs
      run: |
        ls -la src/apps/portal/static/portal/css/tailwind*.css
        ./scripts/tailwind-dev.sh stats
    
    - name: Run Django tests
      run: |
        python manage.py test
    
    - name: Deploy to staging
      if: github.ref == 'refs/heads/main'
      run: |
        # Deploy commands here
```

### **GitLab CI Example**
```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - deploy

variables:
  NODE_VERSION: "18"

build_assets:
  stage: build
  image: node:${NODE_VERSION}
  cache:
    paths:
      - node_modules/
      - src/apps/portal/node_modules/
      - src/apps/widgets/finder_v2/app/node_modules/
  script:
    - npm install
    - cd src/apps/portal && npm install && cd ../../..
    - cd src/apps/widgets/finder_v2/app && npm install && cd ../../../..
    - chmod +x scripts/build-tailwind.sh
    - ./scripts/build-tailwind.sh all --production
    - ./scripts/tailwind-dev.sh stats
  artifacts:
    paths:
      - src/apps/portal/static/portal/css/tailwind.min.css
      - src/apps/widgets/finder_v2/static/finder_v2/
    expire_in: 1 hour

test:
  stage: test
  image: python:3.11
  dependencies:
    - build_assets
  script:
    - pip install -r requirements.txt
    - python manage.py test

deploy_production:
  stage: deploy
  dependencies:
    - build_assets
  script:
    - # Production deployment commands
  only:
    - main
```

---

## 📦 **Production Deployment Workflow**

### **Pre-Deployment Checklist**
```bash
# 1. Build production assets
npm run build:all:prod

# 2. Verify build outputs
./scripts/tailwind-dev.sh stats
ls -la src/apps/portal/static/portal/css/tailwind.min.css

# 3. Validate critical utility classes
./scripts/tailwind-dev.sh validate "bg-ws-primary"
./scripts/tailwind-dev.sh validate "border-ws-secondary"

# 4. Test in staging environment
# ... staging deployment commands ...

# 5. Run Django collectstatic
python manage.py collectstatic --noinput

# 6. Verify static files
ls -la staticfiles/portal/css/tailwind.min.css
```

### **Deployment Script Integration**
```bash
#!/bin/bash
# deploy.sh

set -e

log_info() {
    echo "[INFO] $1"
}

log_success() {
    echo "[SUCCESS] $1"
}

# Build TailwindCSS assets for production
log_info "Building TailwindCSS assets..."
./scripts/build-tailwind.sh all --production

# Collect Django static files
log_info "Collecting Django static files..."
python manage.py collectstatic --noinput

# Run database migrations
log_info "Running database migrations..."
python manage.py migrate

# Restart application servers
log_info "Restarting application..."
systemctl restart wheel-size-services

log_success "Deployment completed successfully!"
```

---

## ⚙️ **Environment-Specific Configuration**

### **Development Environment**
```bash
# .env.development
TAILWIND_MODE=debug
CSS_MINIFY=false
WATCH_MODE=true

# Build commands for development
npm run dev:watch                 # Watch mode
./scripts/tailwind-dev.sh debug   # Debug build
```

### **Staging Environment**
```bash
# .env.staging
TAILWIND_MODE=production
CSS_MINIFY=true
WATCH_MODE=false

# Build commands for staging
npm run build:all:prod           # Production build
./scripts/build-tailwind.sh all --production
```

### **Production Environment**
```bash
# .env.production
TAILWIND_MODE=production
CSS_MINIFY=true
WATCH_MODE=false
DJANGO_COLLECT_STATIC=true

# Build commands for production
npm run build:all:prod           # Production build with collectstatic
```

---

## 🔍 **Build Verification and Testing**

### **Automated Testing Script**
```bash
#!/bin/bash
# scripts/test-tailwind-build.sh

set -e

echo "Testing TailwindCSS build process..."

# Clean previous builds
./scripts/tailwind-dev.sh clean

# Build production assets
./scripts/build-tailwind.sh all --production

# Verify outputs exist
echo "Verifying build outputs..."
test -f "src/apps/portal/static/portal/css/tailwind.min.css" || exit 1
test -d "src/apps/widgets/finder_v2/static/finder_v2" || exit 1

# Verify file sizes (should be reasonable)
PORTAL_SIZE=$(stat -f%z "src/apps/portal/static/portal/css/tailwind.min.css" 2>/dev/null || stat -c%s "src/apps/portal/static/portal/css/tailwind.min.css")
if [ "$PORTAL_SIZE" -lt 10000 ]; then
    echo "ERROR: Portal CSS file too small ($PORTAL_SIZE bytes)"
    exit 1
fi

# Validate critical classes exist
./scripts/tailwind-dev.sh validate "bg-ws-primary" >/dev/null || exit 1
./scripts/tailwind-dev.sh validate "text-ws-secondary" >/dev/null || exit 1

echo "Build verification completed successfully!"
```

### **CSS Validation Tests**
```bash
# Test critical utility classes
./scripts/tailwind-dev.sh validate "bg-ws-primary"      # Brand colors
./scripts/tailwind-dev.sh validate "border-ws-secondary" # Secondary colors
./scripts/tailwind-dev.sh validate "bg-white"           # Standard colors
./scripts/tailwind-dev.sh validate "py-8"               # Spacing utilities

# Check for expected class count
EXPECTED_CLASSES=270
ACTUAL_CLASSES=$(grep -c "bg-ws-\|text-ws-\|border-ws-" src/apps/portal/static/portal/css/tailwind-debug.css)
if [ "$ACTUAL_CLASSES" -lt "$EXPECTED_CLASSES" ]; then
    echo "WARNING: Expected $EXPECTED_CLASSES classes, found $ACTUAL_CLASSES"
fi
```

---

## 📊 **Performance Monitoring**

### **Build Performance Metrics**
```bash
# Measure build time
time ./scripts/build-tailwind.sh all --production

# Check CSS bundle sizes
./scripts/tailwind-dev.sh stats

# Monitor file size trends
ls -lah src/apps/portal/static/portal/css/tailwind*.css
```

### **Expected Performance Targets**
- **Portal build time**: < 500ms
- **Portal CSS size**: 35-50KB (minified)
- **Finder-v2 build time**: < 2 seconds
- **Total build time**: < 3 seconds
- **Template scanning**: < 100ms

---

## 🚨 **Troubleshooting Production Issues**

### **Common Production Problems**

**Issue**: "CSS file not found in production"
```bash
# Solution: Verify collectstatic ran correctly
python manage.py collectstatic --noinput
ls -la staticfiles/portal/css/tailwind.min.css
```

**Issue**: "Classes not working in production but work in development"
```bash
# Solution: Check if production CSS was built correctly
./scripts/build-tailwind.sh portal --production
./scripts/tailwind-dev.sh validate "missing-class"
```

**Issue**: "Build fails in CI/CD pipeline"
```bash
# Solution: Check Node.js version and dependencies
node --version
npm --version
npm install
./scripts/build-tailwind.sh all --production
```

**Issue**: "Docker build timeout"
```bash
# Solution: Use multi-stage build or build caching
# In Dockerfile:
# COPY package*.json ./
# RUN npm install
# COPY . .
# RUN ./scripts/build-tailwind.sh all --production
```

### **Debug Commands for Production**
```bash
# Check build environment
./scripts/build-tailwind.sh help
./scripts/tailwind-dev.sh stats

# Verify dependencies
cd src/apps/portal && npm list tailwindcss
cd src/apps/widgets/finder_v2/app && npm list

# Test build process step by step
./scripts/build-tailwind.sh portal --production
./scripts/build-tailwind.sh finder-v2 --production
./scripts/tailwind-dev.sh validate "bg-ws-primary"
```

---

## 📚 **Integration with Existing Build Systems**

### **Grunt Integration (Widget Builds)**
For widgets that use Grunt (like finder-v2), TailwindCSS builds are kept separate:

```bash
# Widget development workflow
npm run dev:watch                 # TailwindCSS watch (portal)
cd src/apps/widgets/finder_v2/app && grunt watch  # Grunt watch (widget)

# Production build workflow
npm run build:all:prod            # Build all TailwindCSS assets
cd src/apps/widgets/finder_v2/app && grunt all_prod  # Build widget assets
```

### **Django Static Files Integration**
```python
# In Django settings.py
STATICFILES_DIRS = [
    BASE_DIR / "src/apps/portal/static",
    BASE_DIR / "src/apps/widgets/finder/static",
    BASE_DIR / "src/apps/widgets/finder_v2/static",
    # ... other static directories
]

# Ensure TailwindCSS files are collected
STATIC_URL = "/static/"
STATIC_ROOT = BASE_DIR / "staticfiles"
```

---

## 🔄 **Continuous Integration Best Practices**

### **Caching Strategy**
```yaml
# Cache node_modules for faster builds
cache:
  paths:
    - node_modules/
    - src/apps/portal/node_modules/
    - src/apps/widgets/finder_v2/app/node_modules/
```

### **Parallel Builds**
```yaml
# Build portal and finder-v2 in parallel
parallel:
  matrix:
    - TARGET: portal
    - TARGET: finder-v2
script:
  - ./scripts/build-tailwind.sh $TARGET --production
```

### **Artifact Management**
```yaml
# Save build artifacts
artifacts:
  paths:
    - src/apps/portal/static/portal/css/tailwind.min.css
    - src/apps/widgets/finder_v2/static/finder_v2/
  expire_in: 1 week
```

---

## 📖 **References**

### **Internal Documentation**
- `docs/development/tailwindcss-workflow-guide.md` - Development workflow
- `docs/development/tailwindcss-v4-roadmap.md` - Implementation roadmap
- `docs/upgrade/production-deployment-guide.md` - General deployment guide

### **Build Scripts**
- `scripts/build-tailwind.sh` - Main build script with production support
- `scripts/tailwind-dev.sh` - Development workflow script
- `package.json` - NPM scripts for all build commands

### **Configuration Files**
- `src/apps/portal/static/portal/css/tailwind.config.js` - TailwindCSS config
- `docker-compose.yml` - Docker development environment
- `.github/workflows/` - CI/CD pipeline examples

---

**Status**: ✅ **Phase 2.3 Complete** | 🚀 **Production Ready**
**Last Updated**: Current Implementation
**Next Steps**: Phase 3 Advanced Features (Optional)