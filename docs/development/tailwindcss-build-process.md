# TailwindCSS v4.1 Build Process Documentation

## Overview
This document describes the build process for TailwindCSS v4.1 in the wheel-size-services project, specifically for the widget portal interface modernization.

## Project Structure

```
src/apps/portal/
├── package.json                           # Node.js dependencies and scripts
├── static/portal/css/
│   ├── tailwind.config.js                # TailwindCSS configuration
│   ├── tailwind.css                      # Source CSS with theme configuration
│   └── tailwind.min.css                  # Built/minified CSS (generated)
└── templates/                            # Django templates using TailwindCSS classes
```

## Build Commands

### Production Build
```bash
# Navigate to portal directory
cd src/apps/portal

# Install dependencies (first time only)
npm install

# Build minified CSS for production
npm run build-css
```

### Development Build with Watch
```bash
# Navigate to portal directory
cd src/apps/portal

# Watch for changes and rebuild automatically
npm run watch-css
```

### Quick Rebuild Script
```bash
# From project root
./rebuild-portal-css.sh
```

## TailwindCSS Configuration

### Theme Configuration (`tailwind.css`)
The main CSS file includes:

- **Brand Colors**: Wheel-Size.com color scheme (#be3e1d primary)
- **Component Classes**: Buttons, forms, cards, alerts
- **Bootstrap Compatibility**: Legacy class mappings
- **Custom Utilities**: Brand-specific utility classes

### Content Sources (`tailwind.config.js`)
TailwindCSS scans these locations for class usage:
- `../../templates/**/*.html` (Portal templates)
- `../../../templates/**/*.html` (Widget templates)
- `../js/**/*.js` (JavaScript files)
- `../../../widgets/**/templates/**/*.html` (Widget-specific templates)

## CSS Architecture

### Layer Structure
1. **@theme**: CSS variables and design tokens
2. **@layer components**: Reusable component classes
3. **@layer utilities**: Custom utility classes
4. **@layer base**: Bootstrap compatibility and base styles

### Component Classes
- `.btn-primary`, `.btn-secondary`, etc. - Button components
- `.form-input`, `.form-select`, `.form-textarea` - Form components
- `.card`, `.card-header`, `.card-body` - Card components
- `.alert-success`, `.alert-warning`, etc. - Alert components
- `.nav-link`, `.widget-card` - Navigation and widget components

### Bootstrap Compatibility
Legacy Bootstrap classes are mapped to TailwindCSS equivalents:
- `.container` → Modern container with responsive padding
- `.row` → Flexbox row with negative margins
- `.col-md-*` → Responsive grid columns
- `.form-control` → Modern form input styling
- `.btn` → Button base styling

## Development Workflow

### 1. Making Style Changes
1. Edit `src/apps/portal/static/portal/css/tailwind.css`
2. Run `npm run build-css` or use watch mode
3. Refresh browser to see changes

### 2. Adding New Components
1. Add component classes to the `@layer components` section
2. Use CSS custom properties for theming
3. Rebuild CSS and test

### 3. Template Updates
1. Replace Bootstrap classes with TailwindCSS classes
2. Use component classes for consistent styling
3. Test responsive behavior

## File Sizes and Performance

### Current Bundle Size
- **Development**: ~45KB (unminified)
- **Production**: ~15-25KB (minified, tree-shaken)

### Optimization Features
- **Tree Shaking**: Unused classes are removed
- **Minification**: CSS is compressed for production
- **CSS Variables**: Enable dynamic theming
- **Modern CSS**: Uses latest CSS features

## Integration with Django

### Template Usage
```html
<!-- Modern TailwindCSS classes -->
<div class="bg-white rounded-lg shadow-md p-6">
  <h2 class="text-xl font-semibold text-gray-900 mb-4">Widget Title</h2>
  <button class="btn-primary">Create Widget</button>
</div>

<!-- Legacy Bootstrap compatibility -->
<div class="container">
  <div class="row">
    <div class="col-md-6">
      <form class="form-group">
        <input type="text" class="form-control">
      </form>
    </div>
  </div>
</div>
```

### CSS Loading
```html
<!-- In base template -->
<link href="{% static 'portal/css/tailwind.min.css' %}" rel="stylesheet">
```

## Troubleshooting

### Common Issues

1. **CSS Not Updating**
   - Ensure you've run the build command
   - Check browser cache (hard refresh)
   - Verify file permissions

2. **Classes Not Working**
   - Check if class is included in content sources
   - Verify TailwindCSS syntax
   - Check for typos in class names

3. **Build Errors**
   - Ensure Node.js dependencies are installed
   - Check for syntax errors in CSS
   - Verify file paths in config

### Debug Commands
```bash
# Check if CSS file exists and size
ls -la src/apps/portal/static/portal/css/tailwind.min.css

# Verify npm dependencies
cd src/apps/portal && npm list

# Test build process
cd src/apps/portal && npm run build-css
```

## Browser Compatibility

### Supported Browsers
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

### CSS Features Used
- CSS Custom Properties (CSS Variables)
- CSS Grid and Flexbox
- Modern color functions
- CSS logical properties

## Future Enhancements

### Planned Improvements
1. **Dark Mode Support**: CSS variables enable easy dark mode
2. **Component Library**: Extract reusable components
3. **Performance Optimization**: Further bundle size reduction
4. **Advanced Theming**: More customization options

### Migration Path
1. Complete portal interface modernization
2. Extend to widget-specific templates
3. Optimize for production deployment
4. Add advanced features (dark mode, etc.)

## Related Documentation
- [TailwindCSS Modernization Progress](./tailwindcss-modernization-progress.md)
- [Widget Portal Interface Guide](../upgrade/widget-portal-interface.md)
- [Production Deployment Guide](../upgrade/production-deployment-guide.md)
