# Finder-v2 Template System Documentation

This folder contains comprehensive documentation for the **Finder-v2 Custom Template System** - a feature that allows widget administrators to customize search result displays using HTML templates with TailwindCSS styling.

## 📚 Documentation Index

### 🎯 [Knowledge Transfer](./knowledge-transfer.md)
**START HERE** - Comprehensive technical documentation covering:
- System architecture and implementation details
- File structure and key components
- Security considerations and troubleshooting
- Deployment procedures and maintenance tasks
- Future enhancement roadmap

### 📖 [Template Examples](./finder-v2-template-examples.md)
Practical examples and syntax reference:
- Working template code samples
- Placeholder variable reference
- Conditional logic patterns
- Loop processing examples

### 📋 [Implementation Plan](./finder-v2-custom-output-template-plan.md) 
Original design document and project plan:
- Feature requirements and specifications
- Backend and frontend implementation details
- API placeholders catalog (60+ variables)
- Testing strategy and rollout phases

## 🔧 Quick Start

### For Developers
1. Read the [Knowledge Transfer](./knowledge-transfer.md) document
2. Review [Template Examples](./finder-v2-template-examples.md) for syntax
3. Set up local development environment
4. Test template gallery functionality at `http://development.local:8000/widget/finder-v2/?config`

### For Widget Administrators
1. Navigate to the widget configuration page
2. Scroll to "Custom Result Display" section
3. Choose from 6 pre-built templates in the gallery, or
4. Write custom HTML with placeholders from the examples

### For DevOps/Deployment
1. Use `./deploy-finder-v2.sh` for complete build and deployment
2. Monitor CSS file size (~28KB with template classes)
3. Check template engine JavaScript for errors in browser console

## 🏗️ System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Django Admin  │    │    Database      │    │   Vue Frontend  │
│                 │    │                  │    │                 │
│ Template Config ├───►│ Widget Config    ├───►│ Results Display │
│ Form Validation │    │ JSON Storage     │    │ Template Engine │
│ Gallery UI      │    │                  │    │ HTML Rendering  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## ✨ Key Features

- **6 Pre-built Templates**: Default, Compact, Modern, Table, Minimal, Badge styles
- **Template Gallery**: One-click template application with visual previews
- **Custom HTML Support**: Full HTML with TailwindCSS classes
- **Security**: XSS prevention through HTML escaping and validation
- **Backward Compatible**: Existing widgets continue using default table
- **Real-time Application**: Templates apply instantly without page reload

## 🔒 Security

- All template variables automatically HTML-escaped
- Form validation blocks dangerous patterns (script tags, event handlers)
- Template engine isolated from global JavaScript scope
- Safe fallback to default table for invalid templates

## 📊 Template Gallery Options

1. **🏷️ Default Layout** - Card-based with OE/Aftermarket distinction
2. **📋 Compact List** - Space-efficient list view
3. **🎨 Modern Cards** - Premium grid with shadows and gradients  
4. **📊 Professional Table** - Clean table with proper headers
5. **✨ Minimal Clean** - Ultra-clean design with subtle typography
6. **🏆 Badge Style** - Colorful badge-based layout with visual hierarchy

## 🚀 Implementation Status

### ✅ Completed Features
- Backend form integration with validation
- Template engine supporting variables, loops, conditionals
- Vue component conditional rendering
- Template gallery with 6 professional options
- TailwindCSS class safelist for styling support
- Security validation and XSS prevention

### ❌ Future Enhancements
- Unit and integration test suites
- Live template preview in admin interface
- Template marketplace and sharing
- Performance optimization with template caching
- Rich text editor with syntax highlighting

## 🔧 Troubleshooting

### Common Issues
- **Template not rendering**: Check Vue store state and widget config
- **Missing styles**: Add classes to TailwindCSS safelist and rebuild
- **Syntax errors**: Verify balanced template tags and variable syntax
- **Form validation**: Remove dangerous content and fix template syntax

### Debug Commands
```bash
# Check template state
console.log(finderStore.outputTemplate)

# Test template engine
import { renderTemplate } from './utils/templateEngine'

# Rebuild CSS with new classes  
./deploy-finder-v2.sh
```

## 📞 Support

- **Technical Issues**: Development Team
- **Feature Requests**: Product Team  
- **Documentation**: AI Assistant

---

*For detailed technical information, start with the [Knowledge Transfer](./knowledge-transfer.md) document.* 