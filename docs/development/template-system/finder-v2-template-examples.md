# Finder-v2 Custom Template Examples

## Correct Template Syntax

### Example 1: Basic OE/Aftermarket Display
```html
<div class="space-y-4">
  <h3 class="text-lg font-semibold mb-2">
    {{ make.name }} {{ model.name }} ({{ start_year }}-{{ end_year }})
  </h3>
  {% for wheel in wheels %}
    <div class="p-3 rounded-md border {% if wheel.is_stock %}border-indigo-400 bg-indigo-50{% else %}border-gray-300{% endif %}">
      <div class="font-medium uppercase tracking-wide text-sm mb-1 text-primary-color">
        {{ wheel.is_stock ? "OE option" : "After-market option" }}
      </div>
      <div class="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span class="text-gray-500">Front:</span>
          {{ wheel.front.tire }} – {{ wheel.front.rim }}
        </div>
        {% if not wheel.showing_fp_only %}
          {% if wheel.rear.tire %}
            <div>
              <span class="text-gray-500">Rear:</span>
              {{ wheel.rear.tire }} – {{ wheel.rear.rim }}
            </div>
          {% endif %}
        {% endif %}
      </div>
    </div>
  {% endfor %}
</div>
```

### Example 2: Card Layout with Badges
```html
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
  <div class="mb-4 md:col-span-2">
    <h2 class="text-xl font-bold">{{ make.name }} {{ model.name }}</h2>
    <p class="text-gray-600">{{ start_year }}-{{ end_year }} • {{ engine.fuel }} {{ engine.power.kW }}kW</p>
  </div>
  
  {% for wheel in wheels %}
    <div class="bg-white border rounded-lg p-4 shadow-sm">
      <div class="flex items-center justify-between mb-3">
        <span class="text-lg font-semibold">{{ wheel.front.rim_diameter }}"</span>
        <span class="px-2 py-1 text-xs rounded-full {{ wheel.is_stock ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800" }}">
          {{ wheel.is_stock ? "OE" : "Aftermarket" }}
        </span>
      </div>
      
      <div class="space-y-2 text-sm">
        <div>
          <span class="font-medium">Tire:</span> {{ wheel.front.tire }}
        </div>
        <div>
          <span class="font-medium">Rim:</span> {{ wheel.front.rim }}
        </div>
        {% if not wheel.showing_fp_only %}
          {% if wheel.rear.tire %}
            <div class="border-t pt-2 mt-2">
              <div><span class="font-medium">Rear Tire:</span> {{ wheel.rear.tire }}</div>
              <div><span class="font-medium">Rear Rim:</span> {{ wheel.rear.rim }}</div>
            </div>
          {% endif %}
        {% endif %}
      </div>
    </div>
  {% endfor %}
</div>
```

## Template Syntax Reference

### Variables
- `{{ variable.path }}` - Simple variable output
- `{{ condition ? "true_value" : "false_value" }}` - Ternary operator

### Conditionals
- `{% if condition %}...{% endif %}` - Simple if
- `{% if condition %}...{% else %}...{% endif %}` - If-else
- `{% if not condition %}...{% endif %}` - Negated condition

### Loops
- `{% for item in array %}...{% endfor %}` - Loop through array

### Available Data

#### Vehicle Info
- `{{ make.name }}`, `{{ model.name }}`
- `{{ start_year }}`, `{{ end_year }}`
- `{{ generation.name }}`, `{{ trim }}`

#### Engine Data
- `{{ engine.fuel }}`, `{{ engine.power.kW }}`
- `{{ engine.capacity }}`, `{{ engine.type }}`

#### Wheels Loop
- `{{ wheel.is_stock }}` - Boolean: true for OE
- `{{ wheel.showing_fp_only }}` - Boolean: true if front/rear identical
- `{{ wheel.front.tire }}`, `{{ wheel.front.rim }}`
- `{{ wheel.rear.tire }}`, `{{ wheel.rear.rim }}`

### Important Notes

1. **Ternary Operator**: Use `{{ condition ? "value1" : "value2" }}` (NOT Django-style `{% if %}`)
2. **Nested Conditions**: Break complex conditions into separate `{% if %}` blocks
3. **HTML Escaping**: All variables are automatically HTML-escaped for security
4. **TailwindCSS**: All Tailwind classes are supported for styling 