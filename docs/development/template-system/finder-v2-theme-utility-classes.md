# Finder-v2 Theme Utility Classes

## Overview

The Finder-v2 theming system provides CSS utility classes that automatically adapt to the user's selected theme colors. These classes make it easy to create custom templates that respect the theme configuration.

## Available Utility Classes

### Text Color Classes

Apply theme colors to text elements:

```html
<!-- Primary brand color -->
<div class="text-primary-color">Primary color text</div>

<!-- Secondary color -->
<div class="text-secondary-color">Secondary color text</div>

<!-- Accent color -->
<div class="text-accent-color">Accent color text</div>

<!-- Background color (useful for contrast) -->
<div class="text-background-color">Background color text</div>

<!-- Main text color -->
<div class="text-main-color">Main text color</div>
```

### Background Color Classes

Apply theme colors as background colors:

```html
<!-- Primary background -->
<div class="bg-primary-color">Primary background</div>

<!-- Secondary background -->
<div class="bg-secondary-color">Secondary background</div>

<!-- Accent background -->
<div class="bg-accent-color">Accent background</div>

<!-- Widget background -->
<div class="bg-background-color">Widget background</div>

<!-- Text color as background -->
<div class="bg-main-color">Text color background</div>
```

### Border Color Classes

Apply theme colors to borders:

```html
<!-- Primary border -->
<div class="border border-primary-color">Primary border</div>

<!-- Secondary border -->
<div class="border border-secondary-color">Secondary border</div>

<!-- Accent border -->
<div class="border border-accent-color">Accent border</div>

<!-- Background border -->
<div class="border border-background-color">Background border</div>

<!-- Text color border -->
<div class="border border-main-color">Text color border</div>
```

### Hover State Classes

Apply theme colors on hover:

```html
<!-- Text color hover effects -->
<div class="hover:text-primary-color">Hover for primary text</div>
<div class="hover:text-secondary-color">Hover for secondary text</div>
<div class="hover:text-accent-color">Hover for accent text</div>

<!-- Background color hover effects -->
<div class="hover:bg-primary-color">Hover for primary background</div>
<div class="hover:bg-secondary-color">Hover for secondary background</div>
<div class="hover:bg-accent-color">Hover for accent background</div>
```

### Additional Theme Utilities

Apply other theme properties:

```html
<!-- Typography -->
<div class="theme-font-family">Uses theme font family</div>
<div class="theme-font-size">Uses theme font size</div>

<!-- Layout -->
<div class="theme-border-radius">Uses theme border radius</div>
<div class="theme-padding">Uses theme padding</div>
<div class="theme-margin">Uses theme margin</div>
<div class="theme-shadow">Uses theme shadow</div>
```

## Practical Examples

### Product Card Example

```html
<div class="bg-background-color border border-secondary-color theme-border-radius theme-padding theme-shadow">
  <div class="font-medium uppercase tracking-wide text-sm mb-1 text-primary-color">
    {{ wheel.is_stock ? "OE option" : "After-market option" }}
  </div>
  <h3 class="text-main-color theme-font-family font-bold">{{ wheel.name }}</h3>
  <p class="text-secondary-color">{{ wheel.description }}</p>
  <button class="bg-accent-color text-background-color theme-padding theme-border-radius hover:bg-primary-color">
    Add to Cart
  </button>
</div>
```

### Alert Component Example

```html
<!-- Success Alert -->
<div class="bg-accent-color text-background-color theme-padding theme-border-radius theme-margin">
  <strong>Success!</strong> Your configuration has been saved.
</div>

<!-- Warning Alert -->
<div class="bg-primary-color text-background-color theme-padding theme-border-radius theme-margin">
  <strong>Warning!</strong> Please check your selection.
</div>

<!-- Info Alert -->
<div class="bg-secondary-color text-background-color theme-padding theme-border-radius theme-margin">
  <strong>Info:</strong> Additional options available.
</div>
```

### Navigation Example

```html
<nav class="bg-background-color border-b border-secondary-color">
  <ul class="flex space-x-4 theme-padding">
    <li>
      <a href="#" class="text-main-color hover:text-primary-color theme-font-family">
        Home
      </a>
    </li>
    <li>
      <a href="#" class="text-main-color hover:text-primary-color theme-font-family">
        Products
      </a>
    </li>
    <li>
      <a href="#" class="text-accent-color theme-font-family font-bold">
        Current Page
      </a>
    </li>
  </ul>
</nav>
```

### Form Elements Example

```html
<form class="bg-background-color theme-padding theme-border-radius theme-shadow">
  <div class="mb-4">
    <label class="block text-main-color theme-font-family font-bold mb-2">
      Product Name
    </label>
    <input 
      type="text" 
      class="w-full theme-padding border border-secondary-color theme-border-radius text-main-color bg-background-color focus:border-accent-color"
      placeholder="Enter product name"
    >
  </div>
  
  <div class="mb-4">
    <label class="block text-main-color theme-font-family font-bold mb-2">
      Description
    </label>
    <textarea 
      class="w-full theme-padding border border-secondary-color theme-border-radius text-main-color bg-background-color focus:border-accent-color"
      rows="3"
      placeholder="Enter description"
    ></textarea>
  </div>
  
  <button class="bg-primary-color text-background-color theme-padding theme-border-radius hover:bg-accent-color theme-font-family font-bold">
    Submit
  </button>
</form>
```

## Color Mapping Reference

| Utility Class Base | Theme Property | Default Value | Use Case |
|-------------------|----------------|---------------|----------|
| `primary-color` | `--theme-primary` | `#3B82F6` | Buttons, links, highlights |
| `secondary-color` | `--theme-secondary` | `#6B7280` | Borders, dividers, subtle elements |
| `accent-color` | `--theme-accent` | `#10B981` | Success states, calls-to-action |
| `background-color` | `--theme-background` | `#FFFFFF` | Widget background |
| `main-color` | `--theme-text` | `#1F2937` | Primary text color |

## Advanced Usage

### Combining with TailwindCSS

These utility classes work alongside TailwindCSS classes:

```html
<div class="flex items-center space-x-2 bg-primary-color text-background-color theme-padding theme-border-radius">
  <svg class="w-4 h-4" fill="currentColor"><!-- icon --></svg>
  <span class="theme-font-family font-medium">Themed Component</span>
</div>
```

### Responsive Design

Combine with responsive prefixes:

```html
<div class="bg-background-color md:bg-primary-color lg:bg-accent-color theme-padding">
  Responsive themed background
</div>
```

### Custom CSS Variables

Access theme variables directly in custom CSS:

```css
.custom-component {
  background: linear-gradient(45deg, var(--theme-primary), var(--theme-accent));
  color: var(--theme-background);
  padding: var(--theme-padding);
  border-radius: var(--theme-border-radius);
  box-shadow: var(--theme-shadow);
}
```

## Best Practices

1. **Use semantic color names**: Choose colors based on their purpose (primary for main actions, accent for success/highlights)

2. **Ensure contrast**: When using background colors, pair them with appropriate text colors for accessibility

3. **Test with different themes**: Verify your templates work well with all predefined themes

4. **Combine utility classes**: Use multiple theme utilities together for consistent theming

5. **Fallback gracefully**: Theme utilities use `!important` but always include base styling

## Migration from Hard-coded Colors

### Before (Hard-coded)
```html
<div class="bg-blue-500 text-white p-4 rounded-lg shadow-md">
  Themed content
</div>
```

### After (Theme-aware)
```html
<div class="bg-primary-color text-background-color theme-padding theme-border-radius theme-shadow">
  Themed content
</div>
```

This ensures your templates automatically adapt to any theme configuration chosen by the user.