# Finder-v2 Subscription-Tiered API Response Filtering – Implementation Plan

_Last updated: {{DATE}}
_

## 1. Purpose
Implement a flexible, maintainable mechanism that returns **tier-appropriate** data for the `/search/by_model` (and future) endpoints so that:

* **Free** users receive only basic vehicle metadata.
* **Paid** users receive the full technical specification payload.

The solution must preserve backward compatibility, performance, and existing widget behaviour.

---

## 2. Objectives & Success Criteria
1. **Backend filter in place** – Responses are filtered server-side based on subscription tier with < 50 ms added latency.
2. **Zero breaking changes** – Existing widgets continue to render without code changes.
3. **Extensibility** – Easy to add tiers, endpoints, or field rules via configuration.
4. **Robust observability** – Metrics & logs show tier, filter time, and payload size.
5. **Comprehensive tests** – ≥ 90 % coverage of new logic; regression suite green.

---

## 3. Guiding Principles
* **Proxy-layer filtering** – Never modify upstream (api3.wheel-size.com) calls.
* **Configuration-first** – No hard-coded field lists; use settings file or DB table.
* **Feature-flagged rollout** – Controlled via `WS_LIVE_SETTINGS` key `widgets.SUBSCRIPTION_FILTER_ENABLED`.
* **Cache-aware** – Cache key must include `subscription_tier`.
* **Fail-open** – If tier unknown, default to **paid** until business decides otherwise.

---

## 4. Out-of-Scope
* Billing & payment flow.
* UI/UX for upgrading plans (handled separately by product team).
* Changes to external Wheel-Fitment API contracts.

---

## 5. Architecture Impact Diagram
```mermaid
graph TD;
  ClientVue[[Finder-v2 Widget]] -->|HTTP| DjangoProxy(FinderV2WidgetProxyView)
  DjangoProxy -->|HTTP| WFAPI[External API v2]
  DjangoProxy -->|filtered JSON| ClientVue
  subgraph Django Layer
    DjangoProxy --> FilterService[[SubscriptionFilterService]]
    DjangoProxy --> TierResolver[[TierResolver]]
  end
```

---

## 6. Implementation Roadmap
| Phase | Goal | ETA | Owners |
|-------|------|-----|--------|
| 0 | Discovery & design sign-off | **+2 d** | Backend + Product |
| 1 | Core infrastructure (tier resolver, config, unit tests) | +5 d | Backend |
| 2 | Proxy integration & caching tweaks | +3 d | Backend |
| 3 | Front-end graceful-degradation & messaging | +4 d | Frontend |
| 4 | Load & regression testing | +2 d | QA |
| 5 | Staged rollout (dev → stg → prod) | +3 d | DevOps |
| 6 | Post-launch monitoring & docs | +1 d | All |

_Total: ≈ 3 weeks calendar time._

---

## 7. Detailed Task Breakdown
### Phase 0 – Discovery
* Confirm **field matrix** with product (which objects/fields per tier).
* Validate subscription data source (widget config vs. user account vs. token).
* Update this doc & circulate for approval.

### Phase 1 – Core Infrastructure
1. **Config schema**
   * Add `SUBSCRIPTION_FILTER_RULES` to `src/settings/base.py`:
     ```python
     SUBSCRIPTION_FILTER_RULES = {
         'free': {'exclude': ['engine', 'technical', 'performance']},
         'paid': {'exclude': []},
     }
     ```
2. **Tier resolver** (`src/apps/widgets/common/subscription.py`)
   * Resolve tier in priority order: **widget-level setting → user account → default**.
3. **Filter service** (`SubscriptionFilterService`)
   * Recursively delete keys defined in rules.
   * Memoise compiled rules for speed.
4. **Unit tests** – edge cases: nested keys, unknown tier.

### Phase 2 – Proxy Integration
* Inject call in `FinderV2WidgetProxyView.dispatch()` after receiving external response.
* Build **tier-aware cache key**: `f"{tier}:{method}:{url}:{sorted(params)}"`.
* Emit structured log `subscription_filter.duration_ms`.

### Phase 3 – Front-end Adjustments
* Update **ResultsDisplay.vue** templates to use optional chaining (`engine?.fuel`).
* Add Tailwind notice banner component (hidden for paid tiers) – _“Upgrade to view full specs”_.
* Ensure missing data states do not break `CustomTemplate` gallery.

### Phase 4 – Testing
* **Unit**: Filter service, tier resolver.
* **Integration**: Full widget flow for free vs. paid in Cypress.
* **Load**: k6 script comparing latency & payload size.

### Phase 5 – Roll-out
* Enable feature flag on **dev**; QA sign-off.
* Gradual enable on **stg** with internal users.
* **Prod** rollout: 10 % → 50 % → 100 % over 48 h.

### Phase 6 – Post-launch
* Dashboards: Grafana panels for payload size & filter errors.
* Add section to `docs/security/subscription-filtering.md`.

---

## 8. Database / Configuration Changes
* **No migrations** expected unless tier is stored per-widget; then add `subscription_tier` `CharField` to `widgets.WidgetInstance` (nullable, default `paid`).
* LiveSetting key: `widgets.SUBSCRIPTION_FILTER_ENABLED` (bool).

---

## 9. Caching Strategy
* Use existing Redis-backed cache.
* Key includes tier to avoid leakage.
* TTL aligns with external API cache headers (currently 30 min).

---

## 10. Security & Compliance
* Filtering must occur **before** logging of response bodies.
* Add automated Snyk policy to flag accidental sensitive field exposure.
* Pen-test checklist update.

---

## 11. Risks & Mitigations
| Risk | Likelihood | Impact | Mitigation |
|------|-----------|--------|-----------|
| Tier mis-detection exposes premium data | Low | High | Extensive unit tests; fail-open to **paid** tier initially. |
| Latency increase | Med | Med | Profile filter; optimise JSON parsing; add cache. |
| Frontend template breaks | Med | Low | Optional chaining; QA scenarios. |
| Cache key collision leaks data | Low | High | Tier-prefixed cache keys + automated tests. |

---

## 12. Acceptance Criteria
* [ ] Free tier receives ≤ 40 % of original payload size.
* [ ] Paid tier response unchanged.
* [ ] End-to-end tests pass for both tiers.
* [ ] Grafana dashboard shows filter timings < 5 ms p95.
* [ ] Documentation published.

---

## 13. References
* `docs/development/finder-v2-knowledge-transfer.md`
* `docs/security/csrf-token-algorithm.md`
* Internal ticket: **WS-2317** – Subscription-Gated Data

---

> _Prepared by:_ **Backend Team**  
> _Reviewers:_ Product, Frontend, DevOps, QA  
> _Status:_ **Draft ➜ In-Review ➜ Approved** 