# Pytest Docker Integration Plan

**Project**: wheel-size-services  
**Purpose**: Integrate pytest testing framework into Docker environment  
**Date**: December 2025  
**Status**: Planning Phase

## Overview

This plan outlines the complete integration of pytest into the Docker-based development environment for the wheel-size-services project. The goal is to enable reliable, fast, and comprehensive testing of the finder-v2 widget and other components.

## Current State Analysis

### ✅ What's Working
- Docker environment with Python 3.12 and Poetry
- Basic project structure with tests directory
- Django 4.2 application running in containers
- PyTest already listed in pyproject.toml dev dependencies

### ❌ Current Issues
- Poetry virtual environment configuration mismatch
- Missing test-specific Django settings
- No pytest configuration file
- Complex dependency resolution in Docker
- Tests failing due to missing packages

### 🎯 Success Criteria
- Run `pytest tests/widget/finder_v2/test_region_filtering.py` successfully in Docker
- Fast test execution (< 30 seconds for unit tests)
- Proper Django test database isolation
- Easy commands for developers to run tests
- CI/CD ready test setup

## Implementation Plan

### Phase 1: Core Configuration Setup

#### Step 1.1: Create Test-Specific Django Settings
**File**: `src/settings/test.py`

```python
"""
Django settings for testing with pytest.
Optimized for speed and isolation.
"""

from .base import *

# Use in-memory SQLite for faster tests
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'OPTIONS': {
            'timeout': 20,
        }
    }
}

# Disable migrations for faster tests
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Disable logging during tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'root': {
        'handlers': ['null'],
    },
}

# Disable cache during tests
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Disable email sending during tests
EMAIL_BACKEND = 'django.core.mail.backends.dummy.EmailBackend'

# Use a simple password hasher for faster tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Disable CSRF for API tests
CSRF_COOKIE_SECURE = False
CSRF_USE_SESSIONS = False

# Test-specific settings
SECRET_KEY = 'test-secret-key-for-testing-only'
DEBUG = False
ALLOWED_HOSTS = ['*']

# Disable debug toolbar
if 'debug_toolbar' in INSTALLED_APPS:
    INSTALLED_APPS.remove('debug_toolbar')

# Minimal middleware for tests
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
]

# Disable static file handling during tests
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
```

**Estimated Time**: 30 minutes  
**Dependencies**: None  
**Risk**: Low

#### Step 1.2: Create Pytest Configuration
**File**: `pytest.ini`

```ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = src.settings.test
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
addopts = 
    --tb=short
    --strict-markers
    --disable-warnings
    --reuse-db
    --nomigrations
    -v
    --durations=10
testpaths = tests
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    region_filtering: marks tests related to region filtering functionality
    api: marks tests that make API calls
    widget: marks tests for widget functionality
    finder_v2: marks tests specific to finder-v2 widget

# Django-specific configuration
django_find_project = true
```

**Estimated Time**: 15 minutes  
**Dependencies**: Step 1.1  
**Risk**: Low

### Phase 2: Docker Environment Fixes

#### Step 2.1: Fix Poetry Virtual Environment Configuration

**Option A: Direct Python Approach (Recommended)**

Modify `docker-compose.yml` to use Python directly instead of `poetry run`:

```yaml
services:
  web:
    # ... existing configuration ...
    command: >
      sh -c "cd /code &&
             export PATH=/root/.pyenv/versions/3.12.0/bin:$PATH &&
             export PYTHONPATH=/code:/code/src &&
             find /code/ -name '*.pyc' -delete &&
             /root/.pyenv/versions/3.12.0/bin/python manage.py collectstatic --no-input -v 0 &&
             /root/.pyenv/versions/3.12.0/bin/python manage.py runserver 0:8000 --noreload --insecure ||
             tail -f /dev/null"
```

**Option B: Fix Poetry Virtual Environment**

Modify `docker/Dockerfile` to create proper virtual environment:

```dockerfile
# Replace this line:
# RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false

# With these lines:
RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create true
RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project true
RUN /root/.pyenv/versions/3.12.0/bin/poetry install --no-root
```

**Estimated Time**: 1 hour (including rebuild)  
**Dependencies**: None  
**Risk**: Medium (requires container rebuild)

#### Step 2.2: Create Test Runner Scripts

**File**: `scripts/test_runner.sh`

```bash
#!/bin/bash
# Test runner script for Docker environment

set -e

echo "🧪 Running pytest in Docker container..."

# Configuration
PYTHON_PATH="/root/.pyenv/versions/3.12.0/bin/python"
DJANGO_SETTINGS="src.settings.test"
PROJECT_PATH="/code"

# Set environment variables
export DJANGO_SETTINGS_MODULE=$DJANGO_SETTINGS
export PYTHONPATH=$PROJECT_PATH:$PROJECT_PATH/src

# Change to project directory
cd $PROJECT_PATH

# Run tests with proper configuration
echo "📍 Running tests with settings: $DJANGO_SETTINGS"
echo "📍 Python path: $PYTHONPATH"
echo "📍 Working directory: $(pwd)"

# Execute pytest with all arguments passed through
$PYTHON_PATH -m pytest "$@"
```

**File**: `scripts/test_commands.sh`

```bash
#!/bin/bash
# Common test commands for easy access

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🧪 Available Test Commands${NC}"
echo "=================================="

echo -e "${YELLOW}1. Run all tests:${NC}"
echo "docker compose exec web bash /code/scripts/test_runner.sh"

echo -e "${YELLOW}2. Run region filtering tests:${NC}"
echo "docker compose exec web bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py -v"

echo -e "${YELLOW}3. Run specific test method:${NC}"
echo "docker compose exec web bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py::RegionParameterNormalisationTest::test_region_array_param_is_normalised_and_overrides_widget_config -v"

echo -e "${YELLOW}4. Run tests with coverage:${NC}"
echo "docker compose exec web bash /code/scripts/test_runner.sh --cov=src --cov-report=html"

echo -e "${YELLOW}5. Run only fast tests:${NC}"
echo "docker compose exec web bash /code/scripts/test_runner.sh -m 'not slow'"

echo -e "${YELLOW}6. Run tests in parallel:${NC}"
echo "docker compose exec web bash /code/scripts/test_runner.sh -n auto"

echo "=================================="
```

**Estimated Time**: 45 minutes  
**Dependencies**: Step 2.1  
**Risk**: Low

### Phase 3: Enhanced Testing Setup

#### Step 3.1: Add Test Service to Docker Compose

**File**: Add to `docker-compose.yml`

```yaml
  test:
    image: ws_services
    build:
      context: .
      dockerfile: ./docker/Dockerfile
    hostname: ws_services_test
    container_name: ws_services_test
    entrypoint: []
    command: >
      sh -c "echo 'Test container ready. Use: docker compose run test [pytest-args]' && tail -f /dev/null"
    volumes:
      - .:/code
    environment:
      - DJANGO_SETTINGS_MODULE=src.settings.test
      - PYTHONPATH=/code:/code/src
      - DISABLE_MIGRATIONS=1
    profiles:
      - test
    depends_on:
      - postgres15
```

**Usage Commands**:
```bash
# Run all tests
docker compose --profile test run --rm test bash /code/scripts/test_runner.sh

# Run specific test
docker compose --profile test run --rm test bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py -v
```

**Estimated Time**: 30 minutes  
**Dependencies**: Step 2.1, 2.2  
**Risk**: Low

#### Step 3.2: Add Testing Dependencies

Update `pyproject.toml` to ensure all testing dependencies are included:

```toml
[tool.poetry.group.dev.dependencies]
# ... existing dependencies ...
pytest = "^7.4.0"
pytest-django = "^4.5.2"
pytest-cov = "^4.1.0"
pytest-xdist = "^3.3.1"  # For parallel test execution
pytest-mock = "^3.11.1"  # For mocking
factory-boy = "^3.3.0"   # For test data factories
```

**Estimated Time**: 15 minutes  
**Dependencies**: None  
**Risk**: Low (requires container rebuild)

### Phase 4: Test Organization and Enhancement

#### Step 4.1: Organize Test Structure

```
tests/
├── conftest.py                          # Pytest configuration and fixtures
├── factories/                           # Test data factories
│   ├── __init__.py
│   ├── widget_factories.py
│   └── user_factories.py
├── fixtures/                            # Test data fixtures
│   ├── widget_configs.json
│   └── api_responses.json
├── unit/                                # Unit tests
│   ├── __init__.py
│   └── test_models.py
├── integration/                         # Integration tests
│   ├── __init__.py
│   └── test_api_endpoints.py
└── widget/                              # Widget-specific tests
    ├── __init__.py
    ├── finder_v2/
    │   ├── __init__.py
    │   ├── test_region_filtering.py     # Existing test
    │   ├── test_brand_filtering.py      # New test
    │   ├── test_api_proxy.py           # New test
    │   └── test_configuration.py       # New test
    └── common/
        ├── __init__.py
        └── test_widget_base.py
```

**File**: `tests/conftest.py`

```python
"""
Pytest configuration and shared fixtures.
"""

import pytest
from django.test import RequestFactory
from django.contrib.auth.models import User, AnonymousUser
from rest_framework.request import Request

@pytest.fixture
def request_factory():
    """Provide Django RequestFactory for tests."""
    return RequestFactory()

@pytest.fixture
def anonymous_request(request_factory):
    """Create an anonymous request."""
    request = request_factory.get('/')
    request.user = AnonymousUser()
    return request

@pytest.fixture
def authenticated_request(request_factory, django_user_model):
    """Create an authenticated request."""
    user = django_user_model.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='testpass123'
    )
    request = request_factory.get('/')
    request.user = user
    return request

@pytest.fixture
def drf_request(request_factory):
    """Create a DRF Request object."""
    http_request = request_factory.get('/')
    return Request(http_request)

@pytest.fixture
def widget_config_data():
    """Sample widget configuration data."""
    return {
        "content": {
            "regions": ["usdm", "cdm"],
            "filter": {
                "by": "brands",
                "brands": ["toyota", "honda"],
                "brands_exclude": []
            },
            "only_oem": False
        },
        "interface": {
            "flow_type": "primary",
            "api_version": "v2"
        }
    }

@pytest.mark.django_db
@pytest.fixture
def sample_widget(django_user_model):
    """Create a sample widget instance."""
    from src.apps.widgets.common.models.config import WidgetConfig
    
    user = django_user_model.objects.create_user(
        username='widgetuser',
        email='<EMAIL>'
    )
    
    return WidgetConfig.objects.create(
        user=user,
        widget_type='finder-v2',
        name='Test Widget',
        config_data='{"test": "data"}'
    )
```

**Estimated Time**: 2 hours  
**Dependencies**: Previous steps  
**Risk**: Low

#### Step 4.2: Create Additional Test Cases

**File**: `tests/widget/finder_v2/test_brand_filtering.py`

```python
"""
Tests for brand filtering functionality in finder-v2 widget.
"""

import pytest
from django.test import RequestFactory
from rest_framework.request import Request
from src.apps.widgets.api_proxy.views import FinderV2WidgetProxyView

@pytest.mark.django_db
@pytest.mark.finder_v2
class TestBrandFiltering:
    """Test brand filtering functionality."""
    
    def setup_method(self):
        self.factory = RequestFactory()
        self.view = FinderV2WidgetProxyView()
        self.view.proxy_settings = type('ps', (), {'DISALLOWED_PARAMS': []})
    
    def test_include_brands_parameter_generation(self):
        """Test that include brands generates correct parameters."""
        # Test implementation here
        pass
    
    def test_exclude_brands_parameter_generation(self):
        """Test that exclude brands generates correct parameters."""
        # Test implementation here
        pass
    
    def test_brand_and_region_filtering_combined(self):
        """Test combined brand and region filtering."""
        # Test implementation here
        pass
```

**Estimated Time**: 3 hours  
**Dependencies**: Step 4.1  
**Risk**: Medium

### Phase 5: Documentation and Developer Experience

#### Step 5.1: Create Testing Documentation

**File**: `docs/development/testing-guide.md`

```markdown
# Testing Guide

## Quick Start

### Run All Tests
```bash
docker compose exec web bash /code/scripts/test_runner.sh
```

### Run Specific Test File
```bash
docker compose exec web bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py -v
```

### Run Tests with Coverage
```bash
docker compose exec web bash /code/scripts/test_runner.sh --cov=src --cov-report=html
```

## Test Categories

- **Unit Tests**: Fast, isolated tests of individual functions/methods
- **Integration Tests**: Tests that verify component interactions
- **Widget Tests**: Specific tests for widget functionality
- **API Tests**: Tests for API endpoints and proxy functionality

## Writing Tests

### Test Naming Convention
- Test files: `test_*.py`
- Test classes: `Test*`
- Test methods: `test_*`

### Using Fixtures
[Examples and best practices]

### Mocking External APIs
[Examples of mocking external API calls]
```

**Estimated Time**: 1 hour  
**Dependencies**: All previous steps  
**Risk**: Low

#### Step 5.2: Create Developer Shortcuts

**File**: `Makefile`

```makefile
# Testing shortcuts
.PHONY: test test-unit test-integration test-widget test-coverage

test:
	docker compose exec web bash /code/scripts/test_runner.sh

test-unit:
	docker compose exec web bash /code/scripts/test_runner.sh tests/unit/ -v

test-integration:
	docker compose exec web bash /code/scripts/test_runner.sh tests/integration/ -v

test-widget:
	docker compose exec web bash /code/scripts/test_runner.sh tests/widget/ -v

test-region:
	docker compose exec web bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py -v

test-coverage:
	docker compose exec web bash /code/scripts/test_runner.sh --cov=src --cov-report=html --cov-report=term

test-fast:
	docker compose exec web bash /code/scripts/test_runner.sh -m "not slow" -x

test-parallel:
	docker compose exec web bash /code/scripts/test_runner.sh -n auto
```

**Usage**:
```bash
make test-region    # Run region filtering tests
make test-fast      # Run only fast tests
make test-coverage  # Generate coverage report
```

**Estimated Time**: 30 minutes  
**Dependencies**: All previous steps  
**Risk**: Low

## Implementation Timeline

### Week 1: Core Setup
- **Day 1-2**: Steps 1.1, 1.2 (Configuration files)
- **Day 3-4**: Step 2.1 (Docker fixes)
- **Day 5**: Step 2.2 (Test runner scripts)

### Week 2: Enhancement
- **Day 1-2**: Step 3.1, 3.2 (Test service and dependencies)
- **Day 3-4**: Step 4.1 (Test organization)
- **Day 5**: Step 4.2 (Additional test cases)

### Week 3: Polish
- **Day 1-2**: Step 5.1 (Documentation)
- **Day 3**: Step 5.2 (Developer shortcuts)
- **Day 4-5**: Testing and refinement

## Risk Assessment

### High Risk Items
- Docker container rebuild (Step 2.1) - **Mitigation**: Test in development first
- Poetry configuration changes - **Mitigation**: Have rollback plan ready

### Medium Risk Items
- New test dependencies - **Mitigation**: Lock versions in pyproject.toml
- Complex test fixtures - **Mitigation**: Start simple, iterate

### Low Risk Items
- Configuration files - Easy to modify and test
- Documentation - No system impact
- Shell scripts - Easy to debug and fix

## Success Metrics

### Immediate Goals (Week 1)
- ✅ `pytest tests/widget/finder_v2/test_region_filtering.py` runs successfully
- ✅ Test execution time < 30 seconds
- ✅ Clear error messages when tests fail

### Medium-term Goals (Week 2)
- ✅ All existing tests pass
- ✅ New test cases for brand filtering
- ✅ Test coverage reports generated

### Long-term Goals (Week 3)
- ✅ Developer-friendly test commands
- ✅ Comprehensive test documentation
- ✅ CI/CD integration ready

## Rollback Plan

If issues arise during implementation:

1. **Immediate Rollback**: Revert docker-compose.yml changes
2. **Container Rebuild Issues**: Use backup container image
3. **Test Failures**: Disable problematic tests temporarily
4. **Dependency Issues**: Pin working versions in pyproject.toml

## Next Steps

1. **Review and Approve Plan**: Stakeholder review of this plan
2. **Create Branch**: `feature/pytest-integration`
3. **Begin Implementation**: Start with Phase 1
4. **Regular Testing**: Test each step before proceeding
5. **Documentation**: Update as implementation progresses

## Resources

- [Pytest Documentation](https://docs.pytest.org/)
- [pytest-django Documentation](https://pytest-django.readthedocs.io/)
- [Django Testing Best Practices](https://docs.djangoproject.com/en/4.2/topics/testing/)
- [Docker Testing Strategies](https://docs.docker.com/language/python/run-tests/)

---

**Last Updated**: December 2025  
**Next Review**: After Phase 1 completion 