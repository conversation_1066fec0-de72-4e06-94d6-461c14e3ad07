# Finder-v2 Widget – Dropdown Auto-Expand Implementation Guide

Last Modified: 2025-06-24 04:35 UTC+6

## Purpose

To streamline the Year → Make → Model → (Generation) → Modification workflow the widget now **automatically opens** the next dropdown as soon as its data is loaded.  This eliminates a click per step and makes the form feel wizard-like.

## Key Concepts

1. **`stateLoaded` Flag** – Each API call in `store/finder.js` sets a boolean (e.g. `stateLoadedMakes`) to `true` once data is fetched.
2. **`autoExpand` Prop** – Opt-in per selector.  Defaults to `true`; first selector in each flow passes `:auto-expand="false"` so the user still starts the process manually.
3. **Programmatic Click** – When the watcher detects `stateLoaded` → `true` **and** there are options, we programmatically click the `<ListboxButton>` which opens the Headless-UI Listbox.
4. **Focus & Accessibility** – The button is focused before the synthetic click to satisfy keyboard-navigation users.

## Code Hot-Spots

| File | Section | Notes |
|------|---------|-------|
| `CustomSelector.vue` | `props` | adds `autoExpand`, default `true`. |
| *same* | `watch( [stateLoaded, options.length] … )` | triggers click when ready & `autoExpand` allowed. |
| *same* | `ref="buttonRef" as="button"` | ensures the template ref points to a real `<button>` element so `.click()` works. |
| `VehicleSearch.vue` | Primary flow Year selector + Alternative flow Make selector | pass `:auto-expand="false"` to disable the first-hop auto-click. |
| `store/finder.js` | sets each `stateLoaded* = true` after successful API response. |

### Core Snippet

```18:60:src/apps/widgets/finder_v2/app/src/components/CustomSelector.vue
watch(
  () => [props.stateLoaded, props.options.length],
  ([loaded, len], [prevLoaded]) => {
    if (!prevLoaded && loaded && len > 0 && !props.disabled && props.autoExpand) {
      // Delay 200 ms for DOM/transition readiness
      setTimeout(async () => {
        await nextTick()
        const el = buttonRef.value.$el ?? buttonRef.value
        if (el && typeof el.click === 'function') {
          el.focus({ preventScroll: true })
          el.click()
        }
      }, 200)
    }
  },
  { flush: 'post' }
)
```

## Flow Diagram (Primary Flow)

```mermaid
graph LR
  A[User chooses Year] --> B[store.loadMakes()]
  B -->|sets stateLoadedMakes=true| C[CustomSelector (Make) watcher]
  C -->|auto click| D[Make dropdown opens]
  D --> E[User selects Make] --> F[loadModels()] … and so on
```

## Edge-Case Handling

| Case | Behaviour |
|------|-----------|
| No data returned (`options.length === 0`) | No auto-expand. |
| Selector disabled (`disabled` prop) | No auto-expand. |
| User manually opened another dropdown while loading | Watcher checks current `document.activeElement` – skips auto-click if user is already interacting elsewhere. |

## 2025-06-24 Enhancements

After initial release the following robustness measures were added:

1. **Respects Reduced-Motion Preference** – If the user has `prefers-reduced-motion: reduce`, auto-expand is skipped, keeping the interface static for motion-sensitive users.
2. **Active-Element Guard** – Auto-expand is suppressed only when the active element is an `<input>` or `<textarea>` that is *outside* the current selector, so we don't interrupt someone who is typing.
3. **Error Boundary** – The focus / click sequence is wrapped in a `try…catch`; if it fails we log a warning and the user can still click manually.

These tweaks live inside the same watcher and do not change public APIs.

### Updated Watcher Extract

```19:65:src/apps/widgets/finder_v2/app/src/components/CustomSelector.vue
const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches

watch(
  () => [props.stateLoaded, props.options.length],
  ([loaded, len], [prevLoaded]) => {
    if (!prevLoaded && loaded && len > 0 && !props.disabled && props.autoExpand && !prefersReducedMotion) {
      const activeEl = document.activeElement
      const btnEl    = buttonRef.value.$el ?? buttonRef.value
      const userBusy = activeEl && activeEl !== document.body && btnEl && !btnEl.contains(activeEl) && ['INPUT','TEXTAREA'].includes(activeEl.tagName)

      if (userBusy) return   // don't steal focus from ongoing text entry

      setTimeout(async () => {
        await nextTick()
        try {
          btnEl.focus({ preventScroll:true })
          btnEl.click()
          triggerResize()
        } catch (e) {
          console.warn('Auto-expand failed:', e)
        }
      }, 200)
    }
  },
  { flush:'post' }
)
```

## Tuning Delays

* **Watcher delay (200 ms)** – covers axios latency + tailwind/Headless-UI transition start.  Adjust if dropdown animation timing changes.
* **iframe resize** – After auto-click we invoke `triggerResize()` so iframe grows to fit the newly opened menu (see iframe guide).

## Testing Checklist

1. **Primary flow** – Year → Make → Model → Modification should require **3** clicks instead of 4.  Dropdowns open automatically after each data fetch.
2. **Alternative flow** – Make → Model → Generation → Modification behaves similarly.
3. **First step** – No auto-expand; user must open Year / Make manually.
4. **Disabled states** – If a selector is disabled the auto-click logic is skipped.

## Troubleshooting

* **Dropdown didn't open** – Verify `buttonRef` points to a real button (`as="button"`).  Check DevTools for `console.debug('AutoExpand →', …)` messages.
* **Unexpected auto-click** – Ensure `auto-expand="false"` is set on that particular selector in `VehicleSearch.vue`.

---

© Wheel-Size Services – Internal documentation.