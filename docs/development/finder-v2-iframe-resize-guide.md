# Finder-v2 Widget – Iframe Height Auto-Adjustment Guide

Last Modified: 2025-07-24 12:30 UTC+6

## Overview

The finder-v2 widget is usually embedded in third-party pages through an `<iframe>` generated by the widget portal.  Because the widget's height varies (search form, dropdowns, results table, etc.) we rely on **iframeResizer** to grow/shrink the iframe automatically.  This document explains how the integration works after the July 2025 fixes.

## Technology Stack

* **iframeResizer 4.x** – two-part library:
  * **content-window helper** (lives inside iframe) – `static/finder_v2/js/libs/iframeResizer.contentWindow.min.js`
  * **host-page script** (lives in parent page) – `static/finder_v2/js/libs/iframeResizer.min.js`
* **Vue 3 components** – dropdowns (Headless-UI `Listbox`) and results table.
* **Django templates** – inject the content-window script into `widgets/finder_v2/iframe/page.html`.

## High-Level Flow

```mermaid
sequenceDiagram
    Browser→>Iframe: load page.html
    page.html→>iframeResizer.contentWindow: initialise
    loop Any DOM change
        Vue Components-->>parentIFrame.size(): request resize
        parent window-->>Iframe: set new height via postMessage
    end
```

## Key Integration Points

| Location | Code | Purpose |
|----------|------|---------|
| `iframe/page.html` | `<script src="…iframeResizer.contentWindow.min.js"></script>` | Bootstraps content-window auto-resize helper. |
| `finder_v2/page.html` (config & demo preview) | `<script src="…iframeResizer.min.js"></script>` + `iFrameResize({...}, previewFrame)` | Initialises parent-side resize for admin/demo preview iframe. |
| `finder_v2/page.html` (config & demo preview) | `<iframe … autoresize="1" scrolling="no">` | Removes fixed height & disables inner scrollbars. |
| `FinderV2Widget.vue` | `data-iframe-height` attribute on root div | Signals tagged-element strategy & allows initial height detection. |
| `src/apps/widgets/finder_v2/app/src/stores/finder.js` | `window.parentIFrame.size()` (after results / clear) | Resize when results block appears or disappears. |
| `src/apps/widgets/finder_v2/app/src/components/CustomSelector.vue` | `triggerResize()` helper | Resize when any `<Listbox>` is opened or closed. Includes a second call +350 ms for transition completion. |
| `src/apps/widgets/embed/static/widget/code/local/ws-widget.js` | `iFrameResize({...}, iframe[0])` | Parent-side initialization with enhanced configuration for cross-domain iframe resizing. |

### CustomSelector.vue Details

```12:60:src/apps/widgets/finder_v2/app/src/components/CustomSelector.vue
// ... existing code ...
const triggerResize = () => {
  if (window.parentIFrame && window.parentIFrame.size) {
    window.parentIFrame.size()               // immediate
    setTimeout(() => {
      window.parentIFrame && window.parentIFrame.size() // after 350 ms
    }, 350)
  }
}
```

Why two calls?

1. **Immediate** – accounts for the button taking the focus row.
2. **Delayed (350 ms)** – after Headless-UI finishes the dropdown enter / leave transition so the full menu height is known.

`triggerResize()` is wired to:

* Every manual click on a Listbox button.
* The programmatic click used by the auto-expand feature.
* A watcher on `modelValue` (fires when the dropdown closes after selection).

### store/finder.js Details

* After the search API resolves, the results table is added to the DOM – call `parentIFrame.size()` after `results.value` is updated.
* Clearing results (new search) calls `size()` after 50 ms to shrink back.

## Troubleshooting

1. **Iframe does not grow when dropdown opens**
   * Confirm the dropdown `ListboxOptions` is **not** absolutely positioned (`position: static`/`relative`).
   * Check DevTools console for `parentIFrame is undefined` – usually means the host page removed the iframeResizer initialisation script.
2. **Jumpiness / double scroll**
   * Ensure only one layer of scrollbars – host page must not clip the iframe.
3. **Height updates delayed**
   * Adjust the secondary timeout in `triggerResize()` if Headless-UI transition duration changes.
4. **TypeError: function is not defined in ws-widget.js**
   * Ensure `getLoadingHeight` function has support for `finder-v2` widget type.
   * Check that the widget type passed matches the supported types in the function.

## Recent Fixes (July 2025)

### Fixed Widget Embedding Script Issues
- **Issue**: `ws-widget.js` was missing support for `finder-v2` widget type in `getLoadingHeight` function
- **Fix**: Added `'finder-v2': function() { return 150; }` to support the new widget type
- **Location**: `src/apps/widgets/embed/static/widget/code/local/ws-widget.js:107-109`

### Enhanced iFrame Resizer Configuration  
- **Issue**: iFrame resizer configuration was minimal and lacked proper settings for finder-v2
- **Fix**: Enhanced configuration with `autoResize: true`, `resizeFrom: 'child'`, `enablePublicMethods: true`, `warningTimeout: 0`
- **Location**: `src/apps/widgets/embed/static/widget/code/local/ws-widget.js:136-145`

### Fixed Duplicate Script Includes
- **Issue**: `iframe/page.html` had duplicate `iframeResizer.contentWindow.min.js` script tags
- **Fix**: Removed duplicate script tag to prevent conflicts
- **Location**: `src/templates/widgets/finder_v2/iframe/page.html:109-112`

### Fixed Search History Positioning Issues
- **Issue**: SearchHistoryIcon used fixed positioning causing it to be cut off in iframe and not trigger iframe resizing
- **Fix**: Changed to relative positioning with inline accordion layout using flex for search items
- **Changes**:
  - Replaced fixed floating button with full-width toggle button above search form
  - Changed accordion from absolute to relative positioning  
  - Search history items now display inline using flexbox (2 columns on desktop, 1 on mobile)
  - Added iframe resize triggers when accordion opens/closes
  - Improved layout flow: Search History → Search Form → Search Results
- **Location**: `src/apps/widgets/finder_v2/app/src/components/SearchHistoryIcon.vue`

## Best Practices

* **Never** hard-code a fixed height for the iframe – leave `height` attr empty and rely on iFrameResize.
* Always include **both** parts of iframeResizer (host + content) when embedding widgets outside of portal (e.g. marketing pages).
* Always run `./deploy-finder-v2.sh` after touching JS to update the minified bundle shipped to production.
* Keep `iframeResizer.contentWindow.min.js` version in sync with the parent-side script (served by portal).

## Related Documents

* [finder-v2-knowledge-transfer.md](./finder-v2-knowledge-transfer.md) – complete widget documentation.
* [widget-api-testing-guide.md](../security/widget-api-testing-guide.md) – CSRF & API troubleshooting.

---

© Wheel-Size Services – Internal documentation. 