# Finder-v2 Demo Endpoint - Plain JavaScript Implementation

## Overview

This document describes the implementation of the new finder-v2 demo endpoint that replaces Angular with plain JavaScript for the widget configuration interface.

## Problem Solved

The original `/widget/finder-v2/try/` endpoint was experiencing issues with Angular controllers, particularly with the regions dropdown showing "Sorry, list of regions is currently unavailable because of some server problems". Instead of debugging complex Angular dependencies, we created a clean, modern alternative.

## Solution: `/widget/finder-v2/config-demo/`

A new demo endpoint that provides the same functionality as the try endpoint but uses **plain JavaScript instead of Angular**.

### Key Benefits

- ✅ **No Angular Framework**: Eliminates Angular dependency and related debugging complexity
- ✅ **Faster Loading**: Reduced JavaScript bundle size and faster initialization
- ✅ **Modern Vanilla JS**: Clean, maintainable code using modern JavaScript features
- ✅ **Better Browser Compatibility**: Works across more browsers without framework requirements
- ✅ **Easier Debugging**: Straightforward JavaScript without Angular magic

## Implementation Details

### File Structure

```
src/templates/widgets/finder_v2/demo/
├── page.html           # Main demo configuration page
├── content.html        # Brand/country/region filtering interface
└── interface.html      # Widget tabs and flow type configuration
```

### Widget Type Configuration

Updated `src/apps/widgets/finder_v2/widget_type.py`:

```python
@class_cached_property
def templates(cls):
    return {
        'config': 'widgets/finder_v2/config/page.html',
        'demo': 'widgets/finder_v2/demo/page.html'  # ← New demo template
    }
```

### Core JavaScript Functions

#### Tag Selection System
Replaces Angular's `TagChoiceCtrl` with vanilla JavaScript:

```javascript
function initTagChoiceControls() {
  document.querySelectorAll('.tag-cloud').forEach(function(cloud) {
    const hiddenInput = cloud.parentElement.querySelector('input[type="text"]');
    const tags = {};
    
    cloud.querySelectorAll('span[data-slug]').forEach(function(span) {
      span.addEventListener('click', function(e) {
        e.preventDefault();
        // Toggle tag selection
        if (tags[slug]) {
          delete tags[slug];
          span.classList.remove('active');
        } else {
          tags[slug] = true;
          span.classList.add('active');
        }
        hiddenInput.value = Object.keys(tags).join(',');
      });
    });
  });
}
```

#### Tab Management
Replaces Angular tab controllers:

```javascript
function initTabControls() {
  document.querySelectorAll('input[name="content-by"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
      // Hide all tab panes
      document.querySelectorAll('.tab-pane').forEach(function(pane) {
        pane.classList.remove('active');
      });
      
      // Show selected tab
      if (this.value) {
        const targetTab = document.getElementById('tab-' + this.value);
        if (targetTab) {
          targetTab.classList.add('active');
        }
      }
    });
  });
}
```

#### Live Preview Updates
Handles widget dimension changes:

```javascript
function initPreviewUpdates() {
  const widthInput = document.querySelector('input[name="interface-width"]');
  const iframe = document.querySelector('.iframe-preview iframe');
  
  if (widthInput && iframe) {
    widthInput.addEventListener('input', function() {
      const width = parseInt(this.value) || 600;
      iframe.style.width = width + 'px';
      iframe.setAttribute('width', width);
    });
  }
}
```

### CSS Styling

Clean, modern styling without Bootstrap dependencies:

```css
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.tag-cloud span {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.tag-cloud span.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}
```

## Usage

### Access the Demo Endpoint

1. **URL**: `http://development.local:8000/widget/finder-v2/config-demo/`
2. **Authentication**: Requires login (same as regular config endpoint)
3. **Functionality**: Full widget configuration with live preview

### Form Fields Supported

- **Content Filtering**:
  - `content-brands` - Selected brands for filtering
  - `content-countries` - Selected countries for filtering  
  - `content-markets` - Selected regions/markets for prioritization
  - `content-brands_exclude` - Brands to exclude
  - `content-countries_exclude` - Countries to exclude

- **Interface Configuration**:
  - `interface-tabs` - Enabled widget tabs (checkboxes)
  - `interface-primary_tab` - Primary tab selection
  - `interface-flow_type` - API flow type (primary/alternative)
  - `interface-button_to_ws` - Show/hide "See on Wheel-Size.com" button

### Testing the Implementation

```bash
# Check endpoint accessibility (should return 302 redirect to login)
curl -I "http://development.local:8000/widget/finder-v2/config-demo/"

# Verify no Angular dependencies in response
curl -s "http://development.local:8000/widget/finder-v2/config-demo/" | grep -i "ng-"
# Should return no results

# Check for plain JS implementation
curl -s "http://development.local:8000/widget/finder-v2/config-demo/" | grep "Plain JavaScript"
# Should find the implementation marker
```

## Migration Path

This implementation serves as a **blueprint for migrating other widget configuration pages** away from Angular:

1. **Copy Template Structure**: Use the demo templates as a starting point
2. **Adapt JavaScript Functions**: Modify the tag selection and form handling for specific widgets
3. **Update Widget Type**: Add demo template mapping
4. **Test Functionality**: Verify form submission and preview updates work correctly

## Maintenance

### Adding New Form Fields

1. Add the field to the Django form class
2. Add the HTML input in the appropriate template
3. Update JavaScript initialization if interactive behavior is needed

### Modifying Tag Selection

The tag selection system is modular and can be easily extended:

```javascript
// Add custom tag validation
span.addEventListener('click', function(e) {
  e.preventDefault();
  
  // Custom validation logic here
  if (customValidation(slug)) {
    // Toggle tag as normal
  } else {
    alert('This selection is not allowed');
  }
});
```

## Future Improvements

1. **Dynamic Choice Loading**: Implement AJAX loading for brands/countries/regions
2. **Enhanced Validation**: Add client-side form validation
3. **Accessibility**: Add ARIA labels and keyboard navigation
4. **Performance**: Implement virtual scrolling for large tag lists

## Conclusion

The finder-v2 demo endpoint successfully demonstrates that modern widget configuration interfaces can be built without heavy framework dependencies. The plain JavaScript implementation is:

- **Maintainable**: Easy to understand and modify
- **Performant**: Fast loading and responsive
- **Compatible**: Works across different browsers
- **Extensible**: Easy to add new features

This approach can serve as a model for future widget development and Angular migration efforts. 