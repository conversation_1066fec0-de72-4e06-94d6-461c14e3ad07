# Finder-v2 Theming System Documentation

## Overview

The Finder-v2 theming system provides comprehensive theme customization capabilities for widget appearance and behavior. This system includes dynamic CSS generation, real-time preview, predefined themes, and advanced customization options.

## Architecture

### Core Components

1. **WidgetTheme Model** (`src/apps/widgets/common/models/theme.py`)
   - Stores theme configuration with 17 customizable properties
   - Includes advanced styling controls and performance optimizations
   - Supports JSON export/import functionality

2. **ThemeGenerator** (`src/apps/widgets/finder_v2/utils/theme_generator.py`)
   - Generates CSS custom properties from theme configuration
   - Includes performance optimization with caching and monitoring
   - Validates generated CSS for security and compatibility

3. **Theme Template Tags** (`src/apps/widgets/finder_v2/templatetags/theme_tags.py`)
   - Provides Django template tags for theme CSS injection
   - Includes fallback support and error handling

4. **Vue Component Integration** (`src/apps/widgets/finder_v2/app/src/components/FinderV2Widget.vue`)
   - Applies theme styles to Vue 3 components
   - Supports responsive design and dynamic theme switching

## Theme Configuration

### Basic Properties

```python
# Color Configuration
primary_color = '#3B82F6'      # Main brand color
secondary_color = '#6B7280'    # Secondary elements
accent_color = '#10B981'       # Accent and highlights
background_color = '#FFFFFF'   # Widget background
text_color = '#1F2937'         # Text color

# Typography
font_family = 'system-ui'      # Font family
base_font_size = '16px'        # Base font size
font_weight = '400'            # Font weight
line_height = '1.5'            # Line height
letter_spacing = '0'           # Letter spacing

# Layout
element_padding = '0.75rem'    # Element padding
element_margin = '0.5rem'      # Element margins
border_radius = '0.375rem'     # Border radius
border_width = '1px'           # Border width

# Effects
shadow_intensity = 'medium'    # Shadow intensity
animation_speed = '0.3s'       # Animation speed
hover_effect = 'darken'        # Hover effect type
```

### Advanced Configuration

```python
# Advanced JSON configuration
advanced_config = {
    'responsive_overrides': {
        'mobile': {
            'font-size': '14px',
            'padding': '0.5rem'
        },
        'tablet': {
            'font-size': '15px',
            'padding': '0.625rem'
        },
        'desktop': {
            'font-size': '16px',
            'padding': '0.75rem'
        }
    },
    'custom_css': '/* Additional CSS */',
    'performance_mode': 'optimized'
}
```

## Usage

### Django Admin Interface

1. Navigate to Widget Configurations
2. Select a finder-v2 widget
3. Configure theme properties in the Theme section
4. Use Live Preview to see changes in real-time
5. Select from predefined themes or create custom themes

### Template Integration

```django
{% load theme_tags %}

<!-- Inject theme CSS -->
{% render_theme_css config.theme %}

<!-- Widget container -->
<div class="finder-v2-widget">
    <!-- Widget content -->
</div>
```

### Vue Component Usage

```javascript
// Theme configuration is automatically injected
const config = window.FinderV2Config || {}
const theme = config.theme || {}

// Apply theme styles
const themeStyles = computed(() => {
    return {
        '--theme-primary': theme.colors.primary,
        '--theme-secondary': theme.colors.secondary,
        // ... other theme properties
    }
})
```

## CSS Custom Properties

The theming system generates CSS custom properties that can be used throughout the widget:

```css
:root {
    --theme-primary: #3B82F6;
    --theme-secondary: #6B7280;
    --theme-accent: #10B981;
    --theme-background: #FFFFFF;
    --theme-text: #1F2937;
    --theme-font-family: system-ui;
    --theme-font-size: 16px;
    --theme-font-weight: 400;
    --theme-line-height: 1.5;
    --theme-letter-spacing: 0;
    --theme-padding: 0.75rem;
    --theme-margin: 0.5rem;
    --theme-border-radius: 0.375rem;
    --theme-border-width: 1px;
    --theme-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --theme-animation-speed: 0.3s;
    --theme-hover-effect: brightness(0.9);
}
```

## Predefined Themes

### Available Themes

1. **Default** - Clean, professional appearance
2. **Dark** - Dark mode with high contrast
3. **Blue** - Blue-themed corporate design
4. **Green** - Eco-friendly green theme
5. **Purple** - Modern purple accent theme
6. **Orange** - Warm orange accent theme

### Using Predefined Themes

```python
from src.apps.widgets.finder_v2.default_config.predefined_themes import PredefinedThemes

# Get all predefined themes
themes = PredefinedThemes.get_all_themes()

# Apply a specific theme
theme_config = PredefinedThemes.get_theme('dark')
```

## Performance Optimization

### Caching System

The theming system includes multiple levels of caching:

1. **CSS Generation Cache** - Caches generated CSS content
2. **Theme Hash Cache** - Uses content-based hashing for cache invalidation
3. **Shared Resource Cache** - Shares common resources across themes

### Performance Monitoring

```python
from src.apps.widgets.finder_v2.utils.theme_generator import ThemeGenerator

# Get performance metrics
metrics = ThemeGenerator.get_performance_metrics()

# Clear metrics
ThemeGenerator.clear_performance_metrics()
```

## Management Commands

### Theme Validation

```bash
# Validate all themes
python manage.py theme_validate --all-checks

# Validate specific theme
python manage.py theme_validate --theme-id 1 --check-accessibility

# Export validation report
python manage.py theme_validate --all-checks --export-report validation_report.json
```

### Theme Migration

```bash
# Migrate themes to new format
python manage.py theme_migrate --action migrate --widget-type finder-v2

# Add missing fields
python manage.py theme_migrate --action update --add-missing-fields

# Validate themes
python manage.py theme_migrate --action validate --widget-type finder-v2
```

### Theme Duplication

```bash
# Duplicate theme to another config
python manage.py theme_duplicate --source-theme-id 1 --target-config-id 2

# Create theme variations
python manage.py theme_duplicate --source-theme-id 1 --create-variations --variations-count 5

# Export theme as template
python manage.py theme_duplicate --source-theme-id 1 --export-template theme_template.json
```

## Security Features

### XSS Prevention

- All theme values are escaped in templates
- CSS content is validated for malicious patterns
- Advanced configurations are sanitized

### CSS Injection Protection

- Dangerous CSS patterns are detected and blocked
- Color values are validated for proper format
- JavaScript execution in CSS is prevented

### Access Control

- Theme modifications require proper permissions
- Theme export/import is restricted to authorized users
- Configuration changes are logged for auditing

## Accessibility Features

### Color Contrast

- Automatic contrast ratio calculation
- WCAG AA/AAA compliance checking
- High contrast mode support

### Typography

- Minimum font size validation
- Line height accessibility checks
- Font family fallback support

### Responsive Design

- Mobile-first responsive variables
- Breakpoint-specific overrides
- Touch-friendly sizing

## Testing

### Unit Tests

```bash
# Run theme unit tests
python manage.py test src.apps.widgets.finder_v2.tests.test_theme_models
python manage.py test src.apps.widgets.finder_v2.tests.test_theme_generator
```

### Integration Tests

```bash
# Run integration tests
python manage.py test src.apps.widgets.finder_v2.tests.test_theme_integration
```

### Performance Tests

```bash
# Run performance tests
python manage.py test src.apps.widgets.finder_v2.tests.test_theme_integration.ThemePerformanceTestCase
```

### Security Tests

```bash
# Run security tests
python manage.py test src.apps.widgets.finder_v2.tests.test_theme_integration.ThemeSecurityTestCase
```

## Troubleshooting

### Common Issues

1. **Theme not applying**
   - Check if theme is properly saved
   - Verify CSS generation is working
   - Clear browser cache

2. **Performance issues**
   - Check CSS generation time
   - Verify caching is enabled
   - Monitor performance metrics

3. **Accessibility issues**
   - Run accessibility validation
   - Check color contrast ratios
   - Verify font sizes and line heights

4. **Theme changes not persisting after save** *(resolved July 2025)*
   - **Symptoms:** After changing values (e.g. *Base Font Size → 20 px*) and clicking **Update Configuration**, the page reloads but the old values re-appear.
   - **Root causes:**
     1. `WidgetConfigForm` did not pass the current `widget` instance to `FinderV2ThemeForm`, so the form could not update the related `WidgetTheme` record.
     2. When a **Predefined Theme** was selected, the form re-applied the original theme values during `clean()`, overwriting any manual edits the user made in the same request.
   - **Fix:**
     - Ensure `WidgetConfigForm.get_form_args_kwargs()` passes `widget` to every sub-form (merged in commit `946f24f`).
     - Update `FinderV2ThemeForm.clean()` so it records the selected predefined theme **without** overriding explicitly provided fields (commit `14d7ab6`).
   - **How to verify:**
     1. Turn on DEBUG logging and submit the configuration form.
     2. Look for `🎨 THEME COMPOSE_TO_SAVE` log lines confirming the updated values are persisted.
     3. Reload the page – the new values should appear in the form and the widget preview.

### Debug Mode

Enable debug mode for detailed theme generation logging:

```python
THEME_DEBUG = True
```

5. **JavaScript and Python predefined themes out of sync** *(resolved July 2025)*
   - **Symptoms:** When selecting a predefined theme (e.g. "Ocean Teal") from the dropdown, the wrong colors are applied, or the default theme shown doesn't match the expected `PredefinedThemes.DEFAULT_THEME`.
   - **Root cause:** The JavaScript theme definitions in `/src/templates/widgets/finder_v2/config/theme.html` had hardcoded theme data that was out of sync with the Python definitions in `/src/apps/widgets/finder_v2/default_config/predefined_themes.py`.
   - **Example:** The JavaScript `ocean-teal` theme had `primary: '#0891B2'` while Python had `primary: '#0D9488'`.
   - **Fix:** Updated JavaScript theme definitions to exactly match the Python `predefined_themes.py` file (commit in July 2025).
   - **Prevention:** 
     - Keep JavaScript and Python theme definitions synchronized
     - Consider generating JavaScript themes dynamically from Python data
     - Add validation tests to catch theme definition mismatches
   - **How to verify:**
     1. Open the widget configuration page
     2. Select different predefined themes from the dropdown
     3. Verify that the colors applied match the Python `predefined_themes.py` definitions
     4. Confirm that the default theme matches `PredefinedThemes.DEFAULT_THEME`

6. **API calls failing with 404 errors and brands/regions not loading** *(resolved July 2025)*
   - **Symptoms:** Configuration page shows "Sorry, list of brands is currently unavailable because of some server problems" and API calls to `/widget/finder-v2/api/mk` return 404 errors.
   - **Root cause:** Widget API proxy required CSRF token validation but the demo configuration page was not including CSRF tokens in API requests.
   - **Fix:** Added JavaScript CSRF token generation using the same algorithm as the server and included `X-CSRF-Token` header in all API requests.
   - **Implementation:** 
     ```javascript
     function generateCsrfToken() {
         const userAgent = navigator.userAgent;
         const token = btoa(userAgent);
         const tokenSlice = token.slice(0, 32);
         let result = [];
         for (let i = 0; i < tokenSlice.length; i++) {
             const index = (27 + 11 - (7 + i * 11) % 39) % token.length;
             result.push(token[index]);
         }
         return result.join('');
     }
     ```
   - **How to verify:**
     1. Open the widget configuration page
     2. Check that brands and regions load in the filter sections
     3. Verify API calls in browser dev tools return 200 status codes
     4. Confirm dropdown options are populated with actual data

7. **"WidgetConfig has no subscription" errors** *(resolved July 2025)*
   - **Symptoms:** Configuration pages fail to load with `RelatedObjectDoesNotExist: WidgetConfig has no subscription` errors.
   - **Root cause:** Widget config queries were not using `select_related('subscription')` to load the related subscription object, causing errors when accessing subscription properties.
   - **Fix:** 
     - Added `select_related('subscription')` to `WidgetConfig.get_default()` and `get()` methods
     - Added defensive exception handling in `WidgetConfig.__str__()` and `get_absolute_url()` methods
     - Fixed `WidgetConfigView.get_object()` to include subscription when loading finder-v2 config
   - **How to verify:**
     1. Access any widget configuration page
     2. Verify the page loads without database errors
     3. Check Django logs for absence of subscription-related errors

8. **IntegrityError when creating new widget configurations with themes** *(resolved July 2025)*
   - **Symptoms:** Creating new widget configurations fails with foreign key constraint violation: `Key (widget_id)=(uuid) is not present in table "widgets_widgetconfig"`.
   - **Root cause:** Theme forms tried to save `WidgetTheme` records before the `WidgetConfig` was saved to the database, causing foreign key constraint violations.
   - **Fix:** 
     - Modified `FinderV2ThemeForm.compose_to_save()` to defer theme saving until after widget config is saved
     - Added `save_theme_after_config()` method to handle theme saving with valid widget reference
     - Updated `WidgetConfigForm.save()` to call theme saving after config persistence
   - **Implementation pattern:**
     ```python
     # Store theme data for deferred saving
     self._theme_data_to_save = data
     
     # Save after config exists
     def save_theme_after_config(self, widget_config):
         if hasattr(self, '_theme_data_to_save'):
             # Now safely save theme with valid widget reference
             theme = WidgetTheme.objects.create(widget=widget_config, ...)
     ```
   - **How to verify:**
     1. Create a new widget configuration with custom theme settings
     2. Verify the form saves successfully without database errors
     3. Confirm both the widget config and theme are created properly

### Cache Issues

Clear theme cache:

```python
from django.core.cache import cache
cache.clear()
```