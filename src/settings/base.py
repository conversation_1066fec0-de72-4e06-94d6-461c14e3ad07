"""
Django common to all instances of the project settings for s3service project.

For more information on this file, see
https://docs.djangoproject.com/en/1.6/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/1.6/ref/settings/

Quick-start development settings - unsuitable for production, see
https://docs.djangoproject.com/en/1.6/howto/deployment/checklist/
"""

import os

from django.core.exceptions import ImproperlyConfigured
from .env import get_env_variable, get_env_bool, get_env_int, get_env_list, load_env_file

# Load environment variables from .env.local file if it exists
load_env_file('.env.local')


# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
SETTINGS_DIR = os.path.dirname(os.path.abspath(__file__))
BASE_DIR = os.path.normpath(os.path.join(SETTINGS_DIR, '../../'))

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = get_env_variable('SECRET_KEY', default='c@r+p1s+l+yyrv5ox#!-jx$km347z6nixea$x*6s(3e=$vjqe+', required=False)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = get_env_bool('DEBUG', default=False)

# A list of strings representing the host/domain names that this Django site can serve.
ALLOWED_HOSTS = get_env_list('ALLOWED_HOSTS', default=['.wheel-size.com'])


ADMINS = (
    ('Evgeniy Medvedev', '<EMAIL>'),
)

MANAGERS = (
    ('Evgeniy Medvedev', '<EMAIL>'),
    ('Alexander Zagvozdin', '<EMAIL>')
)



# Local time zone for this installation. Choices can be found here:
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# although not all choices may be available on all operating systems.
# In a Windows environment this must be set to your system time zone.
TIME_ZONE = 'America/Chicago'

# Language code for this installation. All choices can be found here:
# http://www.i18nguy.com/unicode/language-identifiers.html
LANGUAGE_CODE = 'en' #'en-us'

LANGUAGES = (
    ('en', 'English'),
)

# A tuple of directories where Django looks for translation files.
LOCALE_PATHS = (
    os.path.join(BASE_DIR, 'locale'),
)

SITE_ID = 1

# If you set this to False, Django will make some optimizations so as not
# to load the internationalization machinery.
USE_I18N = True

# If you set this to False, Django will not format dates, numbers and
# calendars according to the current locale.
USE_L10N = False

# If you set this to False, Django will not use timezone-aware datetimes.
USE_TZ = True

# Absolute filesystem path to the directory that will hold user-uploaded files.
# Example: "/home/<USER>/media.lawrence.com/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# URL that handles the media served from MEDIA_ROOT. Make sure to use a
# trailing slash.
# Examples: "http://media.lawrence.com/media/", "http://example.com/media/"
MEDIA_URL = '/media/'

# Absolute path to the directory static files should be collected to.
# Don't put anything in this directory yourself; store your static files
# in apps' "static/" subdirectories and in STATICFILES_DIRS.
# Example: "/home/<USER>/media.lawrence.com/static/"
STATIC_ROOT = os.path.join(BASE_DIR, 'collected_static')

# URL prefix for static files.
# Example: "http://media.lawrence.com/static/"
STATIC_URL = '/static/'

# URL prefix for admin media -- CSS, JavaScript and images. Make sure to use a
# trailing slash.
# Examples: "http://foo.com/media/", "/media/".
ADMIN_MEDIA_PREFIX = STATIC_URL + 'admin/'

# Additional locations of static files
STATICFILES_DIRS = (
    # Put strings here, like "/home/<USER>/static" or "C:/www/django/static".
    # Always use forward slashes, even on Windows.
    # Don't forget to use absolute paths, not relative paths.
)

# List of finder classes that know how to find static files in
# various locations.
STATICFILES_FINDERS = (
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    # 'django.contrib.staticfiles.finders.DefaultStorageFinder',
)

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',         # Security middleware (should be first)
    'django.contrib.sessions.middleware.SessionMiddleware',  # Enables session support
    'django.middleware.common.CommonMiddleware',             # Adds a few conveniences for perfectionists
    'django.middleware.csrf.CsrfViewMiddleware',             # Adds protection against CSRF by adding hidden form fields
                                                             # to POST forms and checking requests for the correct value
    'django.contrib.auth.middleware.AuthenticationMiddleware',  # Adds the user attribute, to every HttpRequest object
    'django.contrib.messages.middleware.MessageMiddleware',  # Enables cookie- and session-based message support
    'django.middleware.clickjacking.XFrameOptionsMiddleware',  # Clickjacking protection
    'maintenance_mode.middleware.MaintenanceModeMiddleware',
    # Registration rate limiting middleware
    'src.apps.portal.middleware.RegistrationRateLimitMiddleware',

    'src.apps.widgets.common.middleware.WidgetConfigurationLoader',
    'src.apps.widgets.common.middleware.WidgetApiUrlResolver',
]

ROOT_URLCONF = 'src.urls.services'

# Python dotted path to the WSGI application used by Django's runserver.
WSGI_APPLICATION = 'wsgi.application'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            os.path.join(BASE_DIR, 'src', 'templates'),
        ],
        # 'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': (
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.debug",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "django.template.context_processors.request",
                "django.contrib.messages.context_processors.messages",

                "src.apps.portal.context_processors.maintenance_mode",
                "src.apps.portal.context_processors.tailwind_css",
                "src.apps.widgets.common.context_processors.widget_settings",
            ),
            'loaders': (
                ('django.template.loaders.cached.Loader', (
                    'django.template.loaders.filesystem.Loader',
                    'django.template.loaders.app_directories.Loader',
                )),
            )
        },
    },
]

INSTALLED_APPS = [
    # Django Admin Interface (must be before django.contrib.admin)
    'admin_interface',
    'colorfield',

    # Re-enabled to test JavaScript fix (LESS compilation handled separately)
    'ws_calc',
    'ws_fields',
    'ws_django_helpers',
    'ws_live_settings',
    'src.apps.portal',
    # Re-enabled with Django 4.2+ compatible rest_framework_proxy
    'src.apps.widgets.api_proxy',
    # Re-enabled after removing six dependency
    'src.apps.widgets.translation',
    'src.apps.widgets.common',
    'src.apps.widgets.embed',
    # Re-enabled with Django 4.2+ compatible ws_fields v2.0.0
    'src.apps.widgets.demo',
    'src.apps.widgets.docs',
    # Re-enabled - finder widget works without LESS compilation issues
    'src.apps.widgets.finder',
    # New finder-v2 widget with Vue 3 + TailwindCSS v4 + v2 API
    'src.apps.widgets.finder_v2',
    # Re-enabled - will handle LESS compilation error separately
    'src.apps.widgets.calc',
    # Re-enabled with Django 4.2+ compatible ws_live_settings v2.0.0
    'src.apps.widgets.main',
    'src.apps.widgets.healthcheck',
    # Temporarily disabled due to Django 4.2 compatibility issues in ws_loaddata command
    # 'src.apps.sync',
    'src.apps.api_docs',
    'src.apps.release_notes',

    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.staticfiles',
    'django.contrib.contenttypes',
    'django.contrib.messages',
    'django.contrib.sessions',
    'django.contrib.sites',
    'django.contrib.flatpages',

    # 'django_ses',

    # Re-enabled with Django 4.2+ compatible version
    'rest_framework_proxy',

    'maintenance_mode',

    # django_ace should be after src.apps.widgets.main
    'django_ace',

    # Django registration with Cloudflare Turnstile
    'registration',
    # Cloudflare Turnstile for CAPTCHA functionality
    'turnstile',
    'multiselectfield',
]

# The model to use to represent a User.
# AUTH_USER_MODEL = 'portal.User'

# A sample logging configuration. The only tangible logging
# performed by this configuration is to send an email to
# the site admins on every HTTP 500 error when DEBUG=False.
# See http://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOGGING = {
    'version': 1,
    'disable_existing_loggers': True,
    'root': {
        'level': 'ERROR',
        'handlers': ['sentry'],
    },
    'formatters': {
        'verbose': {
            'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
        },
    },
    'handlers': {
        'sentry': {
            'level': 'ERROR',
            'class': 'sentry_sdk.integrations.logging.EventHandler',
        },
        'sentry_throttling': {
            'level': 'WARNING',
            'class': 'sentry_sdk.integrations.logging.EventHandler',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose'
        },
        'null': {
            'class': 'logging.NullHandler',
        }
    },
    'loggers': {
        'django.request': {
            'level': 'ERROR',
            'handlers': ['console'],
            'propagate': False,
        },
        'django.security.DisallowedHost': {
            'handlers': ['null'],
            'propagate': False,
        },
        'src.apps.widgets.api_proxy.throttling': {
            'level': 'WARNING',
            'handlers': ['sentry_throttling'],
        },
        'src.apps.widgets.finder_v2.forms': {
            'level': 'DEBUG',
            'handlers': ['console'],
            'propagate': False,
        },
        'src.apps.widgets.common.forms.main': {
            'level': 'DEBUG',
            'handlers': ['console'],
            'propagate': False,
        },
    }
}

# The backend to use for sending emails.
EMAIL_BACKEND = 'django_ses.SESBackend'

# The email address that error messages come from, such as those sent to ADMINS and MANAGERS.
SERVER_EMAIL = '<EMAIL>'  # SES-verified address

# Default email address to use for various automated correspondence from the site manager(s).
# This doesn't include error messages sent to ADMINS and MANAGERS;
DEFAULT_FROM_EMAIL = 'WheelSize <<EMAIL>>'

# Default file storage class to be used for any file-related operations that don't specify a particular storage system.
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'


# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.memcached.MemcachedCache',
#         'LOCATION': 'cache-cluster.3l95rv.0001.use1.cache.amazonaws.com:11211',
#     }
# }


CONTACTS_RECIPIENT_LIST = ['<EMAIL>']

SUPPORT_TEAM_EMAILS = ['<EMAIL>']

# AWS Configuration - using environment variables for security
AWS_ACCESS_KEY_ID = get_env_variable('AWS_ACCESS_KEY_ID', default='********************', required=False)
AWS_SECRET_ACCESS_KEY = get_env_variable('AWS_SECRET_ACCESS_KEY', default='wanNYhND8zwh1QEjyMx/WEqLO57Y+obLCU3qCiML', required=False)
AWS_STORAGE_BUCKET_NAME = get_env_variable('AWS_STORAGE_BUCKET_NAME', default='automobile-assets', required=False)
AWS_QUERYSTRING_AUTH = False
AWS_SES_REGION_NAME = get_env_variable('AWS_SES_REGION_NAME', default='us-east-1', required=False)
AWS_SES_REGION_ENDPOINT = get_env_variable('AWS_SES_REGION_ENDPOINT', default='email.us-east-1.amazonaws.com', required=False)

AWS_DEFAULT_ACL = None

FILER_IS_PUBLIC_DEFAULT = True
AUTO_RENDER_SELECT2_STATICS = False


# Django registration
ACCOUNT_ACTIVATION_DAYS = 3
REGISTRATION_AUTO_LOGIN = True
REGISTRATION_FORM = 'src.apps.portal.auth.RegistrationForm'

# Legacy reCAPTCHA settings - DEPRECATED (use Turnstile instead)
# These are kept for backward compatibility during migration period
RECAPTCHA_PUBLIC_KEY = get_env_variable('RECAPTCHA_PUBLIC_KEY', default='6LdUjxEUAAAAAKsnlPJcOu3iYmn-U9wq2xM44428', required=False)
RECAPTCHA_PRIVATE_KEY = get_env_variable('RECAPTCHA_PRIVATE_KEY', default='6LdUjxEUAAAAAJJ5_O3LSomANr-3hOj1lzWdNBj4', required=False)
NORECAPTCHA_SITE_KEY = RECAPTCHA_PUBLIC_KEY
NORECAPTCHA_SECRET_KEY = RECAPTCHA_PRIVATE_KEY

# Cloudflare Turnstile settings
TURNSTILE_SITEKEY = get_env_variable('TURNSTILE_SITEKEY', default='0x4AAAAAABkh7r6su2_ky-7R', required=False)
TURNSTILE_SECRET = get_env_variable('TURNSTILE_SECRET', default='0x4AAAAAABkh7mVABDG6dVVD9vUJJ_Z1bUE', required=False)

# Turnstile default configuration
TURNSTILE_DEFAULT_CONFIG = {
    'theme': 'auto',
    'size': 'normal',
    'render': 'auto',
}

# Registration rate limiting settings
REGISTRATION_RATE_LIMIT_ENABLED = get_env_variable('REGISTRATION_RATE_LIMIT_ENABLED', default='True', required=False).lower() == 'true'
REGISTRATION_RATE_LIMIT_MAX = int(get_env_variable('REGISTRATION_RATE_LIMIT_MAX', default='3', required=False))
REGISTRATION_RATE_LIMIT_HOURS = int(get_env_variable('REGISTRATION_RATE_LIMIT_HOURS', default='1', required=False))

# Anti-spam validation settings
EMAIL_DOMAIN_VALIDATION_ENABLED = get_env_variable('EMAIL_DOMAIN_VALIDATION_ENABLED', default='True', required=False).lower() == 'true'
FORM_TIMING_VALIDATION_ENABLED = get_env_variable('FORM_TIMING_VALIDATION_ENABLED', default='True', required=False).lower() == 'true'
FORM_MIN_SUBMISSION_TIME = int(get_env_variable('FORM_MIN_SUBMISSION_TIME', default='5', required=False))
HONEYPOT_VALIDATION_ENABLED = get_env_variable('HONEYPOT_VALIDATION_ENABLED', default='True', required=False).lower() == 'true'


AUTHENTICATION_BACKENDS = (
    'src.apps.portal.auth.EmailBackend',
    'django.contrib.auth.backends.ModelBackend',
)

REST_PROXY = {
    'HOST': 'https://api3.wheel-size.com/v1',
    'HEADERS': {
        'X-WS-API-SECRET-TOKEN': 'uJnxEaznliaMfXIy',
        'X-AMZN-TRACE-ID': 'CalledFrom=ws-services-unknown',
    }
}

REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_RATES': {
        'proxy_minute': '50/minute',
        'proxy_day': '1000/day',
    }
}

MAINTENANCE_MODE_STATE_FILE_PATH = '/tmp/maintenance_mode.txt'
MAINTENANCE_MODE = False
MAINTENANCE_MODE_IGNORE_URLS = (
    r'^/maintenance/',
    r'^/widget/finder/$',
    r'^/widget/calc/$',
    r'^/widget/[a-z0-9]{32}/?$',
    r'^/widget/[a-z0-9]{32}/api/',
)

WIDGET_SETTINGS = {
    'enable_load_events': False,
}

# Widget CSRF Protection Settings
# Controls how hostname validation works for widget API CSRF protection
WIDGET_CSRF_SETTINGS = {
    # Whether to ignore port numbers when comparing referer and request hostnames
    # This is needed for development environments where referer might be 'development.local'
    # but request host is 'development.local:8000'
    'ignore_port_in_hostname_check': get_env_bool('WIDGET_CSRF_IGNORE_PORT', default=False),

    # List of trusted hostnames for widget API access (without ports)
    # These hostnames are allowed even if they don't exactly match the request host
    'trusted_hostnames': get_env_list('WIDGET_CSRF_TRUSTED_HOSTS', default=[]),

    # Whether to enable debug logging for CSRF validation (development only)
    'debug_csrf_validation': get_env_bool('WIDGET_CSRF_DEBUG', default=False),
}

# Security Settings (Django Best Practices)
SECURE_BROWSER_XSS_FILTER = get_env_bool('SECURE_BROWSER_XSS_FILTER', default=True)
SECURE_CONTENT_TYPE_NOSNIFF = get_env_bool('SECURE_CONTENT_TYPE_NOSNIFF', default=True)
X_FRAME_OPTIONS = get_env_variable('X_FRAME_OPTIONS', default='DENY', required=False)

# HTTPS Settings (enable in production)
SECURE_SSL_REDIRECT = get_env_bool('SECURE_SSL_REDIRECT', default=False)
SECURE_HSTS_SECONDS = get_env_int('SECURE_HSTS_SECONDS', default=0)
SECURE_HSTS_INCLUDE_SUBDOMAINS = get_env_bool('SECURE_HSTS_INCLUDE_SUBDOMAINS', default=False)
SECURE_HSTS_PRELOAD = get_env_bool('SECURE_HSTS_PRELOAD', default=False)

# Session Security
SESSION_COOKIE_SECURE = get_env_bool('SESSION_COOKIE_SECURE', default=False)
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_SECURE = get_env_bool('CSRF_COOKIE_SECURE', default=False)
CSRF_COOKIE_HTTPONLY = True

# Django 3.2+ Default Auto Field
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Live Settings default values
# These values are used if the setting is not found in the database
# It is recommended to configure these in the Django admin interface for WsLiveSettings
WS_LIVE_SETTINGS_DEFAULTS = {
    # Example: 'some_live_setting_key': 'default_value',
}

# For example, to run Gulp when collectstatic is run:
# POST_COLLECTSTATIC_COMMANDS = (
#    'cd {static_root_parent_dir}/assets && gulp build',
# )


