"""
Simple environment variable loader for Django settings.
This replaces the need for django-environ until we can resolve dependency issues.
"""
import os
from django.core.exceptions import ImproperlyConfigured


def get_env_variable(var_name, default=None, required=True):
    """
    Get the environment variable or return exception/default.
    
    Args:
        var_name (str): Environment variable name
        default: Default value if variable not found
        required (bool): Whether the variable is required
    
    Returns:
        str: Environment variable value
    
    Raises:
        ImproperlyConfigured: If required variable is not found
    """
    try:
        return os.environ[var_name]
    except KeyError:
        if default is not None:
            return default
        if required:
            error_msg = f"Set the {var_name} environment variable"
            raise ImproperlyConfigured(error_msg)
        return None


def get_env_bool(var_name, default=False):
    """
    Get environment variable as boolean.
    
    Args:
        var_name (str): Environment variable name
        default (bool): Default value if variable not found
    
    Returns:
        bool: Boolean value
    """
    value = get_env_variable(var_name, default=str(default), required=False)
    if isinstance(value, bool):
        return value
    return value.lower() in ('true', '1', 'yes', 'on')


def get_env_int(var_name, default=0):
    """
    Get environment variable as integer.
    
    Args:
        var_name (str): Environment variable name
        default (int): Default value if variable not found
    
    Returns:
        int: Integer value
    """
    value = get_env_variable(var_name, default=str(default), required=False)
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def get_env_list(var_name, default=None, separator=','):
    """
    Get environment variable as list.
    
    Args:
        var_name (str): Environment variable name
        default (list): Default value if variable not found
        separator (str): List separator
    
    Returns:
        list: List of values
    """
    if default is None:
        default = []
    
    value = get_env_variable(var_name, default='', required=False)
    if not value:
        return default
    
    return [item.strip() for item in value.split(separator) if item.strip()]


def load_env_file(env_file_path='.env.local'):
    """
    Load environment variables from a file.
    
    Args:
        env_file_path (str): Path to environment file
    """
    if not os.path.exists(env_file_path):
        return
    
    with open(env_file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # Remove quotes if present
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]
                
                os.environ.setdefault(key, value)
