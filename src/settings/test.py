"""
Django settings for testing with pytest.
Optimized for speed and isolation while maintaining compatibility.
"""

from .dev_docker import *

# Use in-memory SQLite for faster tests (but keep PostgreSQL if needed for complex tests)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'OPTIONS': {
            'timeout': 20,
        }
    }
}

# Keep the same database for complex integration tests
# Uncomment this if you need PostgreSQL for integration tests:
# DATABASES = DATABASES  # Use the same PostgreSQL from dev_docker

# Disable migrations for faster tests
MIGRATION_MODULES = {app: None for app in INSTALLED_APPS if app.startswith('src.')}

# Disable logging during tests (but keep essential logging)
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'ERROR',  # Only show errors during tests
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'ERROR',
    },
}

# Use simple cache for tests
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# Disable email sending during tests
EMAIL_BACKEND = 'django.core.mail.backends.dummy.EmailBackend'

# Use a simple password hasher for faster tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Test-specific optimizations
DEBUG = False
TEMPLATE_DEBUG = False

# Disable CSRF for API tests
CSRF_COOKIE_SECURE = False
CSRF_USE_SESSIONS = False

# Speed up tests
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Disable LESS compilation during tests to avoid compilation errors
# LESS compilation is not needed for unit/integration tests
DISABLE_LESS_COMPILATION = True

# Test-specific settings
SECRET_KEY = 'test-secret-key-for-testing-only'
ALLOWED_HOSTS = ['*'] 