from django.conf import settings
from django.urls import path, include, re_path
from django.contrib import admin
from django.views.generic import RedirectView
from django.http import JsonResponse
from django.conf.urls.static import static

# Force import widget types to ensure they're registered
# This ensures widget types are available for URL routing
try:
    from src.apps.widgets.finder.widget_type import FinderWidgetType
    from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType
    from src.apps.widgets.calc.widget_type import CalcWidgetType
except ImportError:
    # Ignore import errors during Django startup
    pass

# Using standard ws_calc with fixed ws-django-rest-framework-proxy v2.0.1
from ws_calc import urls as ws_calc_urls


def setup_finder_v2_default(request):
    """
    Helper endpoint to create finder-v2 default configuration.
    """
    from src.apps.widgets.common.models import WidgetConfig, WidgetSubscription

    try:
        # Check if default config already exists
        existing_config = WidgetConfig.objects.filter(type='finder-v2', is_default=True).first()
        if existing_config:
            return JsonResponse({
                'status': 'exists',
                'message': f'Default configuration already exists: {existing_config.uuid}',
                'uuid': existing_config.uuid
            })

        # Create default configuration
        config = WidgetConfig(
            user=None,
            name='Finder-v2 Default',
            is_default=True,
            type='finder-v2',
            lang='en',
            raw_params={
                "flow_type": "primary",
                "theme": {
                    "active": {
                        "templates": {
                            "page": ["widgets/finder_v2/iframe/page.html"]
                        }
                    }
                },
                "interface_tabs": {
                    "tabs": {
                        "by_vehicle": {"resource": "by_vehicle"},
                        "by_tire": {"resource": "by_tire"},
                        "by_rim": {"resource": "by_rim"}
                    },
                    "primary": {"resource": "by_vehicle"}
                },
                "interface_blocks": {
                    "button_to_ws": {"hide": False}
                },
                "content": {
                    "markets": []
                },
                "permissions": {
                    "domains": [
                        "localhost",
                        "development.local",
                        "127.0.0.1",
                        "*.localhost",
                        "*.development.local"
                    ]
                }
            }
        )
        config.save()

        # Create subscription
        subscription = WidgetSubscription(
            widget_config=config,
            contact_email='<EMAIL>',
            client_name='Default Configuration',
            notes='Default configuration for finder-v2 widget'
        )
        subscription.save()

        return JsonResponse({
            'status': 'created',
            'message': 'Finder-v2 default configuration created successfully!',
            'uuid': config.uuid,
            'public_url': 'http://development.local:8000/widget/finder-v2/try/'
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error creating default configuration: {str(e)}'
        }, status=500)


def recreate_finder_v2_default(request):
    """
    Helper endpoint to recreate finder-v2 default configuration with proper theme structure.
    """
    from src.apps.widgets.common.models import WidgetConfig, WidgetSubscription
    from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType

    try:
        # Delete existing default config if it exists
        existing_configs = WidgetConfig.objects.filter(type='finder-v2', is_default=True)
        for config in existing_configs:
            # Delete associated subscription
            if hasattr(config, 'subscription'):
                config.subscription.delete()
            config.delete()

        # Create default configuration using proper widget type configuration
        default_config = FinderV2WidgetType.default_config.copy()
        default_config.update(FinderV2WidgetType.INTERNAL_META_DATA)

        config = WidgetConfig(
            user=None,
            name='Finder-v2 Default',
            is_default=True,
            type='finder-v2',
            lang='en',
            raw_params=default_config,
        )
        config.save()

        # Create subscription
        subscription = WidgetSubscription(
            widget_config=config,
            contact_email='<EMAIL>',
            client_name='Default Configuration',
            notes='Default configuration for finder-v2 widget'
        )
        subscription.save()

        return JsonResponse({
            'status': 'recreated',
            'message': 'Finder-v2 default configuration recreated successfully!',
            'uuid': config.uuid,
            'public_url': 'http://development.local:8000/widget/finder-v2/try/',
            'config_structure': list(default_config.keys())
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error recreating default configuration: {str(e)}'
        }, status=500)


def debug_finder_v2_config(request):
    """
    Debug endpoint to inspect finder-v2 configuration structure.
    """
    from src.apps.widgets.common.models import WidgetConfig

    try:
        # Get the default configuration
        config = WidgetConfig.objects.filter(type='finder-v2', is_default=True).first()
        if not config:
            return JsonResponse({
                'status': 'error',
                'message': 'No default configuration found'
            })

        # Test interface access
        interface_get_result = None
        interface_get_error = None
        try:
            interface_get_result = config.params.get('interface')
            interface_get_result = 'Found' if interface_get_result is not None else 'None'
        except Exception as e:
            interface_get_error = str(e)

        interface_bracket_result = None
        interface_bracket_error = None
        try:
            interface_bracket_result = config.params['interface']
            interface_bracket_result = 'Found' if interface_bracket_result is not None else 'None'
        except Exception as e:
            interface_bracket_error = str(e)

        return JsonResponse({
            'status': 'debug',
            'config_uuid': config.uuid,
            'raw_params_has_interface': 'interface' in config.raw_params,
            'raw_params_interface_has_flow_type': 'flow_type' in config.raw_params.get('interface', {}),
            'params_type': type(config.params).__name__,
            'params_has_keys_method': hasattr(config.params, 'keys'),
            'params_keys_count': len(list(config.params.keys())) if hasattr(config.params, 'keys') else 0,
            'interface_get_result': interface_get_result,
            'interface_get_error': interface_get_error,
            'interface_bracket_result': interface_bracket_result,
            'interface_bracket_error': interface_bracket_error,
            'params_default_exists': hasattr(config.params, 'default'),
            'params_primary_exists': hasattr(config.params, 'primary'),
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error debugging configuration: {str(e)}'
        }, status=500)


def debug_raw_config(request):
    """
    Debug endpoint to inspect raw configuration structure.
    """
    from src.apps.widgets.common.models import WidgetConfig

    try:
        # Get the default configuration
        config = WidgetConfig.objects.filter(type='finder-v2', is_default=True).first()
        if not config:
            return JsonResponse({
                'status': 'error',
                'message': 'No default configuration found'
            })

        raw_interface = config.raw_params.get('interface', 'Missing')

        return JsonResponse({
            'status': 'debug_raw',
            'config_uuid': config.uuid,
            'raw_params_type': type(config.raw_params).__name__,
            'raw_interface_type': type(raw_interface).__name__,
            'raw_interface_is_dict': isinstance(raw_interface, dict),
            'raw_interface_is_str': isinstance(raw_interface, str),
            'raw_interface_keys': list(raw_interface.keys()) if isinstance(raw_interface, dict) else 'Not a dict',
            'raw_interface_content': raw_interface if isinstance(raw_interface, dict) else f"String content: {raw_interface[:100]}...",
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error debugging raw configuration: {str(e)}'
        }, status=500)


def test_finder_v2_widget_view(request):
    """
    Test endpoint to check if finder-v2 widget view components are working.
    """
    try:
        from src.apps.widgets.widget_type import WidgetType
        from src.apps.widgets.common.models import WidgetConfig

        # Check widget type registration
        widget_type = WidgetType.get('finder-v2')
        if not widget_type:
            return JsonResponse({
                'status': 'error',
                'message': 'finder-v2 widget type not registered',
                'registered_types': list(WidgetType.types.keys())
            })

        # Check default configuration
        config = WidgetConfig.objects.filter(type='finder-v2', is_default=True).first()
        if not config:
            return JsonResponse({
                'status': 'error',
                'message': 'No default configuration found for finder-v2'
            })

        # Test widget view instantiation
        from src.apps.widgets.main.views import WidgetView
        view = WidgetView()

        return JsonResponse({
            'status': 'success',
            'widget_type_found': True,
            'widget_type_class': widget_type.__name__,
            'config_found': True,
            'config_uuid': config.uuid,
            'view_class': view.__class__.__name__,
            'url_pattern': WidgetType.slug_url_pattern,
        })

    except Exception as e:
        import traceback
        return JsonResponse({
            'status': 'error',
            'message': f'Error testing widget view: {str(e)}',
            'traceback': traceback.format_exc()
        }, status=500)


def debug_widget_view(request):
    """
    Debug endpoint to test WidgetView configuration loading and permission checking.
    """
    from django.http import JsonResponse
    from src.apps.widgets.common.models import WidgetConfig
    from src.apps.widgets.widget_type import WidgetType
    from src.apps.widgets.main.views.iframe import WidgetView
    import traceback
    
    try:
        # Simulate the middleware behavior
        widget_slug = 'finder-v2'
        
        # Check if widget type exists
        widget_type = WidgetType.get(widget_slug)
        if not widget_type:
            return JsonResponse({'error': 'Widget type not found'}, status=404)
        
        # Get default configuration
        config = WidgetConfig.get_default(widget_slug)
        if isinstance(config, Exception):
            return JsonResponse({'error': f'Configuration error: {config}'}, status=404)
        
        # Check subscription with detailed error handling
        subscription_info = {}
        try:
            if hasattr(config, 'subscription'):
                subscription_info = {
                    'deny_iframe': str(config.subscription.deny_iframe()),
                    'is_demo': str(config.subscription.is_demo),
                    'is_trial': str(config.subscription.is_trial),
                    'is_banned': str(config.subscription.is_banned),
                }
            else:
                subscription_info = {'error': 'No subscription attribute'}
        except Exception as sub_e:
            subscription_info = {'error': f'Subscription error: {str(sub_e)}'}
        
        # Check template path with detailed error handling
        template_path = None
        template_error = None
        try:
            template_path = str(config.params['theme']['active']['templates']['page'])
        except KeyError as ke:
            template_error = f'KeyError accessing template path: {str(ke)}'
        except Exception as te:
            template_error = f'Error accessing template path: {str(te)}'
        
        # Check params structure
        params_info = {}
        try:
            params_info = {
                'type': type(config.params).__name__,
                'has_theme': 'theme' in config.params,
                'keys': str(list(config.params.keys())) if hasattr(config.params, 'keys') else 'No keys method'
            }
        except Exception as pe:
            params_info = {'error': f'Params error: {str(pe)}'}
        
        return JsonResponse({
            'status': 'success',
            'widget_type_found': True,
            'widget_type_class': widget_type.__class__.__name__,
            'config_found': True,
            'config_uuid': str(config.uuid),
            'config_type': str(config.type),
            'config_is_default': bool(config.is_default),
            'subscription_info': subscription_info,
            'template_path': template_path,
            'template_error': template_error,
            'params_info': params_info
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'error': str(e),
            'error_type': type(e).__name__,
            'traceback': str(traceback.format_exc())
        }, status=500)


urlpatterns = [
    re_path(r'^favicon\.ico$', RedirectView.as_view(
        url='%s%s' % (settings.STATIC_URL, 'img/icons/favicon.ico'))),
]

urlpatterns += [
    # Helper endpoints for finder-v2 setup
    path('setup-finder-v2-default/', setup_finder_v2_default, name='setup-finder-v2-default'),
    path('recreate-finder-v2-default/', recreate_finder_v2_default, name='recreate-finder-v2-default'),
    path('debug-finder-v2-config/', debug_finder_v2_config, name='debug-finder-v2-config'),
    path('debug-raw-config/', debug_raw_config, name='debug-raw-config'),
    path('test-finder-v2-widget-view/', test_finder_v2_widget_view, name='test-finder-v2-widget-view'),
    path('debug-widget-view/', debug_widget_view, name='debug-widget-view'),

    path('admin/', admin.site.urls),
    # Portal URLs - must come before registration URLs to override auth_login
    path('', include('src.apps.portal.urls')),
    # Registration URLs now use Turnstile for CAPTCHA validation
    path('accounts/', include('registration.backends.default.urls')),
    path('widget/', include('src.apps.widgets.urls')),
    path('api/', include('src.apps.api_docs.urls')),
    re_path(r'^calc/', include((ws_calc_urls, 'ws_calc'), namespace='calc')),
    path('maintenance/', include('maintenance_mode.urls')),
    path('release-notes/', include('src.apps.release_notes.urls')),
    path('healthcheck/', include('src.apps.widgets.healthcheck.urls')),
    path('', include('django.contrib.flatpages.urls')),
]

if settings.DEBUG:
    # This is for run static files by django not nginx
    # like `http://development.local:8000/static/portal/css/style.css`
    urlpatterns += static(settings.STATIC_URL,
                          document_root=settings.STATIC_ROOT)
