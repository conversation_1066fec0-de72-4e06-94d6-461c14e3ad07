{% extends 'portal/base.html' %}

{% load i18n %}

{% block title %}{% trans 'Registration Temporarily Unavailable' %}{% endblock %}

{% block content %}
  <div class="form-wrapper form-medium">
    <h3 class="title-divider">
      <span>{% trans 'Too Many Attempts' %}</span>
    </h3>
    
    <div class="alert alert-warning">
      <h4>{% trans 'Registration Temporarily Limited' %}</h4>
      <p>
        {% blocktrans with max_attempts=max_attempts time_window=time_window %}
          You have exceeded the maximum number of registration attempts ({{ max_attempts }}) 
          within {{ time_window }} hour{{ time_window|pluralize }}.
        {% endblocktrans %}
      </p>
      <p>
        {% blocktrans with time_window=time_window %}
          Please wait {{ time_window }} hour{{ time_window|pluralize }} before trying again.
        {% endblocktrans %}
      </p>
    </div>
    
    <div class="alert alert-info">
      <h5>{% trans 'Why is this happening?' %}</h5>
      <p>
        {% trans 'To protect against automated spam and abuse, we limit the number of registration attempts from each IP address.' %}
      </p>
      <p>
        {% trans 'If you are having trouble registering, please contact our support team for assistance.' %}
      </p>
    </div>
    
    <div class="text-center">
      <a href="{% url 'registration_register' %}" class="btn btn-default">
        {% trans 'Back to Registration' %}
      </a>
      <a href="{% url 'auth_login' %}" class="btn btn-primary">
        {% trans 'Login Instead' %}
      </a>
    </div>
    
    <hr>
    
    <div class="text-center">
      <small class="text-muted">
        {% trans 'Need help? Contact us at' %} 
        <a href="mailto:<EMAIL>"><EMAIL></a>
      </small>
    </div>
  </div>
{% endblock content %}