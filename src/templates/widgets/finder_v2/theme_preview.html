{% comment %}
Theme preview template for finder-v2 widgets.

This template provides the HTML structure for the real-time theme preview
system in the Django admin interface.

Usage:
{% include 'widgets/finder_v2/theme_preview.html' with widget_config=config %}
{% endcomment %}

{% load static %}

<!-- Theme Preview Container -->
<div class="theme-preview-section">
    <h4>
        <i class="fas fa-eye"></i>
        Live Preview
        <small class="text-muted">See your changes in real-time</small>
    </h4>
    
    <!-- Preview Controls -->
    <div class="preview-controls">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="btn-group btn-group-sm" role="group" aria-label="Preview controls">
                <button type="button" class="btn btn-outline-secondary" onclick="window.themePreviewManager?.resetPreview()">
                    <i class="fas fa-redo"></i>
                    Reset
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="window.themePreviewManager?.applyCurrentTheme()">
                    <i class="fas fa-sync"></i>
                    Update
                </button>
            </div>
            
            <div class="theme-validation-status">
                <span class="badge badge-success" id="theme-validation-badge">
                    <i class="fas fa-check"></i>
                    Valid Theme
                </span>
            </div>
        </div>
    </div>
    
    <!-- Responsive Preview Controls will be inserted here by JavaScript -->
    
    <!-- Preview Container -->
    <div id="theme-preview-container" class="preview-container">
        <iframe 
            id="theme-preview-frame" 
            src="{% url 'widget-iframe' widget_config.slug %}" 
            frameborder="0" 
            scrolling="auto"
            title="Widget Theme Preview"
            aria-label="Live preview of your widget theme">
            <p>Your browser does not support iframes. Please use a modern browser to see the theme preview.</p>
        </iframe>
    </div>
    
    <!-- Preview Status -->
    <div class="preview-status mt-2">
        <small class="text-muted">
            <i class="fas fa-info-circle"></i>
            Preview updates automatically as you make changes. 
            <span id="last-updated">Last updated: Never</span>
        </small>
    </div>
</div>

<!-- Theme Gallery Section -->
<div class="theme-gallery-section mt-4">
    <h5>
        <i class="fas fa-palette"></i>
        Theme Gallery
        <small class="text-muted">Click any theme to apply it</small>
    </h5>
    
    <!-- Theme gallery will be populated by JavaScript -->
</div>

<!-- Theme Import/Export Section -->
<div class="theme-import-export-section mt-4">
    <h5>
        <i class="fas fa-download"></i>
        Import/Export
    </h5>
    
    <div class="import-export-controls">
        <div class="row">
            <div class="col-md-6">
                <label for="theme-import-file" class="form-label">Import Theme</label>
                <input 
                    type="file" 
                    id="theme-import-file" 
                    class="form-control" 
                    accept=".json"
                    onchange="handleThemeImport(this)">
                <small class="form-text text-muted">Select a JSON theme file to import</small>
            </div>
            <div class="col-md-6">
                <label class="form-label">Export Theme</label>
                <div>
                    <button 
                        type="button" 
                        class="btn btn-outline-primary" 
                        onclick="exportCurrentTheme()">
                        <i class="fas fa-download"></i>
                        Export Current Theme
                    </button>
                </div>
                <small class="form-text text-muted">Download your current theme as JSON</small>
            </div>
        </div>
    </div>
</div>

<!-- Theme Validation Messages -->
<div id="theme-validation-messages" class="theme-validation mt-3" style="display: none;">
    <!-- Validation messages will be inserted here -->
</div>

<!-- Include CSS and JavaScript -->
<link rel="stylesheet" href="{% static 'finder_v2/css/theme-preview.css' %}">
<script src="{% static 'finder_v2/js/theme-preview.js' %}"></script>

<script>
// Theme import/export functionality
function handleThemeImport(input) {
    const file = input.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const themeData = JSON.parse(e.target.result);
            
            // Validate theme data
            if (!themeData.colors || !themeData.typography || !themeData.effects) {
                throw new Error('Invalid theme file format');
            }
            
            // Apply theme to form
            applyImportedTheme(themeData);
            
            // Show success message
            showThemeMessage('Theme imported successfully!', 'success');
            
        } catch (error) {
            showThemeMessage('Failed to import theme: ' + error.message, 'error');
        }
    };
    
    reader.readAsText(file);
}

function exportCurrentTheme() {
    if (!window.themePreviewManager) {
        showThemeMessage('Theme preview not initialized', 'error');
        return;
    }
    
    const theme = window.themePreviewManager.getCurrentTheme();
    const themeData = {
        name: 'Exported Theme',
        colors: {
            primary: theme.primary,
            secondary: theme.secondary,
            accent: theme.accent,
            background: theme.background,
            text: theme.text
        },
        typography: {
            font_family: theme.fontFamily,
            base_font_size: theme.fontSize
        },
        effects: {
            border_radius: theme.borderRadius,
            shadow_intensity: theme.shadowIntensity
        },
        exported_at: new Date().toISOString()
    };
    
    // Create download link
    const blob = new Blob([JSON.stringify(themeData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `theme-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showThemeMessage('Theme exported successfully!', 'success');
}

function applyImportedTheme(themeData) {
    // Apply colors
    if (themeData.colors) {
        Object.entries(themeData.colors).forEach(([key, value]) => {
            const field = document.querySelector(`[name*="${key}_color"]`);
            if (field) {
                field.value = value;
            }
        });
    }
    
    // Apply typography
    if (themeData.typography) {
        Object.entries(themeData.typography).forEach(([key, value]) => {
            const field = document.querySelector(`[name*="${key}"]`);
            if (field) {
                field.value = value;
            }
        });
    }
    
    // Apply effects
    if (themeData.effects) {
        Object.entries(themeData.effects).forEach(([key, value]) => {
            const field = document.querySelector(`[name*="${key}"]`);
            if (field) {
                field.value = value;
            }
        });
    }
    
    // Update preview
    if (window.themePreviewManager) {
        window.themePreviewManager.applyCurrentTheme();
    }
}

function showThemeMessage(message, type = 'info') {
    const container = document.getElementById('theme-validation-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
    messageDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    container.appendChild(messageDiv);
    container.style.display = 'block';
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        messageDiv.remove();
        if (container.children.length === 0) {
            container.style.display = 'none';
        }
    }, 5000);
}

// Update last updated timestamp
function updateLastUpdated() {
    const element = document.getElementById('last-updated');
    if (element) {
        element.textContent = 'Last updated: ' + new Date().toLocaleTimeString();
    }
}

// Initialize theme preview when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme preview manager
    if (window.ThemePreviewManager) {
        window.themePreviewManager = new window.ThemePreviewManager();
        
        // Update timestamp when theme changes
        const originalApplyCurrentTheme = window.themePreviewManager.applyCurrentTheme;
        window.themePreviewManager.applyCurrentTheme = function() {
            originalApplyCurrentTheme.call(this);
            updateLastUpdated();
        };
    }
});
</script>

<style>
/* Additional styles for the theme preview integration */
.theme-preview-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin: 1rem 0;
}

.theme-preview-section h4 {
    margin-bottom: 1rem;
    color: #495057;
    font-weight: 600;
}

.theme-preview-section h4 small {
    display: block;
    font-size: 0.875rem;
    font-weight: 400;
    margin-top: 0.25rem;
}

.theme-gallery-section,
.theme-import-export-section {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1.5rem;
}

.theme-gallery-section h5,
.theme-import-export-section h5 {
    margin-bottom: 1rem;
    color: #495057;
    font-weight: 600;
}

.theme-gallery-section h5 small,
.theme-import-export-section h5 small {
    display: block;
    font-size: 0.875rem;
    font-weight: 400;
    margin-top: 0.25rem;
}

.preview-status {
    text-align: center;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 0.25rem;
}

#theme-validation-badge {
    font-size: 0.8125rem;
    padding: 0.375rem 0.75rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8125rem;
}
</style>