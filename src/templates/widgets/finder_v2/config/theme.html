{% load i18n %}
{% load form_ext %}

<!-- Theme & Styling Section -->
<div class="mb-8">
  <h3 class="text-base font-semibold text-gray-900 mb-3">
    {% trans 'Theme & Styling' %}
  </h3>
  
  <!-- Theme & Styling Accordion -->
  <div class="mb-4">
    <details class="group">
      <summary class="flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-left text-gray-900 bg-gray-50 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1">
        <span class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
          </svg>
          {% trans 'Theme & Visual Customization' %}
          <span class="ml-2 text-xs text-gray-500">(11 options)</span>
        </span>
        <svg class="w-4 h-4 text-gray-500 transition-transform group-open:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </summary>
      <div class="mt-3 border border-gray-200 rounded-lg p-4 bg-white">
        <p class="text-xs text-gray-500 mb-4">
          {% trans 'Customize the visual appearance of your widget to match your website design.' %}
        </p>
        
        <div class="form-section">
          {% if form.theme %}
      <div class="row">
        <!-- Theme Name -->
        <div class="col-md-6">
          <div class="form-group">
            {% if form.theme.theme_name %}
              {{ form.theme.theme_name.label_tag }}
              {{ form.theme.theme_name }}
              {% if form.theme.theme_name.help_text %}
                <small class="form-text text-muted">{{ form.theme.theme_name.help_text }}</small>
              {% endif %}
              {% if form.theme.theme_name.errors %}
                <div class="invalid-feedback">{{ form.theme.theme_name.errors }}</div>
              {% endif %}
            {% else %}
              <label for="theme-theme_name">Theme Name</label>
              <input type="text" name="theme-theme_name" id="theme-theme_name" class="form-control" placeholder="Custom Theme" value="Custom Theme" />
              <small class="form-text text-muted">Name for this theme configuration</small>
            {% endif %}
          </div>
        </div>

        <!-- Predefined Theme Selection -->
        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.predefined_theme.label_tag }}
            {{ form.theme.predefined_theme }}
            {% if form.theme.predefined_theme.help_text %}
              <small class="form-text text-muted">{{ form.theme.predefined_theme.help_text }}</small>
            {% endif %}
            {% if form.theme.predefined_theme.errors %}
              <div class="invalid-feedback">{{ form.theme.predefined_theme.errors }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Color Customization -->
      <div class="row">
        <div class="col-md-12">
          <h4>{% trans 'Color Settings' %}</h4>
        </div>
        
        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.primary_color.label_tag }}
            {{ form.theme.primary_color }}
            {% if form.theme.primary_color.help_text %}
              <small class="form-text text-muted">{{ form.theme.primary_color.help_text }}</small>
            {% endif %}
            {% if form.theme.primary_color.errors %}
              <div class="invalid-feedback">{{ form.theme.primary_color.errors }}</div>
            {% endif %}
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.secondary_color.label_tag }}
            {{ form.theme.secondary_color }}
            {% if form.theme.secondary_color.help_text %}
              <small class="form-text text-muted">{{ form.theme.secondary_color.help_text }}</small>
            {% endif %}
            {% if form.theme.secondary_color.errors %}
              <div class="invalid-feedback">{{ form.theme.secondary_color.errors }}</div>
            {% endif %}
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.accent_color.label_tag }}
            {{ form.theme.accent_color }}
            {% if form.theme.accent_color.help_text %}
              <small class="form-text text-muted">{{ form.theme.accent_color.help_text }}</small>
            {% endif %}
            {% if form.theme.accent_color.errors %}
              <div class="invalid-feedback">{{ form.theme.accent_color.errors }}</div>
            {% endif %}
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.background_color.label_tag }}
            {{ form.theme.background_color }}
            {% if form.theme.background_color.help_text %}
              <small class="form-text text-muted">{{ form.theme.background_color.help_text }}</small>
            {% endif %}
            {% if form.theme.background_color.errors %}
              <div class="invalid-feedback">{{ form.theme.background_color.errors }}</div>
            {% endif %}
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.text_color.label_tag }}
            {{ form.theme.text_color }}
            {% if form.theme.text_color.help_text %}
              <small class="form-text text-muted">{{ form.theme.text_color.help_text }}</small>
            {% endif %}
            {% if form.theme.text_color.errors %}
              <div class="invalid-feedback">{{ form.theme.text_color.errors }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Typography Settings -->
      <div class="row">
        <div class="col-md-12">
          <h4>{% trans 'Typography Settings' %}</h4>
        </div>
        
        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.font_family.label_tag }}
            {{ form.theme.font_family }}
            {% if form.theme.font_family.help_text %}
              <small class="form-text text-muted">{{ form.theme.font_family.help_text }}</small>
            {% endif %}
            {% if form.theme.font_family.errors %}
              <div class="invalid-feedback">{{ form.theme.font_family.errors }}</div>
            {% endif %}
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.base_font_size.label_tag }}
            {{ form.theme.base_font_size }}
            {% if form.theme.base_font_size.help_text %}
              <small class="form-text text-muted">{{ form.theme.base_font_size.help_text }}</small>
            {% endif %}
            {% if form.theme.base_font_size.errors %}
              <div class="invalid-feedback">{{ form.theme.base_font_size.errors }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Visual Effects -->
      <div class="row">
        <div class="col-md-12">
          <h4>{% trans 'Visual Effects' %}</h4>
        </div>
        
        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.border_radius.label_tag }}
            {{ form.theme.border_radius }}
            {% if form.theme.border_radius.help_text %}
              <small class="form-text text-muted">{{ form.theme.border_radius.help_text }}</small>
            {% endif %}
            {% if form.theme.border_radius.errors %}
              <div class="invalid-feedback">{{ form.theme.border_radius.errors }}</div>
            {% endif %}
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            {{ form.theme.shadow_intensity.label_tag }}
            {{ form.theme.shadow_intensity }}
            {% if form.theme.shadow_intensity.help_text %}
              <small class="form-text text-muted">{{ form.theme.shadow_intensity.help_text }}</small>
            {% endif %}
            {% if form.theme.shadow_intensity.errors %}
              <div class="invalid-feedback">{{ form.theme.shadow_intensity.errors }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Real-time Preview Enhancement -->
      <div class="row">
        <div class="col-md-12">
          <div class="theme-preview-info">
            <div class="alert alert-info">
              <strong>{% trans 'Live Preview' %}</strong>
              <p>{% trans 'Your theme changes are reflected in real-time in the preview section below. Adjust colors, fonts, and visual effects to see how they look on your widget.' %}</p>
              <p><strong>{% trans 'How it works:' %}</strong></p>
              <ul>
                <li>{% trans 'Select a predefined theme to automatically fill in all color and styling options' %}</li>
                <li>{% trans 'Manually adjust individual colors, fonts, and effects for custom styling' %}</li>
                <li>{% trans 'Click "Update Configuration" to save your theme changes' %}</li>
                <li>{% trans 'The preview will reload automatically to show your changes' %}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

          {% else %}
            <div class="alert alert-warning">
              <strong>{% trans 'Theme Form Not Available' %}</strong>
              <p>{% trans 'The theme customization form is not available for this widget configuration.' %}</p>
            </div>
          {% endif %}
        </div>
      </div>
    </details>
  </div>
</div>

<!-- Theme Real-time Preview JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  console.log('Theme form initialization');
  
  // Initialize theme form functionality
  initThemeForm();
  
  function initThemeForm() {
    // Handle color picker changes
    document.querySelectorAll('input[type="color"]').forEach(function(colorInput) {
      colorInput.addEventListener('change', function() {
        console.log('Color changed:', this.name, this.value);
        updatePreview();
      });
    });
    
    // Handle predefined theme selection
    const themeSelector = document.querySelector('select[name="theme-predefined_theme"]');
    if (themeSelector) {
      themeSelector.addEventListener('change', function() {
        console.log('Predefined theme changed:', this.value);
        applyPredefinedTheme(this.value);
      });
    }
    
    // Handle typography changes
    document.querySelectorAll('select[name^="theme-font"], input[name^="theme-base_font_size"]').forEach(function(input) {
      input.addEventListener('change', function() {
        console.log('Typography changed:', this.name, this.value);
        updatePreview();
      });
    });
    
    // Handle visual effects changes
    document.querySelectorAll('input[name^="theme-border_radius"], select[name^="theme-shadow_intensity"]').forEach(function(input) {
      input.addEventListener('change', function() {
        console.log('Visual effect changed:', this.name, this.value);
        updatePreview();
      });
    });
  }
  
  function updatePreview() {
    // Update the preview iframe by reloading it with a cache buster
    const iframe = document.querySelector('.iframe-preview iframe');
    if (iframe) {
      // Add timestamp to force reload and bypass cache
      const originalSrc = iframe.src.split('?')[0];
      iframe.src = originalSrc + '?t=' + Date.now();
    }
  }
  
  function applyPredefinedTheme(themeName) {
    console.log('Applying predefined theme:', themeName);
    
    if (!themeName || themeName === '') {
      return;
    }
    
    // Define predefined themes (exactly matching Python predefined_themes.py)
    const predefinedThemes = {
      'modern-blue': {
        name: 'Modern Blue',
        colors: {
          primary: '#2563EB',
          secondary: '#64748B',
          accent: '#0EA5E9',
          background: '#FFFFFF',
          text: '#1E293B'
        },
        typography: {
          font_family: 'Inter',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'medium'
        }
      },
      'corporate-gray': {
        name: 'Corporate Gray',
        colors: {
          primary: '#374151',
          secondary: '#9CA3AF',
          accent: '#F59E0B',
          background: '#F9FAFB',
          text: '#111827'
        },
        typography: {
          font_family: 'system-ui',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.375rem',
          shadow_intensity: 'light'
        }
      },
      'vibrant-green': {
        name: 'Vibrant Green',
        colors: {
          primary: '#059669',
          secondary: '#6B7280',
          accent: '#10B981',
          background: '#FFFFFF',
          text: '#1F2937'
        },
        typography: {
          font_family: 'Roboto',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'medium'
        }
      },
      'elegant-purple': {
        name: 'Elegant Purple',
        colors: {
          primary: '#7C3AED',
          secondary: '#A78BFA',
          accent: '#8B5CF6',
          background: '#FAFAFA',
          text: '#1F2937'
        },
        typography: {
          font_family: 'Inter',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.75rem',
          shadow_intensity: 'heavy'
        }
      },
      'warm-orange': {
        name: 'Warm Orange',
        colors: {
          primary: '#EA580C',
          secondary: '#78716C',
          accent: '#F97316',
          background: '#FFFBEB',
          text: '#1C1917'
        },
        typography: {
          font_family: 'Open Sans',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'medium'
        }
      },
      'ocean-teal': {
        name: 'Ocean Teal',
        colors: {
          primary: '#0D9488',
          secondary: '#6B7280',
          accent: '#14B8A6',
          background: '#F0FDFA',
          text: '#134E4A'
        },
        typography: {
          font_family: 'Lato',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'light'
        }
      },
      'sunset-red': {
        name: 'Sunset Red',
        colors: {
          primary: '#DC2626',
          secondary: '#6B7280',
          accent: '#EF4444',
          background: '#FEF2F2',
          text: '#1F2937'
        },
        typography: {
          font_family: 'Montserrat',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.375rem',
          shadow_intensity: 'medium'
        }
      },
      'midnight-dark': {
        name: 'Midnight Dark',
        colors: {
          primary: '#3B82F6',
          secondary: '#6B7280',
          accent: '#60A5FA',
          background: '#1F2937',
          text: '#F9FAFB'
        },
        typography: {
          font_family: 'Poppins',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'heavy'
        }
      }
    };
    
    const theme = predefinedThemes[themeName];
    if (!theme) {
      console.warn('Unknown predefined theme:', themeName);
      return;
    }
    
    // Apply theme colors
    if (theme.colors) {
      document.querySelector('input[name="theme-primary_color"]').value = theme.colors.primary;
      document.querySelector('input[name="theme-secondary_color"]').value = theme.colors.secondary;
      document.querySelector('input[name="theme-accent_color"]').value = theme.colors.accent;
      document.querySelector('input[name="theme-background_color"]').value = theme.colors.background;
      document.querySelector('input[name="theme-text_color"]').value = theme.colors.text;
    }
    
    // Apply typography
    if (theme.typography) {
      const fontFamilySelect = document.querySelector('select[name="theme-font_family"]');
      if (fontFamilySelect) {
        fontFamilySelect.value = theme.typography.font_family;
      }
      const fontSizeInput = document.querySelector('input[name="theme-base_font_size"]');
      if (fontSizeInput) {
        fontSizeInput.value = theme.typography.base_font_size;
      }
    }
    
    // Apply effects
    if (theme.effects) {
      const borderRadiusInput = document.querySelector('input[name="theme-border_radius"]');
      if (borderRadiusInput) {
        borderRadiusInput.value = theme.effects.border_radius;
      }
      const shadowSelect = document.querySelector('select[name="theme-shadow_intensity"]');
      if (shadowSelect) {
        shadowSelect.value = theme.effects.shadow_intensity;
      }
    }
    
    // Apply theme name
    const themeNameInput = document.querySelector('input[name="theme-theme_name"]');
    if (themeNameInput) {
      themeNameInput.value = theme.name;
    }
    
    // Show visual feedback that theme is being applied
    const themeSelector = document.querySelector('select[name="theme-predefined_theme"]');
    if (themeSelector) {
      themeSelector.style.borderColor = '#28a745';
      setTimeout(() => {
        themeSelector.style.borderColor = '';
      }, 2000);
    }
    
    // Trigger preview update
    updatePreview();
  }
});
</script>

<style>
.theme-preview-info {
  margin-top: 20px;
}

.config-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.config-section h3 {
  margin-top: 0;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 10px;
}

.config-section h4 {
  margin-top: 25px;
  margin-bottom: 15px;
  color: #6c757d;
  font-size: 1.1rem;
}

.config-description {
  margin-bottom: 20px;
  color: #6c757d;
  font-size: 0.95rem;
}

.form-section {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

input[type="color"] {
  width: 60px;
  height: 40px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  cursor: pointer;
}

input[type="color"]:hover {
  border-color: #80bdff;
}

.invalid-feedback {
  display: block;
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
</style>
