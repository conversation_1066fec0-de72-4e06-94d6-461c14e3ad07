{% load i18n %}
{% load form_ext %}

<div class="border-b border-gray-200 my-5 max-w-4xl">
  <div class="my-5">
    <h3 class="text-base font-semibold text-gray-900">{% trans 'Authorized Domains' %}</h3>
    <p class="mt-2 max-w-4xl text-sm text-gray-500">
      {% blocktrans %}
      Specify all domain names where you want to deploy this widget configuration.
      Each domain will match itself and all its subdomains.
      {% endblocktrans %}
    </p>
  </div>

  <!-- Domain Examples Info Box -->
  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-5">
    <div class="flex items-start space-x-2">
      <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <div class="text-sm text-blue-800">
        <p class="font-medium mb-2">{% trans 'Domain Matching Examples:' %}</p>
        <div class="space-y-1">
          <p>
            <code class="bg-blue-100 px-1 rounded font-mono">wheel-size.com</code> → matches 
            <span class="text-green-700 font-medium">wheel-size.com</span>, 
            <span class="text-green-700 font-medium">www.wheel-size.com</span>, 
            <span class="text-green-700 font-medium">services.wheel-size.com</span>
          </p>
          <p>
            <code class="bg-blue-100 px-1 rounded font-mono">*.wheel-size.com</code> → provides the same behavior
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Form Fields -->
  <div class="max-w-md">
    {% if form.permissions.errors.domains %}
      <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="text-sm text-red-600 font-medium">{{ form.permissions.errors.domains.as_text }}</span>
        </div>
      </div>
    {% endif %}

    <div class="form-group">
      <label class="block text-sm font-medium text-gray-900 mb-3">{% trans 'Domain Names' %}</label>
      
      <!-- Hidden form field -->
      {{ form.permissions.domains|htmlclass:"hidden" }}

      <!-- User-friendly textarea -->
      <textarea 
        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm font-mono domains-textarea"
        rows="6"
        placeholder="{% trans 'Enter one domain per line, e.g.:' %}&#10;example.com&#10;*.example.com&#10;subdomain.example.com">
      </textarea>
      
      <p class="mt-2 text-xs text-gray-500">
        {% trans 'Enter one domain per line. Use * for wildcard subdomains.' %}
      </p>
    </div>
  </div>
</div>

<script>
// Modern vanilla JavaScript for domain validation (TailwindCSS v4 compatible)
document.addEventListener('DOMContentLoaded', function() {
  const hiddenField = document.querySelector('[name="permissions-domains"]');
  const userTextarea = document.querySelector('textarea.domains-textarea');

  if (hiddenField && userTextarea) {
    // Initialize user textarea with values from hidden field
    initializeTextareaFromHiddenField();

    // Update hidden field when user textarea changes
    userTextarea.addEventListener('input', updateHiddenFieldFromTextarea);
    userTextarea.addEventListener('blur', validateDomains);

    // Update hidden field before form submission
    const form = hiddenField.closest('form');
    if (form) {
      form.addEventListener('submit', updateHiddenFieldFromTextarea);
    }
  }

  function initializeTextareaFromHiddenField() {
    try {
      let currentValue = hiddenField.value;
      if (currentValue) {
        // Try to parse as JSON array
        if (typeof currentValue === 'string') {
          if (currentValue.startsWith('[') && currentValue.endsWith(']')) {
            const domainsArray = JSON.parse(currentValue);
            if (Array.isArray(domainsArray)) {
              userTextarea.value = domainsArray.join('\n');
              return;
            }
          }
        } else if (Array.isArray(currentValue)) {
          userTextarea.value = currentValue.join('\n');
          return;
        }
      }
      // If no valid value, leave textarea empty
      userTextarea.value = '';
    } catch (e) {
      console.warn('Could not parse domains from hidden field:', e);
      userTextarea.value = '';
    }
  }

  function updateHiddenFieldFromTextarea() {
    const lines = userTextarea.value.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
    hiddenField.value = JSON.stringify(lines);
  }

  function validateDomains() {
    const lines = userTextarea.value.split('\n');
    const invalidDomains = [];

    lines.forEach(function(line, index) {
      const domain = line.trim();
      if (domain && !isValidDomain(domain)) {
        invalidDomains.push('Line ' + (index + 1) + ': ' + domain);
      }
    });

    // Remove any existing validation messages
    const existingError = userTextarea.parentElement.querySelector('.domain-validation-error');
    if (existingError) {
      existingError.remove();
    }

    // Show validation errors if any
    if (invalidDomains.length > 0) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'domain-validation-error mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md';
      errorDiv.innerHTML = `
        <div class="flex items-start space-x-2">
          <svg class="w-4 h-4 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          <div class="text-sm">
            <p class="font-medium text-yellow-800">{% trans "Warning:" %}</p>
            <p class="text-yellow-700">{% trans "Invalid domain format detected:" %}</p>
            <ul class="list-disc list-inside mt-1 text-yellow-700">
              ${invalidDomains.map(domain => `<li>${domain}</li>`).join('')}
            </ul>
          </div>
        </div>
      `;
      userTextarea.parentElement.appendChild(errorDiv);
    }
  }
});

function isValidDomain(domain) {
  // Basic domain validation (allows wildcards)
  if (!domain || domain.length === 0) return false;
  
  // Allow wildcard domains
  if (domain.startsWith('*.')) {
    domain = domain.substring(2);
  }
  
  // Basic domain pattern check
  const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return domainPattern.test(domain);
}
</script> 