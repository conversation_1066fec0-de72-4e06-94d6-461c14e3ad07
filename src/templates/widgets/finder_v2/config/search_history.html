{% load i18n %}
{% load form_ext %}

<!-- Search History Settings Section -->
<div class="mb-8">
  <h3 class="text-base font-semibold text-gray-900 mb-3">
    {% trans 'Search History Settings' %}
  </h3>
  
  <!-- Search History Settings Accordion -->
  <div class="mb-4">
    <details class="group">
      <summary class="flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-left text-gray-900 bg-gray-50 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1">
        <span class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          {% trans 'Search History Configuration' %}
          <span class="ml-2 text-xs text-gray-500">(5 options)</span>
        </span>
        <svg class="w-4 h-4 text-gray-500 transition-transform group-open:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </summary>
      <div class="mt-3 border border-gray-200 rounded-lg p-4 bg-white">
        <p class="text-xs text-gray-500 mb-4">
          {% trans 'Configure how search history behaves for users of this widget. Search history allows users to save and quickly re-execute their recent vehicle searches.' %}
        </p>
        
        <div class="config-section-content">
          <div class="row">
            <!-- Enable Search History -->
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label">
                  {{ form.search_history.enabled.label }}
                  {% if form.search_history.enabled.help_text %}
                    <i class="fa fa-question-circle help-tooltip" 
                       title="{{ form.search_history.enabled.help_text }}"></i>
                  {% endif %}
                </label>
                <div class="form-control-wrapper">
                  {{ form.search_history.enabled }}
                  {% if form.search_history.enabled.errors %}
                    <div class="form-error">
                      {{ form.search_history.enabled.errors.0 }}
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Show Timestamps -->
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label">
                  {{ form.search_history.show_timestamps.label }}
                  {% if form.search_history.show_timestamps.help_text %}
                    <i class="fa fa-question-circle help-tooltip" 
                       title="{{ form.search_history.show_timestamps.help_text }}"></i>
                  {% endif %}
                </label>
                <div class="form-control-wrapper">
                  {{ form.search_history.show_timestamps }}
                  {% if form.search_history.show_timestamps.errors %}
                    <div class="form-error">
                      {{ form.search_history.show_timestamps.errors.0 }}
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <!-- Maximum Stored Searches -->
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-label">
                  {{ form.search_history.max_items.label }}
                  {% if form.search_history.max_items.help_text %}
                    <i class="fa fa-question-circle help-tooltip" 
                       title="{{ form.search_history.max_items.help_text }}"></i>
                  {% endif %}
                </label>
                <div class="form-control-wrapper">
                  {{ form.search_history.max_items }}
                  {% if form.search_history.max_items.errors %}
                    <div class="form-error">
                      {{ form.search_history.max_items.errors.0 }}
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Default Display Items -->
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-label">
                  {{ form.search_history.display_items.label }}
                  {% if form.search_history.display_items.help_text %}
                    <i class="fa fa-question-circle help-tooltip" 
                       title="{{ form.search_history.display_items.help_text }}"></i>
                  {% endif %}
                </label>
                <div class="form-control-wrapper">
                  {{ form.search_history.display_items }}
                  {% if form.search_history.display_items.errors %}
                    <div class="form-error">
                      {{ form.search_history.display_items.errors.0 }}
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Auto-expand Panel -->
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-label">
                  {{ form.search_history.auto_expand.label }}
                  {% if form.search_history.auto_expand.help_text %}
                    <i class="fa fa-question-circle help-tooltip" 
                       title="{{ form.search_history.auto_expand.help_text }}"></i>
                  {% endif %}
                </label>
                <div class="form-control-wrapper">
                  {{ form.search_history.auto_expand }}
                  {% if form.search_history.auto_expand.errors %}
                    <div class="form-error">
                      {{ form.search_history.auto_expand.errors.0 }}
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>

          <!-- Usage Instructions -->
          <div class="row mt-4">
            <div class="col-md-12">
              <div class="config-info-box">
                <h4 class="info-title">
                  <i class="fa fa-info-circle"></i>
                  {% trans 'How Search History Works' %}
                </h4>
                <ul class="info-list">
                  <li>{% trans 'Search history is stored locally in the user\'s browser using localStorage' %}</li>
                  <li>{% trans 'Users can click on any previous search to instantly re-execute it' %}</li>
                  <li>{% trans 'Search data is automatically cleaned and deduplicated' %}</li>
                  <li>{% trans 'No personal data is sent to servers - everything stays in the user\'s browser' %}</li>
                  <li>{% trans 'History gracefully degrades if localStorage is not available' %}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </details>
  </div>
</div>

<style>
.config-section {
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #fff;
}

.config-section-title {
  margin: 0;
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 8px 8px 0 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
}

.config-section-title i {
  margin-right: 0.5rem;
  color: #4299e1;
}

.config-section-content {
  padding: 1.5rem;
}

.config-section-description {
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.form-label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  display: block;
}

.help-tooltip {
  color: #6b7280;
  cursor: help;
  margin-left: 0.25rem;
}

.form-control-wrapper {
  position: relative;
}

.form-error {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}


.config-info-box {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 1rem;
}

.info-title {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 500;
  color: #0369a1;
}

.info-title i {
  margin-right: 0.5rem;
}

.info-list {
  margin: 0;
  padding-left: 1.25rem;
  color: #075985;
  line-height: 1.6;
}

.info-list li {
  margin-bottom: 0.25rem;
}

.info-list li:last-child {
  margin-bottom: 0;
}
</style>