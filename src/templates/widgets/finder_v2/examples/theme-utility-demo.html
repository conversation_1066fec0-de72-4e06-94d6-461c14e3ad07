<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finder-v2 Theme Utility Classes Demo</title>
    {% load theme_tags %}
    
    <!-- Render theme CSS with utility classes -->
    {% render_theme_css config.theme %}
    
    <!-- TailwindCSS for layout -->
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-main-color">
            Theme Utility Classes Demo
        </h1>
        
        <!-- Color Swatches -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-main-color">Color Swatches</h2>
            <div class="grid grid-cols-5 gap-4">
                <div class="text-center">
                    <div class="bg-primary-color h-16 w-full theme-border-radius theme-shadow mb-2"></div>
                    <p class="text-sm text-primary-color">Primary</p>
                </div>
                <div class="text-center">
                    <div class="bg-secondary-color h-16 w-full theme-border-radius theme-shadow mb-2"></div>
                    <p class="text-sm text-secondary-color">Secondary</p>
                </div>
                <div class="text-center">
                    <div class="bg-accent-color h-16 w-full theme-border-radius theme-shadow mb-2"></div>
                    <p class="text-sm text-accent-color">Accent</p>
                </div>
                <div class="text-center">
                    <div class="bg-background-color border border-secondary-color h-16 w-full theme-border-radius theme-shadow mb-2"></div>
                    <p class="text-sm text-background-color bg-main-color theme-padding">Background</p>
                </div>
                <div class="text-center">
                    <div class="bg-main-color h-16 w-full theme-border-radius theme-shadow mb-2"></div>
                    <p class="text-sm text-main-color">Text</p>
                </div>
            </div>
        </section>
        
        <!-- Text Color Examples -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-main-color">Text Colors</h2>
            <div class="space-y-2">
                <p class="text-primary-color">This text uses the primary theme color</p>
                <p class="text-secondary-color">This text uses the secondary theme color</p>
                <p class="text-accent-color">This text uses the accent theme color</p>
                <p class="text-main-color">This text uses the main text color</p>
            </div>
        </section>
        
        <!-- Button Examples -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-main-color">Button Examples</h2>
            <div class="space-x-4">
                <button class="bg-primary-color text-background-color theme-padding theme-border-radius hover:bg-accent-color transition-all theme-font-family">
                    Primary Button
                </button>
                <button class="bg-secondary-color text-background-color theme-padding theme-border-radius hover:bg-primary-color transition-all theme-font-family">
                    Secondary Button
                </button>
                <button class="bg-accent-color text-background-color theme-padding theme-border-radius hover:bg-primary-color transition-all theme-font-family">
                    Accent Button
                </button>
                <button class="border border-primary-color text-primary-color bg-background-color theme-padding theme-border-radius hover:bg-primary-color hover:text-background-color transition-all theme-font-family">
                    Outline Button
                </button>
            </div>
        </section>
        
        <!-- Card Examples -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-main-color">Card Examples</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Product Card -->
                <div class="bg-background-color border border-secondary-color theme-border-radius theme-padding theme-shadow">
                    <div class="font-medium uppercase tracking-wide text-sm mb-1 text-primary-color">
                        OE Option
                    </div>
                    <h3 class="text-main-color theme-font-family font-bold text-lg mb-2">Premium Wheel Set</h3>
                    <p class="text-secondary-color mb-4">High-quality aluminum wheels with premium finish.</p>
                    <button class="bg-accent-color text-background-color theme-padding theme-border-radius w-full hover:bg-primary-color transition-all">
                        Add to Cart
                    </button>
                </div>
                
                <!-- Info Card -->
                <div class="bg-primary-color text-background-color theme-border-radius theme-padding theme-shadow">
                    <h3 class="font-bold text-lg mb-2">Featured</h3>
                    <p class="mb-4">This product is currently featured in our catalog.</p>
                    <button class="bg-background-color text-primary-color theme-padding theme-border-radius hover:bg-accent-color hover:text-background-color transition-all">
                        Learn More
                    </button>
                </div>
                
                <!-- Alert Card -->
                <div class="bg-accent-color text-background-color theme-border-radius theme-padding theme-shadow">
                    <h3 class="font-bold text-lg mb-2">Success!</h3>
                    <p>Your configuration has been saved successfully.</p>
                </div>
            </div>
        </section>
        
        <!-- Form Example -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-main-color">Form Example</h2>
            <form class="bg-background-color theme-padding theme-border-radius theme-shadow max-w-md">
                <div class="mb-4">
                    <label class="block text-main-color theme-font-family font-bold mb-2">
                        Product Name
                    </label>
                    <input 
                        type="text" 
                        class="w-full theme-padding border border-secondary-color theme-border-radius text-main-color bg-background-color focus:border-accent-color outline-none transition-all"
                        placeholder="Enter product name"
                    >
                </div>
                
                <div class="mb-4">
                    <label class="block text-main-color theme-font-family font-bold mb-2">
                        Category
                    </label>
                    <select class="w-full theme-padding border border-secondary-color theme-border-radius text-main-color bg-background-color focus:border-accent-color outline-none">
                        <option>Select category</option>
                        <option>Wheels</option>
                        <option>Tires</option>
                        <option>Accessories</option>
                    </select>
                </div>
                
                <button class="bg-primary-color text-background-color theme-padding theme-border-radius hover:bg-accent-color theme-font-family font-bold w-full transition-all">
                    Submit
                </button>
            </form>
        </section>
        
        <!-- Hover Effects Demo -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-main-color">Hover Effects</h2>
            <div class="space-y-4">
                <div class="bg-background-color border border-secondary-color theme-padding theme-border-radius hover:bg-primary-color hover:text-background-color transition-all cursor-pointer">
                    Hover me for primary background
                </div>
                <div class="bg-background-color border border-secondary-color theme-padding theme-border-radius hover:bg-accent-color hover:text-background-color transition-all cursor-pointer">
                    Hover me for accent background
                </div>
                <div class="text-secondary-color hover:text-primary-color transition-all cursor-pointer theme-padding">
                    Hover me for primary text color
                </div>
            </div>
        </section>
        
        <!-- Typography Examples -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-main-color">Typography</h2>
            <div class="space-y-2">
                <p class="theme-font-family theme-font-size text-main-color">This uses the theme font family and size</p>
                <p class="theme-font-family text-lg text-primary-color">Large primary colored text</p>
                <p class="theme-font-family text-sm text-secondary-color">Small secondary colored text</p>
            </div>
        </section>
        
        <!-- Border Examples -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-main-color">Border Examples</h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="theme-padding border-2 border-primary-color theme-border-radius">
                    Primary border
                </div>
                <div class="theme-padding border-2 border-secondary-color theme-border-radius">
                    Secondary border
                </div>
                <div class="theme-padding border-2 border-accent-color theme-border-radius">
                    Accent border
                </div>
                <div class="theme-padding border-2 border-main-color theme-border-radius">
                    Text color border
                </div>
            </div>
        </section>
        
        <!-- Theme Information -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold mb-4 text-main-color">Current Theme Information</h2>
            <div class="bg-background-color border border-secondary-color theme-padding theme-border-radius">
                <p class="text-main-color"><strong>Theme Name:</strong> {{ config.theme.theme_name|default:"Default" }}</p>
                <p class="text-main-color"><strong>Primary Color:</strong> <span class="text-primary-color">{{ config.theme.primary_color|default:"#3B82F6" }}</span></p>
                <p class="text-main-color"><strong>Secondary Color:</strong> <span class="text-secondary-color">{{ config.theme.secondary_color|default:"#6B7280" }}</span></p>
                <p class="text-main-color"><strong>Accent Color:</strong> <span class="text-accent-color">{{ config.theme.accent_color|default:"#10B981" }}</span></p>
                <p class="text-main-color"><strong>Background Color:</strong> {{ config.theme.background_color|default:"#FFFFFF" }}</p>
                <p class="text-main-color"><strong>Text Color:</strong> {{ config.theme.text_color|default:"#1F2937" }}</p>
            </div>
        </section>
    </div>
</body>
</html>