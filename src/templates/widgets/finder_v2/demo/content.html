{% load i18n %}
{% load jsonify %}

<div class="">
  <div class="makes-filter border-b border-gray-200">
    <div class=" pb-5">
      <h3 class="text-base font-semibold text-gray-900">{% trans 'Include or Exclude Brands' %}</h3>
      <p class="mt-2 max-w-4xl text-sm text-gray-500">
        {% blocktrans %}
        Use the tabs to control which brands appear in the widget. "Include Brands" shows only the brands you pick. "Exclude Brands" hides the brands you pick from the results.
        {% endblocktrans %}
      </p>
    </div>





    <!-- Hidden form field for tab state -->
    <input type="hidden" name="content-by" value="{{ form.content.by.value|default:'' }}" id="content-by-field">

    <!-- Mobile dropdown (hidden on desktop) -->
    <div class="grid grid-cols-1 sm:hidden mt-2">
      <!-- Use an "onChange" listener to redirect the user to the selected tab URL. -->
      <select aria-label="Select a tab" id="brand-filter-mobile" class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-2 pr-8 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600">
        <option value=""{% if not form.content.by.value %} selected{% endif %}>{% trans 'No Filter' %}</option>
        <option value="brands_exclude"{% if form.content.by.value == 'brands_exclude' %} selected{% endif %}>{% trans 'Exclude Brands' %}</option>
        <option value="brands"{% if form.content.by.value == 'brands' %} selected{% endif %}>{% trans 'Include Brands' %}</option>
      </select>
      <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end fill-gray-500" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
        <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
    </div>


    <!-- Desktop tabs (hidden on mobile) -->
    <div class="hidden sm:block">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8" aria-label="Brand Filter Tabs">
          <!-- No Filter Tab -->
          <button type="button"
                  class="brand-filter-tab group inline-flex items-center border-b-2 px-1 py-4 text-sm font-medium{% if not form.content.by.value %} border-ws-primary-500 text-ws-primary-600{% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %}"
                  data-tab=""
                  {% if not form.content.by.value %}aria-current="page"{% endif %}>
            <svg class="mr-2 -ml-0.5 size-5{% if not form.content.by.value %} text-ws-primary-500{% else %} text-gray-400 group-hover:text-gray-500{% endif %}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
            </svg>
            <span>{% trans 'No Filter' %}</span>
          </button>

            <!-- Exclude Brands Tab -->
                    <button type="button"
                    class="brand-filter-tab group inline-flex items-center border-b-2 px-1 py-4 text-sm font-medium{% if form.content.by.value == 'brands_exclude' %} border-ws-primary-500 text-ws-primary-600{% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %}"
                    data-tab="brands_exclude"
                    {% if form.content.by.value == 'brands_exclude' %}aria-current="page"{% endif %}>
              <svg class="mr-2 -ml-0.5 size-5{% if form.content.by.value == 'brands_exclude' %} text-ws-primary-500{% else %} text-gray-400 group-hover:text-gray-500{% endif %}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{% trans 'Exclude Brands' %}</span>
            </button>
          <!-- Include Brands Tab -->
          <button type="button"
                  class="brand-filter-tab group inline-flex items-center border-b-2 px-1 py-4 text-sm font-medium{% if form.content.by.value == 'brands' %} border-ws-primary-500 text-ws-primary-600{% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %}"
                  data-tab="brands"
                  {% if form.content.by.value == 'brands' %}aria-current="page"{% endif %}>
            <svg class="mr-2 -ml-0.5 size-5{% if form.content.by.value == 'brands' %} text-ws-primary-500{% else %} text-gray-400 group-hover:text-gray-500{% endif %}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{% trans 'Include Brands' %}</span>
          </button>


        </nav>
      </div>
    </div>

    <!-- Tab content panels -->
    <div class="tab-content mt-6">
      <!-- No Filter Panel -->
      <div id="panel-no-filter" class="tab-panel{% if not form.content.by.value %} block{% else %} hidden{% endif %} max-w-4xl">
        <div class="text-center py-8 text-gray-500">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{% trans 'No brand filtering' %}</h3>
          <p class="mt-1 text-sm text-gray-500">{% trans 'All brands will be shown in the widget' %}</p>
        </div>
      </div>

      <!-- Exclude Brands Panel -->
      <div id="panel-brands-exclude" class="tab-panel{% if form.content.by.value == 'brands_exclude' %} block{% else %} hidden{% endif %} max-w-4xl">
        <div class="form-group">
          <input type="hidden" name="content-brands_exclude" value="{{ form.content.brands_exclude.value|default:'' }}">
          {% if form.content.errors.brands_exclude %}
            <p class="mt-2 text-sm text-red-600">{{ form.content.errors.brands_exclude.as_text }}</p>
          {% endif %}
          <label class="block text-sm font-medium text-gray-700 mb-3">{% trans 'Select brands to exclude' %}</label>
          <div class="tag-cloud" data-field="brands_exclude">
            {% if form.content.choices.brands %}
              {% for brand in form.content.choices.brands %}
                <span data-slug="{{ brand.slug }}" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-red-100 hover:text-red-800 cursor-pointer transition-colors duration-200 mr-2 mb-2">
                  {{ brand.name }}
                </span>
              {% endfor %}
            {% else %}
              <div class="loading-state text-gray-500">{% trans 'Loading brands...' %}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Include Brands Panel -->
      <div id="panel-brands" class="tab-panel{% if form.content.by.value == 'brands' %} block{% else %} hidden{% endif %} max-w-4xl">
        <div class="form-group">
          <input type="hidden" name="content-brands" value="{{ form.content.brands.value|default:'' }}">
          {% if form.content.errors.brands %}
            <p class="mt-2 text-sm text-red-600">{{ form.content.errors.brands.as_text }}</p>
          {% endif %}
          <label class="block text-sm font-medium text-gray-700 mb-3">{% trans 'Select brands to include' %}</label>
          <div class="tag-cloud" data-field="brands">
            {% if form.content.choices.brands %}
              {% for brand in form.content.choices.brands %}
                <span data-slug="{{ brand.slug }}" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-ws-primary-100 hover:text-ws-primary-800 cursor-pointer transition-colors duration-200 mr-2 mb-2">
                  {{ brand.name }}
                </span>
              {% endfor %}
            {% else %}
              <div class="loading-state text-gray-500">{% trans 'Loading brands...' %}</div>
            {% endif %}
          </div>
        </div>
      </div>


    </div>


  </div>

  <div class="regions-filter border-b border-gray-200">
    


      <!-- Regions Section (separate from brand filtering tabs) -->
      <div class="mt-4">
        <div class="mb-4 ">

          <div class="pb-5">
            <h3 class="text-base font-semibold text-gray-900">{% trans 'Geographic Market Filtering' %}</h3>
            <p class="mt-2 max-w-4xl text-sm text-gray-500">
            {% blocktrans %}
            Target your audience by selecting the regions where they are located 
            (e.g., select 'USDM' for the United States region). The widget will then display
             vehicles sold in those regions.
            Leave blank to include all available regions.
            {% endblocktrans %}</p>
          </div>


        </div>

        <div class="form-group">
          <input type="hidden" name="content-regions" value="{{ form.content.regions.value|default:'' }}">
          {% if form.content.errors.regions %}
            <p class="mt-2 text-sm text-red-600">{{ form.content.errors.regions.as_text }}</p>
          {% endif %}
          <label class="block text-sm font-medium text-gray-700 mb-3">{% trans 'Choose Regions' %}</label>
          <div class="tag-cloud max-w-4xl" data-field="regions">
            {% if form.content.choices.regions %}
              {% for region in form.content.choices.regions %}
                <span data-slug="{{ region.slug }}" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-blue-100 hover:text-blue-800 cursor-pointer transition-colors duration-200 mr-2 mb-2">
                  {{ region.display }}
                </span>
              {% endfor %}
            {% else %}
              <div class="loading-state text-gray-500">{% trans 'Loading regions...' %}</div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
</div>

<script>
  // Initialize content choices and tab functionality when page loads
  document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 DOM LOADED: Starting finder-v2 demo initialization');
    
    // Initialize TailwindUI tab functionality
    initBrandFilterTabs();

    // Initialize tag functionality for all containers that already have data
    // Fixed browser compatibility: use explicit check instead of :has() selector
    document.querySelectorAll('.tag-cloud').forEach(function(cloud) {
      // Skip clouds that still have a loading-state placeholder
      // Browser compatibility fix: querySelector works across all browsers
      const hasLoadingState = cloud.querySelector('.loading-state') !== null;
      if (hasLoadingState) {
        console.log('🔍 DOM LOADED: Skipping cloud with loading state:', cloud.dataset.field);
        return; // Will be initialized after data loads
      }
      console.log('🔍 DOM LOADED: Initializing existing tag cloud for:', cloud.dataset.field);
      initTagChoiceControlsForContainer(cloud);
    });

    // Load choices data if not already available
    const cloudsWithoutData = document.querySelectorAll('.tag-cloud .loading-state');

    cloudsWithoutData.forEach(function(loadingElement) {
      const cloud = loadingElement.parentElement;
      const field = cloud.dataset.field;

      if (field === 'brands' || field === 'brands_exclude') {
        loadChoicesForField(cloud, 'makes', 'brands');
      } else if (field === 'regions') {
        loadChoicesForField(cloud, 'regions', 'regions');
      }
    });
  });

  // Modern tab switching logic for TailwindUI tabs
  function initBrandFilterTabs() {
    const tabButtons = document.querySelectorAll('.brand-filter-tab');
    const mobileSelect = document.getElementById('brand-filter-mobile');
    const contentByInput = document.getElementById('content-by-field');

    // Desktop tab click handlers
    tabButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const tabValue = button.dataset.tab;
        switchToTab(tabValue);
      });
    });

    // Mobile select change handler
    if (mobileSelect) {
      mobileSelect.addEventListener('change', (e) => {
        switchToTab(e.target.value);
      });
    }

    function switchToTab(tabValue) {
      // Update visual state for desktop tabs
      updateTabVisualState(tabValue);

      // Update mobile select
      if (mobileSelect) {
        mobileSelect.value = tabValue;
      }

      // Update form field
      if (contentByInput) {
        contentByInput.value = tabValue;
      }

      // Show/hide content panels
      updateContentPanels(tabValue);

      console.log('Switched to tab:', tabValue);
    }

    function updateTabVisualState(activeTab) {
      tabButtons.forEach(button => {
        const isActive = button.dataset.tab === activeTab;
        const svg = button.querySelector('svg');

        if (isActive) {
          // Active state
          button.className = 'brand-filter-tab group inline-flex items-center border-b-2 border-ws-primary-500 text-ws-primary-600 px-1 py-4 text-sm font-medium';
          button.setAttribute('aria-current', 'page');
          if (svg) svg.className = 'mr-2 -ml-0.5 size-5 text-ws-primary-500';
        } else {
          // Inactive state
          button.className = 'brand-filter-tab group inline-flex items-center border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 px-1 py-4 text-sm font-medium';
          button.removeAttribute('aria-current');
          if (svg) svg.className = 'mr-2 -ml-0.5 size-5 text-gray-400 group-hover:text-gray-500';
        }
      });
    }

    function updateContentPanels(activeTab) {
      const panels = {
        '': document.getElementById('panel-no-filter'),
        'brands': document.getElementById('panel-brands'),
        'brands_exclude': document.getElementById('panel-brands-exclude')
      };

      // Hide all panels
      Object.values(panels).forEach(panel => {
        if (panel) {
          panel.classList.add('hidden');
          panel.classList.remove('block');
        }
      });

      // Show active panel
      const activePanel = panels[activeTab];
      if (activePanel) {
        activePanel.classList.remove('hidden');
        activePanel.classList.add('block');
      }
    }
  }

  function loadChoicesForField(cloudElement, apiEndpoint, fieldName) {
    // Generate API URL - it should be specific for finder-v2 demo
    const apiUrl = '/widget/finder-v2/api/' + getApiEndpointCode(apiEndpoint);

    // Generate CSRF token using the same algorithm as the widget
    const csrfToken = generateCsrfToken();

    fetch(apiUrl, {
      method: 'GET',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Referer': window.location.origin + '/'
      },
      credentials: 'same-origin'
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('Network response was not ok: ' + response.status);
      }
      return response.json();
    })
    .then(data => {
      if (data && data.data && Array.isArray(data.data)) {
        renderChoicesForField(cloudElement, data.data, fieldName);
      } else {
        throw new Error('Invalid data format');
      }
    })
    .catch(error => {
      const errorHtml = '<div class="error-state">Sorry, list of ' + fieldName + ' is currently unavailable because of some server problems</div>';
      cloudElement.innerHTML = errorHtml;
    });
  }

  function renderChoicesForField(container, choices, fieldName) {
    const field = container.dataset.field;
    let hoverClass;

    // Set appropriate hover colors for different field types
    if (field === 'brands_exclude') {
      hoverClass = 'hover:bg-red-100 hover:text-red-800';
    } else if (field === 'regions') {
      hoverClass = 'hover:bg-blue-100 hover:text-blue-800';
    } else {
      hoverClass = 'hover:bg-ws-primary-100 hover:text-ws-primary-800';
    }

    const cloudHtml = choices.map(choice => {
      const name = choice.name || choice.display || choice.slug;
      const slug = choice.slug;
      return '<span data-slug="' + slug + '" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 ' + hoverClass + ' cursor-pointer transition-colors duration-200 mr-2 mb-2">' + name + '</span>';
    }).join('');

    container.innerHTML = cloudHtml;

    // Re-initialize tag functionality for this container
    initTagChoiceControlsForContainer(container);
  }

  function getApiEndpointCode(endpoint) {
    const mapping = {
      'makes': 'mk',
      'regions': 'rg'
    };
    return mapping[endpoint] || endpoint;
  }

  function generateCsrfToken() {
    // Generate CSRF token using the same algorithm as the server
    const userAgent = navigator.userAgent;
    const token = btoa(userAgent);
    const tokenSlice = token.slice(0, 32);
    
    let result = [];
    for (let i = 0; i < tokenSlice.length; i++) {
      // JavaScript: token[27 + 11 - (7 + i * 11) % 39]
      const index = (27 + 11 - (7 + i * 11) % 39) % token.length;
      result.push(token[index]);
    }
    
    return result.join('');
  }
  
  // Helper function to initialize tag choice controls for a specific container
  function initTagChoiceControlsForContainer(container) {
    const cloud = container;
    const hiddenInput = cloud.parentElement.querySelector('input[type="hidden"]');
    if (!hiddenInput) {
      console.log('🔍 TAG INIT: No hidden input found for container:', cloud.dataset.field);
      return;
    }

    const tags = {};
    const field = cloud.dataset.field;
    
    console.log('🔍 TAG INIT: Starting initialization for field:', field);
    console.log('🔍 TAG INIT: Hidden input name:', hiddenInput.name);
    console.log('🔍 TAG INIT: Hidden input value:', hiddenInput.value);

    // Parse existing values - handle both comma-separated strings and JSON arrays
    try {
      const existingValue = hiddenInput.value;
      if (existingValue) {
        let parsedValues = [];
        
        console.log('🔍 TAG INIT: Processing existing value:', existingValue);
        console.log('🔍 TAG INIT: Value type:', typeof existingValue);
        
        // Try parsing as JSON first
        try {
          const jsonParsed = JSON.parse(existingValue);
          console.log('🔍 TAG INIT: Successfully parsed as JSON:', jsonParsed);
          if (Array.isArray(jsonParsed)) {
            parsedValues = jsonParsed;
            console.log('🔍 TAG INIT: Using JSON array:', parsedValues);
          } else if (jsonParsed) {
            parsedValues = [jsonParsed];
            console.log('🔍 TAG INIT: Converting single JSON value to array:', parsedValues);
          }
        } catch (jsonError) {
          // Not JSON, treat as comma-separated string
          console.log('🔍 TAG INIT: Not valid JSON, treating as comma-separated string');
          parsedValues = existingValue.split(',');
          console.log('🔍 TAG INIT: Split comma-separated values:', parsedValues);
        }
        
        // Add each value to tags object
        parsedValues.forEach(function (tag, index) {
            const originalTag = String(tag).trim();
            const cleanTag = originalTag.toLowerCase();
            console.log('🔍 TAG INIT: Processing tag', index + ':', `"${tag}" -> "${cleanTag}"`);
            if (cleanTag && cleanTag !== '[]' && cleanTag !== 'null') {
              tags[cleanTag] = originalTag; // Store original case
              console.log('🔍 TAG INIT: Added tag to selection:', cleanTag, 'with original case:', originalTag);
            } else {
              console.log('🔍 TAG INIT: Skipped empty/invalid tag:', cleanTag);
            }
        });
        
        console.log('🔍 TAG INIT: Final parsed tags for', field, ':', Object.keys(tags));
      } else {
        console.log('🔍 TAG INIT: No existing value found for field:', field);
      }
    } catch (e) {
      console.error('🔍 TAG INIT: Error parsing existing tags:', e);
    }

    // Handle tag clicks
    cloud.querySelectorAll('span[data-slug]').forEach(function(span) {
      const originalSlug = span.dataset.slug.trim();
      const slug = originalSlug.toLowerCase();   // Normalize for matching

      // Set initial state with TailwindUI styling
      if (tags[slug]) {
        updateTagActiveState(span, true, field);
      }

      span.addEventListener('click', function(e) {
        e.preventDefault();

        // Toggle tag
        if (tags[slug]) {
          delete tags[slug];
          updateTagActiveState(span, false, field);
          console.log('Removed tag:', slug, 'from field:', field);
        } else {
          tags[slug] = originalSlug; // Store original case
          updateTagActiveState(span, true, field);
          console.log('Added tag:', slug, 'to field:', field);
        }

        // Update hidden input with comma-separated format (for demo template compatibility)
        // Use original case values from tags object
        const tagArray = Object.values(tags);
        hiddenInput.value = tagArray.join(',');

        console.log('Updated', hiddenInput.name, 'field with tags:', tagArray);
        console.log('Hidden input value set to:', hiddenInput.value);
      });
    });
  }

  // Update tag visual state with TailwindUI styling
  function updateTagActiveState(span, isActive, field) {
    if (isActive) {
      if (field === 'brands_exclude') {
        span.className = 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-500 text-white cursor-pointer transition-colors duration-200 mr-2 mb-2';
      } else if (field === 'regions') {
        span.className = 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-500 text-white cursor-pointer transition-colors duration-200 mr-2 mb-2';
      } else {
        span.className = 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-ws-primary-500 text-white cursor-pointer transition-colors duration-200 mr-2 mb-2';
      }
    } else {
      let hoverClass;
      if (field === 'brands_exclude') {
        hoverClass = 'hover:bg-red-100 hover:text-red-800';
      } else if (field === 'regions') {
        hoverClass = 'hover:bg-blue-100 hover:text-blue-800';
      } else {
        hoverClass = 'hover:bg-ws-primary-100 hover:text-ws-primary-800';
      }
      span.className = 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 ' + hoverClass + ' cursor-pointer transition-colors duration-200 mr-2 mb-2';
    }
  }
</script> 