<div id="finder-v2-app" class="widget" data-iframe-height>
  {% if config.params.interface_tabs|length > 1 %}
    <ul class="nav nav-tabs widget-tabs">
      {% for tab in config.params.interface_tabs %}
        <li role="presentation"{% if tab.primary %} class="active"{% endif %}>
          <a href="#{{ tab.html_id }}" data-toggle="tab" @click="onSelectTab('{{ tab.resource }}')">
            {% templatetag openvariable %}'{{ tab.title }}'|i18n {% templatetag closevariable %}
          </a>
        </li>
      {% endfor %}
    </ul>
  {% endif %}

  <div class="tab-content widget-tab-content">
    {% for tab in config.params.interface_tabs %}
      <div role="tabpanel" class="tab-pane{% if tab.primary %} active{% endif %}" id="{{ tab.html_id }}">
        {% include tab.template with blocks=config.params.interface.blocks %}
      </div>
    {% endfor %}
  </div>
</div>