"""
Theme model for widget customization.

This model provides comprehensive theming capabilities for widgets, including:
- Color customization (primary, secondary, accent, background, text)
- Typography settings (font family, sizes)
- Spacing and visual effects (border radius, shadow intensity)
- Advanced configuration through JSON field
- Validation and security measures
"""

from django.db import models
from django.core.validators import RegexValidator
from django.core.exceptions import ValidationError
import json
import re


class WidgetTheme(models.Model):
    """
    Theme configuration model for widgets.
    
    Provides comprehensive theming capabilities while maintaining
    security and accessibility standards.
    """
    
    # Relation to widget
    widget = models.OneToOneField(
        'widgets.WidgetConfig',
        on_delete=models.CASCADE,
        related_name='theme',
        help_text="The widget this theme is applied to"
    )
    
    # Theme identification
    theme_name = models.CharField(
        max_length=100,
        default="Custom",
        help_text="Display name for this theme"
    )
    
    predefined_theme = models.CharField(
        max_length=50,
        blank=True,
        default="",
        help_text="Selected predefined theme (if any)"
    )
    
    # Color configuration - using hex color validator
    hex_color_validator = RegexValidator(
        regex=r'^#(?:[0-9a-fA-F]{3}){1,2}$',
        message='Color must be a valid hex code (e.g., #FF0000 or #F00)'
    )
    
    primary_color = models.CharField(
        max_length=7,
        default="#3B82F6",
        validators=[hex_color_validator],
        help_text="Primary brand color (buttons, links, highlights)"
    )
    
    secondary_color = models.CharField(
        max_length=7,
        default="#6B7280",
        validators=[hex_color_validator],
        help_text="Secondary color (borders, dividers, subtle elements)"
    )
    
    accent_color = models.CharField(
        max_length=7,
        default="#10B981",
        validators=[hex_color_validator],
        help_text="Accent color (success states, calls-to-action)"
    )
    
    background_color = models.CharField(
        max_length=7,
        default="#FFFFFF",
        validators=[hex_color_validator],
        help_text="Background color for the widget"
    )
    
    text_color = models.CharField(
        max_length=7,
        default="#1F2937",
        validators=[hex_color_validator],
        help_text="Primary text color"
    )
    
    # Typography configuration
    FONT_FAMILY_CHOICES = [
        ('system-ui', 'System UI'),
        ('Inter', 'Inter'),
        ('Roboto', 'Roboto'),
        ('Open Sans', 'Open Sans'),
        ('Lato', 'Lato'),
        ('Montserrat', 'Montserrat'),
        ('Poppins', 'Poppins'),
        ('custom', 'Custom (specify in advanced config)'),
    ]
    
    font_family = models.CharField(
        max_length=100,
        choices=FONT_FAMILY_CHOICES,
        default="system-ui",
        help_text="Font family for text elements"
    )
    
    FONT_SIZE_CHOICES = [
        ('14px', 'Small (14px)'),
        ('16px', 'Medium (16px)'),
        ('18px', 'Large (18px)'),
        ('20px', 'Extra Large (20px)'),
    ]
    
    base_font_size = models.CharField(
        max_length=10,
        choices=FONT_SIZE_CHOICES,
        default="16px",
        help_text="Base font size for the widget"
    )
    
    # Spacing and visual effects
    BORDER_RADIUS_CHOICES = [
        ('0', 'None (0px)'),
        ('0.25rem', 'Small (4px)'),
        ('0.375rem', 'Medium (6px)'),
        ('0.5rem', 'Large (8px)'),
        ('0.75rem', 'Extra Large (12px)'),
        ('1rem', 'Round (16px)'),
    ]
    
    border_radius = models.CharField(
        max_length=10,
        choices=BORDER_RADIUS_CHOICES,
        default="0.375rem",
        help_text="Border radius for form elements and containers"
    )
    
    SHADOW_INTENSITY_CHOICES = [
        ('none', 'None'),
        ('light', 'Light'),
        ('medium', 'Medium'),
        ('heavy', 'Heavy'),
    ]
    
    shadow_intensity = models.CharField(
        max_length=20,
        choices=SHADOW_INTENSITY_CHOICES,
        default="medium",
        help_text="Shadow intensity for elevated elements"
    )
    
    # Advanced Typography Controls
    FONT_WEIGHT_CHOICES = [
        ('300', 'Light (300)'),
        ('400', 'Normal (400)'),
        ('500', 'Medium (500)'),
        ('600', 'Semi-bold (600)'),
        ('700', 'Bold (700)'),
    ]
    
    font_weight = models.CharField(
        max_length=10,
        choices=FONT_WEIGHT_CHOICES,
        default="400",
        help_text="Font weight for text elements"
    )
    
    LINE_HEIGHT_CHOICES = [
        ('1.2', 'Tight (1.2)'),
        ('1.4', 'Snug (1.4)'),
        ('1.5', 'Normal (1.5)'),
        ('1.6', 'Relaxed (1.6)'),
        ('1.8', 'Loose (1.8)'),
    ]
    
    line_height = models.CharField(
        max_length=10,
        choices=LINE_HEIGHT_CHOICES,
        default="1.5",
        help_text="Line height for text elements"
    )
    
    LETTER_SPACING_CHOICES = [
        ('-0.05em', 'Tighter (-0.05em)'),
        ('-0.025em', 'Tight (-0.025em)'),
        ('0', 'Normal (0)'),
        ('0.025em', 'Wide (0.025em)'),
        ('0.05em', 'Wider (0.05em)'),
        ('0.1em', 'Widest (0.1em)'),
    ]
    
    letter_spacing = models.CharField(
        max_length=10,
        choices=LETTER_SPACING_CHOICES,
        default="0",
        help_text="Letter spacing for text elements"
    )
    
    # Advanced Spacing Controls
    PADDING_CHOICES = [
        ('0.25rem', 'Extra Small (4px)'),
        ('0.5rem', 'Small (8px)'),
        ('0.75rem', 'Medium (12px)'),
        ('1rem', 'Large (16px)'),
        ('1.25rem', 'Extra Large (20px)'),
        ('1.5rem', 'XXL (24px)'),
    ]
    
    element_padding = models.CharField(
        max_length=10,
        choices=PADDING_CHOICES,
        default="0.75rem",
        help_text="Padding for form elements and buttons"
    )
    
    MARGIN_CHOICES = [
        ('0.25rem', 'Extra Small (4px)'),
        ('0.5rem', 'Small (8px)'),
        ('0.75rem', 'Medium (12px)'),
        ('1rem', 'Large (16px)'),
        ('1.25rem', 'Extra Large (20px)'),
        ('1.5rem', 'XXL (24px)'),
    ]
    
    element_margin = models.CharField(
        max_length=10,
        choices=MARGIN_CHOICES,
        default="0.5rem",
        help_text="Margin between elements"
    )
    
    # Advanced Border Controls
    BORDER_WIDTH_CHOICES = [
        ('0', 'None (0px)'),
        ('1px', 'Thin (1px)'),
        ('2px', 'Medium (2px)'),
        ('3px', 'Thick (3px)'),
        ('4px', 'Extra Thick (4px)'),
    ]
    
    border_width = models.CharField(
        max_length=10,
        choices=BORDER_WIDTH_CHOICES,
        default="1px",
        help_text="Border width for form elements"
    )
    
    # Advanced Animation Controls
    ANIMATION_SPEED_CHOICES = [
        ('0.1s', 'Very Fast (0.1s)'),
        ('0.2s', 'Fast (0.2s)'),
        ('0.3s', 'Normal (0.3s)'),
        ('0.4s', 'Slow (0.4s)'),
        ('0.5s', 'Very Slow (0.5s)'),
    ]
    
    animation_speed = models.CharField(
        max_length=10,
        choices=ANIMATION_SPEED_CHOICES,
        default="0.3s",
        help_text="Animation speed for hover and focus effects"
    )
    
    # Hover Effects
    HOVER_EFFECT_CHOICES = [
        ('none', 'None'),
        ('darken', 'Darken'),
        ('lighten', 'Lighten'),
        ('scale', 'Scale'),
        ('shadow', 'Shadow'),
        ('glow', 'Glow'),
    ]
    
    hover_effect = models.CharField(
        max_length=20,
        choices=HOVER_EFFECT_CHOICES,
        default="darken",
        help_text="Hover effect for interactive elements"
    )
    
    # Advanced configuration (JSON field for extensibility)
    advanced_config = models.JSONField(
        default=dict,
        blank=True,
        help_text="Advanced theme configuration in JSON format"
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'widget_theme'
        verbose_name = 'Widget Theme'
        verbose_name_plural = 'Widget Themes'
        indexes = [
            models.Index(fields=['widget']),
            models.Index(fields=['theme_name']),
        ]
    
    def __str__(self):
        return f"{self.theme_name} - {self.widget}"
    
    def clean(self):
        """
        Validate theme configuration for security and accessibility.
        """
        super().clean()
        
        # Validate color contrast (basic check)
        if self._calculate_luminance(self.background_color) < 0.5:
            # Dark background
            if self._calculate_contrast_ratio(self.background_color, self.text_color) < 4.5:
                raise ValidationError(
                    "Text color does not provide sufficient contrast with background color. "
                    "Please choose a lighter text color for accessibility."
                )
        else:
            # Light background
            if self._calculate_contrast_ratio(self.background_color, self.text_color) < 4.5:
                raise ValidationError(
                    "Text color does not provide sufficient contrast with background color. "
                    "Please choose a darker text color for accessibility."
                )
        
        # Validate advanced configuration JSON
        if self.advanced_config:
            self._validate_advanced_config()
    
    def _calculate_luminance(self, hex_color):
        """
        Calculate relative luminance of a color.
        Used for contrast ratio calculations.
        """
        # Convert hex to RGB
        hex_color = hex_color.lstrip('#')
        r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        
        # Convert to relative luminance
        def normalize_component(c):
            c = c / 255.0
            if c <= 0.03928:
                return c / 12.92
            else:
                return pow((c + 0.055) / 1.055, 2.4)
        
        r_norm = normalize_component(r)
        g_norm = normalize_component(g)
        b_norm = normalize_component(b)
        
        return 0.2126 * r_norm + 0.7152 * g_norm + 0.0722 * b_norm
    
    def _calculate_contrast_ratio(self, color1, color2):
        """
        Calculate contrast ratio between two colors.
        Returns ratio between 1 and 21.
        """
        l1 = self._calculate_luminance(color1)
        l2 = self._calculate_luminance(color2)
        
        # Ensure l1 is the lighter color
        if l1 < l2:
            l1, l2 = l2, l1
        
        return (l1 + 0.05) / (l2 + 0.05)
    
    def _validate_advanced_config(self):
        """
        Validate advanced configuration JSON for security.
        """
        if not isinstance(self.advanced_config, dict):
            raise ValidationError("Advanced configuration must be a valid JSON object")
        
        # Whitelist of allowed advanced configuration keys
        allowed_keys = {
            'custom_font_family', 'custom_font_sizes', 'responsive_overrides',
            'animation_speed', 'transition_timing', 'hover_effects',
            'focus_styles', 'custom_shadows', 'gradient_colors',
            'button_styles', 'input_styles', 'card_styles', 'spacing_overrides',
            'typography_overrides', 'border_overrides', 'animation_overrides'
        }
        
        for key in self.advanced_config.keys():
            if key not in allowed_keys:
                raise ValidationError(f"Advanced configuration key '{key}' is not allowed")
    
    def get_css_custom_properties(self):
        """
        Generate CSS custom properties for this theme.
        Returns a dictionary of CSS variable names and values.
        """
        return {
            # Color properties
            '--theme-primary': self.primary_color,
            '--theme-secondary': self.secondary_color,
            '--theme-accent': self.accent_color,
            '--theme-background': self.background_color,
            '--theme-text': self.text_color,
            
            # Typography properties
            '--theme-font-family': self._get_font_family_css(),
            '--theme-font-size': self.base_font_size,
            '--theme-font-weight': self.font_weight,
            '--theme-line-height': self.line_height,
            '--theme-letter-spacing': self.letter_spacing,
            
            # Spacing properties
            '--theme-padding': self.element_padding,
            '--theme-margin': self.element_margin,
            
            # Border properties
            '--theme-border-radius': self.border_radius,
            '--theme-border-width': self.border_width,
            
            # Shadow properties
            '--theme-shadow': self._get_shadow_css(),
            
            # Animation properties
            '--theme-animation-speed': self.animation_speed,
            '--theme-hover-effect': self._get_hover_effect_css(),
            
            # Derived colors
            '--theme-primary-rgb': self._hex_to_rgb(self.primary_color),
            '--theme-secondary-rgb': self._hex_to_rgb(self.secondary_color),
            '--theme-accent-rgb': self._hex_to_rgb(self.accent_color),
        }
    
    def _get_font_family_css(self):
        """
        Get CSS-ready font family string.
        """
        if self.font_family == 'system-ui':
            return 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
        elif self.font_family == 'custom':
            return self.advanced_config.get('custom_font_family', 'system-ui')
        else:
            return f'"{self.font_family}", system-ui, sans-serif'
    
    def _get_shadow_css(self):
        """
        Get CSS shadow values based on intensity.
        """
        shadows = {
            'none': 'none',
            'light': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            'medium': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            'heavy': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        }
        return shadows.get(self.shadow_intensity, shadows['medium'])
    
    def _get_hover_effect_css(self):
        """
        Get CSS for hover effects based on selected effect.
        """
        effects = {
            'none': 'none',
            'darken': 'brightness(0.9)',
            'lighten': 'brightness(1.1)',
            'scale': 'scale(1.05)',
            'shadow': 'none',  # Shadow is handled separately
            'glow': 'none',    # Glow is handled separately
        }
        return effects.get(self.hover_effect, effects['darken'])
    
    def _hex_to_rgb(self, hex_color):
        """
        Convert hex color to RGB values for use in CSS rgba() functions.
        """
        hex_color = hex_color.lstrip('#')
        r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        return f"{r}, {g}, {b}"
    
    def export_to_json(self):
        """
        Export theme configuration to JSON format.
        """
        return {
            'theme_name': self.theme_name,
            'colors': {
                'primary': self.primary_color,
                'secondary': self.secondary_color,
                'accent': self.accent_color,
                'background': self.background_color,
                'text': self.text_color,
            },
            'typography': {
                'font_family': self.font_family,
                'base_font_size': self.base_font_size,
            },
            'effects': {
                'border_radius': self.border_radius,
                'shadow_intensity': self.shadow_intensity,
                'border_width': self.border_width,
                'hover_effect': self.hover_effect,
                'animation_speed': self.animation_speed,
            },
            'spacing': {
                'element_padding': self.element_padding,
                'element_margin': self.element_margin,
            },
            'advanced_typography': {
                'font_weight': self.font_weight,
                'line_height': self.line_height,
                'letter_spacing': self.letter_spacing,
            },
            'advanced_config': self.advanced_config,
        }
    
    @classmethod
    def import_from_json(cls, json_data, widget):
        """
        Import theme configuration from JSON format.
        """
        theme = cls(widget=widget)
        
        theme.theme_name = json_data.get('theme_name', 'Imported Theme')
        
        colors = json_data.get('colors', {})
        theme.primary_color = colors.get('primary', '#3B82F6')
        theme.secondary_color = colors.get('secondary', '#6B7280')
        theme.accent_color = colors.get('accent', '#10B981')
        theme.background_color = colors.get('background', '#FFFFFF')
        theme.text_color = colors.get('text', '#1F2937')
        
        typography = json_data.get('typography', {})
        theme.font_family = typography.get('font_family', 'system-ui')
        theme.base_font_size = typography.get('base_font_size', '16px')
        
        effects = json_data.get('effects', {})
        theme.border_radius = effects.get('border_radius', '0.375rem')
        theme.shadow_intensity = effects.get('shadow_intensity', 'medium')
        theme.border_width = effects.get('border_width', '1px')
        theme.hover_effect = effects.get('hover_effect', 'darken')
        theme.animation_speed = effects.get('animation_speed', '0.3s')
        
        spacing = json_data.get('spacing', {})
        theme.element_padding = spacing.get('element_padding', '0.75rem')
        theme.element_margin = spacing.get('element_margin', '0.5rem')
        
        advanced_typography = json_data.get('advanced_typography', {})
        theme.font_weight = advanced_typography.get('font_weight', '400')
        theme.line_height = advanced_typography.get('line_height', '1.5')
        theme.letter_spacing = advanced_typography.get('letter_spacing', '0')
        
        theme.advanced_config = json_data.get('advanced_config', {})
        
        return theme