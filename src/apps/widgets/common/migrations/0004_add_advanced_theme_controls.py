# Generated migration for advanced theme controls
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('widgets', '0003_add_widget_theme'),
    ]

    operations = [
        migrations.AddField(
            model_name='widgettheme',
            name='font_weight',
            field=models.CharField(
                choices=[
                    ('300', 'Light (300)'),
                    ('400', 'Normal (400)'),
                    ('500', 'Medium (500)'),
                    ('600', 'Semi-bold (600)'),
                    ('700', 'Bold (700)'),
                ],
                default='400',
                help_text='Font weight for text elements',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='widgettheme',
            name='line_height',
            field=models.CharField(
                choices=[
                    ('1.2', 'Tight (1.2)'),
                    ('1.4', 'Snug (1.4)'),
                    ('1.5', 'Normal (1.5)'),
                    ('1.6', 'Relaxed (1.6)'),
                    ('1.8', 'Loose (1.8)'),
                ],
                default='1.5',
                help_text='Line height for text elements',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='widgettheme',
            name='letter_spacing',
            field=models.CharField(
                choices=[
                    ('-0.05em', 'Tighter (-0.05em)'),
                    ('-0.025em', 'Tight (-0.025em)'),
                    ('0', 'Normal (0)'),
                    ('0.025em', 'Wide (0.025em)'),
                    ('0.05em', 'Wider (0.05em)'),
                    ('0.1em', 'Widest (0.1em)'),
                ],
                default='0',
                help_text='Letter spacing for text elements',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='widgettheme',
            name='element_padding',
            field=models.CharField(
                choices=[
                    ('0.25rem', 'Extra Small (4px)'),
                    ('0.5rem', 'Small (8px)'),
                    ('0.75rem', 'Medium (12px)'),
                    ('1rem', 'Large (16px)'),
                    ('1.25rem', 'Extra Large (20px)'),
                    ('1.5rem', 'XXL (24px)'),
                ],
                default='0.75rem',
                help_text='Padding for form elements and buttons',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='widgettheme',
            name='element_margin',
            field=models.CharField(
                choices=[
                    ('0.25rem', 'Extra Small (4px)'),
                    ('0.5rem', 'Small (8px)'),
                    ('0.75rem', 'Medium (12px)'),
                    ('1rem', 'Large (16px)'),
                    ('1.25rem', 'Extra Large (20px)'),
                    ('1.5rem', 'XXL (24px)'),
                ],
                default='0.5rem',
                help_text='Margin between elements',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='widgettheme',
            name='border_width',
            field=models.CharField(
                choices=[
                    ('0', 'None (0px)'),
                    ('1px', 'Thin (1px)'),
                    ('2px', 'Medium (2px)'),
                    ('3px', 'Thick (3px)'),
                    ('4px', 'Extra Thick (4px)'),
                ],
                default='1px',
                help_text='Border width for form elements',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='widgettheme',
            name='animation_speed',
            field=models.CharField(
                choices=[
                    ('0.1s', 'Very Fast (0.1s)'),
                    ('0.2s', 'Fast (0.2s)'),
                    ('0.3s', 'Normal (0.3s)'),
                    ('0.4s', 'Slow (0.4s)'),
                    ('0.5s', 'Very Slow (0.5s)'),
                ],
                default='0.3s',
                help_text='Animation speed for hover and focus effects',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='widgettheme',
            name='hover_effect',
            field=models.CharField(
                choices=[
                    ('none', 'None'),
                    ('darken', 'Darken'),
                    ('lighten', 'Lighten'),
                    ('scale', 'Scale'),
                    ('shadow', 'Shadow'),
                    ('glow', 'Glow'),
                ],
                default='darken',
                help_text='Hover effect for interactive elements',
                max_length=20
            ),
        ),
    ]