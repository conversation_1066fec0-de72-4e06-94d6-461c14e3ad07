# Generated manually for widget theme system
from django.db import migrations, models
import django.core.validators


class Migration(migrations.Migration):

    dependencies = [
        ('widgets', '0002_widgetsubscription_is_mobile'),
    ]

    operations = [
        migrations.CreateModel(
            name='WidgetTheme',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('theme_name', models.CharField(default='Custom', help_text='Display name for this theme', max_length=100)),
                ('primary_color', models.CharField(default='#3B82F6', help_text='Primary brand color (buttons, links, highlights)', max_length=7, validators=[django.core.validators.RegexValidator(message='Color must be a valid hex code (e.g., #FF0000 or #F00)', regex='^#(?:[0-9a-fA-F]{3}){1,2}$')])),
                ('secondary_color', models.<PERSON><PERSON><PERSON><PERSON>(default='#6B7280', help_text='Secondary color (borders, dividers, subtle elements)', max_length=7, validators=[django.core.validators.RegexValidator(message='Color must be a valid hex code (e.g., #FF0000 or #F00)', regex='^#(?:[0-9a-fA-F]{3}){1,2}$')])),
                ('accent_color', models.CharField(default='#10B981', help_text='Accent color (success states, calls-to-action)', max_length=7, validators=[django.core.validators.RegexValidator(message='Color must be a valid hex code (e.g., #FF0000 or #F00)', regex='^#(?:[0-9a-fA-F]{3}){1,2}$')])),
                ('background_color', models.CharField(default='#FFFFFF', help_text='Background color for the widget', max_length=7, validators=[django.core.validators.RegexValidator(message='Color must be a valid hex code (e.g., #FF0000 or #F00)', regex='^#(?:[0-9a-fA-F]{3}){1,2}$')])),
                ('text_color', models.CharField(default='#1F2937', help_text='Primary text color', max_length=7, validators=[django.core.validators.RegexValidator(message='Color must be a valid hex code (e.g., #FF0000 or #F00)', regex='^#(?:[0-9a-fA-F]{3}){1,2}$')])),
                ('font_family', models.CharField(choices=[('system-ui', 'System UI'), ('Inter', 'Inter'), ('Roboto', 'Roboto'), ('Open Sans', 'Open Sans'), ('Lato', 'Lato'), ('Montserrat', 'Montserrat'), ('Poppins', 'Poppins'), ('custom', 'Custom (specify in advanced config)')], default='system-ui', help_text='Font family for text elements', max_length=100)),
                ('base_font_size', models.CharField(choices=[('14px', 'Small (14px)'), ('16px', 'Medium (16px)'), ('18px', 'Large (18px)'), ('20px', 'Extra Large (20px)')], default='16px', help_text='Base font size for the widget', max_length=10)),
                ('border_radius', models.CharField(choices=[('0', 'None (0px)'), ('0.25rem', 'Small (4px)'), ('0.375rem', 'Medium (6px)'), ('0.5rem', 'Large (8px)'), ('0.75rem', 'Extra Large (12px)'), ('1rem', 'Round (16px)')], default='0.375rem', help_text='Border radius for form elements and containers', max_length=10)),
                ('shadow_intensity', models.CharField(choices=[('none', 'None'), ('light', 'Light'), ('medium', 'Medium'), ('heavy', 'Heavy')], default='medium', help_text='Shadow intensity for elevated elements', max_length=20)),
                ('advanced_config', models.JSONField(blank=True, default=dict, help_text='Advanced theme configuration in JSON format')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('widget', models.OneToOneField(help_text='The widget this theme is applied to', on_delete=models.deletion.CASCADE, related_name='theme', to='widgets.widgetconfig')),
            ],
            options={
                'verbose_name': 'Widget Theme',
                'verbose_name_plural': 'Widget Themes',
                'db_table': 'widget_theme',
            },
        ),
        migrations.AddIndex(
            model_name='widgettheme',
            index=models.Index(fields=['widget'], name='widget_theme_widget_idx'),
        ),
        migrations.AddIndex(
            model_name='widgettheme',
            index=models.Index(fields=['theme_name'], name='widget_theme_theme_name_idx'),
        ),
    ]