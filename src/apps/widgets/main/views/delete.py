from django.shortcuts import get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_protect

from src.apps.widgets.common.models import WidgetConfig


@login_required
@require_POST
@csrf_protect
def delete_widget(request, widget_slug):
    """
    Delete a widget configuration.
    
    This view allows authenticated users to delete their own widget configurations.
    Only the owner of the widget can delete it, and default configurations cannot be deleted.
    """
    # Get the widget configuration
    widget = get_object_or_404(WidgetConfig, uuid=widget_slug)
    
    # Check permissions
    if widget.user != request.user:
        messages.error(request, _('You do not have permission to delete this widget.'))
        return redirect('widgets')
    
    if widget.is_default:
        messages.error(request, _('Default widget configurations cannot be deleted.'))
        return redirect('widgets')
    
    # Store widget name for success message
    widget_name = widget.name
    
    # Delete the widget
    widget.delete()
    
    # Add success message
    messages.success(request, _('Widget "%s" has been deleted successfully.') % widget_name)
    
    # Redirect to widgets dashboard
    return redirect('widgets') 