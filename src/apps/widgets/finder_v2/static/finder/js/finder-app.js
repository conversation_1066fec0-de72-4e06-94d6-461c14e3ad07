var App = angular.module('wsFinder', [
    'wsLocalize',
    'wsMessages',
    'ngRoute',
    'ngResource',
    'angular-bootstrap-select'
]);

App.config([
    '$routeProvider', '$locationProvider',
    function($routeProvider, $locationProvider) {
        $locationProvider.html5Mode({
            enabled: true,
            requireBase: false
        });
    }
]);

App.factory('wsCache', [
    '$cacheFactory',
    function($cacheFactory) {
        var cache = $cacheFactory('widget');
        return cache;
    }
]);

App.factory('widgetConfig', [
    'widgetConfigObject',
    function(conf) {
        var config = angular.extend({}, conf);
        config.locale = {};
        return config;
    }
]);

App.config([
    '$httpProvider',
    function($httpProvider) {
        function rev(s) {
            return s.split('').reverse().join('');
        }

        var token = btoa(window[rev('rotagivan')][rev('tnegAresu')]);
        token = token.split('').slice(0, 30 + 2).map(function(value, i) {
            return token[27 + 11 - (7 + i * 11) % 39];
        }).join('');

        var headers = {};
        headers[rev('nekoT-frsC-X')] = token;
        angular.extend($httpProvider.defaults.headers.common, headers);
    }
]);

App.factory('Resources', [
    '$resource', 'widgetResources',
    function($resource, widgetResources) {
        function createResource(title, url) {
            var resource = $resource(url, {}, {
                get: { method: 'GET', isArray: true }
            });
            resource.title = title;
            return resource;
        }

        var resources = {};

        for (var resourceName in widgetResources) {
            var resource = widgetResources[resourceName];
            resources[resourceName] = createResource(resource[0], resource[1]);
        }

        return resources;
    }
]);

App.factory('ResourceChain', [
    '$timeout', 'Resources', 'WheelSizeMessages',
    function($timeout, Resources, WheelSizeMessages) {
        var Resource = function(name) {
            var resource = Resources[name];

            this.title = resource.title;
            this.name = name;
            this.resource = resource;
            this.reset();
        };
        Resource.prototype = {
            load: function(params) {
                var self = this;
                this.resource.get(params, function(data) {
                    self.items = data;
                    $timeout(function() {
                        self.disabled = false;
                        //var event = {
                        //    resource: self.name,
                        //    result: data
                        //};
                        //WheelSizeMessages.send('load', event);
                        //WheelSizeMessages.send('load:' + self.name, event);
                    }, 1);
                });
            },
            reset: function() {
                this.value = '';
                this.items = [];
                this.disabled = true;
            },
            getReadableValue: function() {
                if (!this.value) {
                    return '';
                }

                var slugs = this.items.map(function(resource) {
                    return resource.slug + '';
                });
                var index = slugs.indexOf(this.value);
                return this.items[index].name;
            },
            get: function() {
                return {
                    slug: this.value,
                    title: this.getReadableValue()
                };
            }
        };

        var ResourceChain = function(chain, final, prototypeExtension) {
            angular.extend(this, prototypeExtension);
            
            this.chain = chain.map(function(resourceName) {
                return new Resource(resourceName);
            });
            this.final = new Resource(final);
            this.nextResource = this.chain[0];
            this.resetResult();
        };
        ResourceChain.prototype = {
            loadNext: function() {
                if (this.nextResource === this.final) {
                    this.loading = true;
                }
                this.nextResource.load(this.params());
            },
            resetResult: function () {
                this.loading = false;
                this.result = {
                    items: [],
                    info: {}
                };
            },
            changed: function(index) {
                var resource = this.chain[index];
                var event = {
                    resource: resource.name,
                    value: resource.get()
                };
                WheelSizeMessages.send('change', event);
                WheelSizeMessages.send('change:' + resource.name, event);

                this.resetResult();
                for (var i = index + 1; i < this.chain.length; ++i) {
                    this.chain[i].reset();
                }

                if (resource.value) {
                    if (index + 1 < this.chain.length) {
                        this.nextResource = this.chain[index + 1];
                    } else {
                        this.nextResource = this.final;
                    }

                    this.loadNext();
                }
            },
            params: function() {
                var parameters = {};
                this.chain.forEach(function(resource) {
                    parameters[resource.name] = resource.value;
                });
                return parameters;
            },
            updateResult: function(items) {
                this.result.items = this.prepareItems(items);
                this.result.info = this.prepareInfo();
                this.loading = false;
            }
        };

        return ResourceChain;
    }
]);
App.factory('UserLinks', [
    '$interpolate', '$compile', 'WheelSizeMessages',
    function($interpolate, $compile, wsMessages) {
        var UserLink = function(place, options) {
            var match = place.match(/^(after|before|replace)(\w+)/);
            this.place = {
                type: match[1],
                column: match[2].toLowerCase(),
                original: place
            };

            this.template = this.templates[options.template];
            this.clickPreventDefault = options.clickPreventDefault;

            this.scope = {};
            angular.forEach(this.scopeVars, function(value, key) {
                var option = options[key] || value;
                if (typeof option === 'string') {
                    this.scope[key] = $interpolate(option, false, null, true);
                } else {
                    this.scope[key] = function() {
                        return option;
                    };
                }
            }, this);
        };

        UserLink.prototype = {
            scopeVars: {
                text: '',
                title: null,
                href: null,
                icon: null
            },
            templates: {
                userText: '<span ng-attr-title="{{ title }}">{{ text }}<span ng-if="text && icon"> </span><span ng-if="icon" class="glyphicon glyphicon-{{ icon }}"></span></span>',
                userLink: '<a href="{{ href }}" target="_blank" ng-click="click($event)" ng-attr-title="{{ title }}" class="user-link">{{ text }}<span ng-if="text && icon"> </span><span ng-if="icon" class="glyphicon glyphicon-{{ icon }}"></span></a>',
                textFooter: '<div class="user-footer">{{ text }}</div>',
                linkFooter: '<a class="user-footer" href="{{ href }}" ng-attr-title="{{ title }}">{{ text }}</a>'
            },
            clickHandler: function($event, context) {
                var target = angular.element($event.target);

                var event = {
                    column: this.place.column,
                    type: this.place.type,
                    link: {
                        href: target.attr('href'),
                        text: target.text()
                    },
                    context: context
                };
                wsMessages.send('click', event);
                wsMessages.send('click:' + this.place.column, event);
                wsMessages.send('click:' + this.place.original, event);
                if (this.clickPreventDefault) {
                    $event.preventDefault();
                }
            },
            updateScope: function($scope, $elem, $attrs) {
                var context = {};

                if ($scope.vehicle && $scope.wheel) {
                    $scope.side = $attrs.side || 'front';
                    angular.extend(context, {
                        trim: $scope.vehicle.trim,
                        is_oem: $scope.wheel.is_stock,
                        bolt_pattern: $scope.vehicle.bolt_pattern,
                        thd: $scope.vehicle.lock_text,
                        cb: $scope.vehicle.centre_bore,
                        lock_type: $scope.vehicle.lock_type,
                        power: $scope.vehicle.power,
                        fuel: $scope.vehicle.fuel,
                        engine_type: $scope.vehicle.engine_type,
                        generation: $scope.generation
                    });

                    // extend context with wheel parameters
                    angular.extend(context, $scope.wheel[$scope.side]);
                }
                // extend context with resources
                angular.forEach($scope.$parent.finder.chain, function(resource) {
                    context[resource.name] = resource.get();
                });

                angular.forEach(context, function(value, key) {
                    context[key] = value || undefined;
                });

                for (var prop in this.scope) {
                    $scope[prop] = this.scope[prop](context);
                }
                if ($scope.href) {
                    $scope.href = $scope.href.replace(/\s/g, '');
                }

                if (this.place.type === 'replace') {
                    $scope.text = $interpolate($scope.text || $elem.html())($scope);
                }

                var self = this;
                $scope.click = function($event) {
                    return self.clickHandler($event, context);
                };

                return $scope;
            },
            getClass: function() {
                return [
                    'user-link-',
                    this.place.type,
                    ' user-link-',
                    this.place.column
                ].join('');
            }
        };


        var UserLinks = {};

        wsMessages.on('register:userLinks', function(e) {
            for (var prop in e.data) {
                var options = e.data[prop];
                if (options.href) {
                    options.template = 'userLink';
                } else {
                    options.template = 'userText';
                }
                UserLinks[prop] = new UserLink(prop, options);
            }
        });
        wsMessages.on('register:userFooter', function(e) {
            for (var prop in e.data) {
                var options = e.data[prop];
                if (options.type === 'link') {
                    options.template = 'linkFooter';
                } else {
                    options.template = 'textFooter';
                }
                UserLinks[prop] = new UserLink(prop, options);
            }
        });

        return UserLinks;
    }
]);

App.directive('wsUserLink', [
    'UserLinks', '$compile', '$interpolate',
    function(UserLinks, $compile, $interpolate) {
        return {
            restrict: 'E',
            scope: {},
            link: function($scope, $elem, $attrs) {
                var link = UserLinks[$attrs.place];
                var originalHtml = $elem.html();

                try {
                    $scope.vehicle = $scope.$parent.vehicle;
                    $scope.wheel = $scope.$parent.wheel;
                    link.updateScope($scope, $elem, $attrs);

                    if ($interpolate(link.template, false, null, true)($scope)) {
                        var elem = $compile(link.template)($scope);
                        elem.addClass(link.getClass());
                        $elem.replaceWith(elem);
                    }
                } catch (e) {
                    var elem = $interpolate(originalHtml)($scope);
                    $elem.replaceWith(elem);
                }
            }
        };
    }
]);

App.factory('VehiclesGroup', [
    'widgetConfig',
    function(widgetConfig) {
        var markets = widgetConfig.markets;

        var vehicleMethods = {
            getProperty: function(wheelPair, property) {
                if (wheelPair.showing_fp_only) {
                    return wheelPair.front[property];
                }
                return [wheelPair.front[property], wheelPair.rear[property]].join(' / ');
            }
        };

        var VehiclesGroup = function() {
            this.groups = [];
        };
        VehiclesGroup.prototype = {
            getMarketGroup: function(market) {
                if (this.groups[market.slug] === undefined) {
                    this.groups.push({
                        market: market,
                        vehicles: []
                    });
                    this.groups[market.slug] = this.groups[this.groups.length - 1];
                }
                return this.groups[market.slug];
            },

            add: function(vehicle) {
                var group = this.getMarketGroup(vehicle.market);
                angular.extend(vehicle, vehicleMethods);
                group.vehicles.push(vehicle);
            },

            sortGroups: function() {
                if (markets.length !== 0) {
                    this.groups.sort(function (group1, group2) {
                        var index1 = markets.indexOf(group1.market.slug);
                        var index2 = markets.indexOf(group2.market.slug);
                        if (index1 !== -1 && index2 !== -1) {
                            return index1 - index2;
                        } else if (index1 === -1 || index2 === -1) {
                            return index2 - index1;
                        }
                        return 0;
                    });

                    angular.forEach(markets, function(market) {
                        var group = this.groups[market];
                        if (group) {
                            group.expand = true;
                        }
                    }, this);
                }

                if (this.groups.length) {
                    this.groups[0].expand = true;
                }

                return this.groups;
            }
        };

        return VehiclesGroup;
    }
]);


App.factory('FinderByModel', [
    'ResourceChain', 'VehiclesGroup', 'widgetConfig',
    function(ResourceChain, VehiclesGroup, widgetConfig) {

        return new ResourceChain(['make', 'year', 'model'], 'search_by_model', {
            prepareItems: function(vehicles) {
                var vehicleGroups = new VehiclesGroup();

                vehicles.forEach(function(vehicle) {
                    vehicleGroups.add(vehicle);
                });

                return vehicleGroups.sortGroups();
            },


            
            prepareInfo: function() {
                return {
                    urlToWs: this.buildModelUrl(),
                    title: this.buildModelTitle()
                };
            },

            buildModelUrl: function() {
                return [
                    'http://wheel-size.com/size',
                    this.chain[0].value,
                    this.chain[2].value,
                    this.chain[1].value,
                    ''
                ].join('/') + widgetConfig.utm;
            },

            buildModelTitle: function() {
                return [
                    this.chain[0].getReadableValue(),
                    this.chain[2].getReadableValue(),
                    this.chain[1].getReadableValue()
                ].join(' ');
            }
        });
    }
]);
App.factory('FinderByTire', [
    'ResourceChain',
    function(ResourceChain) {
        return new ResourceChain(['tire_width', 'aspect_ratio', 'rim_diameter'], 'search_by_tire', {
            prepareItems: function(items) {
                return items;
            },
            prepareInfo: function() {
                return {};
            }
        });
    }
]);
App.factory('FinderByRim', [
    'ResourceChain',
    function(ResourceChain) {
        return new ResourceChain(['rim_diameter', 'rim_width', 'bolt_pattern'], 'search_by_rim', {
            prepareItems: function(items) {
                return items;
            },
            prepareInfo: function() {
                return {};
            }

        });
    }
]);
App.directive('finderCollapse', [
    function() {
        function caretOnClick(e) {
            e.preventDefault();

            var $caret = $(this);
            var $rows = $caret.closest('tr')
                              .nextUntil('.fake-row')
                              .filter('.collapse');

            $rows.toggleClass('in');
            $caret.toggleClass('caret-collapsed caret-expanded');
        }

        return {
            scope: {},
            link: function($scope, $element, $attr) {
                $element.on('click', caretOnClick);
            }
        };
    }
]);

App.controller('WidgetMainCtrl', [
    '$scope', '$window', 'WheelSizeMessages',
    function($scope, $window, WheelSizeMessages) {
        WheelSizeMessages.beforeSend(function(e) {
            e.data.tab = $scope.activeTab;
        });
        WheelSizeMessages.afterSend(function(e) {
            var eventParams = e.type.split(':');
            if (eventParams.length < 2) {
                return;
            }
            $window.ga('send', 'event', eventParams[0], eventParams[1], e.data.tab);
        });

        $scope.onSelectTab = function(resourceName) {
            if ($scope.activeTab == resourceName) {
                return;
            }

            var event = {
                newTab: resourceName
            };
            WheelSizeMessages.send('activate', event);
            WheelSizeMessages.send('activate:' + resourceName, event);
            $scope.activeTab = resourceName;
        };
    }
]);
App.controller('FinderCtrl', [
    '$scope', '$element', '$timeout', 'widgetConfig',
    'FinderByModel', 'FinderByTire', 'FinderByRim',
    function($scope, $element, $timeout, widgetConfig,
             FinderByModel, FinderByTire, FinderByRim) {
        var finders = {
            byModel: FinderByModel,
            byTire: FinderByTire,
            byRim: FinderByRim
        };

        $scope.finder = finders[$element.attr('name')];

        $scope.$watch('finder.final.items', function (items) {
            $timeout(function () {
                if (items.length === 0) {
                    return;
                }

                $scope.finder.updateResult(items);
            }, 1);
        });

        $scope.buildUrl = function(make, model, year, vehicle) {
            var url = ['http://wheel-size.com/size'];
            url = url.concat([].slice.call(arguments, 0, 3));
            url = url.join('/');
            if (vehicle) {
                url = [url, '/#trim-', slugify(vehicle.trim), '-', vehicle.market.slug].join('');
            }
            return url + widgetConfig.utm;
        };

        function slugify(value) {
            return value.replace(/(\s+)|_/g, '-')
                        .replace(/[^\w-]/g, '')
                        .toLowerCase();
        }
    }
]);
