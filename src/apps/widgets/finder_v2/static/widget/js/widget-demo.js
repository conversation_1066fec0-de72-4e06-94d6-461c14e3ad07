var widget = WheelSizeWidgets.create('#user-links-demo', {
  uuid: 'finder',
  width: 800
});
widget.registerUserLinks({
  beforeTrim: {
    href: 'http://www.wheel-size.com/size/{{ make.slug }}/{{ model.slug }}/{{ year.slug }}/',
    icon: 'new-window',
    title: '{{ make.title }} {{ model.title }} {{ year.title }} on wheel-size.com'
  },
  replaceTire: {
    href: 'http://www.wheel-size.com/tire/{{ tire }}/',
    title: '{{ tire }} on wheel-size.com'
  },
  afterBoltPattern: {
    href: 'http://www.wheel-size.com/pcd/{{ bolt_pattern }}/',
    text: 'on site',
    title: '{{ bolt_pattern }} on wheel-size.com'
  }
});

