var App=angular.module("wsWidgetClientConfig",["colorpicker.module","ngPostMessage","wsLessManager"]);App.factory("WidgetTheme",[function(){var a=function(a,b){angular.extend(this,a),this.isDeclared=b};return a.prototype={getOriginal:function(){return this.source.original},getPrimaryColors:function(){return this.vars_order.slice(0,this.primary_colors_count)},getRestColors:function(){return this.vars_order.slice(this.primary_colors_count)}},a}]),App.factory("ThemeManager",["$timeout","LessManager","WidgetTheme",function(a,b,c){var d=function(a,b){this.editor=a;var c=this;a.getSession().on("change",function(){c.manual||b()})};d.prototype={getValue:function(){return this.manual=!1,this.editor.getSession().getValue()},setValue:function(a){return this.manual=!0,this.editor.getSession().setValue(a)}};var e=function(a,b){this.widget=b,this.all=a.all.map(function(b){return b=new c(b,!0),b.theme_name==a.activeThemeName&&this.setTheme(b),b},this),this.baseThemeName=a.baseThemeName,this.newThemeStub=a.newThemeStub,this.varPrefix=a.varPrefix};return e.prototype={lessManager:new b,setTheme:function(a,b){this.setActive(a),this.editor&&!b&&this.editor.setValue(a.source.advanced);var c=this;this.widget.ready(function(){c.updateVars(),c.sendAdvanced()})},changeVar:function(a){this.active.isDeclared&&this.createTheme(),this.updateVars(a)},changeAdvanced:function(){this.active.isDeclared&&this.createTheme(!0),this.active.source.advanced=this.editor.getValue(),this.sendAdvanced()},setActive:function(a){this.active=a,this.activeThemeName=a.theme_name,a.isDeclared?this.vars=angular.extend({},a.vars):this.vars=a.vars},updateVars:function(a){var b=this,c=this.refreshColorpicker();c.then(function(){b.errors={},b.sendOriginal()},function(c){b.errors[a]=!0})},sendOriginal:function(){this.widget.send("less:update",{vars:this.vars,name:"widget-original",source:this.active.getOriginal()})},sendAdvanced:function(){if(this.editor){var b=this.widget;b.send("less:update",{vars:this.vars,name:"widget-advanced",source:this.editor.getValue()}),a(function(){b.resize()},1)}},createTheme:function(a){this.newTheme&&this.all.pop();var b=angular.extend({},this.active,this.newThemeStub);b.vars=angular.extend({},this.vars),b.source=angular.extend({},this.active.source),b=this.newTheme=new c(b,!1),this.all.push(b),this.baseThemeName=this.activeThemeName,this.setTheme(b,a)},refreshColorpicker:function(){var a=[];for(var b in this.vars)a.push([".colorpicker-",b,"{","background-color: @",b,"}"].join(""));return this.lessManager.refresh(this.vars,"colorpicker",a.join(""))},resizeWidget:function(){this.widget.resize()},errorInColors:function(){for(var a in this.lessErrors)if(this.lessErrors[a])return!0;return!1},setEditor:function(a){var b=this;this.editor=new d(a,function(){b.changeAdvanced()})}},e}]),App.controller("WidgetMainCtrl",["$scope","$window","ThemeManager",function(a,b,c){a.init=function(b){var d=WheelSizeWidgets.create(".iframe-preview",b.config);a.config=b.config,a.themes=new c(b.themes,d)},angular.element(b).load(function(){var b=ace.editors["theme-advanced"];a.themes.setEditor(b)}),a.$watch("config.width",function(b){var c="100%";!isNaN(Number(b))&&b>0&&(c=b>=a.config.minWidth?b:a.config.minWidth),a.cleanedWidth=c})}]),App.controller("MakesFilterCtrl",["$scope",function(a){a.init=function(b,c,d){a.makesFilter={brands:angular.fromJson(b),countries:angular.fromJson(c),markets:angular.fromJson(d)}}}]),App.controller("TagChoiceCtrl",["$scope",function(a){function b(){var b=[];for(var c in a.tags)a.tags[c]&&b.push(c);b.sort(function(b,c){return a.choicesHash[b].index-a.choicesHash[c].index}),a.tagsString=angular.toJson(b)}a.init=function(c,d){if(a.choices=d,a.choicesHash={},angular.forEach(d,function(b,c){a.choicesHash[b.slug]=b,b.index=c}),a.tags={},c)for(var e=0;e<c.length;++e)a.tags[c[e]]=!0;b()},a.toggleTag=function(c){a.tags[c]=!a.tags[c],b()}}]),App.controller("DomainsListCtrl",["$scope",function(a){function b(){0===a.domains.length&&a.addDomain()}function c(){var b=[];angular.forEach(a.domains,function(a){a&&b.push(a)}),a.config.domains=angular.toJson(b)}a.domains=a.config.domains,"string"==typeof a.domains&&(a.domains=angular.fromJson(a.domains)),a.addDomain=function(){a.domains.push("")},b(),a.removeDomain=function(c){a.domains.splice(c,1),b()},a.$watch("domains",c,!0)}]);