/**
 * Theme preview styles for finder-v2 widget admin interface.
 * 
 * This CSS provides styling for the real-time theme preview system,
 * including responsive controls, theme gallery, and preview interface.
 */

/* Preview Container */
.preview-container {
    position: relative;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background: #f8f9fa;
    padding: 1rem;
    margin: 1rem 0;
}

.preview-container.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.preview-container.loading::before {
    content: '';
    width: 2rem;
    height: 2rem;
    border: 3px solid #dee2e6;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Preview iframe */
#theme-preview-frame {
    width: 100%;
    height: 600px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background: white;
    transition: width 0.3s ease, height 0.3s ease;
}

/* Responsive controls */
.responsive-controls {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.responsive-controls .btn-group {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.responsive-controls .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.responsive-controls .btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* Preview size variations */
.preview-container.preview-mobile {
    max-width: 425px;
    margin: 1rem auto;
}

.preview-container.preview-tablet {
    max-width: 820px;
    margin: 1rem auto;
}

.preview-container.preview-desktop {
    max-width: 100%;
    margin: 1rem 0;
}

/* Theme Gallery */
.theme-gallery {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 1rem 0;
}

.theme-gallery h5 {
    margin-bottom: 1rem;
    color: #495057;
    font-weight: 600;
}

.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
}

.theme-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0.375rem;
    overflow: hidden;
    border: 2px solid transparent;
}

.theme-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.theme-card.active {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.theme-preview {
    height: 80px;
    position: relative;
    display: flex;
    align-items: flex-end;
    padding: 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-color-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.theme-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-title {
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    color: inherit;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Form Controls */
.color-picker {
    width: 60px;
    height: 38px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    cursor: pointer;
    display: inline-block;
    margin-right: 0.5rem;
}

.color-picker:hover {
    border-color: #80bdff;
}

.color-picker:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Theme form sections */
.theme-form-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 1rem 0;
}

.theme-form-section h5 {
    margin-bottom: 1rem;
    color: #495057;
    font-weight: 600;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
}

.theme-form-section .form-group {
    margin-bottom: 1rem;
}

.theme-form-section .form-group:last-child {
    margin-bottom: 0;
}

/* Color group display */
.color-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.color-group label {
    min-width: 120px;
    margin-bottom: 0;
    font-weight: 500;
}

.color-group .color-picker {
    margin-right: 0.5rem;
}

.color-group .color-value {
    font-family: monospace;
    font-size: 0.875rem;
    color: #6c757d;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    min-width: 80px;
}

/* Advanced configuration */
.advanced-config-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 1rem 0;
}

.advanced-config-section textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    background: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.75rem;
    resize: vertical;
}

/* Import/Export controls */
.import-export-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 1rem 0;
}

.import-export-controls .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Preview fallback */
.preview-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 2rem;
}

.preview-fallback .alert {
    max-width: 400px;
    text-align: center;
}

/* Accessibility indicators */
.accessibility-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

.accessibility-indicator.good {
    background: #d4edda;
    color: #155724;
}

.accessibility-indicator.warning {
    background: #fff3cd;
    color: #856404;
}

.accessibility-indicator.error {
    background: #f8d7da;
    color: #721c24;
}

/* Theme validation messages */
.theme-validation {
    margin: 1rem 0;
}

.theme-validation .alert {
    margin-bottom: 0.5rem;
}

.theme-validation .alert:last-child {
    margin-bottom: 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .theme-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
    }
    
    .responsive-controls .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8125rem;
    }
    
    .responsive-controls .btn span {
        display: none;
    }
    
    .color-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .color-group label {
        min-width: auto;
    }
    
    .import-export-controls {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Focus styles for accessibility */
.theme-card:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.responsive-controls .btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Animation for theme transitions */
.theme-transition {
    transition: all 0.3s ease;
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #dee2e6;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Success/Error states */
.theme-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.375rem;
    margin: 0.5rem 0;
}

.theme-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.theme-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Utility classes */
.text-mono {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.border-dashed {
    border-style: dashed !important;
}

.cursor-pointer {
    cursor: pointer;
}