/**
 * Real-time theme preview system for finder-v2 widgets.
 * 
 * This module provides real-time preview functionality for theme changes
 * in the Django admin interface, enabling users to see theme modifications
 * instantly without page refresh.
 */

(function() {
    'use strict';

    /**
     * ThemePreviewManager handles real-time theme preview updates
     */
    class ThemePreviewManager {
        constructor(options = {}) {
            this.options = {
                previewFrameId: 'theme-preview-frame',
                previewContainerId: 'theme-preview-container',
                debounceDelay: 300,
                fallbackDelay: 5000,
                ...options
            };

            this.iframe = null;
            this.container = null;
            this.updateTimer = null;
            this.fallbackTimer = null;
            this.isInitialized = false;
            
            this.init();
        }

        init() {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.setup());
            } else {
                this.setup();
            }
        }

        setup() {
            this.iframe = document.getElementById(this.options.previewFrameId);
            this.container = document.getElementById(this.options.previewContainerId);

            if (!this.iframe || !this.container) {
                console.warn('Theme preview: Required elements not found');
                return;
            }

            // Initialize preview iframe
            this.setupPreviewFrame();
            
            // Bind form controls
            this.bindFormControls();
            
            // Setup responsive preview controls
            this.setupResponsiveControls();
            
            // Setup theme gallery
            this.setupThemeGallery();
            
            this.isInitialized = true;
            console.log('Theme preview system initialized');
        }

        setupPreviewFrame() {
            // Add loading state
            this.container.classList.add('loading');
            
            // Setup iframe load handler
            this.iframe.addEventListener('load', () => {
                this.container.classList.remove('loading');
                this.applyCurrentTheme();
            });

            // Setup fallback timer
            this.fallbackTimer = setTimeout(() => {
                if (this.container.classList.contains('loading')) {
                    this.showFallbackPreview();
                }
            }, this.options.fallbackDelay);
        }

        bindFormControls() {
            // Bind color pickers
            const colorPickers = document.querySelectorAll('.color-picker');
            colorPickers.forEach(picker => {
                picker.addEventListener('input', (e) => {
                    this.scheduleUpdate();
                });
                
                picker.addEventListener('change', (e) => {
                    this.scheduleUpdate();
                });
            });

            // Bind select controls
            const selectControls = document.querySelectorAll('select[name*="theme-"]');
            selectControls.forEach(select => {
                select.addEventListener('change', (e) => {
                    this.scheduleUpdate();
                });
            });

            // Bind predefined theme selector
            const themeSelector = document.querySelector('.theme-selector');
            if (themeSelector) {
                themeSelector.addEventListener('change', (e) => {
                    this.applyPredefinedTheme(e.target.value);
                });
            }

            // Bind advanced config textarea
            const advancedConfig = document.querySelector('textarea[name*="advanced_config"]');
            if (advancedConfig) {
                advancedConfig.addEventListener('input', (e) => {
                    this.scheduleUpdate();
                });
            }
        }

        setupResponsiveControls() {
            // Create responsive preview controls
            const responsiveControls = document.createElement('div');
            responsiveControls.className = 'responsive-controls';
            responsiveControls.innerHTML = `
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-size="mobile">
                        <i class="fas fa-mobile-alt"></i> Mobile
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary active" data-size="tablet">
                        <i class="fas fa-tablet-alt"></i> Tablet
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-size="desktop">
                        <i class="fas fa-desktop"></i> Desktop
                    </button>
                </div>
            `;

            // Insert before preview container
            this.container.parentNode.insertBefore(responsiveControls, this.container);

            // Bind responsive controls
            responsiveControls.querySelectorAll('[data-size]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    this.setPreviewSize(e.target.dataset.size);
                    
                    // Update active state
                    responsiveControls.querySelectorAll('.btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                });
            });
        }

        setupThemeGallery() {
            // Create theme gallery if predefined themes exist
            const themeSelector = document.querySelector('.theme-selector');
            if (!themeSelector) return;

            const galleryContainer = document.createElement('div');
            galleryContainer.className = 'theme-gallery';
            galleryContainer.innerHTML = `
                <h5>Theme Gallery</h5>
                <div class="theme-grid" id="theme-gallery-grid">
                    <!-- Theme previews will be inserted here -->
                </div>
            `;

            // Insert after theme selector
            themeSelector.parentNode.insertBefore(galleryContainer, themeSelector.nextSibling);

            // Load theme gallery data
            this.loadThemeGallery();
        }

        loadThemeGallery() {
            // This would normally load from the server
            // For now, we'll use the predefined themes from forms.py
            const themes = [
                { id: 'modern-blue', name: 'Modern Blue', colors: { primary: '#2563EB', background: '#FFFFFF' } },
                { id: 'corporate-gray', name: 'Corporate Gray', colors: { primary: '#374151', background: '#F9FAFB' } },
                { id: 'vibrant-green', name: 'Vibrant Green', colors: { primary: '#059669', background: '#FFFFFF' } },
                { id: 'elegant-purple', name: 'Elegant Purple', colors: { primary: '#7C3AED', background: '#FAFAFA' } },
                { id: 'warm-orange', name: 'Warm Orange', colors: { primary: '#EA580C', background: '#FFFBEB' } },
                { id: 'ocean-teal', name: 'Ocean Teal', colors: { primary: '#0D9488', background: '#F0FDFA' } },
                { id: 'sunset-red', name: 'Sunset Red', colors: { primary: '#DC2626', background: '#FEF2F2' } },
                { id: 'midnight-dark', name: 'Midnight Dark', colors: { primary: '#3B82F6', background: '#1F2937' } }
            ];

            const grid = document.getElementById('theme-gallery-grid');
            if (!grid) return;

            themes.forEach(theme => {
                const themeCard = document.createElement('div');
                themeCard.className = 'theme-card';
                themeCard.dataset.themeId = theme.id;
                themeCard.innerHTML = `
                    <div class="theme-preview" style="background: ${theme.colors.background};">
                        <div class="theme-color-bar" style="background: ${theme.colors.primary};"></div>
                        <div class="theme-content">
                            <div class="theme-title">${theme.name}</div>
                        </div>
                    </div>
                `;

                themeCard.addEventListener('click', () => {
                    this.selectThemeFromGallery(theme.id);
                });

                grid.appendChild(themeCard);
            });
        }

        selectThemeFromGallery(themeId) {
            // Update theme selector
            const themeSelector = document.querySelector('.theme-selector');
            if (themeSelector) {
                themeSelector.value = themeId;
                themeSelector.dispatchEvent(new Event('change'));
            }

            // Update active state in gallery
            document.querySelectorAll('.theme-card').forEach(card => {
                card.classList.remove('active');
            });
            
            const selectedCard = document.querySelector(`[data-theme-id="${themeId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('active');
            }
        }

        applyPredefinedTheme(themeId) {
            if (!themeId) return;

            // This would normally fetch theme data from the server
            // For now, we'll use hardcoded theme data
            const themes = {
                'modern-blue': {
                    primary: '#2563EB',
                    secondary: '#64748B',
                    accent: '#0EA5E9',
                    background: '#FFFFFF',
                    text: '#1E293B'
                },
                'corporate-gray': {
                    primary: '#374151',
                    secondary: '#9CA3AF',
                    accent: '#F59E0B',
                    background: '#F9FAFB',
                    text: '#111827'
                },
                'vibrant-green': {
                    primary: '#059669',
                    secondary: '#6B7280',
                    accent: '#10B981',
                    background: '#FFFFFF',
                    text: '#1F2937'
                },
                'elegant-purple': {
                    primary: '#7C3AED',
                    secondary: '#A78BFA',
                    accent: '#8B5CF6',
                    background: '#FAFAFA',
                    text: '#1F2937'
                },
                'warm-orange': {
                    primary: '#EA580C',
                    secondary: '#78716C',
                    accent: '#F97316',
                    background: '#FFFBEB',
                    text: '#1C1917'
                },
                'ocean-teal': {
                    primary: '#0D9488',
                    secondary: '#6B7280',
                    accent: '#14B8A6',
                    background: '#F0FDFA',
                    text: '#134E4A'
                },
                'sunset-red': {
                    primary: '#DC2626',
                    secondary: '#6B7280',
                    accent: '#EF4444',
                    background: '#FEF2F2',
                    text: '#1F2937'
                },
                'midnight-dark': {
                    primary: '#3B82F6',
                    secondary: '#6B7280',
                    accent: '#60A5FA',
                    background: '#1F2937',
                    text: '#F9FAFB'
                }
            };

            const theme = themes[themeId];
            if (!theme) return;

            // Update form fields
            this.updateFormFields(theme);
            
            // Apply theme to preview
            this.applyThemeToPreview(theme);
        }

        updateFormFields(theme) {
            Object.entries(theme).forEach(([key, value]) => {
                const field = document.querySelector(`[name*="${key}_color"]`);
                if (field) {
                    field.value = value;
                }
            });
        }

        scheduleUpdate() {
            // Clear existing timer
            if (this.updateTimer) {
                clearTimeout(this.updateTimer);
            }

            // Schedule new update
            this.updateTimer = setTimeout(() => {
                this.applyCurrentTheme();
            }, this.options.debounceDelay);
        }

        applyCurrentTheme() {
            if (!this.isInitialized || !this.iframe || !this.iframe.contentWindow) {
                return;
            }

            const theme = this.getCurrentTheme();
            this.applyThemeToPreview(theme);
        }

        getCurrentTheme() {
            const formData = new FormData(document.querySelector('form'));
            
            return {
                primary: formData.get('theme-primary_color') || '#3B82F6',
                secondary: formData.get('theme-secondary_color') || '#6B7280',
                accent: formData.get('theme-accent_color') || '#10B981',
                background: formData.get('theme-background_color') || '#FFFFFF',
                text: formData.get('theme-text_color') || '#1F2937',
                fontFamily: formData.get('theme-font_family') || 'system-ui',
                fontSize: formData.get('theme-base_font_size') || '16px',
                borderRadius: formData.get('theme-border_radius') || '0.375rem',
                shadowIntensity: formData.get('theme-shadow_intensity') || 'medium'
            };
        }

        applyThemeToPreview(theme) {
            if (!this.iframe || !this.iframe.contentDocument) {
                return;
            }

            const doc = this.iframe.contentDocument;
            
            // Remove existing theme styles
            const existingStyle = doc.getElementById('theme-preview-styles');
            if (existingStyle) {
                existingStyle.remove();
            }

            // Create new theme styles
            const style = doc.createElement('style');
            style.id = 'theme-preview-styles';
            style.textContent = this.generateThemeCSS(theme);

            // Inject styles
            doc.head.appendChild(style);

            // Update root CSS custom properties
            const root = doc.documentElement;
            root.style.setProperty('--theme-primary', theme.primary);
            root.style.setProperty('--theme-secondary', theme.secondary);
            root.style.setProperty('--theme-accent', theme.accent);
            root.style.setProperty('--theme-background', theme.background);
            root.style.setProperty('--theme-text', theme.text);
            root.style.setProperty('--theme-font-family', this.getFontFamilyCSS(theme.fontFamily));
            root.style.setProperty('--theme-font-size', theme.fontSize);
            root.style.setProperty('--theme-border-radius', theme.borderRadius);
            root.style.setProperty('--theme-shadow', this.getShadowCSS(theme.shadowIntensity));
        }

        generateThemeCSS(theme) {
            return `
                :root {
                    --theme-primary: ${theme.primary};
                    --theme-secondary: ${theme.secondary};
                    --theme-accent: ${theme.accent};
                    --theme-background: ${theme.background};
                    --theme-text: ${theme.text};
                    --theme-font-family: ${this.getFontFamilyCSS(theme.fontFamily)};
                    --theme-font-size: ${theme.fontSize};
                    --theme-border-radius: ${theme.borderRadius};
                    --theme-shadow: ${this.getShadowCSS(theme.shadowIntensity)};
                }

                /* Apply theme to widget elements */
                .widget {
                    background-color: var(--theme-background);
                    color: var(--theme-text);
                    font-family: var(--theme-font-family);
                    font-size: var(--theme-font-size);
                }

                .btn-primary {
                    background-color: var(--theme-primary);
                    border-color: var(--theme-primary);
                    border-radius: var(--theme-border-radius);
                    box-shadow: var(--theme-shadow);
                }

                .btn-primary:hover {
                    background-color: var(--theme-primary);
                    filter: brightness(0.9);
                }

                .form-control {
                    border-color: var(--theme-secondary);
                    border-radius: var(--theme-border-radius);
                    color: var(--theme-text);
                    background-color: var(--theme-background);
                }

                .form-control:focus {
                    border-color: var(--theme-accent);
                    box-shadow: 0 0 0 0.2rem rgba(${this.hexToRgb(theme.accent)}, 0.25);
                }

                .alert-success {
                    background-color: var(--theme-accent);
                    border-color: var(--theme-accent);
                    border-radius: var(--theme-border-radius);
                }

                .card {
                    background-color: var(--theme-background);
                    border-color: var(--theme-secondary);
                    border-radius: var(--theme-border-radius);
                    box-shadow: var(--theme-shadow);
                }
            `;
        }

        getFontFamilyCSS(fontFamily) {
            if (fontFamily === 'system-ui') {
                return 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            }
            return `"${fontFamily}", system-ui, sans-serif`;
        }

        getShadowCSS(intensity) {
            const shadows = {
                'none': 'none',
                'light': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                'medium': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                'heavy': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            };
            return shadows[intensity] || shadows['medium'];
        }

        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? 
                `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : 
                '0, 0, 0';
        }

        setPreviewSize(size) {
            const sizes = {
                mobile: { width: '375px', height: '667px' },
                tablet: { width: '768px', height: '1024px' },
                desktop: { width: '1200px', height: '800px' }
            };

            const targetSize = sizes[size];
            if (!targetSize) return;

            this.iframe.style.width = targetSize.width;
            this.iframe.style.height = targetSize.height;
            
            // Update container class for responsive styling
            this.container.className = `preview-container preview-${size}`;
        }

        showFallbackPreview() {
            this.container.classList.remove('loading');
            this.container.innerHTML = `
                <div class="preview-fallback">
                    <div class="alert alert-warning">
                        <h5>Preview Unavailable</h5>
                        <p>The widget preview could not be loaded. Your theme changes will still be applied when you save.</p>
                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="location.reload()">
                            Reload Preview
                        </button>
                    </div>
                </div>
            `;
        }

        resetPreview() {
            if (this.iframe && this.iframe.contentWindow) {
                this.iframe.contentWindow.location.reload();
            }
        }
    }

    // Initialize theme preview when DOM is ready
    let previewManager = null;

    function initializeThemePreview() {
        if (previewManager) return;
        
        previewManager = new ThemePreviewManager();
        
        // Expose to global scope for debugging
        window.themePreviewManager = previewManager;
    }

    // Auto-initialize
    initializeThemePreview();

    // Export for manual initialization
    window.ThemePreviewManager = ThemePreviewManager;
    window.initializeThemePreview = initializeThemePreview;

})();