"""
Finder-v2 Widget Type Definition

This module defines the FinderV2WidgetType class which configures the finder-v2 widget
for the wheel-size services platform. The finder-v2 widget allows users to search
for wheel and tire information using the new v2 API with enhanced functionality.

Key Features:
- Vehicle search with two flow types: Year→Make→Model and Make→Model→Generation
- Uses v2 API endpoints with improved data structure
- Vue 3 + TailwindCSS v4 frontend architecture
- Enhanced user experience with modern UI components
- Supports multiple themes and translations
- Complete isolation from legacy finder widget
"""

import os

from src.apps.widgets.finder_v2.default_config.config import FINDER_V2_DEFAULT_CONFIG
from src.apps.widgets.finder_v2.default_config.themes import FinderV2Themes
from src.apps.widgets.finder_v2.models import FinderV2JsonWrapper
from src.apps.widgets.finder_v2.translations.stub import FINDER_V2_TRANSLATION_STUB
from src.apps.widgets.widget_type import WidgetType, class_cached_property


class FinderV2WidgetType(WidgetType):
    """
    Widget type definition for the finder-v2 widget.

    The finder-v2 widget provides an enhanced search interface for wheel and tire information
    with two main flow types:
    1. Primary Flow: Year → Make → Model → Modifications
    2. Alternative Flow: Make → Model → Generation → Modifications

    This widget type uses v2 API endpoints and Vue 3 + TailwindCSS v4 frontend.
    It is completely isolated from the legacy finder widget.
    """

    # Widget identification
    type = 'finder-v2'  # Used in URLs: /widget/finder-v2/
    label = 'Search Form v2'  # Display name in admin interface

    # Widget constraints
    min_width = 250  # Minimum width in pixels for proper display

    # Visual configuration
    icon = 'widget/img/finder-v2-widget-icon.png'  # Icon for admin interface

    # Configuration and content
    default_config = FINDER_V2_DEFAULT_CONFIG  # Default widget settings
    translation_stub = FINDER_V2_TRANSLATION_STUB  # Translation keys
    translations_dir = os.path.join(os.path.dirname(__file__), 'translations')

    # Data handling
    json_wrapper = FinderV2JsonWrapper  # Handles widget configuration JSON

    # Theming
    themes = FinderV2Themes  # Available visual themes

    # API access - CRITICAL: This enables API proxy functionality for v2 endpoints
    allow_api = True  # Allows widget to make API calls via FinderV2WidgetProxyView

    # Static file configuration - paths to compiled CSS/JS files (Vue 3 build output)
    static = {
        'app_css_libs': 'finder_v2/css/finder-v2-app.css',       # Combined CSS (TailwindCSS + widget styles)
        'app_css': 'finder_v2/css/finder-v2-app.css',            # Widget-specific CSS (TailwindCSS)
        'app_js_libs':  'finder_v2/js/finder-v2-app-libs.js',    # Third-party JS libraries
        'app_js':  'finder_v2/js/finder-v2-app.js',              # Vue 3 app bundle (includes CSRF token logic)
    }

    INTERNAL_META_DATA = {
        'meta': {
            'version': '2.0',
            'type': type,
        }
    }

    @class_cached_property
    def templates(cls):
        """
        Template paths for finder-v2 widget views.

        Note: Uses 'finder_v2' (underscores) in paths because Django template
        directory structure uses underscores, while widget type uses 'finder-v2' (hyphens).

        Unified template consolidation: Both config and demo endpoints now use the same
        template with conditional content based on the URL name for consistent UX.
        """
        return {
            'config': 'widgets/finder_v2/page.html',
            'demo': 'widgets/finder_v2/page.html'
        }

    @class_cached_property
    def forms(cls):
        from src.apps.widgets.finder_v2.forms import FinderV2ConfigForm, \
            FinderV2DemoConfigForm

        return {
            'config': FinderV2ConfigForm,
            'demo': FinderV2DemoConfigForm,
        }

    @classmethod
    def get_template_name(cls, request, config):
        """
        Get the template name for the iframe view.
        
        This method determines which template to use for rendering the widget iframe
        based on the request and configuration.
        
        Args:
            request: Django HttpRequest object
            config: Widget configuration object
            
        Returns:
            str: Template path for the widget iframe
        """
        # For finder-v2, we always use the same iframe template
        return 'widgets/finder_v2/iframe/page.html'
