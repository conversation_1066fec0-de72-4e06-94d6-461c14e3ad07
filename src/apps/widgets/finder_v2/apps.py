from django.apps import AppConfig
from django.core.management import call_command
from django.db.models.signals import post_migrate

from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType


class FinderV2WidgetAppConfig(AppConfig):
    name = 'src.apps.widgets.finder_v2'
    label = 'widgets_finder_v2'

    def load_defaults(self, **kwargs):
        from src.apps.widgets.common.config_loader import DefaultConfigLoader

        config_loader = DefaultConfigLoader(widget_type=FinderV2WidgetType)

        config_loader.load_config()
        call_command('ws_load_translations',
                     type=FinderV2WidgetType.type,
                     initial=True)

    def ready(self):
        post_migrate.connect(self.load_defaults, sender=self)
