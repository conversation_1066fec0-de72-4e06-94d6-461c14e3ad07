"""
Predefined themes for finder-v2 widgets.

This module contains 8 professionally designed themes that users can select
from a gallery interface. Each theme is designed to meet accessibility
standards and provide a unique visual identity.

⚠️  CRITICAL SYNCHRONIZATION ALERT ⚠️
=====================================
When modifying theme definitions in this file, you MUST also update the 
JavaScript theme definitions in:
    /src/templates/widgets/finder_v2/config/theme.html (lines ~276-421)

The JavaScript contains hardcoded theme data that MUST match these Python 
definitions exactly. Failure to keep them synchronized will cause:
- Wrong colors applied when selecting predefined themes
- Incorrect default theme behavior  
- User confusion and theme inconsistencies

EXAMPLE OF REQUIRED SYNC:
If you change ocean-teal primary color from '#0D9488' to '#123456' here,
you MUST also change it in the JavaScript predefinedThemes object.

TODO: Consider generating JavaScript themes dynamically from this Python data
to eliminate this manual synchronization requirement.
"""

from typing import Dict, Any


class PredefinedThemes:
    """
    Collection of predefined themes for finder-v2 widgets.
    
    Each theme includes:
    - Complete color palette
    - Typography settings
    - Visual effects configuration
    - Preview image reference
    - Accessibility compliance
    """
    
    # Theme definitions with comprehensive configuration
    # ⚠️  SYNC ALERT: These definitions MUST match JavaScript in theme.html ⚠️
    THEMES = {
        'modern-blue': {
            'name': 'Modern Blue',
            'description': 'Clean, professional blue theme with modern styling',
            'preview_image': 'themes/previews/modern-blue.png',
            'colors': {
                'primary': '#2563EB',
                'secondary': '#64748B',
                'accent': '#0EA5E9',
                'background': '#FFFFFF',
                'text': '#1E293B'
            },
            'typography': {
                'font_family': 'Inter',
                'base_font_size': '16px'
            },
            'effects': {
                'border_radius': '0.5rem',
                'shadow_intensity': 'medium'
            },
            'advanced_config': {
                'hover_effects': True,
                'transition_timing': 'ease-in-out',
                'animation_speed': '0.2s'
            }
        },
        
        'corporate-gray': {
            'name': 'Corporate Gray',
            'description': 'Professional grayscale theme with business-friendly aesthetics',
            'preview_image': 'themes/previews/corporate-gray.png',
            'colors': {
                'primary': '#374151',
                'secondary': '#9CA3AF',
                'accent': '#F59E0B',
                'background': '#F9FAFB',
                'text': '#111827'
            },
            'typography': {
                'font_family': 'system-ui',
                'base_font_size': '16px'
            },
            'effects': {
                'border_radius': '0.375rem',
                'shadow_intensity': 'light'
            },
            'advanced_config': {
                'hover_effects': True,
                'transition_timing': 'ease',
                'animation_speed': '0.15s'
            }
        },
        
        'vibrant-green': {
            'name': 'Vibrant Green',
            'description': 'Fresh, energetic green theme with natural appeal',
            'preview_image': 'themes/previews/vibrant-green.png',
            'colors': {
                'primary': '#059669',
                'secondary': '#6B7280',
                'accent': '#10B981',
                'background': '#FFFFFF',
                'text': '#1F2937'
            },
            'typography': {
                'font_family': 'Roboto',
                'base_font_size': '16px'
            },
            'effects': {
                'border_radius': '0.5rem',
                'shadow_intensity': 'medium'
            },
            'advanced_config': {
                'hover_effects': True,
                'transition_timing': 'ease-out',
                'animation_speed': '0.25s'
            }
        },
        
        'elegant-purple': {
            'name': 'Elegant Purple',
            'description': 'Sophisticated purple theme with premium feel',
            'preview_image': 'themes/previews/elegant-purple.png',
            'colors': {
                'primary': '#7C3AED',
                'secondary': '#A78BFA',
                'accent': '#8B5CF6',
                'background': '#FAFAFA',
                'text': '#1F2937'
            },
            'typography': {
                'font_family': 'Inter',
                'base_font_size': '16px'
            },
            'effects': {
                'border_radius': '0.75rem',
                'shadow_intensity': 'heavy'
            },
            'advanced_config': {
                'hover_effects': True,
                'transition_timing': 'ease-in-out',
                'animation_speed': '0.3s',
                'gradient_colors': ['#7C3AED', '#A78BFA']
            }
        },
        
        'warm-orange': {
            'name': 'Warm Orange',
            'description': 'Friendly, approachable orange theme with warm tones',
            'preview_image': 'themes/previews/warm-orange.png',
            'colors': {
                'primary': '#EA580C',
                'secondary': '#78716C',
                'accent': '#F97316',
                'background': '#FFFBEB',
                'text': '#1C1917'
            },
            'typography': {
                'font_family': 'Open Sans',
                'base_font_size': '16px'
            },
            'effects': {
                'border_radius': '0.5rem',
                'shadow_intensity': 'medium'
            },
            'advanced_config': {
                'hover_effects': True,
                'transition_timing': 'ease-out',
                'animation_speed': '0.2s'
            }
        },
        
        'ocean-teal': {
            'name': 'Ocean Teal',
            'description': 'Calming teal theme inspired by ocean waters',
            'preview_image': 'themes/previews/ocean-teal.png',
            'colors': {
                'primary': '#0D9488',
                'secondary': '#6B7280',
                'accent': '#14B8A6',
                'background': '#F0FDFA',
                'text': '#134E4A'
            },
            'typography': {
                'font_family': 'Lato',
                'base_font_size': '16px'
            },
            'effects': {
                'border_radius': '0.5rem',
                'shadow_intensity': 'light'
            },
            'advanced_config': {
                'hover_effects': True,
                'transition_timing': 'ease',
                'animation_speed': '0.25s'
            }
        },
        
        'sunset-red': {
            'name': 'Sunset Red',
            'description': 'Bold, attention-grabbing red theme with energy',
            'preview_image': 'themes/previews/sunset-red.png',
            'colors': {
                'primary': '#DC2626',
                'secondary': '#6B7280',
                'accent': '#EF4444',
                'background': '#FEF2F2',
                'text': '#1F2937'
            },
            'typography': {
                'font_family': 'Montserrat',
                'base_font_size': '16px'
            },
            'effects': {
                'border_radius': '0.375rem',
                'shadow_intensity': 'medium'
            },
            'advanced_config': {
                'hover_effects': True,
                'transition_timing': 'ease-in-out',
                'animation_speed': '0.2s'
            }
        },
        
        'midnight-dark': {
            'name': 'Midnight Dark',
            'description': 'Modern dark theme with blue accents for night mode',
            'preview_image': 'themes/previews/midnight-dark.png',
            'colors': {
                'primary': '#3B82F6',
                'secondary': '#6B7280',
                'accent': '#60A5FA',
                'background': '#1F2937',
                'text': '#F9FAFB'
            },
            'typography': {
                'font_family': 'Poppins',
                'base_font_size': '16px'
            },
            'effects': {
                'border_radius': '0.5rem',
                'shadow_intensity': 'heavy'
            },
            'advanced_config': {
                'hover_effects': True,
                'transition_timing': 'ease-out',
                'animation_speed': '0.25s',
                'focus_styles': {
                    'outline_color': '#60A5FA',
                    'outline_width': '2px'
                }
            }
        }
    }
    
    # Default theme selection
    # ⚠️  SYNC ALERT: If changed, verify JavaScript uses same default ⚠️
    DEFAULT_THEME = 'modern-blue'
    
    @classmethod
    def get_theme(cls, theme_name: str) -> Dict[str, Any]:
        """
        Get a specific theme configuration.
        
        Args:
            theme_name: Name of the theme to retrieve
            
        Returns:
            Theme configuration dictionary
        """
        return cls.THEMES.get(theme_name, cls.THEMES[cls.DEFAULT_THEME])
    
    @classmethod
    def get_theme_choices(cls) -> list:
        """
        Get theme choices for Django forms.
        
        Returns:
            List of (value, label) tuples for form choices
        """
        return [(name, theme['name']) for name, theme in cls.THEMES.items()]
    
    @classmethod
    def get_theme_gallery(cls) -> list:
        """
        Get theme gallery data for preview interface.
        
        Returns:
            List of theme dictionaries with preview information
        """
        gallery = []
        for theme_id, theme_data in cls.THEMES.items():
            gallery.append({
                'id': theme_id,
                'name': theme_data['name'],
                'description': theme_data['description'],
                'preview_image': theme_data['preview_image'],
                'colors': theme_data['colors'],
                'is_dark': theme_data['colors']['background'] in ['#1F2937', '#111827']
            })
        return gallery
    
    @classmethod
    def create_widget_theme(cls, widget_config, theme_name: str = None):
        """
        Create a WidgetTheme instance from a predefined theme.
        
        Args:
            widget_config: WidgetConfig instance
            theme_name: Name of the theme to use (defaults to DEFAULT_THEME)
            
        Returns:
            WidgetTheme instance
        """
        from ..models import WidgetTheme
        
        if theme_name is None:
            theme_name = cls.DEFAULT_THEME
        
        theme_data = cls.get_theme(theme_name)
        
        # Create WidgetTheme instance
        widget_theme = WidgetTheme(
            widget=widget_config,
            theme_name=theme_data['name'],
            primary_color=theme_data['colors']['primary'],
            secondary_color=theme_data['colors']['secondary'],
            accent_color=theme_data['colors']['accent'],
            background_color=theme_data['colors']['background'],
            text_color=theme_data['colors']['text'],
            font_family=theme_data['typography']['font_family'],
            base_font_size=theme_data['typography']['base_font_size'],
            border_radius=theme_data['effects']['border_radius'],
            shadow_intensity=theme_data['effects']['shadow_intensity'],
            advanced_config=theme_data.get('advanced_config', {})
        )
        
        return widget_theme
    
    @classmethod
    def validate_theme_accessibility(cls, theme_name: str) -> Dict[str, Any]:
        """
        Validate accessibility compliance for a theme.
        
        Args:
            theme_name: Name of the theme to validate
            
        Returns:
            Validation results dictionary
        """
        from ..utils.theme_validator import ThemeValidator
        
        theme_data = cls.get_theme(theme_name)
        validator = ThemeValidator()
        
        return validator.validate_theme(theme_data)
    
    @classmethod
    def generate_theme_css(cls, theme_name: str) -> str:
        """
        Generate CSS for a predefined theme.
        
        Args:
            theme_name: Name of the theme
            
        Returns:
            CSS string for the theme
        """
        from ..utils.theme_generator import ThemeGenerator
        
        theme_data = cls.get_theme(theme_name)
        
        # Create a temporary widget theme for CSS generation
        class TempTheme:
            def __init__(self, data):
                self.theme_name = data['name']
                self.primary_color = data['colors']['primary']
                self.secondary_color = data['colors']['secondary']
                self.accent_color = data['colors']['accent']
                self.background_color = data['colors']['background']
                self.text_color = data['colors']['text']
                self.font_family = data['typography']['font_family']
                self.base_font_size = data['typography']['base_font_size']
                self.border_radius = data['effects']['border_radius']
                self.shadow_intensity = data['effects']['shadow_intensity']
                self.advanced_config = data.get('advanced_config', {})
                
            def get_css_custom_properties(self):
                return {
                    '--theme-primary': self.primary_color,
                    '--theme-secondary': self.secondary_color,
                    '--theme-accent': self.accent_color,
                    '--theme-background': self.background_color,
                    '--theme-text': self.text_color,
                    '--theme-font-family': self._get_font_family_css(),
                    '--theme-font-size': self.base_font_size,
                    '--theme-border-radius': self.border_radius,
                    '--theme-shadow': self._get_shadow_css(),
                }
            
            def _get_font_family_css(self):
                if self.font_family == 'system-ui':
                    return 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                else:
                    return f'"{self.font_family}", system-ui, sans-serif'
            
            def _get_shadow_css(self):
                shadows = {
                    'none': 'none',
                    'light': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                    'medium': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    'heavy': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                }
                return shadows.get(self.shadow_intensity, shadows['medium'])
        
        temp_theme = TempTheme(theme_data)
        generator = ThemeGenerator(temp_theme)
        
        return generator.generate_theme_css()
    
    @classmethod
    def get_accessibility_scores(cls) -> Dict[str, float]:
        """
        Get accessibility scores for all themes.
        
        Returns:
            Dictionary of theme names to accessibility scores
        """
        scores = {}
        
        for theme_name in cls.THEMES.keys():
            validation_result = cls.validate_theme_accessibility(theme_name)
            scores[theme_name] = validation_result.get('accessibility_score', 0.0)
        
        return scores
    
    @classmethod
    def get_theme_stats(cls) -> Dict[str, Any]:
        """
        Get statistics about the predefined themes.
        
        Returns:
            Dictionary with theme statistics
        """
        stats = {
            'total_themes': len(cls.THEMES),
            'dark_themes': 0,
            'light_themes': 0,
            'accessibility_compliant': 0,
            'font_families': set(),
            'color_schemes': []
        }
        
        for theme_name, theme_data in cls.THEMES.items():
            # Count dark vs light themes
            if theme_data['colors']['background'] in ['#1F2937', '#111827']:
                stats['dark_themes'] += 1
            else:
                stats['light_themes'] += 1
            
            # Collect font families
            stats['font_families'].add(theme_data['typography']['font_family'])
            
            # Check accessibility compliance
            validation_result = cls.validate_theme_accessibility(theme_name)
            if validation_result.get('accessibility_score', 0) >= 90:
                stats['accessibility_compliant'] += 1
            
            # Collect color schemes
            stats['color_schemes'].append({
                'theme': theme_name,
                'primary': theme_data['colors']['primary'],
                'accent': theme_data['colors']['accent']
            })
        
        stats['font_families'] = list(stats['font_families'])
        
        return stats