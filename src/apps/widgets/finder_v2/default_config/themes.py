from collections import OrderedDict

from src.apps.widgets.common.less.themes import WidgetThemes


class FinderV2Themes(WidgetThemes):
    """
    Themes configuration for Finder-v2 widget.

    Unlike legacy widgets that use LESS, finder-v2 uses Vue 3 + TailwindCSS v4
    for styling. This configuration provides minimal theme support for compatibility
    with the widget system while using pre-compiled CSS from the Vue build.
    """

    default_theme = 'light'

    DESKTOP_THEME = {
        'source': {
            'advanced': '',  # No LESS source for Vue 3 + TailwindCSS
            'original': '',  # CSS is handled by Vue build process
        },
        'compiled': {
            'original': '',  # Pre-compiled by Vite build
            'advanced': '',  # Pre-compiled by Vite build
        },
        'screen': {
            'desktop': True,
            'mobile': True,
        },
        'templates': {
            'page': 'widgets/finder_v2/iframe/themes/desktop/page.html',
            'content': 'widgets/finder_v2/iframe/themes/desktop/content.html',
        },
        'custom': False,
        'base_theme': None,
    }

    THEMES = OrderedDict([
        ('light', WidgetThemes.extend_theme(DESKTOP_THEME, {
            'label': 'Light v2',
            'theme_name': 'light',
            'version': '2.0',
            'primary_colors_count': 4,
            'vars': {},  # TailwindCSS handles theming via CSS variables
        })),
        ('dark', WidgetThemes.extend_theme(DESKTOP_THEME, {
            'label': 'Dark v2',
            'theme_name': 'dark',
            'version': '2.0',
            'primary_colors_count': 4,
            'vars': {},  # TailwindCSS handles theming via CSS variables
        })),
    ])
