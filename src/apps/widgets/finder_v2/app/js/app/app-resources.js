App.factory('Resources', [
    '$resource', 'widgetResources',
    function($resource, widgetResources) {
        function createResource(title, url) {
            var resource = $resource(url, {}, {
                get: { method: 'GET', isArray: true }
            });
            resource.title = title;
            return resource;
        }

        var resources = {};

        for (var resourceName in widgetResources) {
            var resource = widgetResources[resourceName];
            resources[resourceName] = createResource(resource[0], resource[1]);
        }

        return resources;
    }
]);

App.factory('ResourceChain', [
    '$timeout', 'Resources', 'WheelSizeMessages',
    function($timeout, Resources, WheelSizeMessages) {
        var Resource = function(name) {
            var resource = Resources[name];

            this.title = resource.title;
            this.name = name;
            this.resource = resource;
            this.reset();
        };
        Resource.prototype = {
            load: function(params) {
                var self = this;
                this.resource.get(params, function(data) {
                    self.items = data;
                    $timeout(function() {
                        self.disabled = false;
                        //var event = {
                        //    resource: self.name,
                        //    result: data
                        //};
                        //WheelSizeMessages.send('load', event);
                        //WheelSizeMessages.send('load:' + self.name, event);
                    }, 1);
                });
            },
            reset: function() {
                this.value = '';
                this.items = [];
                this.disabled = true;
            },
            getReadableValue: function() {
                if (!this.value) {
                    return '';
                }

                var slugs = this.items.map(function(resource) {
                    return resource.slug + '';
                });
                var index = slugs.indexOf(this.value);
                return this.items[index].name;
            },
            get: function() {
                return {
                    slug: this.value,
                    title: this.getReadableValue()
                };
            }
        };

        var ResourceChain = function(chain, final, prototypeExtension) {
            angular.extend(this, prototypeExtension);
            
            this.chain = chain.map(function(resourceName) {
                return new Resource(resourceName);
            });
            this.final = new Resource(final);
            this.nextResource = this.chain[0];
            this.resetResult();
        };
        ResourceChain.prototype = {
            loadNext: function() {
                if (this.nextResource === this.final) {
                    this.loading = true;
                }
                this.nextResource.load(this.params());
            },
            resetResult: function () {
                this.loading = false;
                this.result = {
                    items: [],
                    info: {}
                };
            },
            changed: function(index) {
                var resource = this.chain[index];
                var event = {
                    resource: resource.name,
                    value: resource.get()
                };
                WheelSizeMessages.send('change', event);
                WheelSizeMessages.send('change:' + resource.name, event);

                this.resetResult();
                for (var i = index + 1; i < this.chain.length; ++i) {
                    this.chain[i].reset();
                }

                if (resource.value) {
                    if (index + 1 < this.chain.length) {
                        this.nextResource = this.chain[index + 1];
                    } else {
                        this.nextResource = this.final;
                    }

                    this.loadNext();
                }
            },
            params: function() {
                var parameters = {};
                this.chain.forEach(function(resource) {
                    parameters[resource.name] = resource.value;
                });
                return parameters;
            },
            updateResult: function(items) {
                this.result.items = this.prepareItems(items);
                this.result.info = this.prepareInfo();
                this.loading = false;
            }
        };

        return ResourceChain;
    }
]);