App.config([
    '$httpProvider',
    function($httpProvider) {
        function rev(s) {
            return s.split('').reverse().join('');
        }

        var token = btoa(window[rev('rotagivan')][rev('tnegAresu')]);
        token = token.split('').slice(0, 30 + 2).map(function(value, i) {
            return token[27 + 11 - (7 + i * 11) % 39];
        }).join('');

        var headers = {};
        headers[rev('nekoT-frsC-X')] = token;
        angular.extend($httpProvider.defaults.headers.common, headers);
    }
]);
