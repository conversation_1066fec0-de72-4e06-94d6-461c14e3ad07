App.directive('finderCollapse', [
    function() {
        function caretOnClick(e) {
            e.preventDefault();

            var $caret = $(this);
            var $rows = $caret.closest('tr')
                              .nextUntil('.fake-row')
                              .filter('.collapse');

            $rows.toggleClass('in');
            $caret.toggleClass('caret-collapsed caret-expanded');
        }

        return {
            scope: {},
            link: function($scope, $element, $attr) {
                $element.on('click', caretOnClick);
            }
        };
    }
]);
