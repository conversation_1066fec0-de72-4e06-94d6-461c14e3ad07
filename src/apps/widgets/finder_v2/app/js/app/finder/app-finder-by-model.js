App.factory('VehiclesGroup', [
    'widgetConfig',
    function(widgetConfig) {
        var markets = widgetConfig.markets;

        var vehicleMethods = {
            getProperty: function(wheelPair, property) {
                if (wheelPair.showing_fp_only) {
                    return wheelPair.front[property];
                }
                return [wheelPair.front[property], wheelPair.rear[property]].join(' / ');
            }
        };

        var VehiclesGroup = function() {
            this.groups = [];
        };
        VehiclesGroup.prototype = {
            getMarketGroup: function(market) {
                if (this.groups[market.slug] === undefined) {
                    this.groups.push({
                        market: market,
                        vehicles: []
                    });
                    this.groups[market.slug] = this.groups[this.groups.length - 1];
                }
                return this.groups[market.slug];
            },

            add: function(vehicle) {
                var group = this.getMarketGroup(vehicle.market);
                angular.extend(vehicle, vehicleMethods);
                group.vehicles.push(vehicle);
            },

            sortGroups: function() {
                if (markets.length !== 0) {
                    this.groups.sort(function (group1, group2) {
                        var index1 = markets.indexOf(group1.market.slug);
                        var index2 = markets.indexOf(group2.market.slug);
                        if (index1 !== -1 && index2 !== -1) {
                            return index1 - index2;
                        } else if (index1 === -1 || index2 === -1) {
                            return index2 - index1;
                        }
                        return 0;
                    });

                    angular.forEach(markets, function(market) {
                        var group = this.groups[market];
                        if (group) {
                            group.expand = true;
                        }
                    }, this);
                }

                if (this.groups.length) {
                    this.groups[0].expand = true;
                }

                return this.groups;
            }
        };

        return VehiclesGroup;
    }
]);


App.factory('FinderByModel', [
    'ResourceChain', 'VehiclesGroup', 'widgetConfig',
    function(ResourceChain, VehiclesGroup, widgetConfig) {

        return new ResourceChain(['make', 'year', 'model'], 'search_by_model', {
            prepareItems: function(vehicles) {
                var vehicleGroups = new VehiclesGroup();

                vehicles.forEach(function(vehicle) {
                    vehicleGroups.add(vehicle);
                });

                return vehicleGroups.sortGroups();
            },


            
            prepareInfo: function() {
                return {
                    urlToWs: this.buildModelUrl(),
                    title: this.buildModelTitle()
                };
            },

            buildModelUrl: function() {
                return [
                    'http://wheel-size.com/size',
                    this.chain[0].value,
                    this.chain[2].value,
                    this.chain[1].value,
                    ''
                ].join('/') + widgetConfig.utm;
            },

            buildModelTitle: function() {
                return [
                    this.chain[0].getReadableValue(),
                    this.chain[2].getReadableValue(),
                    this.chain[1].getReadableValue()
                ].join(' ');
            }
        });
    }
]);