App.config([
    '$routeProvider', '$locationProvider',
    function($routeProvider, $locationProvider) {
        $locationProvider.html5Mode({
            enabled: true,
            requireBase: false
        });
    }
]);

App.factory('wsCache', [
    '$cacheFactory',
    function($cacheFactory) {
        var cache = $cacheFactory('widget');
        return cache;
    }
]);

App.factory('widgetConfig', [
    'widgetConfigObject',
    function(conf) {
        var config = angular.extend({}, conf);
        config.locale = {};
        return config;
    }
]);
