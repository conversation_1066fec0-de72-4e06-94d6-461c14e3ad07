App.controller('FinderCtrl', [
    '$scope', '$element', '$timeout', 'widgetConfig',
    'FinderByModel', 'FinderByTire', 'FinderByRim',
    function($scope, $element, $timeout, widgetConfig,
             FinderByModel, FinderByTire, FinderByRim) {
        var finders = {
            byModel: FinderByModel,
            byTire: FinderByTire,
            byRim: FinderByRim
        };

        $scope.finder = finders[$element.attr('name')];

        $scope.$watch('finder.final.items', function (items) {
            $timeout(function () {
                if (items.length === 0) {
                    return;
                }

                $scope.finder.updateResult(items);
            }, 1);
        });

        $scope.buildUrl = function(make, model, year, vehicle) {
            var url = ['http://wheel-size.com/size'];
            url = url.concat([].slice.call(arguments, 0, 3));
            url = url.join('/');
            if (vehicle) {
                url = [url, '/#trim-', slugify(vehicle.trim), '-', vehicle.market.slug].join('');
            }
            return url + widgetConfig.utm;
        };

        function slugify(value) {
            return value.replace(/(\s+)|_/g, '-')
                        .replace(/[^\w-]/g, '')
                        .toLowerCase();
        }
    }
]);
