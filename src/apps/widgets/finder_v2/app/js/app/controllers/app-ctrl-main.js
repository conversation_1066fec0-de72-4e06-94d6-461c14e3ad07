App.controller('WidgetMainCtrl', [
    '$scope', '$window', 'WheelSizeMessages',
    function($scope, $window, WheelSizeMessages) {
        WheelSizeMessages.beforeSend(function(e) {
            e.data.tab = $scope.activeTab;
        });
        WheelSizeMessages.afterSend(function(e) {
            var eventParams = e.type.split(':');
            if (eventParams.length < 2) {
                return;
            }
            $window.ga('send', 'event', eventParams[0], eventParams[1], e.data.tab);
        });

        $scope.onSelectTab = function(resourceName) {
            if ($scope.activeTab == resourceName) {
                return;
            }

            var event = {
                newTab: resourceName
            };
            WheelSizeMessages.send('activate', event);
            WheelSizeMessages.send('activate:' + resourceName, event);
            $scope.activeTab = resourceName;
        };
    }
]);