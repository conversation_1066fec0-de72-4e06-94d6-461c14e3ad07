App.factory('UserLinks', [
    '$interpolate', '$compile', 'WheelSizeMessages',
    function($interpolate, $compile, wsMessages) {
        var UserLink = function(place, options) {
            var match = place.match(/^(after|before|replace)(\w+)/);
            this.place = {
                type: match[1],
                column: match[2].toLowerCase(),
                original: place
            };

            this.template = this.templates[options.template];
            this.clickPreventDefault = options.clickPreventDefault;

            this.scope = {};
            angular.forEach(this.scopeVars, function(value, key) {
                var option = options[key] || value;
                if (typeof option === 'string') {
                    this.scope[key] = $interpolate(option, false, null, true);
                } else {
                    this.scope[key] = function() {
                        return option;
                    };
                }
            }, this);
        };

        UserLink.prototype = {
            scopeVars: {
                text: '',
                title: null,
                href: null,
                icon: null
            },
            templates: {
                userText: '<span ng-attr-title="{{ title }}">{{ text }}<span ng-if="text && icon"> </span><span ng-if="icon" class="glyphicon glyphicon-{{ icon }}"></span></span>',
                userLink: '<a href="{{ href }}" target="_blank" ng-click="click($event)" ng-attr-title="{{ title }}" class="user-link">{{ text }}<span ng-if="text && icon"> </span><span ng-if="icon" class="glyphicon glyphicon-{{ icon }}"></span></a>',
                textFooter: '<div class="user-footer">{{ text }}</div>',
                linkFooter: '<a class="user-footer" href="{{ href }}" ng-attr-title="{{ title }}">{{ text }}</a>'
            },
            clickHandler: function($event, context) {
                var target = angular.element($event.target);

                var event = {
                    column: this.place.column,
                    type: this.place.type,
                    link: {
                        href: target.attr('href'),
                        text: target.text()
                    },
                    context: context
                };
                wsMessages.send('click', event);
                wsMessages.send('click:' + this.place.column, event);
                wsMessages.send('click:' + this.place.original, event);
                if (this.clickPreventDefault) {
                    $event.preventDefault();
                }
            },
            updateScope: function($scope, $elem, $attrs) {
                var context = {};

                if ($scope.vehicle && $scope.wheel) {
                    $scope.side = $attrs.side || 'front';
                    angular.extend(context, {
                        trim: $scope.vehicle.trim,
                        is_oem: $scope.wheel.is_stock,
                        bolt_pattern: $scope.vehicle.bolt_pattern,
                        thd: $scope.vehicle.lock_text,
                        cb: $scope.vehicle.centre_bore,
                        lock_type: $scope.vehicle.lock_type,
                        power: $scope.vehicle.power,
                        fuel: $scope.vehicle.fuel,
                        engine_type: $scope.vehicle.engine_type,
                        generation: $scope.generation
                    });

                    // extend context with wheel parameters
                    angular.extend(context, $scope.wheel[$scope.side]);
                }
                // extend context with resources
                angular.forEach($scope.$parent.finder.chain, function(resource) {
                    context[resource.name] = resource.get();
                });

                angular.forEach(context, function(value, key) {
                    context[key] = value || undefined;
                });

                for (var prop in this.scope) {
                    $scope[prop] = this.scope[prop](context);
                }
                if ($scope.href) {
                    $scope.href = $scope.href.replace(/\s/g, '');
                }

                if (this.place.type === 'replace') {
                    $scope.text = $interpolate($scope.text || $elem.html())($scope);
                }

                var self = this;
                $scope.click = function($event) {
                    return self.clickHandler($event, context);
                };

                return $scope;
            },
            getClass: function() {
                return [
                    'user-link-',
                    this.place.type,
                    ' user-link-',
                    this.place.column
                ].join('');
            }
        };


        var UserLinks = {};

        wsMessages.on('register:userLinks', function(e) {
            for (var prop in e.data) {
                var options = e.data[prop];
                if (options.href) {
                    options.template = 'userLink';
                } else {
                    options.template = 'userText';
                }
                UserLinks[prop] = new UserLink(prop, options);
            }
        });
        wsMessages.on('register:userFooter', function(e) {
            for (var prop in e.data) {
                var options = e.data[prop];
                if (options.type === 'link') {
                    options.template = 'linkFooter';
                } else {
                    options.template = 'textFooter';
                }
                UserLinks[prop] = new UserLink(prop, options);
            }
        });

        return UserLinks;
    }
]);

App.directive('wsUserLink', [
    'UserLinks', '$compile', '$interpolate',
    function(UserLinks, $compile, $interpolate) {
        return {
            restrict: 'E',
            scope: {},
            link: function($scope, $elem, $attrs) {
                var link = UserLinks[$attrs.place];
                var originalHtml = $elem.html();

                try {
                    $scope.vehicle = $scope.$parent.vehicle;
                    $scope.wheel = $scope.$parent.wheel;
                    link.updateScope($scope, $elem, $attrs);

                    if ($interpolate(link.template, false, null, true)($scope)) {
                        var elem = $compile(link.template)($scope);
                        elem.addClass(link.getClass());
                        $elem.replaceWith(elem);
                    }
                } catch (e) {
                    var elem = $interpolate(originalHtml)($scope);
                    $elem.replaceWith(elem);
                }
            }
        };
    }
]);
