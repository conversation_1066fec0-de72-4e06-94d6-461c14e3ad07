App.controller('DomainsListCtrl', [
    '$scope',
    function($scope) {
        $scope.domains = $scope.config.domains;
        if (typeof $scope.domains === 'string') {
            $scope.domains = angular.fromJson($scope.domains);
        }

        function checkDomainsNotEmpty() {
            if ($scope.domains.length === 0) {
                $scope.addDomain();
            }
        }

        $scope.addDomain = function() {
            $scope.domains.push('');
        };

        checkDomainsNotEmpty();

        $scope.removeDomain = function(index) {
            $scope.domains.splice(index, 1);
            checkDomainsNotEmpty();
        };

        $scope.$watch('domains', updateDomains, true);

        function updateDomains() {
            var domains = [];
            angular.forEach($scope.domains, function(domain) {
                if (domain) {
                    domains.push(domain);
                }
            });
            $scope.config.domains = angular.toJson(domains);
        }
    }
]);
