App.controller('MakesFilterCtrl', [
    '$scope',
    function($scope) {
        $scope.init = function(brands, countries, regions) {
            $scope.makesFilter = {
                brands: angular.fromJson(brands),
                countries: angular.fromJson(countries),
                regions: angular.fromJson(regions)
            };
        };
    }
]);

App.controller('TagChoiceCtrl', [
    '$scope',
    function($scope) {
        $scope.init = function(tags, choices) {
            $scope.choices = choices;
            $scope.choicesHash = {};
            angular.forEach(choices, function(value, index) {
                $scope.choicesHash[value.slug] = value;
                value.index = index;
            });

            $scope.tags = {};
            if (tags) {
                for (var i = 0; i < tags.length; ++i) {
                    $scope.tags[tags[i]] = true;
                }
            }

            updateTagsString();
        };

        $scope.toggleTag = function(slug) {
            $scope.tags[slug] = !$scope.tags[slug];
            updateTagsString();
        };

        function updateTagsString() {
            var tags = [];
            for (var slug in $scope.tags) {
                if ($scope.tags[slug]) {
                    tags.push(slug);
                }
            }
            tags.sort(function(a, b) {
                return $scope.choicesHash[a].index - $scope.choicesHash[b].index;
            });
            $scope.tagsString = angular.toJson(tags);
        }
    }
]);
