App.factory('WidgetTheme', [
    function() {
        var WidgetTheme = function(themeObj, isDeclared) {
            angular.extend(this, themeObj);
            this.isDeclared = isDeclared;
        };
        WidgetTheme.prototype = {
            getOriginal: function() {
                return this.source.original;
            },

            getPrimaryColors: function() {
                return this.vars_order.slice(0, this.primary_colors_count);
            },

            getRestColors: function() {
                return this.vars_order.slice(this.primary_colors_count);
            }
        };

        return WidgetTheme;
    }
]);

App.factory('ThemeManager', [
    '$timeout', 'LessManager', 'WidgetTheme',
    function($timeout, LessManager, WidgetTheme) {
        var AdvancedEditor = function(editor, onChange) {
            this.editor = editor;

            var self = this;
            editor.getSession().on('change', function() {
                if (!self.manual) {
                    onChange();
                }
            });
        };
        AdvancedEditor.prototype = {
            getValue: function() {
                this.manual = false;
                return this.editor.getSession().getValue();
            },

            setValue: function(value) {
                this.manual = true;
                return this.editor.getSession().setValue(value);
            }
        };

        var ThemeManager = function(themes, widget) {
            this.widget = widget;

            this.all = themes.all.map(function(theme) {
                theme = new WidgetTheme(theme, true);
                if (theme.theme_name == themes.activeThemeName) {
                    this.setTheme(theme);
                }
                return theme;
            }, this);

            this.baseThemeName = themes.baseThemeName;
            this.newThemeStub = themes.newThemeStub;
            this.varPrefix = themes.varPrefix;
        };
        ThemeManager.prototype = {
            lessManager: new LessManager(),

            setTheme: function(theme, advancedChanged) {
                this.setActive(theme);

                if (this.editor && !advancedChanged) {
                    this.editor.setValue(theme.source.advanced);
                }

                var self = this;
                this.widget.ready(function() {
                    self.updateVars();
                    self.sendAdvanced();
                });
            },

            changeVar: function(varName) {
                if (this.active.isDeclared) {
                    this.createTheme();
                }

                this.updateVars(varName);
            },

            changeAdvanced: function() {
                if (this.active.isDeclared) {
                    this.createTheme(true);
                }

                this.active.source.advanced = this.editor.getValue();
                this.sendAdvanced();
            },

            setActive: function(theme) {
                this.active = theme;
                this.activeThemeName = theme.theme_name;

                if (theme.isDeclared) {
                    this.vars = angular.extend({}, theme.vars);
                } else {
                    this.vars = theme.vars;
                }
            },

            updateVars: function(varName) {
                var self = this;
                var promise = this.refreshColorpicker();
                promise.then(function() {
                    self.errors = {};
                    self.sendOriginal();
                }, function(reason) {
                    self.errors[varName] = true;
                });
            },

            sendOriginal: function() {
                this.widget.send('less:update', {
                    vars: this.vars,
                    name: 'widget-original',
                    source: this.active.getOriginal()
                });
            },

            sendAdvanced: function() {
                if (!this.editor) {
                    return;
                }

                var widget = this.widget;
                widget.send('less:update', {
                    vars: this.vars,
                    name: 'widget-advanced',
                    source: this.editor.getValue()
                });
                $timeout(function() {
                    widget.resize();
                }, 1);
            },

            createTheme: function(advancedChanged) {
                if (this.newTheme) {
                    this.all.pop();
                }

                var theme = angular.extend({}, this.active, this.newThemeStub);
                theme.vars = angular.extend({}, this.vars);
                theme.source = angular.extend({}, this.active.source);
                theme = this.newTheme = new WidgetTheme(theme, false);

                this.all.push(theme);

                this.baseThemeName = this.activeThemeName;
                this.setTheme(theme, advancedChanged);
            },

            refreshColorpicker: function() {
                var colorpickerLess = [];
                for (var varName in this.vars) {
                    colorpickerLess.push([
                        '.colorpicker-', varName, '{', '' +
                        'background-color: @', varName,
                        '}'
                    ].join(''));
                }

                return this.lessManager.refresh(
                    this.vars,
                    'colorpicker',
                    colorpickerLess.join('')
                );
            },

            resizeWidget: function() {
                this.widget.resize();
            },

            errorInColors: function() {
                for (var varName in this.lessErrors) {
                    if (this.lessErrors[varName]) {
                        return true;
                    }
                }
                return false;
            },

            setEditor: function(editor) {
                var self = this;
                this.editor = new AdvancedEditor(editor, function() {
                    self.changeAdvanced();
                });
            }
        };

        return ThemeManager;
    }
]);

App.controller('WidgetMainCtrl', [
    '$scope', '$window', 'ThemeManager',
    function($scope, $window, ThemeManager) {
        $scope.init = function(data) {
            var widget = WheelSizeWidgets.create('.iframe-preview',
                                                 data.config);
            $scope.config = data.config;
            $scope.themes = new ThemeManager(data.themes, widget);
        };

        angular.element($window).load(function () {
            var editor = ace.editors['theme-advanced'];
            $scope.themes.setEditor(editor);
        });

        $scope.$watch('config.width', function(width) {
            var cleanedWidth = '100%';
            if (!isNaN(Number(width)) && width > 0) {
                cleanedWidth = width >= $scope.config.minWidth ?
                               width :
                               $scope.config.minWidth;
            }
            $scope.cleanedWidth = cleanedWidth;
        });
    }
]);
