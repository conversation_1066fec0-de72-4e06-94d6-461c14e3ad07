/*!
* angular-post-message v1.1.1
* Copyright 2013 <PERSON> <<EMAIL>>
* Licensed under The MIT License
*/
(function(){var a;a=angular.module("ngPostMessage",["ng"]),a.directive("html",["$window","$postMessage",function(a,b){return{restrict:"E",controller:["$scope",function(b){return b.$on("$messageOutgoing",function(c,d){var e;return e=b.sender||a.parent,e.postMessage(d,"*")})}],link:function(c){return c.sendMessageToService=function(a){var d,e;if(a=a.originalEvent||a,a&&a.data){e=null,c.sender=a.source;try{e=angular.fromJson(a.data)}catch(f){d=f,e=a.data}return c.$root.$broadcast("$messageIncoming",e),b.messages(e)}},angular.element(a).bind("message",c.sendMessageToService)}}}]),a.factory("$postMessage",["$rootScope",function(a){var b,c;return b=[],c={messages:function(c){return c&&(b.push(c),a.$digest()),b},lastMessage:function(){return b[b.length-1]},post:function(b){return a.$broadcast("$messageOutgoing",b)}}}])}).call(this);