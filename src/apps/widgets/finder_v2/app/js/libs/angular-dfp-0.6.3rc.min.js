var googletag=googletag||{};googletag.cmd=googletag.cmd||[];
angular.module("ngDfp",[]).constant("ngDfpUrl","//www.googletagservices.com/tag/js/gpt.js").provider("DoubleClick",["ngDfpUrl",function(d){var c={},f={},h=null,l=!0;this._createTag=function(a){if(l){var b=document.createElement("script"),c="https:"===document.location.protocol,e=document.getElementsByTagName("script")[0];b.async=!0;b.type="text/javascript";b.src=(c?"https:":"http:")+d;e.parentNode.insertBefore(b,e);b.onreadystatechange=function(){"complete"==this.readyState&&a()};b.onload=a}};this._initialize=
function(){angular.forEach(c,function(a,b){f[b]=googletag.defineSlot.apply(null,a).addService(googletag.pubads())});googletag.pubads().enableSingleRequest();googletag.enableServices();googletag.pubads().addEventListener("slotRenderEnded",this._slotRenderEnded)};this._slotRenderEnded=function(a){a=c[a.slot.getSlotId().getDomId()].renderCallback;"function"===typeof a&&a()};this._refreshInterval=function(){return h};this.setRefreshInterval=function(a){h=a;return this};this.defineSlot=function(){var a=
arguments;a.getSize=function(){return this[1]};a.setRenderCallback=function(a){this.renderCallback=a};c[arguments[2]]=a;return this};this.setEnabled=function(a){l=a};var g=this;this.$get=["$q","$window","$interval",function(a,b,d){var e=a.defer();g._createTag(function(){g._initialize();null!==g._refreshInterval()&&d(function(){b.googletag.pubads().refresh()},g._refreshInterval());e.resolve()});return{getAdSize:function(a){return e.promise.then(function(){if(angular.isUndefined(c[a]))throw"Slot "+
a+" has not been defined. Define it using DoubleClickProvider.defineSlot().";return c[a][1]})},getSlot:function(a){return e.promise.then(function(){if(angular.isUndefined(c[a]))throw"Slot "+a+" has not been defined. Define it using DoubleClickProvider.defineSlot().";return c[a]})},runAd:function(a){b.googletag.display(a)},refreshAds:function(){var a=[];angular.forEach(arguments,function(b){a.push(f[b])});b.googletag.pubads().refresh(a)}}}]}]).directive("ngDfpAdContainer",function(){return{restrict:"A",
controller:["$element",function(d){this.$$setVisible=function(c,f){c?"visibility"===f?d.css("visibility","visible"):d.show():"visibility"===f?d.css("visibility","hidden"):d.hide()}}]}}).directive("ngDfpAd",["$timeout","$parse","$interval","DoubleClick",function(d,c,f,h){return{restrict:"A",template:'<div id="{{adId}}"></div>',require:"?^ngDfpAdContainer",scope:{adId:"@ngDfpAd",interval:"@ngDfpAdRefreshInterval"},replace:!0,link:function(c,g,a,b){c.$watch("adId",function(k){g.html("");var e=null;h.getSlot(k).then(function(m){var n=
m.getSize();g.css("width",n[0]).css("height",n[1]);d(function(){h.runAd(k)});b&&m.setRenderCallback(function(){angular.isDefined(a.ngDfpAdHideWhenEmpty)&&(0===g.find("iframe:not([id*=hidden])").map(function(){return this.contentWindow.document}).find("body").children().length?b.$$setVisible(!1,a.ngDfpAdHideWhenEmpty):b.$$setVisible(!0,a.ngDfpAdHideWhenEmpty))});c.$watch("interval",function(a){angular.isUndefined(a)||(f.cancel(e),e=f(function(){h.refreshAds(k)},c.interval))})})})}}}]);
