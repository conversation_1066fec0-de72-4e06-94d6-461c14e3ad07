angular.module("angular-bootstrap-select.extra",[]).directive("toggle",function(){return{restrict:"A",link:function(a,b){if(b.hasClass("selectpicker")){var c=b.parent(),d=function(){c.toggleClass("open")},e=function(){c.removeClass("open")};b.on("click",d),b.next().on("click",e),a.$on("$destroy",function(){b.off("click",d),b.next().off("click",e)})}}}}),angular.module("angular-bootstrap-select",[]).directive("selectpicker",["$parse",function(a){return{restrict:"A",require:"?ngModel",priority:10,compile:function(b,c){return b.selectpicker(a(c.selectpicker)()),b.selectpicker("refresh"),function(a,b,c,d){d&&(a.$watch(c.ngModel,function(d){a.$evalAsync(function(){(!c.ngOptions||/track by/.test(c.ngOptions))&&b.val(d),b.selectpicker("refresh")})}),d.$render=function(){a.$evalAsync(function(){b.selectpicker("refresh")})})}}}}]);