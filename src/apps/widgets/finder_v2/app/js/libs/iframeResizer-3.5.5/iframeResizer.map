{"version": 3, "file": "iframeResizer.min.js", "sources": ["iframeResizer.js"], "names": ["window", "addEventListener", "obj", "evt", "func", "attachEvent", "removeEventListener", "el", "detachEvent", "setupRequestAnimationFrame", "x", "vendors", "length", "requestAnimationFrame", "log", "getMyID", "iframeId", "retStr", "top", "self", "parentIFrame", "getId", "formatLogHeader", "msgId", "isLogEnabled", "settings", "logEnabled", "msg", "output", "info", "warn", "type", "enabled", "console", "iFrameListener", "event", "resizeIFrame", "resize", "setSize", "messageData", "setPagePosition", "ensureInRange", "syncResize", "processMsg", "data", "substr", "msgIdLen", "split", "iframe", "id", "height", "width", "Dimension", "max", "Number", "min", "dimension", "toLowerCase", "size", "isMessageFromIFrame", "checkAllowed<PERSON><PERSON><PERSON>", "checkList", "i", "retCode", "<PERSON><PERSON><PERSON><PERSON>", "origin", "checkSingle", "remoteHost", "constructor", "Array", "Error", "isMessageForUs", "isMessageFromMetaParent", "true", "false", "undefined", "getMsgBody", "offset", "indexOf", "msgHeaderLen", "forwardMsgFromIFrame", "msgBody", "callback", "message", "JSON", "parse", "getPageInfo", "bodyPosition", "document", "body", "getBoundingClientRect", "iFramePosition", "stringify", "iframeHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clientHeight", "Math", "documentElement", "innerHeight", "clientWidth", "innerWidth", "offsetTop", "parseInt", "offsetLeft", "left", "scrollTop", "pageYOffset", "scrollLeft", "pageXOffset", "sendPageInfoToIframe", "debounced<PERSON><PERSON><PERSON>", "trigger", "debouce", "startPageInfoMonitor", "setListener", "sendPageInfo", "stop", "for<PERSON>ach", "start", "stopPageInfo", "stopPageInfoMonitor", "checkIFrameExists", "retBool", "getElementPosition", "target", "getPagePosition", "floor", "pagePosition", "y", "scrollRequestFromChild", "addOffset", "reposition", "newPosition", "scrollTo", "calcOffset", "scrollParent", "unsetPagePosition", "<PERSON><PERSON><PERSON><PERSON>", "location", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jumpPosition", "hash", "jumpT<PERSON><PERSON><PERSON><PERSON>", "moveToAnchor", "hashData", "decodeURIComponent", "getElementById", "getElementsByName", "funcName", "val", "chkCallback", "actionMsg", "firstRun", "closeIFrame", "resetIFrame", "hasSettings", "iFrameReadyMsgReceived", "createOutgoingMsg", "logId", "retVal", "TypeError", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "reset", "setDimension", "style", "chkZero", "hidden<PERSON><PERSON>ck<PERSON>nabled", "fixHiddenIFrames", "processDimension", "sizeHeight", "sizeWidth", "doNotSync", "callee<PERSON>g", "postMessageToIFrame", "target<PERSON>rigin", "contentWindow", "postMessage", "iFrameNotFound", "chkAndSend", "bodyMarginV1", "interval", "enablePublicMethods", "autoResize", "<PERSON><PERSON><PERSON><PERSON>", "heightCalculationMethod", "bodyBackground", "bodyPadding", "tolerance", "inPageLinks", "resizeFrom", "widthCalculationMethod", "setupIFrame", "options", "setLimits", "addStyle", "Infinity", "chkMinMax", "newId", "defaults", "count", "ensureHasId", "src", "setScrolling", "scrolling", "overflow", "setupBodyMargin<PERSON><PERSON>ues", "checkReset", "resetRequertMethod", "resetRequiredMethods", "setupIFrameObject", "Function", "prototype", "bind", "iFrameResizer", "close", "anchor", "sendMessage", "init", "iFrameLoaded", "checkOptions", "copyOptions", "option", "hasOwnProperty", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "processOptions", "slice", "join", "been<PERSON>ere", "fn", "time", "timer", "setTimeout", "checkIFrames", "checkIFrame", "settingId", "chkDimension", "isVisible", "offsetParent", "mutationObserved", "mutations", "createMutationObserver", "querySelector", "config", "attributes", "attributeOldValue", "characterData", "characterDataOldValue", "childList", "subtree", "observer", "MutationObserver", "observe", "WebKitMutationObserver", "resizeIFrames", "sendTriggerMsg", "tabVisible", "visibilityState", "eventName", "isIFrameResizeEnabled", "setupEventListeners", "factory", "element", "chkType", "tagName", "toUpperCase", "iFrames", "push", "call", "querySelectorAll", "createJQueryPublicMethod", "$", "iFrameResize", "index", "this", "filter", "each", "end", "msgH<PERSON>er", "scroll", "bodyScroll", "documentElementScroll", "maxHeight", "max<PERSON><PERSON><PERSON>", "minHeight", "min<PERSON><PERSON><PERSON>", "closedCallback", "initCallback", "messageCallback", "resizedCallback", "scrollCallback", "j<PERSON><PERSON><PERSON>", "define", "amd", "module", "exports"], "mappings": ";;;;;;;CAWC,SAAUA,GACV,YA+CA,SAASC,GAAiBC,EAAIC,EAAIC,GAE7B,oBAAsBJ,GACzBE,EAAID,iBAAiBE,EAAIC,GAAM,GACrB,eAAiBJ,IAC3BE,EAAIG,YAAY,KAAKF,EAAIC,GAI3B,QAASE,GAAoBC,EAAGJ,EAAIC,GAE/B,uBAAyBJ,GAC5BO,EAAGD,oBAAoBH,EAAIC,GAAM,GACvB,eAAiBJ,IAC3BO,EAAGC,YAAY,KAAKL,EAAIC,GAI1B,QAASK,KACR,GAECC,GADAC,GAAW,MAAO,SAAU,IAAK,KAIlC,KAAKD,EAAI,EAAGA,EAAIC,EAAQC,SAAWC,EAAuBH,GAAK,EAC9DG,EAAwBb,EAAOW,EAAQD,GAAK,wBAGxC,IACJI,EAAI,QAAQ,uCAId,QAASC,GAAQC,GAChB,GAAIC,GAAS,cAAcD,CAU3B,OARIhB,GAAOkB,MAAMlB,EAAOmB,OAEtBF,EADGjB,EAAOoB,cAAgBpB,EAAOoB,aAAaC,MACrCrB,EAAOoB,aAAaC,QAAQ,KAAKL,EAEjC,qBAAqBA,GAIzBC,EAGR,QAASK,GAAgBN,GACxB,MAAOO,GAAQ,IAAMR,EAAQC,GAAY,IAG1C,QAASQ,GAAaR,GACrB,MAAOS,GAAST,GAAYS,EAAST,GAAUF,IAAMY,EAGtD,QAASZ,GAAIE,EAASW,GACrBC,EAAO,MAAMZ,EAASW,EAAIH,EAAaR,IAGxC,QAASa,GAAKb,EAASW,GACtBC,EAAO,OAAOZ,EAASW,EAAIH,EAAaR,IAGzC,QAASc,GAAKd,EAASW,GACtBC,EAAO,OAAOZ,EAASW,GAAI,GAG5B,QAASC,GAAOG,EAAKf,EAASW,EAAIK,IAC7B,IAASA,GAAW,gBAAoBhC,GAAOiC,SAClDA,QAAQF,GAAMT,EAAgBN,GAAUW,GAI1C,QAASO,GAAeC,GACvB,QAASC,KACR,QAASC,KACRC,EAAQC,GACRC,EAAgBxB,GAGjByB,EAAc,UACdA,EAAc,SAEdC,EAAWL,EAAOE,EAAY,QAG/B,QAASI,KACR,GAAIC,GAAOjB,EAAIkB,OAAOC,GAAUC,MAAM,IAEtC,QACCC,OAAQvB,EAASmB,EAAK,IAAII,OAC1BC,GAAQL,EAAK,GACbM,OAAQN,EAAK,GACbO,MAAQP,EAAK,GACbb,KAAQa,EAAK,IAIf,QAASH,GAAcW,GACtB,GACCC,GAAOC,OAAO7B,EAAST,GAAU,MAAQoC,IACzCG,EAAOD,OAAO7B,EAAST,GAAU,MAAQoC,IACzCI,EAAYJ,EAAUK,cACtBC,EAAOJ,OAAOf,EAAYiB,GAE3B1C,GAAIE,EAAS,YAAcwC,EAAY,gBAAkBD,EAAM,IAAMF,GAE5DE,EAALG,IACHA,EAAKH,EACLzC,EAAIE,EAAS,OAASwC,EAAY,kBAG/BE,EAAKL,IACRK,EAAKL,EACLvC,EAAIE,EAAS,OAASwC,EAAY,kBAGnCjB,EAAYiB,GAAa,GAAKE,EAI/B,QAASC,KACR,QAASC,KACR,QAASC,KACR,GACCC,GAAI,EACJC,GAAU,CAIX,KAFAjD,EAAIE,EAAS,wDAA0DgD,GAEhEF,EAAIE,EAAYpD,OAAQkD,IAC9B,GAAIE,EAAYF,KAAOG,EAAQ,CAC9BF,GAAU,CACV,OAGF,MAAOA,GAGR,QAASG,KACR,GAAIC,GAAc1C,EAAST,GAAUmD,UAErC,OADArD,GAAIE,EAAS,gCAAgCmD,GACtCF,IAAWE,EAGnB,MAAOH,GAAYI,cAAgBC,MAAQR,IAAcK,IAG1D,GACCD,GAAc9B,EAAM8B,OACpBD,EAAcvC,EAAST,GAAUgD,WAElC,IAAIA,GAAgB,GAAGC,GAAW,SAAYL,IAC7C,KAAM,IAAIU,OACT,qCAAuCL,EACvC,QAAU1B,EAAYS,OAAOC,GAC7B,kBAAoBd,EAAMS,KAC1B,qHAIF,QAAO,EAGR,QAAS2B,KACR,MAAOhD,MAAY,GAAKI,GAAKkB,OAAO,EAAEC,IAAenB,EAAIkB,OAAOC,GAAUC,MAAM,KAAK,IAAMtB,GAG5F,QAAS+C,KAGR,GAAIT,GAAUxB,EAAYR,QAAS0C,OAAO,EAAEC,QAAQ,EAAEC,UAAY,EAMlE,OAJIZ,IACHjD,EAAIE,EAAS,+CAGP+C,EAGR,QAASa,GAAWC,GACnB,MAAOlD,GAAIkB,OAAOlB,EAAImD,QAAQ,KAAKC,EAAaF,GAGjD,QAASG,GAAqBC,GAC7BnE,EAAIE,EAAS,oCAAqCuB,EAAYS,OAAOC,GAAK,cAAgBgC,EAAU,KACpGC,EAAS,mBACRlC,OAAQT,EAAYS,OACpBmC,QAASC,KAAKC,MAAMJ,KAErBnE,EAAIE,EAAS,MAGd,QAASsE,KACR,GACCC,GAAiBC,SAASC,KAAKC,wBAC/BC,EAAiBpD,EAAYS,OAAO0C,uBAErC,OAAON,MAAKQ,WACXC,aAAcF,EAAezC,OAC7B4C,YAAcH,EAAexC,MAC7B4C,aAAcC,KAAK3C,IAAImC,SAASS,gBAAgBF,aAAc/F,EAAOkG,aAAe,GACpFC,YAAcH,KAAK3C,IAAImC,SAASS,gBAAgBE,YAAcnG,EAAOoG,YAAe,GACpFC,UAAcC,SAASX,EAAezE,IAAOqE,EAAarE,IAAM,IAChEqF,WAAcD,SAASX,EAAea,KAAOjB,EAAaiB,KAAM,IAChEC,UAAczG,EAAO0G,YACrBC,WAAc3G,EAAO4G,cAIvB,QAASC,GAAqB7D,EAAOhC,GACpC,QAAS8F,KACRC,EACC,iBACA,YAAczB,IACdtC,EACAhC,GAIFgG,EAAQF,EAAiB,IAI1B,QAASG,KACR,QAASC,GAAYnF,EAAK3B,GACzB,QAAS+G,KACJ1F,EAASwB,GACZ4D,EAAqBpF,EAASwB,GAAID,OAAOC,GAEzCmE,KAID,SAAS,UAAUC,QAAQ,SAASlH,GACpCW,EAAImC,EAAIlB,EAAQ5B,EAAM,8BACtBC,EAAKJ,EAAOG,EAAIgH,KAIlB,QAASC,KACRF,EAAY,UAAW5G,GAGxB,QAASgH,KACRJ,EAAY,OAAQjH,GAGrB,GAAIgD,GAAKjC,CAETsG,KAEA7F,EAASwB,GAAIsE,aAAeH,EAG7B,QAASI,KACJ/F,EAAST,IAAaS,EAAST,GAAUuG,eAC5C9F,EAAST,GAAUuG,qBACZ9F,GAAST,GAAUuG,cAI5B,QAASE,KACR,GAAIC,IAAU,CAMd,OAJI,QAASnF,EAAYS,SACxBlB,EAAKd,EAAS,WAAWuB,EAAYU,GAAG,eACxCyE,GAAU,GAEJA,EAGR,QAASC,GAAmBC,GAC3B,GAAIjC,GAAiBiC,EAAOlC,uBAI5B,OAFAmC,GAAgB7G,IAGfN,EAAGsF,KAAK8B,MAAOxE,OAAOqC,EAAea,MAAQlD,OAAOyE,EAAarH,IACjEsH,EAAGhC,KAAK8B,MAAOxE,OAAOqC,EAAezE,KAAQoC,OAAOyE,EAAaC,KAInE,QAASC,GAAuBC,GAE/B,QAASC,KACRJ,EAAeK,EACfC,IACAvH,EAAIE,EAAS,MAGd,QAASsH,KACR,OACC5H,EAAG4C,OAAOf,EAAYY,OAAS0B,EAAOnE,EACtCsH,EAAG1E,OAAOf,EAAYW,QAAU2B,EAAOmD,GAIzC,QAASO,KACJvI,EAAOoB,aACVpB,EAAOoB,aAAa,YAAY8G,EAAU,SAAS,KAAKE,EAAY1H,EAAE0H,EAAYJ,GAElFlG,EAAKd,EAAS,yEAIhB,GACC6D,GAASqD,EAAYP,EAAmBpF,EAAYS,SAAWtC,EAAE,EAAEsH,EAAE,GACrEI,EAAcE,GAEfxH,GAAIE,EAAS,8CAA8C6D,EAAOnE,EAAE,MAAMmE,EAAOmD,EAAE,KAEhFhI,EAAOkB,MAAMlB,EAAOmB,KACtBoH,IAEAJ,IAIF,QAASE,MACJ,IAAUnD,EAAS,iBAAiB6C,GACvCvF,EAAgBxB,GAEhBwH,IAIF,QAASC,GAAWC,GACnB,QAASC,KACR,GAAIC,GAAejB,EAAmBC,EAEtC9G,GAAIE,EAAS,4BAA4B6H,EAAK,WAAWD,EAAalI,EAAE,OAAOkI,EAAaZ,GAC5FD,GACCrH,EAAGkI,EAAalI,EAChBsH,EAAGY,EAAaZ,GAGjBK,IACAvH,EAAIE,EAAS,MAGd,QAAS8H,KACJ9I,EAAOoB,aACVpB,EAAOoB,aAAa2H,aAAaF,GAEjC/H,EAAIE,EAAS,iBAAiB6H,EAAK,gDAIrC,GACCA,GAAWH,EAAS3F,MAAM,KAAK,IAAM,GACrCiG,EAAWC,mBAAmBJ,GAC9BjB,EAAWpC,SAAS0D,eAAeF,IAAaxD,SAAS2D,kBAAkBH,GAAU,EAElFpB,GACHe,IACS3I,EAAOkB,MAAMlB,EAAOmB,KAC7B2H,IAEAhI,EAAIE,EAAS,iBAAiB6H,EAAK,cAIrC,QAAS3D,GAASkE,EAASC,GAC1B,MAAOC,GAAYtI,EAASoI,EAASC,GAGtC,QAASE,KAIR,OAFG9H,EAAST,GAAUwI,UAAUA,IAEzBjH,EAAYR,MACnB,IAAK,QACJ0H,EAAYlH,EAAYS,OACxB,MACD,KAAK,UACJgC,EAAqBJ,EAAW,GAChC,MACD,KAAK,WACJqD,GAAuB,EACvB,MACD,KAAK,iBACJA,GAAuB,EACvB,MACD,KAAK,WACJpB,EAAqBpF,EAAST,GAAUgC,OAAOhC,GAC/CiG,GACA,MACD,KAAK,eACJO,GACA,MACD,KAAK,aACJiB,EAAW7D,EAAW,GACtB,MACD,KAAK,QACJ8E,EAAYnH,EACZ,MACD,KAAK,OACJH,IACA8C,EAAS,eAAe3C,EAAYS,QACpCkC,EAAS,kBAAkB3C,EAC3B,MACD,SACCH,IACA8C,EAAS,kBAAkB3C,IAI7B,QAASoH,GAAY3I,GACpB,GAAI0G,IAAU,CAOd,OALKjG,GAAST,KACb0G,GAAU,EACV5F,EAAKS,EAAYR,KAAO,oBAAsBf,EAAW,kBAAoBW,IAGvE+F,EAGR,QAASkC,KACR,IAAK,GAAI5I,KAAYS,GACpBsF,EAAQ,wBAAwB8C,EAAkB7I,GAAUwE,SAAS0D,eAAelI,GAAUA,GAIhG,QAASwI,KACR/H,EAAST,GAAUwI,UAAW,EAG/B,GACC7H,GAAMQ,EAAMS,KACZL,KACAvB,EAAW,IAET,+BAAgCW,EAClCiI,IACUrF,KACVhC,EAAcI,IACd3B,EAAc8I,EAAQvH,EAAYU,IAE7BuB,KAA6BmF,EAAY3I,KAC7CF,EAAIE,EAAS,aAAaW,GAErB8F,KAAuB9D,KAC3B4F,MAIF1H,EAAKb,EAAS,YAAYW,GAM5B,QAAS2H,GAAYtI,EAASoI,EAASC,GACtC,GACCjJ,GAAO,KACP2J,EAAS,IAEV,IAAGtI,EAAST,GAAU,CAGrB,GAFAZ,EAAOqB,EAAST,GAAUoI,GAEtB,kBAAsBhJ,GAGzB,KAAM,IAAI4J,WAAUZ,EAAS,cAAcpI,EAAS,sBAFpD+I,GAAS3J,EAAKiJ,GAMhB,MAAOU,GAGR,QAASN,GAAYzG,GACpB,GAAIhC,GAAWgC,EAAOC,EAEtBnC,GAAIE,EAAS,oBAAoBA,GACjCgC,EAAOiH,WAAWC,YAAYlH,GAC9BsG,EAAYtI,EAAS,iBAAiBA,GACtCF,EAAIE,EAAS,YACNS,GAAST,GAGjB,QAAS6G,GAAgB7G,GACrB,OAAS+G,IACXA,GACCrH,EAA2BiE,SAAvB3E,EAAO4G,YAA6B5G,EAAO4G,YAAcpB,SAASS,gBAAgBU,WACtFqB,EAA2BrD,SAAvB3E,EAAO0G,YAA6B1G,EAAO0G,YAAclB,SAASS,gBAAgBQ,WAEvF3F,EAAIE,EAAS,sBAAsB+G,EAAarH,EAAE,IAAIqH,EAAaC,IAIrE,QAASxF,GAAgBxB,GACrB,OAAS+G,IACX/H,EAAOqI,SAASN,EAAarH,EAAEqH,EAAaC,GAC5ClH,EAAIE,EAAS,sBAAsB+G,EAAarH,EAAE,IAAIqH,EAAaC,GACnEQ,KAIF,QAASA,KACRT,EAAe,KAGhB,QAAS2B,GAAYnH,GACpB,QAAS4H,KACR7H,EAAQC,GACRwE,EAAQ,QAAQ,QAAQxE,EAAYS,OAAOT,EAAYU,IAGxDnC,EAAIyB,EAAYU,GAAG,4BAA4B,SAASV,EAAYR,KAAK,YAAY,WACrF8F,EAAgBtF,EAAYU,IAC5BP,EAAWyH,EAAM5H,EAAY,SAG9B,QAASD,GAAQC,GAChB,QAAS6H,GAAa5G,GACrBjB,EAAYS,OAAOqH,MAAM7G,GAAajB,EAAYiB,GAAa,KAC/D1C,EACCyB,EAAYU,GACZ,WAAajC,EACb,KAAOwC,EACP,WAAajB,EAAYiB,GAAa,MAIxC,QAAS8G,GAAQ9G,GAMX+G,GAAsB,MAAQhI,EAAYiB,KAC9C+G,GAAqB,EACrBzJ,EAAIE,EAAS,wDACbwJ,KAIF,QAASC,GAAiBjH,GACzB4G,EAAa5G,GACb8G,EAAQ9G,GAGT,GAAIxC,GAAWuB,EAAYS,OAAOC,EAE/BxB,GAAST,KACPS,EAAST,GAAU0J,YAAcD,EAAiB,UAClDhJ,EAAST,GAAU2J,WAAcF,EAAiB,UAIxD,QAAS/H,GAAWtC,EAAKmC,EAAYqI,GAEjCA,IAAYrI,EAAYR,MAAQlB,GAClCC,EAAIyB,EAAYU,GAAG,8BACnBpC,EAAsBT,IAEtBA,IAIF,QAAS2G,GAAQ8D,EAAUlJ,EAAIqB,EAAOC,GACrC,QAAS6H,KACR,GAAIlD,GAASnG,EAASwB,GAAI8H,YAC1BjK,GAAImC,EAAG,IAAM4H,EAAY,2BAA2B5H,EAAG,MAAMtB,EAAI,mBAAmBiG,GACpF5E,EAAOgI,cAAcC,YAAa1J,EAAQI,EAAKiG,GAGhD,QAASsD,KACRrJ,EAAKoB,EAAG,IAAM4H,EAAY,YAAY5H,EAAG,eACtCxB,EAASwB,UACJxB,GAASwB,GAIlB,QAASkI,KACLnI,GAAU,iBAAmBA,IAAW,OAASA,EAAOgI,cAC1DF,IAEAI,IAIFjI,EAAKA,GAAMD,EAAOC,GAEfxB,EAASwB,IACXkI,IAKF,QAAStB,GAAkB7I,GAC1B,MAAOA,GACN,IAAMS,EAAST,GAAUoK,aACzB,IAAM3J,EAAST,GAAU2J,UACzB,IAAMlJ,EAAST,GAAUF,IACzB,IAAMW,EAAST,GAAUqK,SACzB,IAAM5J,EAAST,GAAUsK,oBACzB,IAAM7J,EAAST,GAAUuK,WACzB,IAAM9J,EAAST,GAAUwK,WACzB,IAAM/J,EAAST,GAAUyK,wBACzB,IAAMhK,EAAST,GAAU0K,eACzB,IAAMjK,EAAST,GAAU2K,YACzB,IAAMlK,EAAST,GAAU4K,UACzB,IAAMnK,EAAST,GAAU6K,YACzB,IAAMpK,EAAST,GAAU8K,WACzB,IAAMrK,EAAST,GAAU+K,uBAG3B,QAASC,GAAYhJ,EAAOiJ,GAC3B,QAASC,KACR,QAASC,GAAS9B,GACZ+B,EAAAA,IAAa3K,EAAST,GAAUqJ,IAAY,IAAM5I,EAAST,GAAUqJ,KACzErH,EAAOqH,MAAMA,GAAS5I,EAAST,GAAUqJ,GAAS,KAClDvJ,EAAIE,EAAS,OAAOqJ,EAAM,MAAM5I,EAAST,GAAUqJ,GAAO,OAI5D,QAASgC,GAAU7I,GAClB,GAAI/B,EAAST,GAAU,MAAMwC,GAAW/B,EAAST,GAAU,MAAMwC,GAChE,KAAM,IAAIc,OAAM,gBAAgBd,EAAU,+BAA+BA,GAI3E6I,EAAU,UACVA,EAAU,SAEVF,EAAS,aACTA,EAAS,aACTA,EAAS,YACTA,EAAS,YAGV,QAASG,KACR,GAAIrJ,GAAOgJ,GAAWA,EAAQhJ,IAAOsJ,EAAStJ,GAAKuJ,GAInD,OAHK,QAAOhH,SAAS0D,eAAejG,KACnCA,GAAUuJ,KAEJvJ,EAGR,QAASwJ,GAAYzL,GAUpB,MATA8I,GAAM9I,EACF,KAAKA,IACRgC,EAAOC,GAAKjC,EAAYsL,IACxB5K,GAAcuK,OAAenL,IAC7BgJ,EAAM9I,EACNF,EAAIE,EAAS,4BAA6BA,EAAU,KAAOgC,EAAO0J,IAAM,MAIlE1L,EAGR,QAAS2L,KACR7L,EAAIE,EAAS,qBAAuBS,EAAST,GAAU4L,UAAY,UAAY,YAAc,QAAU5L,GACvGgC,EAAOqH,MAAMwC,UAAW,IAAUpL,EAAST,GAAU4L,UAAY,SAAW,OAC5E5J,EAAO4J,WAAiB,IAAUnL,EAAST,GAAU4L,UAAY,KAAO,MAMzE,QAASE,MACH,gBAAkBrL,GAAST,GAAoB,YAAO,MAAMS,EAAST,GAAUwK,cACnF/J,EAAST,GAAUoK,aAAe3J,EAAST,GAAUwK,WACrD/J,EAAST,GAAUwK,WAAe,GAAK/J,EAAST,GAAUwK,WAAa,MAIzE,QAASuB,KAIR,GACCvD,GAAqB/H,EAAST,GAAUwI,SACxCwD,EAAqBvL,EAAST,GAAUyK,0BAA2BwB,IAE/DzD,GAAYwD,GAChBtD,GAAa1G,OAAOA,EAAQE,OAAO,EAAGC,MAAM,EAAGpB,KAAK,SAItD,QAASmL,KACLC,SAASC,UAAUC,OACrB5L,EAAST,GAAUgC,OAAOsK,eAEzBC,MAAe9D,EAAY4D,KAAK,KAAK5L,EAAST,GAAUgC,QAExDX,OAAe0E,EAAQsG,KAAK,KAAK,gBAAiB,SAAU5L,EAAST,GAAUgC,QAE/E+F,aAAe,SAASyE,GACvBzG,EAAQ,iBAAiB,gBAAgByG,EAAQ/L,EAAST,GAAUgC,OAAOhC,IAG5EyM,YAAe,SAAStI,GACvBA,EAAUC,KAAKQ,UAAUT,GACzB4B,EAAQ,eAAe,WAAW5B,EAAS1D,EAAST,GAAUgC,OAAOhC,MASzE,QAAS0M,GAAK/L,GACb,QAASgM,KACR5G,EAAQ,gBAAgBpF,EAAIqB,GAC5B+J,IAGD9M,EAAiB+C,EAAO,OAAO2K,GAC/B5G,EAAQ,OAAOpF,EAAIqB,GAGpB,QAAS4K,GAAa3B,GACrB,GAAI,gBAAoBA,GACvB,KAAM,IAAIjC,WAAU,4BAItB,QAAS6D,GAAY5B,GACpB,IAAK,GAAI6B,KAAUvB,GACdA,EAASwB,eAAeD,KAC3BrM,EAAST,GAAU8M,GAAU7B,EAAQ8B,eAAeD,GAAU7B,EAAQ6B,GAAUvB,EAASuB,IAK5F,QAASE,GAAiB7J,GACzB,MAAQ,KAAOA,GAAc,YAAcA,EAAc,IAAMA,EAGhE,QAAS8J,GAAehC,GACvBA,EAAUA,MACVxK,EAAST,IACRwI,UAAW,EACXxG,OAAUA,EACVmB,WAAanB,EAAO0J,IAAI3J,MAAM,KAAKmL,MAAM,EAAE,GAAGC,KAAK,MAGpDP,EAAa3B,GACb4B,EAAY5B,GAEZxK,EAAST,GAAU+J,cAAe,IAAStJ,EAAST,GAAUgD,YAAcgK,EAAgBvM,EAAST,GAAUmD,YAAc,IAG9H,QAASiK,KACR,MAAQpN,KAAYS,IAAY,iBAAmBuB,GAGpD,GAAIhC,GAAWyL,EAAYzJ,EAAOC,GAE7BmL,KAQJtM,EAAKd,EAAS,mCAPdiN,EAAehC,GACfU,IACAT,IACAY,IACAY,EAAK7D,EAAkB7I,IACvBkM,KAMF,QAASlG,GAAQqH,EAAGC,GACf,OAASC,IACZA,EAAQC,WAAW,WAClBD,EAAQ,KACRF,KACEC,IAKL,QAAS9D,KACR,QAASiE,KACR,QAASC,GAAYC,GACpB,QAASC,GAAapL,GACrB,MAAO,QAAU/B,EAASkN,GAAW3L,OAAOqH,MAAM7G,GAGnD,QAASqL,GAAUtO,GAClB,MAAQ,QAASA,EAAGuO,aAGjBD,EAAUpN,EAASkN,GAAW3L,UAAY4L,EAAa,WAAaA,EAAa,WACpF7H,EAAQ,oBAAqB,SAAUtF,EAASkN,GAAW3L,OAAO2L,GAIpE,IAAK,GAAIA,KAAalN,GACrBiN,EAAYC,GAId,QAASI,GAAiBC,GACzBlO,EAAI,SAAS,sBAAwBkO,EAAU,GAAGpH,OAAS,IAAMoH,EAAU,GAAGjN,MAC9EiF,EAAQyH,EAAa,IAGtB,QAASQ,KACR,GACCrH,GAASpC,SAAS0J,cAAc,QAEhCC,GACCC,YAAwB,EACxBC,mBAAwB,EACxBC,eAAwB,EACxBC,uBAAwB,EACxBC,WAAwB,EACxBC,SAAwB,GAGzBC,EAAW,GAAIC,GAAiBZ,EAEjCW,GAASE,QAAQhI,EAAQuH,GAG1B,GAAIQ,GAAmB3P,EAAO2P,kBAAoB3P,EAAO6P,sBAErDF,IAAkBV,IAIvB,QAASa,GAAc3N,GACtB,QAASE,KACR0N,EAAe,UAAU5N,EAAM,UAGhCrB,EAAI,SAAS,kBAAkBqB,GAC/B6E,EAAQ3E,EAAO,IAIhB,QAAS2N,KACR,QAAS3N,KACR0N,EAAe,cAAc,UAG3B,WAAavK,SAASyK,kBACxBnP,EAAI,WAAW,mCACfkG,EAAQ3E,EAAO,KAIjB,QAAS0N,GAAeG,EAAU/N,GACjC,QAASgO,GAAsBnP,GAC9B,MAAO,WAAaS,EAAST,GAAU8K,YACrCrK,EAAST,GAAUuK,aAClB9J,EAAST,GAAUwI,SAGvB,IAAK,GAAIxI,KAAYS,GACjB0O,EAAsBnP,IACxB+F,EAAQmJ,EAAU/N,EAAMqD,SAAS0D,eAAelI,GAAUA,GAK7D,QAASoP,KACRnQ,EAAiBD,EAAO,UAAUkC,GAElCjC,EAAiBD,EAAO,SAAU,WAAW8P,EAAc,YAE3D7P,EAAiBuF,SAAS,mBAAmBwK,GAC7C/P,EAAiBuF,SAAS,2BAA2BwK,GACrD/P,EAAiBD,EAAO,UAAU,WAAW8P,EAAc,WAC3D7P,EAAiBD,EAAO,QAAQ,WAAW8P,EAAc,WAI1D,QAASO,KACR,QAAS3C,GAAKzB,EAAQqE,GACrB,QAASC,KACR,IAAID,EAAQE,QACX,KAAM,IAAIxG,WAAU,oCACd,IAAI,WAAasG,EAAQE,QAAQC,cACvC,KAAM,IAAIzG,WAAU,iCAAiCsG,EAAQE,QAAQ,KAIpEF,IACFC,IACAvE,EAAYsE,EAASrE,GACrByE,EAAQC,KAAKL,IAIf,GAAII,EAKJ,OAHAjQ,KACA2P,IAEO,SAAuBnE,EAAQrE,GAGrC,OAFA8I,WAEc,IACd,IAAK,YACL,IAAK,SACJrM,MAAM+I,UAAU/F,QAAQuJ,KACvBpL,SAASqL,iBAAkBjJ,GAAU,UACrC8F,EAAKL,KAAK1I,OAAWsH,GAEtB,MACD,KAAK,SACJyB,EAAKzB,EAAQrE,EACb,MACD,SACC,KAAM,IAAIoC,WAAU,+BAA+B,GAAS,KAG7D,MAAO0G,IAIT,QAASI,GAAyBC,GAC5BA,EAAE1C,GAGN0C,EAAE1C,GAAG2C,aAAe,SAAwB/E,GAC3C,QAASyB,GAAKuD,EAAOX,GACpBtE,EAAYsE,EAASrE,GAGtB,MAAOiF,MAAKC,OAAO,UAAUC,KAAK1D,GAAM2D,OAPzCxP,EAAK,GAAG,qDAr8BV,GACC2K,GAAwB,EACxB9K,GAAwB,EACxB6I,GAAwB,EACxB+G,EAAwB,UACxBvM,EAAwBuM,EAAU1Q,OAClCW,EAAwB,gBACxBuB,EAAwBvB,EAAMX,OAC9BmH,EAAwB,KACxBlH,EAAwBb,EAAOa,sBAC/BoM,GAAyB5J,IAAI,EAAEkO,OAAO,EAAEC,WAAW,EAAEC,sBAAsB,GAC3EhQ,KACA8M,EAAwB,KACxBzE,EAAwB,YAExByC,GACChB,YAA4B,EAC5BG,eAA4B,KAC5BF,WAA4B,KAC5BJ,aAA4B,EAC5BO,YAA4B,KAC5B3H,aAA4B,EAC5B6H,aAA4B,EAC5BP,qBAA4B,EAC5BG,wBAA4B,aAC5BxI,GAA4B,gBAC5BoI,SAA4B,GAC5BvK,KAA4B,EAC5B4Q,UAA4BtF,EAAAA,EAC5BuF,SAA4BvF,EAAAA,EAC5BwF,UAA4B,EAC5BC,SAA4B,EAC5B/F,WAA4B,SAC5Bc,WAA4B,EAC5BlC,YAA4B,EAC5BC,WAA4B,EAC5BiB,UAA4B,EAC5BG,uBAA4B,SAC5B+F,eAA4B,aAC5BC,aAA4B,aAC5BC,gBAA4B,WAAWlQ,EAAK,yCAC5CmQ,gBAA4B,aAC5BC,eAA4B,WAAW,OAAO,GAu6B5ClS,GAAOmS,QAAUrB,EAAyBqB,QAExB,kBAAXC,SAAyBA,OAAOC,IAC1CD,UAAU/B,GACkB,gBAAXiC,SAAiD,gBAAnBA,QAAOC,QACtDD,OAAOC,QAAUlC,IAEjBrQ,EAAOgR,aAAehR,EAAOgR,cAAgBX,KAG5CrQ", "sourcesContent": ["/*\n * File: iframeResizer.js\n * Desc: Force iframes to size to content.\n * Requires: iframeResizer.contentWindow.js to be loaded into the target frame.\n * Doc: https://github.com/davidjbradshaw/iframe-resizer\n * Author: <PERSON> - <EMAIL>\n * Contributor: <PERSON>re <PERSON> - <EMAIL>\n * Contributor: <PERSON>ne - <EMAIL>\n */\n\n\n;(function(window) {\n\t'use strict';\n\n\tvar\n\t\tcount                 = 0,\n\t\tlogEnabled            = false,\n\t\thiddenCheckEnabled    = false,\n\t\tmsgHeader             = 'message',\n\t\tmsgHeaderLen          = msgHeader.length,\n\t\tmsgId                 = '[iFrameSizer]', //Must match iframe msg ID\n\t\tmsgIdLen              = msgId.length,\n\t\tpagePosition          = null,\n\t\trequestAnimationFrame = window.requestAnimationFrame,\n\t\tresetRequiredMethods  = {max:1,scroll:1,bodyScroll:1,documentElementScroll:1},\n\t\tsettings              = {},\n\t\ttimer                 = null,\n\t\tlogId                 = 'Host Page',\n\n\t\tdefaults              = {\n\t\t\tautoResize                : true,\n\t\t\tbodyBackground            : null,\n\t\t\tbodyMargin                : null,\n\t\t\tbodyMarginV1              : 8,\n\t\t\tbodyPadding               : null,\n\t\t\tcheckOrigin               : true,\n\t\t\tinPageLinks               : false,\n\t\t\tenablePublicMethods       : true,\n\t\t\theightCalculationMethod   : 'bodyOffset',\n\t\t\tid                        : 'iFrameResizer',\n\t\t\tinterval                  : 32,\n\t\t\tlog                       : false,\n\t\t\tmaxHeight                 : Infinity,\n\t\t\tmaxWidth                  : Infinity,\n\t\t\tminHeight                 : 0,\n\t\t\tminWidth                  : 0,\n\t\t\tresizeFrom                : 'parent',\n\t\t\tscrolling                 : false,\n\t\t\tsizeHeight                : true,\n\t\t\tsizeWidth                 : false,\n\t\t\ttolerance                 : 0,\n\t\t\twidthCalculationMethod    : 'scroll',\n\t\t\tclosedCallback            : function(){},\n\t\t\tinitCallback              : function(){},\n\t\t\tmessageCallback           : function(){warn('MessageCallback function not defined');},\n\t\t\tresizedCallback           : function(){},\n\t\t\tscrollCallback            : function(){return true;}\n\t\t};\n\n\tfunction addEventListener(obj,evt,func){\n\t\t/* istanbul ignore else */ // Not testable in PhantonJS\n\t\tif ('addEventListener' in window){\n\t\t\tobj.addEventListener(evt,func, false);\n\t\t} else if ('attachEvent' in window){//IE\n\t\t\tobj.attachEvent('on'+evt,func);\n\t\t}\n\t}\n\n\tfunction removeEventListener(el,evt,func){\n\t\t/* istanbul ignore else */ // Not testable in phantonJS\n\t\tif ('removeEventListener' in window){\n\t\t\tel.removeEventListener(evt,func, false);\n\t\t} else if ('detachEvent' in window){ //IE\n\t\t\tel.detachEvent('on'+evt,func);\n\t\t}\n\t}\n\n\tfunction setupRequestAnimationFrame(){\n\t\tvar\n\t\t\tvendors = ['moz', 'webkit', 'o', 'ms'],\n\t\t\tx;\n\n\t\t// Remove vendor prefixing if prefixed and break early if not\n\t\tfor (x = 0; x < vendors.length && !requestAnimationFrame; x += 1) {\n\t\t\trequestAnimationFrame = window[vendors[x] + 'RequestAnimationFrame'];\n\t\t}\n\n\t\tif (!(requestAnimationFrame)){\n\t\t\tlog('setup','RequestAnimationFrame not supported');\n\t\t}\n\t}\n\n\tfunction getMyID(iframeId){\n\t\tvar retStr = 'Host page: '+iframeId;\n\n\t\tif (window.top!==window.self){\n\t\t\tif (window.parentIFrame && window.parentIFrame.getId){\n\t\t\t\tretStr = window.parentIFrame.getId()+': '+iframeId;\n\t\t\t} else {\n\t\t\t\tretStr = 'Nested host page: '+iframeId;\n\t\t\t}\n\t\t}\n\n\t\treturn retStr;\n\t}\n\n\tfunction formatLogHeader(iframeId){\n\t\treturn msgId + '[' + getMyID(iframeId) + ']';\n\t}\n\n\tfunction isLogEnabled(iframeId){\n\t\treturn settings[iframeId] ? settings[iframeId].log : logEnabled;\n\t}\n\n\tfunction log(iframeId,msg){\n\t\toutput('log',iframeId,msg,isLogEnabled(iframeId));\n\t}\n\n\tfunction info(iframeId,msg){\n\t\toutput('info',iframeId,msg,isLogEnabled(iframeId));\n\t}\n\n\tfunction warn(iframeId,msg){\n\t\toutput('warn',iframeId,msg,true);\n\t}\n\n\tfunction output(type,iframeId,msg,enabled){\n\t\tif (true === enabled && 'object' === typeof window.console){\n\t\t\tconsole[type](formatLogHeader(iframeId),msg);\n\t\t}\n\t}\n\n\tfunction iFrameListener(event){\n\t\tfunction resizeIFrame(){\n\t\t\tfunction resize(){\n\t\t\t\tsetSize(messageData);\n\t\t\t\tsetPagePosition(iframeId);\n\t\t\t}\n\n\t\t\tensureInRange('Height');\n\t\t\tensureInRange('Width');\n\n\t\t\tsyncResize(resize,messageData,'init');\n\t\t}\n\n\t\tfunction processMsg(){\n\t\t\tvar data = msg.substr(msgIdLen).split(':');\n\n\t\t\treturn {\n\t\t\t\tiframe: settings[data[0]].iframe,\n\t\t\t\tid:     data[0],\n\t\t\t\theight: data[1],\n\t\t\t\twidth:  data[2],\n\t\t\t\ttype:   data[3]\n\t\t\t};\n\t\t}\n\n\t\tfunction ensureInRange(Dimension){\n\t\t\tvar\n\t\t\t\tmax  = Number(settings[iframeId]['max' + Dimension]),\n\t\t\t\tmin  = Number(settings[iframeId]['min' + Dimension]),\n\t\t\t\tdimension = Dimension.toLowerCase(),\n\t\t\t\tsize = Number(messageData[dimension]);\n\n\t\t\tlog(iframeId,'Checking ' + dimension + ' is in range ' + min + '-' + max);\n\n\t\t\tif (size<min) {\n\t\t\t\tsize=min;\n\t\t\t\tlog(iframeId,'Set ' + dimension + ' to min value');\n\t\t\t}\n\n\t\t\tif (size>max) {\n\t\t\t\tsize=max;\n\t\t\t\tlog(iframeId,'Set ' + dimension + ' to max value');\n\t\t\t}\n\n\t\t\tmessageData[dimension] = '' + size;\n\t\t}\n\n\n\t\tfunction isMessageFromIFrame(){\n\t\t\tfunction checkAllowedOrigin(){\n\t\t\t\tfunction checkList(){\n\t\t\t\t\tvar\n\t\t\t\t\t\ti = 0,\n\t\t\t\t\t\tretCode = false;\n\n\t\t\t\t\tlog(iframeId,'Checking connection is from allowed list of origins: ' + checkOrigin);\n\n\t\t\t\t\tfor (; i < checkOrigin.length; i++) {\n\t\t\t\t\t\tif (checkOrigin[i] === origin) {\n\t\t\t\t\t\t\tretCode = true;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn retCode;\n\t\t\t\t}\n\n\t\t\t\tfunction checkSingle(){\n\t\t\t\t\tvar remoteHost  = settings[iframeId].remoteHost;\n\t\t\t\t\tlog(iframeId,'Checking connection is from: '+remoteHost);\n\t\t\t\t\treturn origin === remoteHost;\n\t\t\t\t}\n\n\t\t\t\treturn checkOrigin.constructor === Array ? checkList() : checkSingle();\n\t\t\t}\n\n\t\t\tvar\n\t\t\t\torigin      = event.origin,\n\t\t\t\tcheckOrigin = settings[iframeId].checkOrigin;\n\n\t\t\tif (checkOrigin && (''+origin !== 'null') && !checkAllowedOrigin()) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Unexpected message received from: ' + origin +\n\t\t\t\t\t' for ' + messageData.iframe.id +\n\t\t\t\t\t'. Message was: ' + event.data +\n\t\t\t\t\t'. This error can be disabled by setting the checkOrigin: false option or by providing of array of trusted domains.'\n\t\t\t\t);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t}\n\n\t\tfunction isMessageForUs(){\n\t\t\treturn msgId === (('' + msg).substr(0,msgIdLen)) && (msg.substr(msgIdLen).split(':')[0] in settings); //''+Protects against non-string msg\n\t\t}\n\n\t\tfunction isMessageFromMetaParent(){\n\t\t\t//Test if this message is from a parent above us. This is an ugly test, however, updating\n\t\t\t//the message format would break backwards compatibity.\n\t\t\tvar retCode = messageData.type in {'true':1,'false':1,'undefined':1};\n\n\t\t\tif (retCode){\n\t\t\t\tlog(iframeId,'Ignoring init message from meta parent page');\n\t\t\t}\n\n\t\t\treturn retCode;\n\t\t}\n\n\t\tfunction getMsgBody(offset){\n\t\t\treturn msg.substr(msg.indexOf(':')+msgHeaderLen+offset);\n\t\t}\n\n\t\tfunction forwardMsgFromIFrame(msgBody){\n\t\t\tlog(iframeId,'MessageCallback passed: {iframe: '+ messageData.iframe.id + ', message: ' + msgBody + '}');\n\t\t\tcallback('messageCallback',{\n\t\t\t\tiframe: messageData.iframe,\n\t\t\t\tmessage: JSON.parse(msgBody)\n\t\t\t});\n\t\t\tlog(iframeId,'--');\n\t\t}\n\n\t\tfunction getPageInfo(){\n\t\t\tvar\n\t\t\t\tbodyPosition   = document.body.getBoundingClientRect(),\n\t\t\t\tiFramePosition = messageData.iframe.getBoundingClientRect();\n\n\t\t\treturn JSON.stringify({\n\t\t\t\tiframeHeight: iFramePosition.height,\n\t\t\t\tiframeWidth:  iFramePosition.width,\n\t\t\t\tclientHeight: Math.max(document.documentElement.clientHeight, window.innerHeight || 0),\n\t\t\t\tclientWidth:  Math.max(document.documentElement.clientWidth,  window.innerWidth  || 0),\n\t\t\t\toffsetTop:    parseInt(iFramePosition.top  - bodyPosition.top,  10),\n\t\t\t\toffsetLeft:   parseInt(iFramePosition.left - bodyPosition.left, 10),\n\t\t\t\tscrollTop:    window.pageYOffset,\n\t\t\t\tscrollLeft:   window.pageXOffset\n\t\t\t});\n\t\t}\n\n\t\tfunction sendPageInfoToIframe(iframe,iframeId){\n\t\t\tfunction debouncedTrigger(){\n\t\t\t\ttrigger(\n\t\t\t\t\t'Send Page Info',\n\t\t\t\t\t'pageInfo:' + getPageInfo(), \n\t\t\t\t\tiframe, \n\t\t\t\t\tiframeId\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tdebouce(debouncedTrigger,32);\n\t\t}\n\n\n\t\tfunction startPageInfoMonitor(){\n\t\t\tfunction setListener(type,func){\n\t\t\t\tfunction sendPageInfo(){\n\t\t\t\t\tif (settings[id]){\n\t\t\t\t\t\tsendPageInfoToIframe(settings[id].iframe,id);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstop();\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t['scroll','resize'].forEach(function(evt){\n\t\t\t\t\tlog(id, type +  evt + ' listener for sendPageInfo');\n\t\t\t\t\tfunc(window,evt,sendPageInfo);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tfunction stop(){\n\t\t\t\tsetListener('Remove ', removeEventListener);\n\t\t\t}\n\n\t\t\tfunction start(){\n\t\t\t\tsetListener('Add ', addEventListener);\n\t\t\t}\n\t\t\t\n\t\t\tvar id = iframeId; //Create locally scoped copy of iFrame ID\n\n\t\t\tstart();\n\n\t\t\tsettings[id].stopPageInfo = stop;\n\t\t}\n\n\t\tfunction stopPageInfoMonitor(){\n\t\t\tif (settings[iframeId] && settings[iframeId].stopPageInfo){\n\t\t\t\tsettings[iframeId].stopPageInfo();\n\t\t\t\tdelete settings[iframeId].stopPageInfo;\n\t\t\t}\n\t\t}\n\n\t\tfunction checkIFrameExists(){\n\t\t\tvar retBool = true;\n\n\t\t\tif (null === messageData.iframe) {\n\t\t\t\twarn(iframeId,'IFrame ('+messageData.id+') not found');\n\t\t\t\tretBool = false;\n\t\t\t}\n\t\t\treturn retBool;\n\t\t}\n\n\t\tfunction getElementPosition(target){\n\t\t\tvar iFramePosition = target.getBoundingClientRect();\n\n\t\t\tgetPagePosition(iframeId);\n\n\t\t\treturn {\n\t\t\t\tx: Math.floor( Number(iFramePosition.left) + Number(pagePosition.x) ),\n\t\t\t\ty: Math.floor( Number(iFramePosition.top)  + Number(pagePosition.y) )\n\t\t\t};\n\t\t}\n\n\t\tfunction scrollRequestFromChild(addOffset){\n\t\t\t/* istanbul ignore next */  //Not testable in Karma\n\t\t\tfunction reposition(){\n\t\t\t\tpagePosition = newPosition;\n\t\t\t\tscrollTo();\n\t\t\t\tlog(iframeId,'--');\n\t\t\t}\n\n\t\t\tfunction calcOffset(){\n\t\t\t\treturn {\n\t\t\t\t\tx: Number(messageData.width) + offset.x,\n\t\t\t\t\ty: Number(messageData.height) + offset.y\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tfunction scrollParent(){\n\t\t\t\tif (window.parentIFrame){\n\t\t\t\t\twindow.parentIFrame['scrollTo'+(addOffset?'Offset':'')](newPosition.x,newPosition.y);\n\t\t\t\t} else {\n\t\t\t\t\twarn(iframeId,'Unable to scroll to requested position, window.parentIFrame not found');\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvar\n\t\t\t\toffset = addOffset ? getElementPosition(messageData.iframe) : {x:0,y:0},\n\t\t\t\tnewPosition = calcOffset();\n\n\t\t\tlog(iframeId,'Reposition requested from iFrame (offset x:'+offset.x+' y:'+offset.y+')');\n\n\t\t\tif(window.top!==window.self){\n\t\t\t\tscrollParent();\n\t\t\t} else {\n\t\t\t\treposition();\n\t\t\t}\n\t\t}\n\n\t\tfunction scrollTo(){\n\t\t\tif (false !== callback('scrollCallback',pagePosition)){\n\t\t\t\tsetPagePosition(iframeId);\n\t\t\t} else {\n\t\t\t\tunsetPagePosition();\n\t\t\t}\n\t\t}\n\n\t\tfunction findTarget(location){\n\t\t\tfunction jumpToTarget(){\n\t\t\t\tvar jumpPosition = getElementPosition(target);\n\n\t\t\t\tlog(iframeId,'Moving to in page link (#'+hash+') at x: '+jumpPosition.x+' y: '+jumpPosition.y);\n\t\t\t\tpagePosition = {\n\t\t\t\t\tx: jumpPosition.x,\n\t\t\t\t\ty: jumpPosition.y\n\t\t\t\t};\n\n\t\t\t\tscrollTo();\n\t\t\t\tlog(iframeId,'--');\n\t\t\t}\n\n\t\t\tfunction jumpToParent(){\n\t\t\t\tif (window.parentIFrame){\n\t\t\t\t\twindow.parentIFrame.moveToAnchor(hash);\n\t\t\t\t} else {\n\t\t\t\t\tlog(iframeId,'In page link #'+hash+' not found and window.parentIFrame not found');\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvar\n\t\t\t\thash     = location.split('#')[1] || '',\n\t\t\t\thashData = decodeURIComponent(hash),\n\t\t\t\ttarget   = document.getElementById(hashData) || document.getElementsByName(hashData)[0];\n\n\t\t\tif (target){\n\t\t\t\tjumpToTarget();\n\t\t\t} else if(window.top!==window.self){\n\t\t\t\tjumpToParent();\n\t\t\t} else {\n\t\t\t\tlog(iframeId,'In page link #'+hash+' not found');\n\t\t\t}\n\t\t}\n\n\t\tfunction callback(funcName,val){\n\t\t\treturn chkCallback(iframeId,funcName,val);\n\t\t}\n\n\t\tfunction actionMsg(){\n\n\t\t\tif(settings[iframeId].firstRun) firstRun();\n\n\t\t\tswitch(messageData.type){\n\t\t\tcase 'close':\n\t\t\t\tcloseIFrame(messageData.iframe);\n\t\t\t\tbreak;\n\t\t\tcase 'message':\n\t\t\t\tforwardMsgFromIFrame(getMsgBody(6));\n\t\t\t\tbreak;\n\t\t\tcase 'scrollTo':\n\t\t\t\tscrollRequestFromChild(false);\n\t\t\t\tbreak;\n\t\t\tcase 'scrollToOffset':\n\t\t\t\tscrollRequestFromChild(true);\n\t\t\t\tbreak;\n\t\t\tcase 'pageInfo':\n\t\t\t\tsendPageInfoToIframe(settings[iframeId].iframe,iframeId);\n\t\t\t\tstartPageInfoMonitor();\n\t\t\t\tbreak;\n\t\t\tcase 'pageInfoStop':\n\t\t\t\tstopPageInfoMonitor();\n\t\t\t\tbreak;\n\t\t\tcase 'inPageLink':\n\t\t\t\tfindTarget(getMsgBody(9));\n\t\t\t\tbreak;\n\t\t\tcase 'reset':\n\t\t\t\tresetIFrame(messageData);\n\t\t\t\tbreak;\n\t\t\tcase 'init':\n\t\t\t\tresizeIFrame();\n\t\t\t\tcallback('initCallback',messageData.iframe);\n\t\t\t\tcallback('resizedCallback',messageData);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tresizeIFrame();\n\t\t\t\tcallback('resizedCallback',messageData);\n\t\t\t}\n\t\t}\n\n\t\tfunction hasSettings(iframeId){\n\t\t\tvar retBool = true;\n\n\t\t\tif (!settings[iframeId]){\n\t\t\t\tretBool = false;\n\t\t\t\twarn(messageData.type + ' No settings for ' + iframeId + '. Message was: ' + msg);\n\t\t\t}\n\n\t\t\treturn retBool;\n\t\t}\n\n\t\tfunction iFrameReadyMsgReceived(){\n\t\t\tfor (var iframeId in settings){\n\t\t\t\ttrigger('iFrame requested init',createOutgoingMsg(iframeId),document.getElementById(iframeId),iframeId);\n\t\t\t}\n\t\t}\n\n\t\tfunction firstRun() {\n\t\t\tsettings[iframeId].firstRun = false;\n\t\t}\n\n\t\tvar\n\t\t\tmsg = event.data,\n\t\t\tmessageData = {},\n\t\t\tiframeId = null;\n\n\t\tif('[iFrameResizerChild]Ready' === msg){\n\t\t\tiFrameReadyMsgReceived();\n\t\t} else if (isMessageForUs()){\n\t\t\tmessageData = processMsg();\n\t\t\tiframeId    = logId = messageData.id;\n\n\t\t\tif (!isMessageFromMetaParent() && hasSettings(iframeId)){\n\t\t\t\tlog(iframeId,'Received: '+msg);\n\n\t\t\t\tif ( checkIFrameExists() && isMessageFromIFrame() ){\n\t\t\t\t\tactionMsg();\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tinfo(iframeId,'Ignored: '+msg);\n\t\t}\n\n\t}\n\n\n\tfunction chkCallback(iframeId,funcName,val){\n\t\tvar\n\t\t\tfunc = null,\n\t\t\tretVal = null;\n\n\t\tif(settings[iframeId]){\n\t\t\tfunc = settings[iframeId][funcName];\n\n\t\t\tif( 'function' === typeof func){\n\t\t\t\tretVal = func(val);\n\t\t\t} else {\n\t\t\t\tthrow new TypeError(funcName+' on iFrame['+iframeId+'] is not a function');\n\t\t\t}\n\t\t}\n\n\t\treturn retVal;\n\t}\n\n\tfunction closeIFrame(iframe){\n\t\tvar iframeId = iframe.id;\n\n\t\tlog(iframeId,'Removing iFrame: '+iframeId);\n\t\tiframe.parentNode.removeChild(iframe);\n\t\tchkCallback(iframeId,'closedCallback',iframeId);\n\t\tlog(iframeId,'--');\n\t\tdelete settings[iframeId];\n\t}\n\n\tfunction getPagePosition(iframeId){\n\t\tif(null === pagePosition){\n\t\t\tpagePosition = {\n\t\t\t\tx: (window.pageXOffset !== undefined) ? window.pageXOffset : document.documentElement.scrollLeft,\n\t\t\t\ty: (window.pageYOffset !== undefined) ? window.pageYOffset : document.documentElement.scrollTop\n\t\t\t};\n\t\t\tlog(iframeId,'Get page position: '+pagePosition.x+','+pagePosition.y);\n\t\t}\n\t}\n\n\tfunction setPagePosition(iframeId){\n\t\tif(null !== pagePosition){\n\t\t\twindow.scrollTo(pagePosition.x,pagePosition.y);\n\t\t\tlog(iframeId,'Set page position: '+pagePosition.x+','+pagePosition.y);\n\t\t\tunsetPagePosition();\n\t\t}\n\t}\n\n\tfunction unsetPagePosition(){\n\t\tpagePosition = null;\n\t}\n\n\tfunction resetIFrame(messageData){\n\t\tfunction reset(){\n\t\t\tsetSize(messageData);\n\t\t\ttrigger('reset','reset',messageData.iframe,messageData.id);\n\t\t}\n\n\t\tlog(messageData.id,'Size reset requested by '+('init'===messageData.type?'host page':'iFrame'));\n\t\tgetPagePosition(messageData.id);\n\t\tsyncResize(reset,messageData,'reset');\n\t}\n\n\tfunction setSize(messageData){\n\t\tfunction setDimension(dimension){\n\t\t\tmessageData.iframe.style[dimension] = messageData[dimension] + 'px';\n\t\t\tlog(\n\t\t\t\tmessageData.id,\n\t\t\t\t'IFrame (' + iframeId +\n\t\t\t\t') ' + dimension +\n\t\t\t\t' set to ' + messageData[dimension] + 'px'\n\t\t\t);\n\t\t}\n\n\t\tfunction chkZero(dimension){\n\t\t\t//FireFox sets dimension of hidden iFrames to zero.\n\t\t\t//So if we detect that set up an event to check for\n\t\t\t//when iFrame becomes visible.\n\n\t\t\t/* istanbul ignore next */  //Not testable in PhantomJS\n\t\t\tif (!hiddenCheckEnabled && '0' === messageData[dimension]){\n\t\t\t\thiddenCheckEnabled = true;\n\t\t\t\tlog(iframeId,'Hidden iFrame detected, creating visibility listener');\n\t\t\t\tfixHiddenIFrames();\n\t\t\t}\n\t\t}\n\n\t\tfunction processDimension(dimension){\n\t\t\tsetDimension(dimension);\n\t\t\tchkZero(dimension);\n\t\t}\n\n\t\tvar iframeId = messageData.iframe.id;\n\n\t\tif(settings[iframeId]){\n\t\t\tif( settings[iframeId].sizeHeight) { processDimension('height'); }\n\t\t\tif( settings[iframeId].sizeWidth ) { processDimension('width'); }\n\t\t}\n\t}\n\n\tfunction syncResize(func,messageData,doNotSync){\n\t\t/* istanbul ignore if */  //Not testable in PhantomJS\n\t\tif(doNotSync!==messageData.type && requestAnimationFrame){\n\t\t\tlog(messageData.id,'Requesting animation frame');\n\t\t\trequestAnimationFrame(func);\n\t\t} else {\n\t\t\tfunc();\n\t\t}\n\t}\n\n\tfunction trigger(calleeMsg,msg,iframe,id){\n\t\tfunction postMessageToIFrame(){\n\t\t\tvar target = settings[id].targetOrigin;\n\t\t\tlog(id,'[' + calleeMsg + '] Sending msg to iframe['+id+'] ('+msg+') targetOrigin: '+target);\n\t\t\tiframe.contentWindow.postMessage( msgId + msg, target );\n\t\t}\n\n\t\tfunction iFrameNotFound(){\n\t\t\tinfo(id,'[' + calleeMsg + '] IFrame('+id+') not found');\n\t\t\tif(settings[id]) {\n\t\t\t\tdelete settings[id];\n\t\t\t}\n\t\t}\n\n\t\tfunction chkAndSend(){\n\t\t\tif(iframe && 'contentWindow' in iframe && (null !== iframe.contentWindow)){ //Null test for PhantomJS\n\t\t\t\tpostMessageToIFrame();\n\t\t\t} else {\n\t\t\t\tiFrameNotFound();\n\t\t\t}\n\t\t}\n\n\t\tid = id || iframe.id;\n\n\t\tif(settings[id]) {\n\t\t\tchkAndSend();\n\t\t}\n\n\t}\n\n\tfunction createOutgoingMsg(iframeId){\n\t\treturn iframeId +\n\t\t\t':' + settings[iframeId].bodyMarginV1 +\n\t\t\t':' + settings[iframeId].sizeWidth +\n\t\t\t':' + settings[iframeId].log +\n\t\t\t':' + settings[iframeId].interval +\n\t\t\t':' + settings[iframeId].enablePublicMethods +\n\t\t\t':' + settings[iframeId].autoResize +\n\t\t\t':' + settings[iframeId].bodyMargin +\n\t\t\t':' + settings[iframeId].heightCalculationMethod +\n\t\t\t':' + settings[iframeId].bodyBackground +\n\t\t\t':' + settings[iframeId].bodyPadding +\n\t\t\t':' + settings[iframeId].tolerance +\n\t\t\t':' + settings[iframeId].inPageLinks +\n\t\t\t':' + settings[iframeId].resizeFrom +\n\t\t\t':' + settings[iframeId].widthCalculationMethod;\n\t}\n\n\tfunction setupIFrame(iframe,options){\n\t\tfunction setLimits(){\n\t\t\tfunction addStyle(style){\n\t\t\t\tif ((Infinity !== settings[iframeId][style]) && (0 !== settings[iframeId][style])){\n\t\t\t\t\tiframe.style[style] = settings[iframeId][style] + 'px';\n\t\t\t\t\tlog(iframeId,'Set '+style+' = '+settings[iframeId][style]+'px');\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction chkMinMax(dimension){\n\t\t\t\tif (settings[iframeId]['min'+dimension]>settings[iframeId]['max'+dimension]){\n\t\t\t\t\tthrow new Error('Value for min'+dimension+' can not be greater than max'+dimension);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tchkMinMax('Height');\n\t\t\tchkMinMax('Width');\n\n\t\t\taddStyle('maxHeight');\n\t\t\taddStyle('minHeight');\n\t\t\taddStyle('maxWidth');\n\t\t\taddStyle('minWidth');\n\t\t}\n\n\t\tfunction newId(){\n\t\t\tvar id = ((options && options.id) || defaults.id + count++);\n\t\t\tif  (null!==document.getElementById(id)){\n\t\t\t\tid = id + count++;\n\t\t\t}\n\t\t\treturn id;\n\t\t}\n\n\t\tfunction ensureHasId(iframeId){\n\t\t\tlogId=iframeId;\n\t\t\tif (''===iframeId){\n\t\t\t\tiframe.id = iframeId =  newId();\n\t\t\t\tlogEnabled = (options || {}).log;\n\t\t\t\tlogId=iframeId;\n\t\t\t\tlog(iframeId,'Added missing iframe ID: '+ iframeId +' (' + iframe.src + ')');\n\t\t\t}\n\n\n\t\t\treturn iframeId;\n\t\t}\n\n\t\tfunction setScrolling(){\n\t\t\tlog(iframeId,'IFrame scrolling ' + (settings[iframeId].scrolling ? 'enabled' : 'disabled') + ' for ' + iframeId);\n\t\t\tiframe.style.overflow = false === settings[iframeId].scrolling ? 'hidden' : 'auto';\n\t\t\tiframe.scrolling      = false === settings[iframeId].scrolling ? 'no' : 'yes';\n\t\t}\n\n\t\t//The V1 iFrame script expects an int, where as in V2 expects a CSS\n\t\t//string value such as '1px 3em', so if we have an int for V2, set V1=V2\n\t\t//and then convert V2 to a string PX value.\n\t\tfunction setupBodyMarginValues(){\n\t\t\tif (('number'===typeof(settings[iframeId].bodyMargin)) || ('0'===settings[iframeId].bodyMargin)){\n\t\t\t\tsettings[iframeId].bodyMarginV1 = settings[iframeId].bodyMargin;\n\t\t\t\tsettings[iframeId].bodyMargin   = '' + settings[iframeId].bodyMargin + 'px';\n\t\t\t}\n\t\t}\n\n\t\tfunction checkReset(){\n\t\t\t// Reduce scope of firstRun to function, because IE8's JS execution\n\t\t\t// context stack is borked and this value gets externally\n\t\t\t// changed midway through running this function!!!\n\t\t\tvar\n\t\t\t\tfirstRun           = settings[iframeId].firstRun,\n\t\t\t\tresetRequertMethod = settings[iframeId].heightCalculationMethod in resetRequiredMethods;\n\n\t\t\tif (!firstRun && resetRequertMethod){\n\t\t\t\tresetIFrame({iframe:iframe, height:0, width:0, type:'init'});\n\t\t\t}\n\t\t}\n\n\t\tfunction setupIFrameObject(){\n\t\t\tif(Function.prototype.bind){ //Ignore unpolyfilled IE8.\n\t\t\t\tsettings[iframeId].iframe.iFrameResizer = {\n\n\t\t\t\t\tclose        : closeIFrame.bind(null,settings[iframeId].iframe),\n\n\t\t\t\t\tresize       : trigger.bind(null,'Window resize', 'resize', settings[iframeId].iframe),\n\n\t\t\t\t\tmoveToAnchor : function(anchor){\n\t\t\t\t\t\ttrigger('Move to anchor','moveToAnchor:'+anchor, settings[iframeId].iframe,iframeId);\n\t\t\t\t\t},\n\n\t\t\t\t\tsendMessage  : function(message){\n\t\t\t\t\t\tmessage = JSON.stringify(message);\n\t\t\t\t\t\ttrigger('Send Message','message:'+message, settings[iframeId].iframe,iframeId);\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\t//We have to call trigger twice, as we can not be sure if all\n\t\t//iframes have completed loading when this code runs. The\n\t\t//event listener also catches the page changing in the iFrame.\n\t\tfunction init(msg){\n\t\t\tfunction iFrameLoaded(){\n\t\t\t\ttrigger('iFrame.onload',msg,iframe);\n\t\t\t\tcheckReset();\n\t\t\t}\n\n\t\t\taddEventListener(iframe,'load',iFrameLoaded);\n\t\t\ttrigger('init',msg,iframe);\n\t\t}\n\n\t\tfunction checkOptions(options){\n\t\t\tif ('object' !== typeof options){\n\t\t\t\tthrow new TypeError('Options is not an object');\n\t\t\t}\n\t\t}\n\n\t\tfunction copyOptions(options){\n\t\t\tfor (var option in defaults) {\n\t\t\t\tif (defaults.hasOwnProperty(option)){\n\t\t\t\t\tsettings[iframeId][option] = options.hasOwnProperty(option) ? options[option] : defaults[option];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tfunction getTargetOrigin (remoteHost){\n\t\t\treturn ('' === remoteHost || 'file://' === remoteHost) ? '*' : remoteHost;\n\t\t}\n\n\t\tfunction processOptions(options){\n\t\t\toptions = options || {};\n\t\t\tsettings[iframeId] = {\n\t\t\t\tfirstRun\t: true,\n\t\t\t\tiframe\t\t: iframe,\n\t\t\t\tremoteHost\t: iframe.src.split('/').slice(0,3).join('/')\n\t\t\t};\n\n\t\t\tcheckOptions(options);\n\t\t\tcopyOptions(options);\n\n\t\t\tsettings[iframeId].targetOrigin = true === settings[iframeId].checkOrigin ? getTargetOrigin(settings[iframeId].remoteHost) : '*';\n\t\t}\n\n\t\tfunction beenHere(){\n\t\t\treturn (iframeId in settings && 'iFrameResizer' in iframe);\n\t\t}\n\n\t\tvar iframeId = ensureHasId(iframe.id);\n\n\t\tif (!beenHere()){\n\t\t\tprocessOptions(options);\n\t\t\tsetScrolling();\n\t\t\tsetLimits();\n\t\t\tsetupBodyMarginValues();\n\t\t\tinit(createOutgoingMsg(iframeId));\n\t\t\tsetupIFrameObject();\n\t\t} else {\n\t\t\twarn(iframeId,'Ignored iFrame, already setup.');\n\t\t}\n\t}\n\n\tfunction debouce(fn,time){\n\t\tif (null === timer){\n\t\t\ttimer = setTimeout(function(){\n\t\t\t\ttimer = null;\n\t\t\t\tfn();\n\t\t\t}, time);\n\t\t}\n\t}\n\n\t/* istanbul ignore next */  //Not testable in PhantomJS\n\tfunction fixHiddenIFrames(){\n\t\tfunction checkIFrames(){\n\t\t\tfunction checkIFrame(settingId){\n\t\t\t\tfunction chkDimension(dimension){\n\t\t\t\t\treturn '0px' === settings[settingId].iframe.style[dimension];\n\t\t\t\t}\n\n\t\t\t\tfunction isVisible(el) {\n\t\t\t\t\treturn (null !== el.offsetParent);\n\t\t\t\t}\n\n\t\t\t\tif (isVisible(settings[settingId].iframe) && (chkDimension('height') || chkDimension('width'))){\n\t\t\t\t\ttrigger('Visibility change', 'resize', settings[settingId].iframe,settingId);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor (var settingId in settings){\n\t\t\t\tcheckIFrame(settingId);\n\t\t\t}\n\t\t}\n\n\t\tfunction mutationObserved(mutations){\n\t\t\tlog('window','Mutation observed: ' + mutations[0].target + ' ' + mutations[0].type);\n\t\t\tdebouce(checkIFrames,16);\n\t\t}\n\n\t\tfunction createMutationObserver(){\n\t\t\tvar\n\t\t\t\ttarget = document.querySelector('body'),\n\n\t\t\t\tconfig = {\n\t\t\t\t\tattributes            : true,\n\t\t\t\t\tattributeOldValue     : false,\n\t\t\t\t\tcharacterData         : true,\n\t\t\t\t\tcharacterDataOldValue : false,\n\t\t\t\t\tchildList             : true,\n\t\t\t\t\tsubtree               : true\n\t\t\t\t},\n\n\t\t\t\tobserver = new MutationObserver(mutationObserved);\n\n\t\t\tobserver.observe(target, config);\n\t\t}\n\n\t\tvar MutationObserver = window.MutationObserver || window.WebKitMutationObserver;\n\n\t\tif (MutationObserver) createMutationObserver();\n\t}\n\n\n\tfunction resizeIFrames(event){\n\t\tfunction resize(){\n\t\t\tsendTriggerMsg('Window '+event,'resize');\n\t\t}\n\n\t\tlog('window','Trigger event: '+event);\n\t\tdebouce(resize,16);\n\t}\n\n\t/* istanbul ignore next */  //Not testable in PhantomJS\n\tfunction tabVisible() {\n\t\tfunction resize(){\n\t\t\tsendTriggerMsg('Tab Visable','resize');\n\t\t}\n\n\t\tif('hidden' !== document.visibilityState) {\n\t\t\tlog('document','Trigger event: Visiblity change');\n\t\t\tdebouce(resize,16);\n\t\t}\n\t}\n\n\tfunction sendTriggerMsg(eventName,event){\n\t\tfunction isIFrameResizeEnabled(iframeId) {\n\t\t\treturn\t'parent' === settings[iframeId].resizeFrom &&\n\t\t\t\t\tsettings[iframeId].autoResize &&\n\t\t\t\t\t!settings[iframeId].firstRun;\n\t\t}\n\n\t\tfor (var iframeId in settings){\n\t\t\tif(isIFrameResizeEnabled(iframeId)){\n\t\t\t\ttrigger(eventName,event,document.getElementById(iframeId),iframeId);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction setupEventListeners(){\n\t\taddEventListener(window,'message',iFrameListener);\n\n\t\taddEventListener(window,'resize', function(){resizeIFrames('resize');});\n\n\t\taddEventListener(document,'visibilitychange',tabVisible);\n\t\taddEventListener(document,'-webkit-visibilitychange',tabVisible); //Andriod 4.4\n\t\taddEventListener(window,'focusin',function(){resizeIFrames('focus');}); //IE8-9\n\t\taddEventListener(window,'focus',function(){resizeIFrames('focus');});\n\t}\n\n\n\tfunction factory(){\n\t\tfunction init(options,element){\n\t\t\tfunction chkType(){\n\t\t\t\tif(!element.tagName) {\n\t\t\t\t\tthrow new TypeError('Object is not a valid DOM element');\n\t\t\t\t} else if ('IFRAME' !== element.tagName.toUpperCase()) {\n\t\t\t\t\tthrow new TypeError('Expected <IFRAME> tag, found <'+element.tagName+'>');\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif(element) {\n\t\t\t\tchkType();\n\t\t\t\tsetupIFrame(element, options);\n\t\t\t\tiFrames.push(element);\n\t\t\t}\n\t\t}\n\n\t\tvar iFrames;\n\n\t\tsetupRequestAnimationFrame();\n\t\tsetupEventListeners();\n\n\t\treturn function iFrameResizeF(options,target){\n\t\t\tiFrames = []; //Only return iFrames past in on this call\n\n\t\t\tswitch (typeof(target)){\n\t\t\tcase 'undefined':\n\t\t\tcase 'string':\n\t\t\t\tArray.prototype.forEach.call(\n\t\t\t\t\tdocument.querySelectorAll( target || 'iframe' ),\n\t\t\t\t\tinit.bind(undefined, options)\n\t\t\t\t);\n\t\t\t\tbreak;\n\t\t\tcase 'object':\n\t\t\t\tinit(options,target);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tthrow new TypeError('Unexpected data type ('+typeof(target)+')');\n\t\t\t}\n\n\t\t\treturn iFrames;\n\t\t};\n\t}\n\n\tfunction createJQueryPublicMethod($){\n\t\tif (!$.fn) {\n\t\t\tinfo('','Unable to bind to jQuery, it is not fully loaded.');\n\t\t} else {\n\t\t\t$.fn.iFrameResize = function $iFrameResizeF(options) {\n\t\t\t\tfunction init(index, element) {\n\t\t\t\t\tsetupIFrame(element, options);\n\t\t\t\t}\n\n\t\t\t\treturn this.filter('iframe').each(init).end();\n\t\t\t};\n\t\t}\n\t}\n\n\tif (window.jQuery) { createJQueryPublicMethod(jQuery); }\n\n\tif (typeof define === 'function' && define.amd) {\n\t\tdefine([],factory);\n\t} else if (typeof module === 'object' && typeof module.exports === 'object') { //Node for browserfy\n\t\tmodule.exports = factory();\n\t} else {\n\t\twindow.iFrameResize = window.iFrameResize || factory();\n\t}\n\n})(window || {});\n"]}