{"version": 3, "file": "ie8.polyfils.min.js", "sources": ["../src/ie8.polyfils.js"], "names": ["Array", "prototype", "for<PERSON>ach", "fun", "this", "TypeError", "t", "Object", "len", "length", "thisArg", "arguments", "i", "call", "Function", "bind", "oThis", "aArgs", "slice", "fToBind", "fNOP", "fBound", "apply", "concat", "callback", "O", "k"], "mappings": ";;AAOMA,MAAMC,UAAUC,UACrBF,MAAMC,UAAUC,QAAU,SAASC,GAClC,YACA,IAAa,SAATC,MAA4B,OAATA,MAAgC,kBAARD,GAAoB,KAAM,IAAIE,UAO7E,KAAK,GAJJC,GAAIC,OAAOH,MACXI,EAAMF,EAAEG,SAAW,EACnBC,EAAUC,UAAUF,QAAU,EAAIE,UAAU,GAAK,OAEzCC,EAAI,EAAOJ,EAAJI,EAASA,IACpBA,IAAKN,IACRH,EAAIU,KAAKH,EAASJ,EAAEM,GAAIA,EAAGN,KAK1BQ,SAASb,UAAUc,OACtBD,SAASb,UAAUc,KAAO,SAASC,GACjC,GAAoB,kBAATZ,MAGT,KAAM,IAAIC,WAAU,uEAGtB,IAAIY,GAAUjB,MAAMC,UAAUiB,MAAML,KAAKF,UAAW,GAChDQ,EAAUf,KACVgB,EAAU,aACVC,EAAU,WACR,MAAOF,GAAQG,MAAMlB,eAAgBgB,GAAOhB,KAAOY,EAC5CC,EAAMM,OAAOvB,MAAMC,UAAUiB,MAAML,KAAKF,aAMrD,OAHAS,GAAKnB,UAAYG,KAAKH,UACtBoB,EAAOpB,UAAY,GAAImB,GAEhBC,IAINrB,MAAMC,UAAUC,UACnBF,MAAMC,UAAUC,QAAU,SAASsB,EAAUd,GAC3C,GAAa,OAATN,KAAe,KAAM,IAAIC,WAAU,+BACvC,IAAwB,kBAAbmB,GAAyB,KAAM,IAAInB,WAAUmB,EAAW,qBAMnE,KAAK,GAHHC,GAAIlB,OAAOH,MACXI,EAAMiB,EAAEhB,SAAW,EAEZiB,EAAE,EAAQlB,EAAJkB,EAAUA,IACnBA,IAAKD,IACPD,EAASX,KAAKH,EAASe,EAAEC,GAAIA,EAAGD", "sourcesContent": ["/*\n * IE8 Polyfils for iframeResizer.js\n *\n * Public domain code - Mozilla Contributors\n * https://developer.mozilla.org/\n */\n\n if (!Array.prototype.forEach){\n\tArray.prototype.forEach = function(fun /*, thisArg */){\n\t\t\"use strict\";\n\t\tif (this === void 0 || this === null || typeof fun !== \"function\") throw new TypeError();\n\n\t\tvar\n\t\t\tt = Object(this),\n\t\t\tlen = t.length >>> 0,\n\t\t\tthisArg = arguments.length >= 2 ? arguments[1] : void 0;\n\n\t\tfor (var i = 0; i < len; i++)\n\t\t\tif (i in t)\n\t\t\t\tfun.call(thisArg, t[i], i, t);\n\t};\n}\n\n\nif (!Function.prototype.bind) {\n  Function.prototype.bind = function(oThis) {\n    if (typeof this !== 'function') {\n      // closest thing possible to the ECMAScript 5\n      // internal IsCallable function\n      throw new TypeError('Function.prototype.bind - what is trying to be bound is not callable');\n    }\n\n    var aArgs   = Array.prototype.slice.call(arguments, 1),\n        fToBind = this,\n        fNOP    = function() {},\n        fBound  = function() {\n          return fToBind.apply(this instanceof fNOP ? this : oThis,\n                 aArgs.concat(Array.prototype.slice.call(arguments)));\n        };\n\n    fNOP.prototype = this.prototype;\n    fBound.prototype = new fNOP();\n\n    return fBound;\n  };\n}\n\nif (!Array.prototype.forEach) {\n  Array.prototype.forEach = function(callback, thisArg) {\n    if (this === null) throw new TypeError(' this is null or not defined');\n    if (typeof callback !== 'function') throw new TypeError(callback + ' is not a function');\n\n    var\n      O = Object(this),\n      len = O.length >>> 0;\n\n    for (var k=0 ; k < len ; k++) {\n      if (k in O)\n        callback.call(thisArg, O[k], k, O);\n    }\n  };\n}\n\n\n"]}