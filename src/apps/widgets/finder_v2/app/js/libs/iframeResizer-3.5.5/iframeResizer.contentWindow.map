{"version": 3, "file": "iframeResizer.contentWindow.min.js", "sources": ["iframeResizer.contentWindow.js"], "names": ["window", "undefined", "addEventListener", "el", "evt", "func", "attachEvent", "removeEventListener", "detachEvent", "capitalizeFirstLetter", "string", "char<PERSON>t", "toUpperCase", "slice", "throttle", "context", "args", "result", "timeout", "previous", "later", "getNow", "apply", "now", "remaining", "throttledTimer", "this", "arguments", "clearTimeout", "setTimeout", "formatLogMsg", "msg", "msgID", "myID", "log", "logging", "console", "warn", "init", "readDataFromParent", "location", "href", "readDataFromPage", "<PERSON><PERSON><PERSON><PERSON>", "setBodyStyle", "bodyBackground", "bodyPadding", "injectClearFixIntoBodyElement", "checkHeightMode", "checkWidthMode", "stopInfiniteResizingOfIFrame", "setupPublicMethods", "startEventListeners", "inPageLinks", "setupInPageLinks", "sendSize", "readyCallback", "strBool", "str", "data", "initMsg", "substr", "msgIdLen", "split", "<PERSON><PERSON><PERSON><PERSON>", "Number", "calculateWidth", "interval", "autoResize", "bodyMarginStr", "heightCalcMode", "tolerance", "enable", "resizeFrom", "widthCalcMode", "readData", "iFrameResizer", "JSON", "stringify", "messageCallback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target<PERSON>rigin", "heightCalculationMethod", "widthCalculationMethod", "setupCustomCalcMethods", "calcMode", "calcFunc", "customCalcMethods", "Object", "constructor", "chkCSS", "attr", "value", "indexOf", "document", "body", "style", "documentElement", "height", "manageTriggerEvent", "options", "handleEvent", "eventName", "eventType", "listener", "add", "remove", "eventNames", "Array", "prototype", "map", "method", "manageEventListeners", "checkCalcMode", "calcModeDefault", "modes", "type", "heightCalcModeDefault", "getHeight", "widthCalcModeDefault", "getWidth", "setupMutationObserver", "stopMsgsToParent", "sendPermit", "removeMsgListener", "receiver", "disconnectMutationObserver", "bodyObserver", "disconnect", "stopEventListeners", "clearInterval", "intervalTimer", "teardown", "clearFix", "createElement", "clear", "display", "append<PERSON><PERSON><PERSON>", "getPagePosition", "x", "pageXOffset", "scrollLeft", "y", "pageYOffset", "scrollTop", "getElementPosition", "elPosition", "getBoundingClientRect", "pagePosition", "parseInt", "left", "top", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target", "jumpPosition", "hash", "sendMsg", "hashData", "decodeURIComponent", "getElementById", "getElementsByName", "checkLocationHash", "bindAnchors", "setupLink", "linkClicked", "e", "preventDefault", "getAttribute", "for<PERSON>ach", "call", "querySelectorAll", "bindLocationHash", "initCheck", "eventCancelTimer", "enableInPageLinks", "win", "parentIFrame", "resize", "close", "getId", "getPageInfo", "callback", "pageInfoCallback", "moveToAnchor", "reset", "resetIFrame", "scrollTo", "scrollToOffset", "sendMessage", "setHeightCalculationMethod", "setWidthCalculationMethod", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "customHeight", "customWidth", "valString", "initInterval", "setInterval", "Math", "abs", "setupBodyMutationObserver", "addImageLoadListners", "mutation", "addImageLoadListener", "element", "complete", "src", "imageLoaded", "imageError", "elements", "push", "attributeName", "removeFromArray", "splice", "removeImageLoadListener", "imageEventTriggered", "event", "typeDesc", "mutationObserved", "mutations", "createMutationObserver", "querySelector", "config", "attributes", "attributeOldValue", "characterData", "characterDataOldValue", "childList", "subtree", "observer", "MutationObserver", "observe", "WebKitMutationObserver", "forceIntervalTimer", "getComputedStyle", "prop", "convertUnitsToPxForIE8", "PIXEL", "test", "base", "runtimeStyle", "currentStyle", "pixelLeft", "retVal", "defaultView", "chkEventThottle", "timer", "getMaxElement", "side", "elements<PERSON>ength", "length", "elVal", "maxVal", "Side", "i", "getAllMeasurements", "dimention", "bodyOffset", "bodyScroll", "documentElementOffset", "documentElementScroll", "getTaggedElements", "tag", "noTaggedElementsFound", "getAllElements", "sizeIFrame", "triggerEvent", "triggerEventDesc", "resizeIFrame", "currentHeight", "width", "currentWidth", "isSizeChangeDetected", "checkTolarance", "a", "b", "isForceResizableEvent", "isForceResizableCalcMode", "resetRequiredMethods", "logIgnored", "checkDownSizing", "lockTrigger", "recordTrigger", "resetPage", "isDoubleFiredEvent", "triggerLocked", "doubleEventList", "sizeIFrameThrottled", "triggerLockedTimer", "triggerReset", "hcm", "sendToParent", "message", "postMessage", "isMessageForUs", "initFromParent", "fireInit", "source", "firstRun", "initLock", "resetFromParent", "resizeFromParent", "anchor", "getData", "getMessageType", "isMiddleTier", "messageFromParent", "msgBody", "parse", "pageInfoFromParent", "isInitMsg", "true", "false", "callFromParent", "processMessage", "chkLateLoaded", "readyState", "parent", "click", "max", "min", "offsetHeight", "scrollWidth", "Date", "getTime", "offset", "scrollHeight", "custom", "grow", "lowestElement", "taggedElement", "offsetWidth", "scroll", "rightMostElement"], "mappings": ";;;;;;;;CAYC,SAAUA,EAAQC,GAClB,YAuDA,SAASC,GAAiBC,EAAGC,EAAIC,GAE5B,oBAAsBL,GACzBG,EAAGD,iBAAiBE,EAAIC,GAAM,GACpB,eAAiBL,IAC3BG,EAAGG,YAAY,KAAKF,EAAIC,GAI1B,QAASE,GAAoBJ,EAAGC,EAAIC,GAE/B,uBAAyBL,GAC5BG,EAAGI,oBAAoBH,EAAIC,GAAM,GACvB,eAAiBL,IAC3BG,EAAGK,YAAY,KAAKJ,EAAIC,GAI1B,QAASI,GAAsBC,GAC9B,MAAOA,GAAOC,OAAO,GAAGC,cAAgBF,EAAOG,MAAM,GAItD,QAASC,GAAST,GACjB,GACCU,GAASC,EAAMC,EACfC,EAAU,KACVC,EAAW,EACXC,EAAQ,WACPD,EAAWE,KACXH,EAAU,KACVD,EAASZ,EAAKiB,MAAMP,EAASC,GACxBE,IACJH,EAAUC,EAAO,MAIpB,OAAO,YACN,GAAIO,GAAMF,IAELF,KACJA,EAAWI,EAGZ,IAAIC,GAAYC,IAAkBF,EAAMJ,EAsBxC,OApBAJ,GAAUW,KACVV,EAAOW,UAEU,GAAbH,GAAkBA,EAAYC,IAC7BP,IACHU,aAAaV,GACbA,EAAU,MAGXC,EAAWI,EACXN,EAASZ,EAAKiB,MAAMP,EAASC,GAExBE,IACJH,EAAUC,EAAO,OAGPE,IACXA,EAAUW,WAAWT,EAAOI,IAGtBP,GAST,QAASa,GAAaC,GACrB,MAAOC,IAAQ,IAAMC,GAAO,KAAYF,EAGzC,QAASG,GAAIH,GACRI,IAAY,gBAAoBnC,GAAOoC,SAC1CA,QAAQF,IAAIJ,EAAaC,IAI3B,QAASM,GAAKN,GACT,gBAAoB/B,GAAOoC,SAC9BA,QAAQC,KAAKP,EAAaC,IAK5B,QAASO,KACRC,IACAL,EAAI,wBAAwBM,SAASC,KAAK,KAC1CC,IACAC,IACAC,EAAa,aAAaC,GAC1BD,EAAa,UAAUE,GACvBC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,GAAcC,IACdC,EAAS,OAAO,+BAChBC,KAGD,QAASjB,KAER,QAASkB,GAAQC,GAChB,MAAO,SAAWA,GAAM,GAAO,EAGhC,GAAIC,GAAOC,GAAQC,OAAOC,IAAUC,MAAM,IAE1C9B,IAAqB0B,EAAK,GAC1BK,EAAsB/D,IAAc0D,EAAK,GAAMM,OAAON,EAAK,IAAQK,EACnEE,GAAsBjE,IAAc0D,EAAK,GAAMF,EAAQE,EAAK,IAAOO,GACnE/B,GAAsBlC,IAAc0D,EAAK,GAAMF,EAAQE,EAAK,IAAOxB,GACnEgC,GAAsBlE,IAAc0D,EAAK,GAAMM,OAAON,EAAK,IAAQQ,GACnEC,EAAsBnE,IAAc0D,EAAK,GAAMF,EAAQE,EAAK,IAAOS,EACnEC,EAAqBV,EAAK,GAC1BW,GAAsBrE,IAAc0D,EAAK,GAAMA,EAAK,GAAeW,GACnEzB,EAAqBc,EAAK,GAC1Bb,EAAqBa,EAAK,IAC1BY,GAAsBtE,IAAc0D,EAAK,IAAOM,OAAON,EAAK,KAAOY,GACnElB,GAAYmB,OAAUvE,IAAc0D,EAAK,IAAOF,EAAQE,EAAK,MAAM,EACnEc,GAAsBxE,IAAc0D,EAAK,IAAOA,EAAK,IAAcc,GACnEC,GAAsBzE,IAAc0D,EAAK,IAAOA,EAAK,IAAce,GAGpE,QAAShC,KACR,QAASiC,KACR,GAAIhB,GAAO3D,EAAO4E,aAElB1C,GAAI,2BAA6B2C,KAAKC,UAAUnB,IAEhDoB,GAAuB,mBAA6BpB,GAAQA,EAAKoB,gBAA0BA,GAC3FvB,GAAuB,iBAA6BG,GAAQA,EAAKH,cAA0BA,GAC3FwB,GAAuB,gBAA6BrB,GAAQA,EAAKsB,aAA0BD,GAC3FV,GAAuB,2BAA6BX,GAAQA,EAAKuB,wBAA0BZ,GAC3FI,GAAuB,0BAA6Bf,GAAQA,EAAKwB,uBAA0BT,GAG5F,QAASU,GAAuBC,EAAUC,GAOzC,MANI,kBAAsBD,KACzBnD,EAAI,gBAAkBoD,EAAW,cACjCC,GAAkBD,GAAYD,EAC9BA,EAAW,UAGLA,EAGJ,iBAAmBrF,IAAYwF,SAAWxF,EAAO4E,cAAca,cAClEd,IACAL,GAAiBc,EAAuBd,GAAgB,UACxDI,GAAiBU,EAAuBV,GAAgB,UAGzDxC,EAAI,mCAAqC8C,IAI1C,QAASU,GAAOC,EAAKC,GAKpB,MAJI,KAAOA,EAAMC,QAAQ,OACxBxD,EAAK,kCAAkCsD,GACvCC,EAAM,IAEAA,EAGR,QAAShD,GAAa+C,EAAKC,GACrB3F,IAAc2F,GAAW,KAAOA,GAAW,SAAWA,IAC1DE,SAASC,KAAKC,MAAML,GAAQC,EAC5B1D,EAAI,QAAQyD,EAAK,YAAYC,EAAM,MAIrC,QAASjD,KAEJ1C,IAAcoE,IACjBA,EAAgBL,EAAW,MAG5BpB,EAAa,SAAS8C,EAAO,SAASrB,IAGvC,QAASnB,KACR4C,SAASG,gBAAgBD,MAAME,OAAS,GACxCJ,SAASC,KAAKC,MAAME,OAAS,GAC7BhE,EAAI,oCAIL,QAASiE,GAAmBC,GAC3B,QAASC,KACR9C,EAAS6C,EAAQE,UAAUF,EAAQG,WAGpC,GAAIC,IACHC,IAAQ,SAASH,GAChBpG,EAAiBF,EAAOsG,EAAUD,IAEnCK,OAAQ,SAASJ,GAChB/F,EAAoBP,EAAOsG,EAAUD,IAIpCD,GAAQO,YAAcC,MAAMC,UAAUC,KACxCV,EAAQE,UAAYF,EAAQO,WAAW,GACvCP,EAAQO,WAAWG,IAAIN,EAASJ,EAAQW,UAExCP,EAASJ,EAAQW,QAAQX,EAAQE,WAGlCpE,EAAIzB,EAAsB2F,EAAQW,QAAU,oBAAsBX,EAAQG,WAG3E,QAASS,GAAqBD,GAC7BZ,GAAoBY,OAAOA,EAAQR,UAAW,kBAA6BI,YAAa,iBAAiB,0BACzGR,GAAoBY,OAAOA,EAAQR,UAAW,sBAA6BI,YAAa,qBAAqB,8BAC7GR,GAAoBY,OAAOA,EAAQR,UAAW,gBAA6BI,YAAa,eAAe,wBACvGR,GAAoBY,OAAOA,EAAQR,UAAW,QAA6BD,UAAY,UACvFH,GAAoBY,OAAOA,EAAQR,UAAW,WAA6BD,UAAY,YACvFH,GAAoBY,OAAOA,EAAQR,UAAW,aAA6BD,UAAY,cACvFH,GAAoBY,OAAOA,EAAQR,UAAW,qBAA6BD,UAAY,sBACvFH,GAAoBY,OAAOA,EAAQR,UAAW,QAA6BD,WAAa,aAAc,iBACtGH,GAAoBY,OAAOA,EAAQR,UAAW,qBAA6BD,UAAY,qBACvFH,GAAoBY,OAAOA,EAAQR,UAAW,cAA6BD,UAAY,eACvFH,GAAoBY,OAAOA,EAAQR,UAAW,YAA6BD,UAAY,aACvFH,GAAoBY,OAAOA,EAAQR,UAAW,eAA6BD,UAAY,gBACvFH,GAAoBY,OAAOA,EAAQR,UAAW,mBAA6BI,YAAa,kBAAkB,wBAAwB,oBAAoB,mBAAmB,sBACzKR,GAAoBY,OAAOA,EAAQR,UAAW,uBAA6BI,YAAa,sBAAsB,4BAA4B,wBAAwB,uBAAuB,0BACzLR,GAAoBY,OAAOA,EAAQR,UAAW,iBAA6BI,YAAa,gBAAgB,sBAAsB,kBAAkB,iBAAiB,oBAC9J,UAAYlC,IACd0B,GAAoBY,OAAOA,EAAQR,UAAW,iBAAyBD,UAAY,WAIrF,QAASW,GAAc5B,EAAS6B,EAAgBC,EAAMC,GASrD,MARIF,KAAoB7B,IACjBA,IAAY8B,KACjB9E,EAAKgD,EAAW,8BAA8B+B,EAAK,sBACnD/B,EAAS6B,GAEVhF,EAAIkF,EAAK,+BAA+B/B,EAAS,MAG3CA,EAGR,QAASrC,KACRsB,GAAiB2C,EAAc3C,GAAe+C,GAAsBC,GAAU,UAG/E,QAASrE,KACRyB,GAAgBuC,EAAcvC,GAAc6C,GAAqBC,GAAS,SAG3E,QAASpE,MACH,IAASgB,GACb4C,EAAqB,OACrBS,KAGAvF,EAAI,wBAIN,QAASwF,KACRxF,EAAI,6BACJyF,IAAa,EAGd,QAASC,KACR1F,EAAI,kCACJ3B,EAAoBP,EAAQ,UAAW6H,GAGxC,QAASC,KACJ,OAASC,GAEZA,EAAaC,aAIf,QAASC,KACRjB,EAAqB,UACrBc,IACAI,cAAcC,IAGf,QAASC,KACRV,IACAE,KACI,IAASxD,GAAY6D,IAG1B,QAASlF,KACR,GAAIsF,GAAWvC,SAASwC,cAAc,MACtCD,GAASrC,MAAMuC,MAAU,OACzBF,EAASrC,MAAMwC,QAAU,QACzB1C,SAASC,KAAK0C,YAAYJ,GAG3B,QAAS/E,KAER,QAASoF,KACR,OACCC,EAAI3I,EAAO4I,cAAgB3I,EAAaD,EAAO4I,YAAc9C,SAASG,gBAAgB4C,WACtFC,EAAI9I,EAAO+I,cAAgB9I,EAAaD,EAAO+I,YAAcjD,SAASG,gBAAgB+C,WAIxF,QAASC,GAAmB9I,GAC3B,GACC+I,GAAe/I,EAAGgJ,wBAClBC,EAAeV,GAEhB,QACCC,EAAGU,SAASH,EAAWI,KAAK,IAAMD,SAASD,EAAaT,EAAE,IAC1DG,EAAGO,SAASH,EAAWK,IAAI,IAAOF,SAASD,EAAaN,EAAE,KAI5D,QAASU,GAAWhH,GACnB,QAASiH,GAAaC,GACrB,GAAIC,GAAeV,EAAmBS,EAEtCxH,GAAI,4BAA4B0H,EAAK,WAAWD,EAAahB,EAAE,OAAOgB,EAAab,GACnFe,EAAQF,EAAab,EAAGa,EAAahB,EAAG,kBAGzC,GACCiB,GAAWpH,EAASuB,MAAM,KAAK,IAAMvB,EACrCsH,EAAWC,mBAAmBH,GAC9BF,EAAW5D,SAASkE,eAAeF,IAAahE,SAASmE,kBAAkBH,GAAU,EAElF7J,KAAcyJ,EACjBD,EAAaC,IAEbxH,EAAI,kBAAoB0H,EAAO,+CAC/BC,EAAQ,EAAE,EAAE,aAAa,IAAID,IAI/B,QAASM,KACJ,KAAO1H,SAASoH,MAAQ,MAAQpH,SAASoH,MAC5CJ,EAAWhH,SAASC,MAItB,QAAS0H,KACR,QAASC,GAAUjK,GAClB,QAASkK,GAAYC,GACpBA,EAAEC,iBAGFf,EAAW9H,KAAK8I,aAAa,SAG1B,MAAQrK,EAAGqK,aAAa,SAC3BtK,EAAiBC,EAAG,QAAQkK,GAI9BzD,MAAMC,UAAU4D,QAAQC,KAAM5E,SAAS6E,iBAAkB,gBAAkBP,GAG5E,QAASQ,KACR1K,EAAiBF,EAAO,aAAakK,GAGtC,QAASW,KACRhJ,WAAWqI,EAAkBY,IAG9B,QAASC,KAELnE,MAAMC,UAAU4D,SAAW3E,SAAS6E,kBACtCzI,EAAI,qCACJiI,IACAS,IACAC,KAEAxI,EAAK,2FAUP,MANGgB,IAAYmB,OACduG,IAEA7I,EAAI,gCAIJsH,WAAWA,GAIb,QAASrG,KACRjB,EAAI,yBAEJ8I,GAAIC,cAEH7G,WAAY,SAAqB8G,GAUhC,OATI,IAASA,IAAU,IAAU9G,GAChCA,GAAW,EACXhB,MAEU,IAAU8H,IAAU,IAAS9G,IACvCA,GAAW,EACX6D,KAGM7D,GAGR+G,MAAO,WACNtB,EAAQ,EAAE,EAAE,SACZzB,KAGDgD,MAAO,WACN,MAAOnJ,KAGRoJ,YAAa,SAAsBC,GAC9B,kBAAsBA,IACzBC,GAAmBD,EACnBzB,EAAQ,EAAE,EAAE,cAEZ0B,GAAmB,aACnB1B,EAAQ,EAAE,EAAE,kBAId2B,aAAc,SAAuB5B,GACpCvG,GAAYmG,WAAWI,IAGxB6B,MAAO,WACNC,EAAY,uBAGbC,SAAU,SAAmBhD,EAAEG,GAC9Be,EAAQf,EAAEH,EAAE,aAGbiD,eAAgB,SAAmBjD,EAAEG,GACpCe,EAAQf,EAAEH,EAAE,mBAGbkD,YAAa,SAAsB9J,EAAIkD,GACtC4E,EAAQ,EAAE,EAAE,UAAUhF,KAAKC,UAAU/C,GAAKkD,IAG3C6G,2BAA4B,SAAqC5G,GAChEZ,GAAiBY,EACjBlC,KAGD+I,0BAA2B,SAAoC5G,GAC9DT,GAAgBS,EAChBlC,KAGD+I,gBAAiB,SAA0B/G,GAC1C/C,EAAI,qBAAqB+C,GACzBD,GAAsBC,GAGvBgH,KAAM,SAAeC,EAAcC,GAClC,GAAIC,GAAY,IAAIF,EAAaA,EAAa,KAAKC,EAAY,IAAIA,EAAY,GAE/E5I,GAAS,OAAO,qBAAqB6I,EAAU,IAAKF,EAAcC,KAKrE,QAASE,KACH,IAAMlI,KACVjC,EAAI,gBAAgBiC,GAAS,MAC7BgE,GAAgBmE,YAAY,WAC3B/I,EAAS,WAAW,gBAAgBY,KACnCoI,KAAKC,IAAIrI,MAKb,QAASsI,KACR,QAASC,GAAqBC,GAC7B,QAASC,GAAqBC,IACzB,IAAUA,EAAQC,WACrB5K,EAAI,uBAAyB2K,EAAQE,KACrCF,EAAQ3M,iBAAiB,OAAQ8M,GAAa,GAC9CH,EAAQ3M,iBAAiB,QAAS+M,GAAY,GAC9CC,EAASC,KAAKN,IAIM,eAAlBF,EAASvF,MAAoD,QAA3BuF,EAASS,cAC9CR,EAAqBD,EAASjD,QACF,cAAlBiD,EAASvF,MACnBR,MAAMC,UAAU4D,QAAQC,KACvBiC,EAASjD,OAAOiB,iBAAiB,OACjCiC,GAKH,QAASS,GAAgBR,GACxBK,EAASI,OAAOJ,EAASrH,QAAQgH,GAAS,GAG3C,QAASU,GAAwBV,GAChC3K,EAAI,yBAA2B2K,EAAQE,KACvCF,EAAQtM,oBAAoB,OAAQyM,GAAa,GACjDH,EAAQtM,oBAAoB,QAAS0M,GAAY,GACjDI,EAAgBR,GAGjB,QAASW,GAAoBC,EAAMrG,EAAKsG,GACvCH,EAAwBE,EAAM/D,QAC9BnG,EAAS6D,EAAMsG,EAAW,KAAOD,EAAM/D,OAAOqD,IAAK9M,EAAWA,GAG/D,QAAS+M,GAAYS,GACpBD,EAAoBC,EAAM,YAAY,gBAGvC,QAASR,GAAWQ,GACnBD,EAAoBC,EAAM,kBAAkB,qBAG7C,QAASE,GAAiBC,GACzBrK,EAAS,mBAAmB,qBAAuBqK,EAAU,GAAGlE,OAAS,IAAMkE,EAAU,GAAGxG,MAG5FwG,EAAUnD,QAAQiC,GAGnB,QAASmB,KACR,GACCnE,GAAS5D,SAASgI,cAAc,QAEhCC,GACCC,YAAwB,EACxBC,mBAAwB,EACxBC,eAAwB,EACxBC,uBAAwB,EACxBC,WAAwB,EACxBC,SAAwB,EAQ1B,OALAC,GAAW,GAAIC,GAAiBZ,GAEhCzL,EAAI,gCACJoM,EAASE,QAAQ9E,EAAQqE,GAElBO,EAGR,GACCpB,MACAqB,EAAmBvO,EAAOuO,kBAAoBvO,EAAOyO,uBACrDH,EAAmBT,GAEpB,QACC7F,WAAY,WACP,cAAgBsG,KACnBpM,EAAI,oCACJoM,EAAStG,aACTkF,EAASzC,QAAQ8C,MAMrB,QAAS9F,KACR,GAAIiH,GAAqB,EAAIvK,EAGzBnE,GAAOuO,kBAAoBvO,EAAOyO,uBACjCC,EACHrC,IAEAtE,EAAe0E,KAGhBvK,EAAI,mDACJmK,KAOF,QAASsC,GAAiBC,EAAKzO,GAE9B,QAAS0O,GAAuBjJ,GAC/B,GAAIkJ,GAAQ,aAEZ,IAAIA,EAAMC,KAAKnJ,GACd,MAAOyD,UAASzD,EAAMoJ,EAGvB,IACChJ,GAAQ7F,EAAG6F,MAAMsD,KACjB2F,EAAe9O,EAAG8O,aAAa3F,IAQhC,OANAnJ,GAAG8O,aAAa3F,KAAOnJ,EAAG+O,aAAa5F,KACvCnJ,EAAG6F,MAAMsD,KAAO1D,GAAS,EACzBA,EAAQzF,EAAG6F,MAAMmJ,UACjBhP,EAAG6F,MAAMsD,KAAOtD,EAChB7F,EAAG8O,aAAa3F,KAAO2F,EAEhBrJ,EAGR,GAAIwJ,GAAS,CAWb,OAVAjP,GAAMA,GAAM2F,SAASC,KAGhB,eAAiBD,WAAc,oBAAsBA,UAASuJ,aAClED,EAAStJ,SAASuJ,YAAYV,iBAAiBxO,EAAI,MACnDiP,EAAU,OAASA,EAAUA,EAAOR,GAAQ,GAE5CQ,EAAUP,EAAuB1O,EAAG+O,aAAaN,IAG3CvF,SAAS+F,EAAOJ,GAGxB,QAASM,GAAgBC,GACrBA,EAAQ9N,GAAe,IACzBA,GAAiB,EAAE8N,EACnBrN,EAAI,+BAAiCT,GAAiB,OAKxD,QAAS+N,GAAcC,EAAKvC,GAQ3B,IAAK,GANJwC,GAAiBxC,EAASyC,OAC1BC,EAAiB,EACjBC,EAAiB,EACjBC,EAAiBrP,EAAsBgP,GACvCF,EAAiBlO,KAET0O,EAAI,EAAOL,EAAJK,EAAoBA,IACnCH,EAAQ1C,EAAS6C,GAAG5G,wBAAwBsG,GAAQd,EAAiB,SAASmB,EAAK5C,EAAS6C,IACxFH,EAAQC,IACXA,EAASD,EAWX,OAPAL,GAAQlO,KAAWkO,EAEnBrN,EAAI,UAAUwN,EAAe,kBAC7BxN,EAAI,kCAAoCqN,EAAQ,MAEhDD,EAAgBC,GAETM,EAGR,QAASG,GAAmBC,GAC3B,OACCA,EAAUC,aACVD,EAAUE,aACVF,EAAUG,wBACVH,EAAUI,yBAIZ,QAASC,GAAkBb,EAAKc,GAC/B,QAASC,KAER,MADAnO,GAAK,uBAAuBkO,EAAI,mBACzBrK,GAGR,GAAIgH,GAAWpH,SAAS6E,iBAAiB,IAAI4F,EAAI,IAEjD,OAAO,KAAMrD,EAASyC,OAAUa,IAA0BhB,EAAcC,EAAKvC,GAG9E,QAASuD,KACR,MAAO3K,UAAS6E,iBAAiB,UA6FlC,QAAS+F,GAAWC,EAAcC,EAAkB1E,EAAcC,GAEjE,QAAS0E,KACR3K,GAAS4K,EACTC,GAASC,EAETnH,EAAQ3D,GAAO6K,GAAMJ,GAGtB,QAASM,KACR,QAASC,GAAeC,EAAEC,GACzB,GAAIhC,GAAS7C,KAAKC,IAAI2E,EAAEC,IAAM7M,EAC9B,QAAQ6K,EAMT,MAHA0B,GAAiB7Q,IAAciM,EAAiBA,EAAe5E,GAAUhD,MACzE0M,EAAiB/Q,IAAckM,EAAiBA,EAAe3E,GAAS9C,MAEjEwM,EAAehL,GAAO4K,IAAmB5M,IAAkBgN,EAAeH,GAAMC,GAGxF,QAASK,KACR,QAASV,KAAiBrO,KAAO,EAAE6B,SAAW,EAAE8H,KAAO,IAGxD,QAASqF,KACR,MAAQhN,MAAkBiN,KAA0BrN,IAAkBQ,KAAiB6M,IAGxF,QAASC,KACRtP,EAAI,8BAGL,QAASuP,KACJJ,KAA2BC,IAC9B5F,EAAYkF,GACAD,KAAiBxM,SAAW,IACxCqN,IAIF,GAAIV,GAAcE,CAEdC,MAA0B,SAAWN,GACxCe,IACAb,KAEAY,IAMF,QAASlO,GAASoN,EAAcC,EAAkB1E,EAAcC,GAC/D,QAASwF,KACFhB,KAAiBlF,MAAQ,EAAEmG,UAAY,EAAEtP,KAAO,IACrDJ,EAAK,kBAAoB0O,GAI3B,QAASiB,KACR,MAAQC,KAAkBnB,IAAgBoB,IAGtCF,IAIJ3P,EAAI,4BAA4ByO,IAHhCgB,IACAK,GAAoBrB,EAAcC,EAAkB1E,EAAcC,IAMpE,QAASuF,KACHI,KACJA,IAAgB,EAChB5P,EAAI,0BAELN,aAAaqQ,IACbA,GAAqBpQ,WAAW,WAC/BiQ,IAAgB,EAChB5P,EAAI,0BACJA,EAAI,OACH4I,IAGH,QAASoH,GAAavB,GACrBzK,GAASoB,GAAUhD,MACnByM,GAASvJ,GAAS9C,MAElBmF,EAAQ3D,GAAO6K,GAAMJ,GAGtB,QAASjF,GAAYkF,GACpB,GAAIuB,GAAM7N,EACVA,IAAiB+C,GAEjBnF,EAAI,wBAA0B0O,GAC9Bc,IACAQ,EAAa,SAEb5N,GAAiB6N,EAGlB,QAAStI,GAAQ3D,EAAO6K,EAAMJ,EAAa5O,EAAIkD,GAC9C,QAAS+G,KACJ/L,IAAcgF,EACjBA,EAAeD,GAEf9C,EAAI,yBAAyB+C,GAI/B,QAASmN,KACR,GACCnG,GAAQ/F,EAAS,IAAM6K,EACvBsB,EAAUpQ,GAAO,IAAOgK,EAAO,IAAM0E,GAAgB1Q,IAAc8B,EAAM,IAAMA,EAAM,GAEtFG,GAAI,iCAAmCmQ,EAAU,KACjD3I,GAAO4I,YAAatQ,GAAQqQ,EAASpN,IAGnC,IAAS0C,KACXqE,IACAoG,KAIF,QAASvK,GAAS4F,GACjB,QAAS8E,KACR,MAAOvQ,OAAW,GAAGyL,EAAM9J,MAAME,OAAO,EAAEC,IAG3C,QAAS0O,KACR,QAASC,KACR7O,GAAU6J,EAAM9J,KAChB+F,GAAU+D,EAAMiF,OAEhBpQ,IACAqQ,IAAW,EACX9Q,WAAW,WAAY+Q,IAAW,GAAQ9H,IAGvChF,SAASC,KACZ0M,KAEAvQ,EAAI,0BACJhC,EAAiBF,EAAO,mBAAmBwS,IAI7C,QAASK,KACHD,GAIJ1Q,EAAI,+BAHJA,EAAI,gCACJgQ,EAAa,cAMf,QAASY,KACRvP,EAAS,eAAe,sCAGzB,QAASiI,KACR,GAAIuH,GAASC,GACb3P,IAAYmG,WAAWuJ,GAGxB,QAASE,KACR,MAAOxF,GAAM9J,KAAKI,MAAM,KAAK,GAAGA,MAAM,KAAK,GAG5C,QAASiP,KACR,MAAOvF,GAAM9J,KAAKE,OAAO4J,EAAM9J,KAAKkC,QAAQ,KAAK,GAGlD,QAASqN,KACR,MAAQ,gBAAkBlT,GAG3B,QAASmT,KACR,GAAIC,GAAUJ,GAEd9Q,GAAI,uCAAyCkR,GAC7CrO,GAAgBF,KAAKwO,MAAMD,IAC3BlR,EAAI,OAGL,QAASoR,KACR,GAAIF,GAAUJ,GACd9Q,GAAI,0CAA4CkR,GAChD7H,GAAiB1G,KAAKwO,MAAMD,IAC5BlR,EAAI,OAGL,QAASqR,KAGR,MAAO9F,GAAM9J,KAAKI,MAAM,KAAK,KAAOyP,OAAO,EAAEC,QAAQ,GAGtD,QAASC,KACR,OAAQT,KACR,IAAK,QACJJ,GACA,MACD,KAAK,SACJC,GACA,MACD,KAAK,aACL,IAAK,eACJtH,GACA,MACD,KAAK,UACJ2H,GACA,MACD,KAAK,WACJG,GACA,MACD,SACMJ,KAAmBK,KACvBlR,EAAK,uBAAuBoL,EAAM9J,KAAK,MAK1C,QAASgQ,MACJ,IAAUhB,GACbe,IACUH,IACVf,IAEAtQ,EAAI,4BAA8B+Q,IAAmB,sCAInDV,KACHoB,IAMF,QAASC,KACL,YAAc9N,SAAS+N,YACzB7T,EAAO8T,OAAOxB,YAAY,4BAA4B,KA3jCxD,GACClO,IAAwB,EACxB4K,EAAwB,GACxBnM,EAAwB,GACxBmB,EAAwB,EACxBK,EAAwB,GACxB0D,EAAwB,KACxBjF,EAAwB,GACxBoB,IAAwB,EACxB6N,IAAyB7G,OAAS,EAAE6I,MAAQ,GAC5CjJ,GAAwB,IACxB6H,IAAwB,EACxBzM,GAAwB,EACxBmB,GAAwB,aACxB/C,GAAwB+C,GACxBuL,IAAwB,EACxBhP,GAAwB,GACxBP,MACAc,GAAwB,GACxBgE,GAAwB,KACxBhG,IAAwB,EACxBH,GAAwB,gBACxB8B,GAAwB9B,GAAM2N,OAC9B1N,GAAwB,GAExBsP,IAAyByC,IAAI,EAAEC,IAAI,EAAE9D,WAAW,EAAEE,sBAAsB,GACxE5L,GAAwB,QACxBkD,IAAwB,EACxB+B,GAAwB1J,EAAO8T,OAC/B9O,GAAwB,IACxBT,GAAwB,EACxBuN,IAAwB,EACxBG,GAAwB,KACxBxQ,GAAwB,GACxBsP,GAAwB,EACxBxJ,GAAwB,SACxB7C,GAAwB6C,GACxByD,GAAwBhL,EACxB+E,GAAwB,WAAY1C,EAAK,yCACzCmB,GAAwB,aACxB+H,GAAwB,aACxBhG,IACCW,OAAQ,WAEP,MADA7D,GAAK,kDACEyD,SAASG,gBAAgBiO,cAEjCnD,MAAO,WAEN,MADA1O,GAAK,iDACEyD,SAASC,KAAKoO,cA2EpB9S,GAAS+S,KAAK7S,KAAO,WAExB,OAAO,GAAI6S,OAAOC,WAgnBlB/M,IACC4I,WAAY,WACX,MAAQpK,UAASC,KAAKmO,aAAevF,EAAiB,aAAeA,EAAiB,iBAGvF2F,OAAQ,WACP,MAAOhN,IAAU4I,cAGlBC,WAAY,WACX,MAAOrK,UAASC,KAAKwO,cAGtBC,OAAQ,WACP,MAAOjP,IAAkBW,UAG1BkK,sBAAuB,WACtB,MAAOtK,UAASG,gBAAgBiO,cAGjC7D,sBAAuB,WACtB,MAAOvK,UAASG,gBAAgBsO,cAGjCP,IAAK,WACJ,MAAOzH,MAAKyH,IAAI1S,MAAM,KAAK0O,EAAmB1I,MAG/C2M,IAAK,WACJ,MAAO1H,MAAK0H,IAAI3S,MAAM,KAAK0O,EAAmB1I,MAG/CmN,KAAM,WACL,MAAOnN,IAAU0M,OAGlBU,cAAe,WACd,MAAOnI,MAAKyH,IAAI1M,GAAU4I,aAAcV,EAAc,SAASiB,OAGhEkE,cAAe,WACd,MAAOrE,GAAkB,SAAS,wBAIpC9I,IACC2I,WAAY,WACX,MAAOrK,UAASC,KAAKoO,aAGtBjE,WAAY,WACX,MAAOpK,UAASC,KAAK6O,aAGtBJ,OAAQ,WACP,MAAOjP,IAAkBwL,SAG1BV,sBAAuB,WACtB,MAAOvK,UAASG,gBAAgBkO,aAGjC/D,sBAAuB,WACtB,MAAOtK,UAASG,gBAAgB2O,aAGjCC,OAAQ,WACP,MAAOtI,MAAKyH,IAAIxM,GAAS2I,aAAc3I,GAAS6I,0BAGjD2D,IAAK,WACJ,MAAOzH,MAAKyH,IAAI1S,MAAM,KAAK0O,EAAmBxI,MAG/CyM,IAAK,WACJ,MAAO1H,MAAK0H,IAAI3S,MAAM,KAAK0O,EAAmBxI,MAG/CsN,iBAAkB,WACjB,MAAOtF,GAAc,QAASiB,MAG/BkE,cAAe,WACd,MAAOrE,GAAkB,QAAS,uBAwDjC0B,GAAsBlR,EAAS4P,EAsMnCxQ,GAAiBF,EAAQ,UAAW6H,GACpC+L,KAIE5T", "sourcesContent": ["/*\n * File: iframeResizer.contentWindow.js\n * Desc: Include this file in any page being loaded into an iframe\n *       to force the iframe to resize to the content size.\n * Requires: iframeResizer.js on host page.\n * Doc: https://github.com/davidjbradshaw/iframe-resizer\n * Author: <PERSON> - <EMAIL>\n * Contributor: <PERSON><PERSON> - <EMAIL>\n * Contributor: <PERSON>@hallnet.co.uk\n */\n\n\n;(function(window, undefined) {\n\t'use strict';\n\n\tvar\n\t\tautoResize            = true,\n\t\tbase                  = 10,\n\t\tbodyBackground        = '',\n\t\tbodyMargin            = 0,\n\t\tbodyMarginStr         = '',\n\t\tbodyObserver          = null,\n\t\tbodyPadding           = '',\n\t\tcalculateWidth        = false,\n\t\tdoubleEventList       = {'resize':1,'click':1},\n\t\teventCancelTimer      = 128,\n\t\tfirstRun              = true,\n\t\theight                = 1,\n\t\theightCalcModeDefault = 'bodyOffset',\n\t\theightCalcMode        = heightCalcModeDefault,\n\t\tinitLock              = true,\n\t\tinitMsg               = '',\n\t\tinPageLinks           = {},\n\t\tinterval              = 32,\n\t\tintervalTimer         = null,\n\t\tlogging               = false,\n\t\tmsgID                 = '[iFrameSizer]',  //Must match host page msg ID\n\t\tmsgIdLen              = msgID.length,\n\t\tmyID                  = '',\n\t\tobserver              = null,\n\t\tresetRequiredMethods  = {max:1,min:1,bodyScroll:1,documentElementScroll:1},\n\t\tresizeFrom            = 'child',\n\t\tsendPermit            = true,\n\t\ttarget                = window.parent,\n\t\ttargetOriginDefault   = '*',\n\t\ttolerance             = 0,\n\t\ttriggerLocked         = false,\n\t\ttriggerLockedTimer    = null,\n\t\tthrottledTimer        = 16,\n\t\twidth                 = 1,\n\t\twidthCalcModeDefault  = 'scroll',\n\t\twidthCalcMode         = widthCalcModeDefault,\n\t\twin                   = window,\n\t\tmessageCallback       = function(){ warn('MessageCallback function not defined'); },\n\t\treadyCallback         = function(){},\n\t\tpageInfoCallback      = function(){},\n\t\tcustomCalcMethods     = {\n\t\t\theight: function(){\n\t\t\t\twarn('Custom height calculation function not defined');\n\t\t\t\treturn document.documentElement.offsetHeight;\n\t\t\t}, \n\t\t\twidth: function(){\n\t\t\t\twarn('Custom width calculation function not defined');\n\t\t\t\treturn document.body.scrollWidth;\n\t\t\t}\n\t\t};\n\n\n\tfunction addEventListener(el,evt,func){\n\t\t/* istanbul ignore else */ // Not testable in phantonJS\n\t\tif ('addEventListener' in window){\n\t\t\tel.addEventListener(evt,func, false);\n\t\t} else if ('attachEvent' in window){ //IE\n\t\t\tel.attachEvent('on'+evt,func);\n\t\t}\n\t}\n\n\tfunction removeEventListener(el,evt,func){\n\t\t/* istanbul ignore else */ // Not testable in phantonJS\n\t\tif ('removeEventListener' in window){\n\t\t\tel.removeEventListener(evt,func, false);\n\t\t} else if ('detachEvent' in window){ //IE\n\t\t\tel.detachEvent('on'+evt,func);\n\t\t}\n\t}\n\n\tfunction capitalizeFirstLetter(string) {\n\t\treturn string.charAt(0).toUpperCase() + string.slice(1);\n\t}\n\n\t//Based on underscore.js\n\tfunction throttle(func) {\n\t\tvar\n\t\t\tcontext, args, result,\n\t\t\ttimeout = null,\n\t\t\tprevious = 0,\n\t\t\tlater = function() {\n\t\t\t\tprevious = getNow();\n\t\t\t\ttimeout = null;\n\t\t\t\tresult = func.apply(context, args);\n\t\t\t\tif (!timeout) {\n\t\t\t\t\tcontext = args = null;\n\t\t\t\t}\n\t\t\t};\n\n\t\treturn function() {\n\t\t\tvar now = getNow();\n\n\t\t\tif (!previous) {\n\t\t\t\tprevious = now;\n\t\t\t}\n\n\t\t\tvar remaining = throttledTimer - (now - previous);\n\n\t\t\tcontext = this;\n\t\t\targs = arguments;\n\n\t\t\tif (remaining <= 0 || remaining > throttledTimer) {\n\t\t\t\tif (timeout) {\n\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\ttimeout = null;\n\t\t\t\t}\n\n\t\t\t\tprevious = now;\n\t\t\t\tresult = func.apply(context, args);\n\n\t\t\t\tif (!timeout) {\n\t\t\t\t\tcontext = args = null;\n\t\t\t\t}\n\n\t\t\t} else if (!timeout) {\n\t\t\t\ttimeout = setTimeout(later, remaining);\n\t\t\t}\n\n\t\t\treturn result;\n\t\t};\n\t}\n\n\tvar getNow = Date.now || function() {\n\t\t/* istanbul ignore next */ // Not testable in PhantonJS\n\t\treturn new Date().getTime();\n\t};\n\n\tfunction formatLogMsg(msg){\n\t\treturn msgID + '[' + myID + ']' + ' ' + msg;\n\t}\n\n\tfunction log(msg){\n\t\tif (logging && ('object' === typeof window.console)){\n\t\t\tconsole.log(formatLogMsg(msg));\n\t\t}\n\t}\n\n\tfunction warn(msg){\n\t\tif ('object' === typeof window.console){\n\t\t\tconsole.warn(formatLogMsg(msg));\n\t\t}\n\t}\n\n\n\tfunction init(){\n\t\treadDataFromParent();\n\t\tlog('Initialising iFrame ('+location.href+')');\n\t\treadDataFromPage();\n\t\tsetMargin();\n\t\tsetBodyStyle('background',bodyBackground);\n\t\tsetBodyStyle('padding',bodyPadding);\n\t\tinjectClearFixIntoBodyElement();\n\t\tcheckHeightMode();\n\t\tcheckWidthMode();\n\t\tstopInfiniteResizingOfIFrame();\n\t\tsetupPublicMethods();\n\t\tstartEventListeners();\n\t\tinPageLinks = setupInPageLinks();\n\t\tsendSize('init','Init message from host page');\n\t\treadyCallback();\n\t}\n\n\tfunction readDataFromParent(){\n\n\t\tfunction strBool(str){\n\t\t\treturn 'true' === str ? true : false;\n\t\t}\n\n\t\tvar data = initMsg.substr(msgIdLen).split(':');\n\n\t\tmyID               = data[0];\n\t\tbodyMargin         = (undefined !== data[1]) ? Number(data[1])   : bodyMargin; //For V1 compatibility\n\t\tcalculateWidth     = (undefined !== data[2]) ? strBool(data[2])  : calculateWidth;\n\t\tlogging            = (undefined !== data[3]) ? strBool(data[3])  : logging;\n\t\tinterval           = (undefined !== data[4]) ? Number(data[4])   : interval;\n\t\tautoResize         = (undefined !== data[6]) ? strBool(data[6])  : autoResize;\n\t\tbodyMarginStr      = data[7];\n\t\theightCalcMode     = (undefined !== data[8]) ? data[8]           : heightCalcMode;\n\t\tbodyBackground     = data[9];\n\t\tbodyPadding        = data[10];\n\t\ttolerance          = (undefined !== data[11]) ? Number(data[11]) : tolerance;\n\t\tinPageLinks.enable = (undefined !== data[12]) ? strBool(data[12]): false;\n\t\tresizeFrom         = (undefined !== data[13]) ? data[13]         : resizeFrom;\n\t\twidthCalcMode      = (undefined !== data[14]) ? data[14]         : widthCalcMode;\n\t}\n\n\tfunction readDataFromPage(){\n\t\tfunction readData(){\n\t\t\tvar data = window.iFrameResizer;\n\n\t\t\tlog('Reading data from page: ' + JSON.stringify(data));\n\n\t\t\tmessageCallback     = ('messageCallback'         in data) ? data.messageCallback         : messageCallback;\n\t\t\treadyCallback       = ('readyCallback'           in data) ? data.readyCallback           : readyCallback;\n\t\t\ttargetOriginDefault = ('targetOrigin'            in data) ? data.targetOrigin            : targetOriginDefault;\n\t\t\theightCalcMode      = ('heightCalculationMethod' in data) ? data.heightCalculationMethod : heightCalcMode;\n\t\t\twidthCalcMode       = ('widthCalculationMethod'  in data) ? data.widthCalculationMethod  : widthCalcMode;\n\t\t}\n\n\t\tfunction setupCustomCalcMethods(calcMode, calcFunc){\n\t\t\tif ('function' === typeof calcMode) {\n\t\t\t\tlog('Setup custom ' + calcFunc + 'CalcMethod');\n\t\t\t\tcustomCalcMethods[calcFunc] = calcMode;\n\t\t\t\tcalcMode = 'custom';\n\t\t\t}\n\n\t\t\treturn calcMode;\n\t\t}\n\n\t\tif(('iFrameResizer' in window) && (Object === window.iFrameResizer.constructor)) {\n\t\t\treadData();\n\t\t\theightCalcMode = setupCustomCalcMethods(heightCalcMode, 'height');\n\t\t\twidthCalcMode  = setupCustomCalcMethods(widthCalcMode,  'width');\n\t\t}\n\n\t\tlog('TargetOrigin for parent set to: ' + targetOriginDefault);\n\t}\n\n\n\tfunction chkCSS(attr,value){\n\t\tif (-1 !== value.indexOf('-')){\n\t\t\twarn('Negative CSS value ignored for '+attr);\n\t\t\tvalue='';\n\t\t}\n\t\treturn value;\n\t}\n\n\tfunction setBodyStyle(attr,value){\n\t\tif ((undefined !== value) && ('' !== value) && ('null' !== value)){\n\t\t\tdocument.body.style[attr] = value;\n\t\t\tlog('Body '+attr+' set to \"'+value+'\"');\n\t\t}\n\t}\n\n\tfunction setMargin(){\n\t\t//If called via V1 script, convert bodyMargin from int to str\n\t\tif (undefined === bodyMarginStr){\n\t\t\tbodyMarginStr = bodyMargin+'px';\n\t\t}\n\n\t\tsetBodyStyle('margin',chkCSS('margin',bodyMarginStr));\n\t}\n\n\tfunction stopInfiniteResizingOfIFrame(){\n\t\tdocument.documentElement.style.height = '';\n\t\tdocument.body.style.height = '';\n\t\tlog('HTML & body height set to \"auto\"');\n\t}\n\n\n\tfunction manageTriggerEvent(options){\n\t\tfunction handleEvent(){\n\t\t\tsendSize(options.eventName,options.eventType);\n\t\t}\n\n\t\tvar listener = {\n\t\t\tadd:    function(eventName){\n\t\t\t\taddEventListener(window,eventName,handleEvent);\n\t\t\t},\n\t\t\tremove: function(eventName){\n\t\t\t\tremoveEventListener(window,eventName,handleEvent);\n\t\t\t}\n\t\t};\n\n\t\tif(options.eventNames && Array.prototype.map){\n\t\t\toptions.eventName = options.eventNames[0];\n\t\t\toptions.eventNames.map(listener[options.method]);\n\t\t} else {\n\t\t\tlistener[options.method](options.eventName);\n\t\t}\n\n\t\tlog(capitalizeFirstLetter(options.method) + ' event listener: ' + options.eventType);\n\t}\n\n\tfunction manageEventListeners(method){\n\t\tmanageTriggerEvent({method:method, eventType: 'Animation Start',           eventNames: ['animationstart','webkitAnimationStart'] });\n\t\tmanageTriggerEvent({method:method, eventType: 'Animation Iteration',       eventNames: ['animationiteration','webkitAnimationIteration'] });\n\t\tmanageTriggerEvent({method:method, eventType: 'Animation End',             eventNames: ['animationend','webkitAnimationEnd'] });\n\t\tmanageTriggerEvent({method:method, eventType: 'Input',                     eventName:  'input' });\n\t\tmanageTriggerEvent({method:method, eventType: 'Mouse Up',                  eventName:  'mouseup' });\n\t\tmanageTriggerEvent({method:method, eventType: 'Mouse Down',                eventName:  'mousedown' });\n\t\tmanageTriggerEvent({method:method, eventType: 'Orientation Change',        eventName:  'orientationchange' });\n\t\tmanageTriggerEvent({method:method, eventType: 'Print',                     eventName:  ['afterprint', 'beforeprint'] });\n\t\tmanageTriggerEvent({method:method, eventType: 'Ready State Change',        eventName:  'readystatechange' });\n\t\tmanageTriggerEvent({method:method, eventType: 'Touch Start',               eventName:  'touchstart' });\n\t\tmanageTriggerEvent({method:method, eventType: 'Touch End',                 eventName:  'touchend' });\n\t\tmanageTriggerEvent({method:method, eventType: 'Touch Cancel',              eventName:  'touchcancel' });\n\t\tmanageTriggerEvent({method:method, eventType: 'Transition Start',          eventNames: ['transitionstart','webkitTransitionStart','MSTransitionStart','oTransitionStart','otransitionstart'] });\n\t\tmanageTriggerEvent({method:method, eventType: 'Transition Iteration',      eventNames: ['transitioniteration','webkitTransitionIteration','MSTransitionIteration','oTransitionIteration','otransitioniteration'] });\n\t\tmanageTriggerEvent({method:method, eventType: 'Transition End',            eventNames: ['transitionend','webkitTransitionEnd','MSTransitionEnd','oTransitionEnd','otransitionend'] });\n\t\tif('child' === resizeFrom){\n\t\t\tmanageTriggerEvent({method:method, eventType: 'IFrame Resized',        eventName:  'resize' });\n\t\t}\n\t}\n\n\tfunction checkCalcMode(calcMode,calcModeDefault,modes,type){\n\t\tif (calcModeDefault !== calcMode){\n\t\t\tif (!(calcMode in modes)){\n\t\t\t\twarn(calcMode + ' is not a valid option for '+type+'CalculationMethod.');\n\t\t\t\tcalcMode=calcModeDefault;\n\t\t\t}\n\t\t\tlog(type+' calculation method set to \"'+calcMode+'\"');\n\t\t}\n\n\t\treturn calcMode;\n\t}\n\n\tfunction checkHeightMode(){\n\t\theightCalcMode = checkCalcMode(heightCalcMode,heightCalcModeDefault,getHeight,'height');\n\t}\n\n\tfunction checkWidthMode(){\n\t\twidthCalcMode = checkCalcMode(widthCalcMode,widthCalcModeDefault,getWidth,'width');\n\t}\n\n\tfunction startEventListeners(){\n\t\tif ( true === autoResize ) {\n\t\t\tmanageEventListeners('add');\n\t\t\tsetupMutationObserver();\n\t\t}\n\t\telse {\n\t\t\tlog('Auto Resize disabled');\n\t\t}\n\t}\n\n\tfunction stopMsgsToParent(){\n\t\tlog('Disable outgoing messages');\n\t\tsendPermit = false;\n\t}\n\n\tfunction removeMsgListener(){\n\t\tlog('Remove event listener: Message');\n\t\tremoveEventListener(window, 'message', receiver);\n\t}\n\n\tfunction disconnectMutationObserver(){\n\t\tif (null !== bodyObserver){\n\t\t\t/* istanbul ignore next */ // Not testable in PhantonJS\n\t\t\tbodyObserver.disconnect();\n\t\t}\n\t}\n\n\tfunction stopEventListeners(){\n\t\tmanageEventListeners('remove');\n\t\tdisconnectMutationObserver();\n\t\tclearInterval(intervalTimer);\n\t}\n\n\tfunction teardown(){\n\t\tstopMsgsToParent();\n\t\tremoveMsgListener();\n\t\tif (true === autoResize) stopEventListeners();\n\t}\n\n\tfunction injectClearFixIntoBodyElement(){\n\t\tvar clearFix = document.createElement('div');\n\t\tclearFix.style.clear   = 'both';\n\t\tclearFix.style.display = 'block'; //Guard against this having been globally redefined in CSS.\n\t\tdocument.body.appendChild(clearFix);\n\t}\n\n\tfunction setupInPageLinks(){\n\n\t\tfunction getPagePosition (){\n\t\t\treturn {\n\t\t\t\tx: (window.pageXOffset !== undefined) ? window.pageXOffset : document.documentElement.scrollLeft,\n\t\t\t\ty: (window.pageYOffset !== undefined) ? window.pageYOffset : document.documentElement.scrollTop\n\t\t\t};\n\t\t}\n\n\t\tfunction getElementPosition(el){\n\t\t\tvar\n\t\t\t\telPosition   = el.getBoundingClientRect(),\n\t\t\t\tpagePosition = getPagePosition();\n\n\t\t\treturn {\n\t\t\t\tx: parseInt(elPosition.left,10) + parseInt(pagePosition.x,10),\n\t\t\t\ty: parseInt(elPosition.top,10)  + parseInt(pagePosition.y,10)\n\t\t\t};\n\t\t}\n\n\t\tfunction findTarget(location){\n\t\t\tfunction jumpToTarget(target){\n\t\t\t\tvar jumpPosition = getElementPosition(target);\n\n\t\t\t\tlog('Moving to in page link (#'+hash+') at x: '+jumpPosition.x+' y: '+jumpPosition.y);\n\t\t\t\tsendMsg(jumpPosition.y, jumpPosition.x, 'scrollToOffset'); // X&Y reversed at sendMsg uses height/width\n\t\t\t}\n\n\t\t\tvar\n\t\t\t\thash     = location.split('#')[1] || location, //Remove # if present\n\t\t\t\thashData = decodeURIComponent(hash),\n\t\t\t\ttarget   = document.getElementById(hashData) || document.getElementsByName(hashData)[0];\n\n\t\t\tif (undefined !== target){\n\t\t\t\tjumpToTarget(target);\n\t\t\t} else {\n\t\t\t\tlog('In page link (#' + hash + ') not found in iFrame, so sending to parent');\n\t\t\t\tsendMsg(0,0,'inPageLink','#'+hash);\n\t\t\t}\n\t\t}\n\n\t\tfunction checkLocationHash(){\n\t\t\tif ('' !== location.hash && '#' !== location.hash){\n\t\t\t\tfindTarget(location.href);\n\t\t\t}\n\t\t}\n\n\t\tfunction bindAnchors(){\n\t\t\tfunction setupLink(el){\n\t\t\t\tfunction linkClicked(e){\n\t\t\t\t\te.preventDefault();\n\n\t\t\t\t\t/*jshint validthis:true */\n\t\t\t\t\tfindTarget(this.getAttribute('href'));\n\t\t\t\t}\n\n\t\t\t\tif ('#' !== el.getAttribute('href')){\n\t\t\t\t\taddEventListener(el,'click',linkClicked);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tArray.prototype.forEach.call( document.querySelectorAll( 'a[href^=\"#\"]' ), setupLink );\n\t\t}\n\n\t\tfunction bindLocationHash(){\n\t\t\taddEventListener(window,'hashchange',checkLocationHash);\n\t\t}\n\n\t\tfunction initCheck(){ //check if page loaded with location hash after init resize\n\t\t\tsetTimeout(checkLocationHash,eventCancelTimer);\n\t\t}\n\n\t\tfunction enableInPageLinks(){\n\t\t\t/* istanbul ignore else */ // Not testable in phantonJS\n\t\t\tif(Array.prototype.forEach && document.querySelectorAll){\n\t\t\t\tlog('Setting up location.hash handlers');\n\t\t\t\tbindAnchors();\n\t\t\t\tbindLocationHash();\n\t\t\t\tinitCheck();\n\t\t\t} else {\n\t\t\t\twarn('In page linking not fully supported in this browser! (See README.md for IE8 workaround)');\n\t\t\t}\n\t\t}\n\n\t\tif(inPageLinks.enable){\n\t\t\tenableInPageLinks();\n\t\t} else {\n\t\t\tlog('In page linking not enabled');\n\t\t}\n\n\t\treturn {\n\t\t\tfindTarget:findTarget\n\t\t};\n\t}\n\n\tfunction setupPublicMethods(){\n\t\tlog('Enable public methods');\n\n\t\twin.parentIFrame = {\n\n\t\t\tautoResize: function autoResizeF(resize){\n\t\t\t\tif (true === resize && false === autoResize) {\n\t\t\t\t\tautoResize=true;\n\t\t\t\t\tstartEventListeners();\n\t\t\t\t\t//sendSize('autoResize','Auto Resize enabled');\n\t\t\t\t} else if (false === resize && true === autoResize) {\n\t\t\t\t\tautoResize=false;\n\t\t\t\t\tstopEventListeners();\n\t\t\t\t}\n\n\t\t\t\treturn autoResize;\n\t\t\t},\n\n\t\t\tclose: function closeF(){\n\t\t\t\tsendMsg(0,0,'close');\n\t\t\t\tteardown();\n\t\t\t},\n\n\t\t\tgetId: function getIdF(){\n\t\t\t\treturn myID;\n\t\t\t},\n\n\t\t\tgetPageInfo: function getPageInfoF(callback){\n\t\t\t\tif ('function' === typeof callback){\n\t\t\t\t\tpageInfoCallback = callback;\n\t\t\t\t\tsendMsg(0,0,'pageInfo');\n\t\t\t\t} else {\n\t\t\t\t\tpageInfoCallback = function(){};\n\t\t\t\t\tsendMsg(0,0,'pageInfoStop');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tmoveToAnchor: function moveToAnchorF(hash){\n\t\t\t\tinPageLinks.findTarget(hash);\n\t\t\t},\n\n\t\t\treset: function resetF(){\n\t\t\t\tresetIFrame('parentIFrame.reset');\n\t\t\t},\n\n\t\t\tscrollTo: function scrollToF(x,y){\n\t\t\t\tsendMsg(y,x,'scrollTo'); // X&Y reversed at sendMsg uses height/width\n\t\t\t},\n\n\t\t\tscrollToOffset: function scrollToF(x,y){\n\t\t\t\tsendMsg(y,x,'scrollToOffset'); // X&Y reversed at sendMsg uses height/width\n\t\t\t},\n\n\t\t\tsendMessage: function sendMessageF(msg,targetOrigin){\n\t\t\t\tsendMsg(0,0,'message',JSON.stringify(msg),targetOrigin);\n\t\t\t},\n\n\t\t\tsetHeightCalculationMethod: function setHeightCalculationMethodF(heightCalculationMethod){\n\t\t\t\theightCalcMode = heightCalculationMethod;\n\t\t\t\tcheckHeightMode();\n\t\t\t},\n\n\t\t\tsetWidthCalculationMethod: function setWidthCalculationMethodF(widthCalculationMethod){\n\t\t\t\twidthCalcMode = widthCalculationMethod;\n\t\t\t\tcheckWidthMode();\n\t\t\t},\n\n\t\t\tsetTargetOrigin: function setTargetOriginF(targetOrigin){\n\t\t\t\tlog('Set targetOrigin: '+targetOrigin);\n\t\t\t\ttargetOriginDefault = targetOrigin;\n\t\t\t},\n\n\t\t\tsize: function sizeF(customHeight, customWidth){\n\t\t\t\tvar valString = ''+(customHeight?customHeight:'')+(customWidth?','+customWidth:'');\n\t\t\t\t//lockTrigger();\n\t\t\t\tsendSize('size','parentIFrame.size('+valString+')', customHeight, customWidth);\n\t\t\t}\n\t\t};\n\t}\n\n\tfunction initInterval(){\n\t\tif ( 0 !== interval ){\n\t\t\tlog('setInterval: '+interval+'ms');\n\t\t\tintervalTimer = setInterval(function(){\n\t\t\t\tsendSize('interval','setInterval: '+interval);\n\t\t\t},Math.abs(interval));\n\t\t}\n\t}\n\n\t/* istanbul ignore next */  //Not testable in PhantomJS\n\tfunction setupBodyMutationObserver(){\n\t\tfunction addImageLoadListners(mutation) {\n\t\t\tfunction addImageLoadListener(element){\n\t\t\t\tif (false === element.complete) {\n\t\t\t\t\tlog('Attach listeners to ' + element.src);\n\t\t\t\t\telement.addEventListener('load', imageLoaded, false);\n\t\t\t\t\telement.addEventListener('error', imageError, false);\n\t\t\t\t\telements.push(element);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (mutation.type === 'attributes' && mutation.attributeName === 'src'){\n\t\t\t\taddImageLoadListener(mutation.target);\n\t\t\t} else if (mutation.type === 'childList'){\n\t\t\t\tArray.prototype.forEach.call(\n\t\t\t\t\tmutation.target.querySelectorAll('img'),\n\t\t\t\t\taddImageLoadListener\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tfunction removeFromArray(element){\n\t\t\telements.splice(elements.indexOf(element),1);\n\t\t}\n\n\t\tfunction removeImageLoadListener(element){\n\t\t\tlog('Remove listeners from ' + element.src);\n\t\t\telement.removeEventListener('load', imageLoaded, false);\n\t\t\telement.removeEventListener('error', imageError, false);\n\t\t\tremoveFromArray(element);\n\t\t}\n\n\t\tfunction imageEventTriggered(event,type,typeDesc){\n\t\t\tremoveImageLoadListener(event.target);\n\t\t\tsendSize(type, typeDesc + ': ' + event.target.src, undefined, undefined);\n\t\t}\n\n\t\tfunction imageLoaded(event) {\n\t\t\timageEventTriggered(event,'imageLoad','Image loaded');\n\t\t}\n\n\t\tfunction imageError(event) {\n\t\t\timageEventTriggered(event,'imageLoadFailed','Image load failed');\n\t\t}\n\n\t\tfunction mutationObserved(mutations) {\n\t\t\tsendSize('mutationObserver','mutationObserver: ' + mutations[0].target + ' ' + mutations[0].type);\n\n\t\t\t//Deal with WebKit asyncing image loading when tags are injected into the page\n\t\t\tmutations.forEach(addImageLoadListners);\n\t\t}\n\n\t\tfunction createMutationObserver(){\n\t\t\tvar\n\t\t\t\ttarget = document.querySelector('body'),\n\n\t\t\t\tconfig = {\n\t\t\t\t\tattributes            : true,\n\t\t\t\t\tattributeOldValue     : false,\n\t\t\t\t\tcharacterData         : true,\n\t\t\t\t\tcharacterDataOldValue : false,\n\t\t\t\t\tchildList             : true,\n\t\t\t\t\tsubtree               : true\n\t\t\t\t};\n\n\t\t\tobserver = new MutationObserver(mutationObserved);\n\n\t\t\tlog('Create body MutationObserver');\n\t\t\tobserver.observe(target, config);\n\n\t\t\treturn observer;\n\t\t}\n\n\t\tvar\n\t\t\telements         = [],\n\t\t\tMutationObserver = window.MutationObserver || window.WebKitMutationObserver,\n\t\t\tobserver         = createMutationObserver();\n\n\t\treturn {\n\t\t\tdisconnect: function (){\n\t\t\t\tif ('disconnect' in observer){\n\t\t\t\t\tlog('Disconnect body MutationObserver');\n\t\t\t\t\tobserver.disconnect();\n\t\t\t\t\telements.forEach(removeImageLoadListener);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t}\n\n\tfunction setupMutationObserver(){\n\t\tvar\tforceIntervalTimer = 0 > interval;\n\n\t\t/* istanbul ignore if */ // Not testable in PhantomJS\n\t\tif (window.MutationObserver || window.WebKitMutationObserver){\n\t\t\tif (forceIntervalTimer) {\n\t\t\t\tinitInterval();\n\t\t\t} else {\n\t\t\t\tbodyObserver = setupBodyMutationObserver();\n\t\t\t}\n\t\t} else {\n\t\t\tlog('MutationObserver not supported in this browser!');\n\t\t\tinitInterval();\n\t\t}\n\t}\n\n\n\t// document.documentElement.offsetHeight is not reliable, so\n\t// we have to jump through hoops to get a better value.\n\tfunction getComputedStyle(prop,el) {\n\t\t/* istanbul ignore next */  //Not testable in PhantomJS\n\t\tfunction convertUnitsToPxForIE8(value) {\n\t\t\tvar PIXEL = /^\\d+(px)?$/i;\n\n\t\t\tif (PIXEL.test(value)) {\n\t\t\t\treturn parseInt(value,base);\n\t\t\t}\n\n\t\t\tvar\n\t\t\t\tstyle = el.style.left,\n\t\t\t\truntimeStyle = el.runtimeStyle.left;\n\n\t\t\tel.runtimeStyle.left = el.currentStyle.left;\n\t\t\tel.style.left = value || 0;\n\t\t\tvalue = el.style.pixelLeft;\n\t\t\tel.style.left = style;\n\t\t\tel.runtimeStyle.left = runtimeStyle;\n\n\t\t\treturn value;\n\t\t}\n\n\t\tvar retVal = 0;\n\t\tel =  el || document.body;\n\n\t\t/* istanbul ignore else */ // Not testable in phantonJS\n\t\tif (('defaultView' in document) && ('getComputedStyle' in document.defaultView)) {\n\t\t\tretVal = document.defaultView.getComputedStyle(el, null);\n\t\t\tretVal = (null !== retVal) ? retVal[prop] : 0;\n\t\t} else {//IE8\n\t\t\tretVal =  convertUnitsToPxForIE8(el.currentStyle[prop]);\n\t\t}\n\n\t\treturn parseInt(retVal,base);\n\t}\n\n\tfunction chkEventThottle(timer){\n\t\tif(timer > throttledTimer/2){\n\t\t\tthrottledTimer = 2*timer;\n\t\t\tlog('Event throttle increased to ' + throttledTimer + 'ms');\n\t\t}\n\t}\n\n\t//Idea from https://github.com/guardian/iframe-messenger\n\tfunction getMaxElement(side,elements) {\n\t\tvar\n\t\t\telementsLength = elements.length,\n\t\t\telVal          = 0,\n\t\t\tmaxVal         = 0,\n\t\t\tSide           = capitalizeFirstLetter(side),\n\t\t\ttimer          = getNow();\n\n\t\tfor (var i = 0; i < elementsLength; i++) {\n\t\t\telVal = elements[i].getBoundingClientRect()[side] + getComputedStyle('margin'+Side,elements[i]);\n\t\t\tif (elVal > maxVal) {\n\t\t\t\tmaxVal = elVal;\n\t\t\t}\n\t\t}\n\n\t\ttimer = getNow() - timer;\n\n\t\tlog('Parsed '+elementsLength+' HTML elements');\n\t\tlog('Element position calculated in ' + timer + 'ms');\n\n\t\tchkEventThottle(timer);\n\n\t\treturn maxVal;\n\t}\n\n\tfunction getAllMeasurements(dimention){\n\t\treturn [\n\t\t\tdimention.bodyOffset(),\n\t\t\tdimention.bodyScroll(),\n\t\t\tdimention.documentElementOffset(),\n\t\t\tdimention.documentElementScroll()\n\t\t];\n\t}\n\n\tfunction getTaggedElements(side,tag){\n\t\tfunction noTaggedElementsFound(){\n\t\t\twarn('No tagged elements ('+tag+') found on page');\n\t\t\treturn height; //current height\n\t\t}\n\n\t\tvar elements = document.querySelectorAll('['+tag+']');\n\n\t\treturn 0 === elements.length ?  noTaggedElementsFound() : getMaxElement(side,elements);\n\t}\n\n\tfunction getAllElements(){\n\t\treturn document.querySelectorAll('body *');\n\t}\n\n\tvar\n\t\tgetHeight = {\n\t\t\tbodyOffset: function getBodyOffsetHeight(){\n\t\t\t\treturn  document.body.offsetHeight + getComputedStyle('marginTop') + getComputedStyle('marginBottom');\n\t\t\t},\n\n\t\t\toffset: function(){\n\t\t\t\treturn getHeight.bodyOffset(); //Backwards compatability\n\t\t\t},\n\n\t\t\tbodyScroll: function getBodyScrollHeight(){\n\t\t\t\treturn document.body.scrollHeight;\n\t\t\t},\n\n\t\t\tcustom: function getCustomWidth(){\n\t\t\t\treturn customCalcMethods.height();\n\t\t\t},\n\n\t\t\tdocumentElementOffset: function getDEOffsetHeight(){\n\t\t\t\treturn document.documentElement.offsetHeight;\n\t\t\t},\n\n\t\t\tdocumentElementScroll: function getDEScrollHeight(){\n\t\t\t\treturn document.documentElement.scrollHeight;\n\t\t\t},\n\n\t\t\tmax: function getMaxHeight(){\n\t\t\t\treturn Math.max.apply(null,getAllMeasurements(getHeight));\n\t\t\t},\n\n\t\t\tmin: function getMinHeight(){\n\t\t\t\treturn Math.min.apply(null,getAllMeasurements(getHeight));\n\t\t\t},\n\n\t\t\tgrow: function growHeight(){\n\t\t\t\treturn getHeight.max(); //Run max without the forced downsizing\n\t\t\t},\n\n\t\t\tlowestElement: function getBestHeight(){\n\t\t\t\treturn Math.max(getHeight.bodyOffset(), getMaxElement('bottom',getAllElements()));\n\t\t\t},\n\n\t\t\ttaggedElement: function getTaggedElementsHeight(){\n\t\t\t\treturn getTaggedElements('bottom','data-iframe-height');\n\t\t\t}\n\t\t},\n\n\t\tgetWidth = {\n\t\t\tbodyScroll: function getBodyScrollWidth(){\n\t\t\t\treturn document.body.scrollWidth;\n\t\t\t},\n\n\t\t\tbodyOffset: function getBodyOffsetWidth(){\n\t\t\t\treturn document.body.offsetWidth;\n\t\t\t},\n\n\t\t\tcustom: function getCustomWidth(){\n\t\t\t\treturn customCalcMethods.width();\n\t\t\t},\n\n\t\t\tdocumentElementScroll: function getDEScrollWidth(){\n\t\t\t\treturn document.documentElement.scrollWidth;\n\t\t\t},\n\n\t\t\tdocumentElementOffset: function getDEOffsetWidth(){\n\t\t\t\treturn document.documentElement.offsetWidth;\n\t\t\t},\n\n\t\t\tscroll: function getMaxWidth(){\n\t\t\t\treturn Math.max(getWidth.bodyScroll(), getWidth.documentElementScroll());\n\t\t\t},\n\n\t\t\tmax: function getMaxWidth(){\n\t\t\t\treturn Math.max.apply(null,getAllMeasurements(getWidth));\n\t\t\t},\n\n\t\t\tmin: function getMinWidth(){\n\t\t\t\treturn Math.min.apply(null,getAllMeasurements(getWidth));\n\t\t\t},\n\n\t\t\trightMostElement: function rightMostElement(){\n\t\t\t\treturn getMaxElement('right', getAllElements());\n\t\t\t},\n\n\t\t\ttaggedElement: function getTaggedElementsWidth(){\n\t\t\t\treturn getTaggedElements('right', 'data-iframe-width');\n\t\t\t}\n\t\t};\n\n\n\tfunction sizeIFrame(triggerEvent, triggerEventDesc, customHeight, customWidth){\n\n\t\tfunction resizeIFrame(){\n\t\t\theight = currentHeight;\n\t\t\twidth  = currentWidth;\n\n\t\t\tsendMsg(height,width,triggerEvent);\n\t\t}\n\n\t\tfunction isSizeChangeDetected(){\n\t\t\tfunction checkTolarance(a,b){\n\t\t\t\tvar retVal = Math.abs(a-b) <= tolerance;\n\t\t\t\treturn !retVal;\n\t\t\t}\n\n\t\t\tcurrentHeight = (undefined !== customHeight)  ? customHeight : getHeight[heightCalcMode]();\n\t\t\tcurrentWidth  = (undefined !== customWidth )  ? customWidth  : getWidth[widthCalcMode]();\n\n\t\t\treturn\tcheckTolarance(height,currentHeight) || (calculateWidth && checkTolarance(width,currentWidth));\n\t\t}\n\n\t\tfunction isForceResizableEvent(){\n\t\t\treturn !(triggerEvent in {'init':1,'interval':1,'size':1});\n\t\t}\n\n\t\tfunction isForceResizableCalcMode(){\n\t\t\treturn (heightCalcMode in resetRequiredMethods) || (calculateWidth && widthCalcMode in resetRequiredMethods);\n\t\t}\n\n\t\tfunction logIgnored(){\n\t\t\tlog('No change in size detected');\n\t\t}\n\n\t\tfunction checkDownSizing(){\n\t\t\tif (isForceResizableEvent() && isForceResizableCalcMode()){\n\t\t\t\tresetIFrame(triggerEventDesc);\n\t\t\t} else if (!(triggerEvent in {'interval':1})){\n\t\t\t\tlogIgnored();\n\t\t\t}\n\t\t}\n\n\t\tvar\tcurrentHeight,currentWidth;\n\n\t\tif (isSizeChangeDetected() || 'init' === triggerEvent){\n\t\t\tlockTrigger();\n\t\t\tresizeIFrame();\n\t\t} else {\n\t\t\tcheckDownSizing();\n\t\t}\n\t}\n\n\tvar sizeIFrameThrottled = throttle(sizeIFrame);\n\n\tfunction sendSize(triggerEvent, triggerEventDesc, customHeight, customWidth){\n\t\tfunction recordTrigger(){\n\t\t\tif (!(triggerEvent in {'reset':1,'resetPage':1,'init':1})){\n\t\t\t\tlog( 'Trigger event: ' + triggerEventDesc );\n\t\t\t}\n\t\t}\n\n\t\tfunction isDoubleFiredEvent(){\n\t\t\treturn  triggerLocked && (triggerEvent in doubleEventList);\n\t\t}\n\n\t\tif (!isDoubleFiredEvent()){\n\t\t\trecordTrigger();\n\t\t\tsizeIFrameThrottled(triggerEvent, triggerEventDesc, customHeight, customWidth);\n\t\t} else {\n\t\t\tlog('Trigger event cancelled: '+triggerEvent);\n\t\t}\n\t}\n\n\tfunction lockTrigger(){\n\t\tif (!triggerLocked){\n\t\t\ttriggerLocked = true;\n\t\t\tlog('Trigger event lock on');\n\t\t}\n\t\tclearTimeout(triggerLockedTimer);\n\t\ttriggerLockedTimer = setTimeout(function(){\n\t\t\ttriggerLocked = false;\n\t\t\tlog('Trigger event lock off');\n\t\t\tlog('--');\n\t\t},eventCancelTimer);\n\t}\n\n\tfunction triggerReset(triggerEvent){\n\t\theight = getHeight[heightCalcMode]();\n\t\twidth  = getWidth[widthCalcMode]();\n\n\t\tsendMsg(height,width,triggerEvent);\n\t}\n\n\tfunction resetIFrame(triggerEventDesc){\n\t\tvar hcm = heightCalcMode;\n\t\theightCalcMode = heightCalcModeDefault;\n\n\t\tlog('Reset trigger event: ' + triggerEventDesc);\n\t\tlockTrigger();\n\t\ttriggerReset('reset');\n\n\t\theightCalcMode = hcm;\n\t}\n\n\tfunction sendMsg(height,width,triggerEvent,msg,targetOrigin){\n\t\tfunction setTargetOrigin(){\n\t\t\tif (undefined === targetOrigin){\n\t\t\t\ttargetOrigin = targetOriginDefault;\n\t\t\t} else {\n\t\t\t\tlog('Message targetOrigin: '+targetOrigin);\n\t\t\t}\n\t\t}\n\n\t\tfunction sendToParent(){\n\t\t\tvar\n\t\t\t\tsize  = height + ':' + width,\n\t\t\t\tmessage = myID + ':' +  size + ':' + triggerEvent + (undefined !== msg ? ':' + msg : '');\n\n\t\t\tlog('Sending message to host page (' + message + ')');\n\t\t\ttarget.postMessage( msgID + message, targetOrigin);\n\t\t}\n\n\t\tif(true === sendPermit){\n\t\t\tsetTargetOrigin();\n\t\t\tsendToParent();\n\t\t}\n\t}\n\n\tfunction receiver(event) {\n\t\tfunction isMessageForUs(){\n\t\t\treturn msgID === (''+event.data).substr(0,msgIdLen); //''+ Protects against non-string messages\n\t\t}\n\n\t\tfunction initFromParent(){\n\t\t\tfunction fireInit(){\n\t\t\t\tinitMsg = event.data;\n\t\t\t\ttarget  = event.source;\n\n\t\t\t\tinit();\n\t\t\t\tfirstRun = false;\n\t\t\t\tsetTimeout(function(){ initLock = false;},eventCancelTimer);\n\t\t\t}\n\n\t\t\tif (document.body){\n\t\t\t\tfireInit();\n\t\t\t} else {\n\t\t\t\tlog('Waiting for page ready');\n\t\t\t\taddEventListener(window,'readystatechange',initFromParent);\n\t\t\t}\n\t\t}\n\n\t\tfunction resetFromParent(){\n\t\t\tif (!initLock){\n\t\t\t\tlog('Page size reset by host page');\n\t\t\t\ttriggerReset('resetPage');\n\t\t\t} else {\n\t\t\t\tlog('Page reset ignored by init');\n\t\t\t}\n\t\t}\n\n\t\tfunction resizeFromParent(){\n\t\t\tsendSize('resizeParent','Parent window requested size check');\n\t\t}\n\n\t\tfunction moveToAnchor(){\n\t\t\tvar anchor = getData();\n\t\t\tinPageLinks.findTarget(anchor);\n\t\t}\n\n\t\tfunction getMessageType(){\n\t\t\treturn event.data.split(']')[1].split(':')[0];\n\t\t}\n\n\t\tfunction getData(){\n\t\t\treturn event.data.substr(event.data.indexOf(':')+1);\n\t\t}\n\n\t\tfunction isMiddleTier(){\n\t\t\treturn ('iFrameResize' in window);\n\t\t}\n\n\t\tfunction messageFromParent(){\n\t\t\tvar msgBody = getData();\n\n\t\t\tlog('MessageCallback called from parent: ' + msgBody );\n\t\t\tmessageCallback(JSON.parse(msgBody));\n\t\t\tlog(' --');\n\t\t}\n\n\t\tfunction pageInfoFromParent(){\n\t\t\tvar msgBody = getData();\n\t\t\tlog('PageInfoFromParent called from parent: ' + msgBody );\n\t\t\tpageInfoCallback(JSON.parse(msgBody));\n\t\t\tlog(' --');\n\t\t}\n\n\t\tfunction isInitMsg(){\n\t\t\t//Test if this message is from a child below us. This is an ugly test, however, updating\n\t\t\t//the message format would break backwards compatibity.\n\t\t\treturn event.data.split(':')[2] in {'true':1,'false':1};\n\t\t}\n\n\t\tfunction callFromParent(){\n\t\t\tswitch (getMessageType()){\n\t\t\tcase 'reset':\n\t\t\t\tresetFromParent();\n\t\t\t\tbreak;\n\t\t\tcase 'resize':\n\t\t\t\tresizeFromParent();\n\t\t\t\tbreak;\n\t\t\tcase 'inPageLink':\n\t\t\tcase 'moveToAnchor':\n\t\t\t\tmoveToAnchor();\n\t\t\t\tbreak;\n\t\t\tcase 'message':\n\t\t\t\tmessageFromParent();\n\t\t\t\tbreak;\n\t\t\tcase 'pageInfo':\n\t\t\t\tpageInfoFromParent();\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tif (!isMiddleTier() && !isInitMsg()){\n\t\t\t\t\twarn('Unexpected message ('+event.data+')');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tfunction processMessage(){\n\t\t\tif (false === firstRun) {\n\t\t\t\tcallFromParent();\n\t\t\t} else if (isInitMsg()) {\n\t\t\t\tinitFromParent();\n\t\t\t} else {\n\t\t\t\tlog('Ignored message of type \"' + getMessageType() + '\". Received before initialization.');\n\t\t\t}\n\t\t}\n\n\t\tif (isMessageForUs()){\n\t\t\tprocessMessage();\n\t\t}\n\t}\n\n\t//Normally the parent kicks things off when it detects the iFrame has loaded.\n\t//If this script is async-loaded, then tell parent page to retry init.\n\tfunction chkLateLoaded(){\n\t\tif('loading' !== document.readyState){\n\t\t\twindow.parent.postMessage('[iFrameResizerChild]Ready','*');\n\t\t}\n\t}\n\n\taddEventListener(window, 'message', receiver);\n\tchkLateLoaded();\n\n\t\n\n})(window || {});\n"]}