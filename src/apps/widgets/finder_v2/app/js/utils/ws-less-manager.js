(function() {
var app = window.App || angular.module('wsLessManager', []);
app.factory('LessManager', [
    '$q',
    function($q) {
        var LessManager = function(type) {
            if (type) {
                this.type = type;
            }
            this.refresh();
        };
        LessManager.prototype = {
            type: 'text/less',
            refresh: function (modifyVars, styleName, source) {
                var q = $q.defer();
                var self = this, styles;
                styles = angular.element('style[type="' + this.type + '"]');
                if (styleName) {
                    styles = styles.filter('[data-name="' + styleName + '"]');
                }

                styles.each(function() {
                    var style = angular.element(this);
                    var name = style.attr('data-name');
                    var lessText = styleName ? source : style.html();
                    var options = angular.extend({}, less.options);
                    if (modifyVars) {
                        options.modifyVars = modifyVars;
                        less.render(lessText, options, function(error, output) {
                            if (error) {
                                q.reject(error);
                            } else {
                                self.refreshStyleTag(name, output);
                                q.resolve();
                            }
                        });
                    } else {
                        less.render(lessText, options).then(function(output) {
                            self.refreshStyleTag(name, output);
                            q.resolve();
                        }, function(error) {
                            q.reject(error);
                        });
                    }
                });

                return q.promise;
            },

            getOrCreateStyleTag: function(name) {
                var style = angular.element('style[type="text/css"][data-name="' + name + '"]');
                if (!style.length) {
                    style = angular.element('<style type="text/css" data-name="' + name +'"></style>');
                    style.appendTo('head');
                }
                return style;
            },

            refreshStyleTag: function(name, output) {
                var style = this.getOrCreateStyleTag(name);
                style.html(output.css);
            }
        };

        return LessManager;
    }
]);

if (window.App) {
    app.controller('LessManagerCtrl', [
        '$scope', 'WheelSizeMessages', 'LessManager',
        function($scope, WheelSizeMessages, LessManager) {
            $scope.lessManager = new LessManager();

            WheelSizeMessages.on('less:update', function (e) {
                $scope.lessManager.refresh(e.data.vars, e.data.name, e.data.source);
            });
        }
    ]);
}
}());
