String.prototype.format = function(pattern) {
    var len = arguments.length + 1, arg, safe;
    var str = this;

    // For each {0} {1} {n...} replace with the argument in that position.  If
    // the argument is an object or an array it will be stringified to JSON.
    for (var i = 0; i < len; arg = arguments[i++]) {
        str = str.replace(RegExp('\\{' + (i - 1) +'\\}', 'g'), arg);
    }
    return str;
};
