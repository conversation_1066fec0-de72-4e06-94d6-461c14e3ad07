angular.module('wsMessages', [])
.factory('WheelSizeMessages', [
    '$window', '$document',
    function($window, $document) {
        var getUrlParser = function(url) {
            var parser = document.createElement('a');
            parser.href = url;
            return parser;
        };

        var InternalEvents = function() {};
        MicroEvent.mixin(InternalEvents);
        var internalEvents = new InternalEvents();

        var WheelSizeMessages = function() {
            this.accept = getUrlParser($window.document.referrer).origin;

            var self = this;
            function onmessage(event) {
                if (self.accept !== event.origin) {
                    return;
                }
                self.trigger(event.data.type, event.data);
            }

            if ($window.addEventListener) {
                $window.addEventListener('message', onmessage, false);
            } else {
                $window.attachEvent('onmessage', onmessage);
            }
        };
        WheelSizeMessages.prototype = {
            beforeSend: function(callback) {
                internalEvents.bind('before', callback);
            },

            afterSend: function(callback) {
                internalEvents.bind('after', callback);
            },

            send: function(type, data) {
                var event = {
                    src: $window.location.href,
                    type: type,
                    data: data
                };

                internalEvents.trigger('before', event);
                $window.parent.postMessage(event, '*');
                internalEvents.trigger('after', event);
            },

            on: function(type, callback) {
                this.bind(type, callback);
            },
            off: function(type, callback) {
                this.unbind(type, callback);
            }
        };
        MicroEvent.mixin(WheelSizeMessages);

        var wsMessages = $window.WheelSizeMessages = new WheelSizeMessages();
        $document.ready(function() {
            wsMessages.send('ready:document', {});
        });
        angular.element($window).load(function() {
            wsMessages.send('ready:window', {});
        });
        return wsMessages;
    }
]);
