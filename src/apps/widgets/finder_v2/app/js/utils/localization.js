angular.module('wsLocalize', [])

.factory('localize', [
    '$window',
    function($window) {
        var localizationDict = {};
        if ($window.wsConfig) {
            localizationDict = wsConfig.translation || {};
        }
        var localize = {
            dictionary: localizationDict,

            updateDict: function(dict) {
                angular.extend(localize.dictionary, dict);
            },

            getTranslationValue: function(value) {
                var translated = localize.dictionary[value];
                if (translated == null) {
                    localize.dictionary[value] = null;
                    return value;
                }
                return translated;
            },

            translate: function(value, placeholders) {
                var translated = localize.getTranslationValue(value);
                return placeholders.length === 0 ?
                        translated : String.prototype.format.apply(translated, placeholders);
            },

            getLocalizedString: function(value) {
                var placeholders = Array.prototype.slice.call(arguments, 1);
                return localize.translate(value, placeholders);
            }
        };

        $window.getLocalizationDictionary = function() {
            return JSON.stringify(localize.dictionary, null, 4);
        };

        return localize.getLocalizedString;
    }
])

.filter('i18n', [
    'localize',
    function(localize) {
        return function(input) {
            return localize(input || '');
        };
    }
]);