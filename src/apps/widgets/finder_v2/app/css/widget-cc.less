.colorpicker-preview {
  background-color: white;

  i {
    display: block;
    width: 20px;
    height: 20px;
  }
}

input[colorpicker] {
  font-family: "Courier New", Monaco, monospace;
  font-size: 12px;
}

.makes-filter {
  .tag-cloud {
    span {
      opacity: 0.6;
      margin: 2px;

      &.active {
        opacity: 1;
        font-weight: bold;
        background-color: #dedede;
      }
    }
  }
}

p.note {
  border-left: 4px solid #be3e1d;
  padding: 5px 0 5px 15px;
  font-size: 16px;
  font-style: italic;
}

.submit-error-text {
  margin: 0 0 0 20px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-20 {
  margin-top: 20px;
}

.btn.color-theme-btn {
  &.active {
    font-weight: bold;
  }

  span {
    color: #be3e1d;
    font-size: 14px;
  }
}

.less-advanced {
  .django-ace-toolbar {
    display: none;
  }
}

.iframe-preview .title-divider span.pull-right {
  padding: 0 0 0 .5em;

  a {
    color: #333;
    text-decoration: none;
  }
}

.domain-wrapper {
  position: relative;

  .remove-button {
    position: absolute;
    left: 102%;
    top: 50%;
    margin-top: -10px;
    font-size: 20px;
    text-decoration: none;
    color: #ccc;

    &:hover {
      color: inherit;
    }
  }
}

.ng-cloak {
  display: none !important;
}

.submit-row {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .payment-item .help {
    text-decoration: none;
    margin: 0 5px;
    font-size: 18px;
  }
}


