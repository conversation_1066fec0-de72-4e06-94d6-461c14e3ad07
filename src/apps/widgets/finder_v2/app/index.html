<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Finder-v2 Widget Development</title>
  </head>
  <body>
    <div class="container mx-auto p-8">
      <h1 class="text-2xl font-bold mb-6">Finder-v2 Widget Development</h1>
      
      <!-- Mock widget configuration -->
      <script>
        window.FinderV2Config = {
          widgetResources: {
            make: ['Make', '/api/makes/'],
            model: ['Model', '/api/models/'],
            year: ['Year', '/api/years/'],
            modification: ['Modification', '/api/modifications/'],
            generation: ['Generation', '/api/generations/'],
            search_by_model: ['', '/api/search/by_model/'],
            tire_width: ['Tire Width', '/api/tire-widths/'],
            aspect_ratio: ['Aspect Ratio', '/api/aspect-ratios/'],
            rim_diameter: ['Rim Diameter', '/api/rim-diameters/'],
            search_by_tire: ['', '/api/search/by_tire/'],
            rim_width: ['Rim Width', '/api/rim-widths/'],
            bolt_pattern: ['Bolt Pattern', '/api/bolt-patterns/'],
            search_by_rim: ['', '/api/search/by_rim/']
          },
          flowType: 'primary',
          apiVersion: 'v2',
          csrfToken: 'mock-csrf-token',
          widgetConfig: {
            markets: [],
            utm: '?utm_campaign=widget&utm_medium=web&utm_source=dev',
            slug: 'dev-widget',
            flowType: 'primary',
            apiVersion: 'v2',
            tabs: {
              visible: ['by_vehicle', 'by_tire', 'by_rim'],
              primary: 'by_vehicle'
            },
            blocks: {
              button_to_ws: {
                hide: false
              }
            }
          }
        };
      </script>
      
      <!-- Widget container -->
      <div id="finder-v2-app" class="border border-gray-300 rounded-lg p-6 max-w-4xl">
        <!-- Vue app will mount here -->
      </div>
    </div>
    
    <script type="module" src="/src/libs.js"></script>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
