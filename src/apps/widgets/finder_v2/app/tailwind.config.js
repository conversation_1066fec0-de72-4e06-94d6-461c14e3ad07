/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    // Remove unused template scanning - widget doesn't use external templates
    // "../templates/**/*.html"
  ],

  theme: {
    extend: {
      colors: {
        gray: {
          25: 'oklch(99.25% .001 247.839)'
        }
      }
    }
  },

  // Minimal safelist - only include classes that cannot be detected by content scanning
  // Most classes are automatically detected from Vue components
  safelist: [
    // Dynamic classes that might be constructed programmatically
    // (Only include if you have dynamic class construction in your Vue components)

    // Ensure ws-secondary color classes are available for generation display formatting
    'text-ws-secondary-500',

    // TailwindUI reference template classes for CustomSelector component
    'grid', 'grid-cols-1', 'col-start-1', 'row-start-1', 'gap-2', 'pr-6', 'pr-9',
    'size-5', 'size-4', 'w-4', 'h-4', 'self-center', 'justify-self-end', 'sm:size-4', 'sm:text-sm/6',
    'outline-1', '-outline-offset-1', 'outline-gray-300', 'focus:outline-2',
    'focus:-outline-offset-2', 'focus:outline-indigo-600', 'ring-black/5',
    'focus:outline-hidden', 'font-semibold', 'text-indigo-600', 'text-indigo-200',
    'bg-indigo-600', 'text-white', 'outline-hidden',

    // Border fallback for CustomSelector visibility
    'border', 'border-gray-300', 'focus:border-indigo-600',

    // Iframe content padding
    'p-2', 'p-1', 'p-4',

    // Modification display layout classes
    'flex-col', 'min-w-0', 'font-medium', 'w-full',

    // Responsive grid layout classes for form-grid
    'grid-cols-2', 'grid-cols-4', 'md:grid-cols-2', 'lg:grid-cols-4', 'gap-4',

    // Template Gallery classes - used in custom output templates
    // Typography
    'text-xl', 'text-3xl', 'text-xs', 'font-light',
    
    // Spacing
    'mb-3', 'mb-4', 'mb-8', 'mt-1', 'mt-3', 'mr-2', 'ml-2', 'ml-12', 'pl-6', 'py-2', 'py-3', 'py-4', 'py-6', 
    'px-2', 'px-3', 'px-4', 'px-6', 'px-2.5', 'py-0.5', 'gap-6', 'gap-x-4', 'gap-y-2',
    'space-x-3', 'space-y-3', 'space-y-6',
    
    // Layout
    'grid-cols-3', 'lg:grid-cols-3', 'max-w-2xl', 'justify-between', 'items-center', 'flex-wrap',
    'text-center', 'text-left', 'text-right', 'inline-flex', 'overflow-hidden', 'overflow-x-auto',
    'whitespace-nowrap', 'min-w-full', 'divide-y', 'divide-gray-200',
    
    // Borders & Radius
    'rounded-xl', 'rounded-2xl', 'rounded-full', 'border-l-4', 'border-b', 
    'border-gray-200', 'border-gray-100',
    
    // Colors - Backgrounds
    'bg-gray-50', 'bg-gray-25', 'bg-gray-600', 'bg-gray-800', 'bg-blue-50', 
    'bg-blue-100', 'bg-blue-800', 'bg-emerald-100', 'bg-emerald-800', 
    'bg-orange-100', 'bg-orange-800', 'bg-green-100', 'bg-green-800',
    'bg-purple-100', 'bg-purple-800', 'bg-gradient-to-br', 'from-gray-50', 'to-gray-100',
    
    // Colors - Text
    'text-gray-600', 'text-gray-800', 'text-blue-800', 'text-emerald-800', 
    'text-orange-800', 'text-green-800', 'text-purple-800', 'text-indigo-500',
    
    // Shadows & Effects
    'shadow-sm', 'shadow-xl', 'hover:shadow-xl',
    
    // Interactions
    'hover:bg-gray-50', 'hover:bg-indigo-100', 'hover:text-indigo-700',
    'transition-all', 'transition-colors', 'transition-transform', 'duration-300',
    
    // Accordion functionality
    'group', 'group-open:rotate-180', 'cursor-pointer', 'focus:ring-2', 
    'focus:ring-indigo-500', 'focus:ring-offset-1', 'focus:outline-none',
    
    // SVG and utility classes
    'stroke-linecap-round', 'stroke-linejoin-round', 'fill-none',
    
    // Typography extras
    'font-mono',

    // Note: Most classes are now detected automatically from Vue component templates
    // This safelist should remain as minimal as possible
  ]
}
