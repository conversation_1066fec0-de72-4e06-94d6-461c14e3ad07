module.exports = function(grunt) {
    var src = {
        app: {
            js: [
                'js/app/app-module.js',
                'js/app/app-config.js',
                'js/app/app-config-token.js',
                //'js/app/app-config-token.min.js',
                'js/app/app-resources.js',
                'js/app/app-user-links.js',
                'js/app/finder/app-finder-by-model.js',
                'js/app/finder/app-finder-by-tire.js',
                'js/app/finder/app-finder-by-rim.js',
                'js/app/finder/app-finder-collapse-directive.js',
                'js/app/controllers/app-ctrl-main.js',
                'js/app/controllers/app-ctrl-finder.js',
                'js/app/controllers/app-ctrl-calc.js',
                'js/app/controllers/app-ctrl-configurator.js'
            ],
            jslibs: [
                'js/libs/jquery-2.1.1.min.js',
                'js/libs/bootstrap-3.3.1.min.js',
                'js/libs/angular-1.3.2.min.js',
                'js/libs/angular-route-1.3.2.min.js',
                'js/libs/angular-resource-1.3.8.min.js',
                'js/libs/bootstrap-select.js',
                'js/libs/angular-bootstrap-select-master-modified.js',
                'js/libs/microevent.js',
                'js/utils/string-format.js',
                'js/utils/localization.js',
                'js/utils/ws-messages.js',
                'js/libs/iframeResizer-3.5.5/iframeResizer.contentWindow.min.js'
            ],
            css: [
                'css/libs/bootstrap.min.css',
                //'css/libs/bootstrap-theme.min.css',
                'css/libs/bootstrap-select.min.css'
            ]
        },
        app_cc: {
            js: [
                'js/libs/less-2.7.1/less.min.js',
                'js/utils/ws-less-manager.js',
            ]
        },
        cc: {
            js: [
                'js/cc/app-config.js',
                'js/cc/app-less-controller.js',
                'js/cc/app-tag-choice.js',
                'js/cc/app-domains-list.js'
            ],
            jslibs: [
                'js/libs/less-2.7.1/less.min.js',
                //'js/libs/jquery-2.1.1.min.js',
                //'js/libs/bootstrap-3.3.1.min.js',
                'js/libs/angular-1.3.2.min.js',
                'js/libs/bootstrap-colorpicker-module.modified.js',
                'js/libs/angular-post-message.min.js',
                'js/utils/ws-less-manager.js'
            ],
            css: [
                //'css/libs/bootstrap.min.css',
                //'css/libs/bootstrap-theme.min.css',
                'css/libs/colorpicker.min.css',
                'dist/css/widget-cc.css'
            ]
        }
    };

    if (!grunt.initConfig({
            pkg: grunt.file.readJSON('package.json'),

            concat: {
                app: {
                    files: {
                        'dist/js/finder-app.js': src.app.js,
                        'dist/js/finder-app-libs.js': src.app.jslibs,
                        'dist/css/finder-app-libs.css': src.app.css,

                        'dist/js/widget-app-cc.js': src.app_cc.js
                    },
                    options: {
                        separator: '\n'
                    }
                },
                cc: {
                    files: {
                        'dist/js/widget-cc.js': src.cc.js,
                        'dist/js/widget-cc-libs.js': src.cc.jslibs,
                        'dist/css/widget-cc.css': src.cc.css
                    },
                    options: {
                        separator: '\n'
                    }
                }
            },

            less: {
                app: {
                    files: {
                        'dist/css/finder-app.css': 'css/finder-app.less'
                    }
                },
                cc: {
                    files: {
                        'dist/css/widget-cc.css': 'css/widget-cc.less'
                    }
                }
            },

            jshint: {
                app: {
                    files: {
                        src: [
                            'Gruntfile.js',
                            'js/app/*.js',
                            'js/app/**/*.js',
                            'js/utils/*.js',
                            '!js/app/app-config-token*.js'
                        ]
                    },
                    options: {
                        globals: {
                            angular: true,
                            console: true,
                            document: true
                        },
                        eqnull: true
                    }
                },
                cc: {
                    files: {
                        src: ['Gruntfile.js', 'js/cc/*.js', 'js/utils/*.js']
                    },
                    options: {
                        globals: {
                            angular: true,
                            console: true,
                            document: true
                        },
                        eqnull: true
                    }
                }
            },

            uglify: {
                app: {
                    files: {
                        'dist/js/finder-app.js': 'dist/js/finder-app.js',
                        'dist/js/finder-app-libs.js': 'dist/js/finder-app-libs.js',

                        'dist/js/widget-app-cc.js': 'dist/js/widget-app-cc.js'
                    }
                },
                cc: {
                    files: {
                        'dist/js/widget-cc.js': 'dist/js/widget-cc.js',
                        'dist/js/widget-cc-libs.js': 'dist/js/widget-cc-libs.js',
                    }
                }
            },

            copy: {
                app: {
                    files: [
                        {
                            expand: true,
                            flatten: true,
                            src: [
                                'dist/js/finder-app.js',
                                'dist/js/finder-app-libs.js'
                            ],
                            dest: '../static/finder/js/',
                            filter: 'isFile'
                        },
                        {
                            expand: true,
                            flatten: true,
                            src: [
                                'dist/js/widget-app-cc.js'
                            ],
                            dest: '../static/widget/js/'
                        },
                        {
                            expand: true,
                            flatten: true,
                            src: [
                                'dist/css/finder-app.css',
                                'dist/css/finder-app-libs.css'
                            ],
                            dest: '../static/finder/css/',
                            filter: 'isFile'
                        },
                        {
                            src: ['fonts/*', 'img/*'],
                            dest: '../static/finder/'
                        },
                        {
                            expand: true,
                            flatten: true,
                            src: [
                                'css/finder-app.less',
                                'css/*-theme.less',
                                'dist/css/finder-app.css'
                            ],
                            dest: 'dist_less/'
                        }
                    ]
                },
                cc: {
                    files: [
                        {
                            expand: true,
                            flatten: true,
                            src: ['dist/js/widget-cc.js', 'dist/js/widget-cc-libs.js'],
                            dest: '../static/widget/js/',
                            filter: 'isFile'
                        },
                        {
                            expand: true,
                            flatten: true,
                            src: ['dist/css/widget-cc.css', 'dist/css/widget-cc-libs.css'],
                            dest: '../static/widget/css/',
                            filter: 'isFile'
                        }
                    ]
                }
            },

            watch: {
                app: {
                    files: [
                        'Gruntfile.js',
                        'js/utils/*.js',
                        'js/app/*.js',
                        'js/app/**/*.js',
                        'css/*.less',
                        '../../templates/finder/*.html'
                    ],
                    tasks: ['app'],
                    options: {
                        livereload: true
                    }
                },
                cc: {
                    files: [
                        'Gruntfile.js',
                        'js/cc/*.js',
                        'css/*.less',
                        '../../templates/widget/client_config.html'
                    ],
                    tasks: ['cc'],
                    options: {
                        livereload: true
                    }
                }
            }
        })) {

    }

    grunt.loadNpmTasks('grunt-contrib-watch');
    grunt.loadNpmTasks('grunt-contrib-jshint');
    grunt.loadNpmTasks('grunt-template');
    grunt.loadNpmTasks('grunt-contrib-concat');
    grunt.loadNpmTasks('grunt-contrib-uglify');
    grunt.loadNpmTasks('grunt-contrib-less');
    grunt.loadNpmTasks('grunt-contrib-copy');

    grunt.registerTask('cc', ['jshint:cc', 'less:cc', 'concat:cc', 'copy:cc']);
    grunt.registerTask('cc_prod', ['jshint:cc', 'less:cc', 'concat:cc', 'uglify:cc', 'copy:cc']);
    grunt.registerTask('app', ['jshint:app', 'less:app', 'concat:app', 'copy:app']);
    grunt.registerTask('app_prod', ['jshint:app', 'less:app', 'concat:app', 'uglify:app', 'copy:app']);
    grunt.registerTask('all', ['app', 'cc']);
    grunt.registerTask('all_prod', ['app_prod', 'cc_prod']);
};