import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    tailwindcss()
  ],
  build: {
    // Output to Django static directory
    outDir: 'dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        // Main app entry point
        'finder-v2-app': resolve(__dirname, 'src/main.js'),
        // Separate libs bundle for third-party dependencies
        'finder-v2-app-libs': resolve(__dirname, 'src/libs.js')
      },
      output: {
        // Organize output files
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name.includes('libs')) {
            return 'js/[name].js'
          }
          return 'js/[name].js'
        },
        chunkFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.name.endsWith('.css')) {
            if (assetInfo.name.includes('libs')) {
              return 'css/[name][extname]'
            }
            return 'css/[name][extname]'
          }
          return 'assets/[name]-[hash][extname]'
        }
      }
    },
    // Bundle size optimization
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: true
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  // Development server configuration
  server: {
    port: 3000,
    host: true,
    cors: true
  },
  // CSS configuration
  css: {
    devSourcemap: true
  }
})
