import { createApp } from 'vue'
import { createPinia } from 'pinia'
import FinderV2Widget from './components/FinderV2Widget.vue'
import './styles/main.css'
import axios from 'axios'

// Create Vue app instance
const app = createApp(FinderV2Widget)

// Add Pinia store
app.use(createPinia())

// Iframe height management for widget embedding
// Note: iframeResizer.contentWindow.js handles the actual resizing
// This function provides additional resize triggers for Vue content changes
function notifyIframeResize() {
  if (window.parent && window.parent !== window && window.parentIFrame) {
    // Use iframeResizer's built-in resize method
    window.parentIFrame.size()
  }
}

// Mount the app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Configure axios with CSRF token for all requests (after DOM is ready and config is loaded)
  axios.defaults.headers.common['X-CSRF-TOKEN'] = window.FinderV2Config?.csrfToken || ''
  console.log('CSRF token configured:', window.FinderV2Config?.csrfToken || 'NOT FOUND')

  // Global properties for widget configuration
  app.config.globalProperties.$config = window.FinderV2Config || {}

  const container = document.getElementById('finder-v2-app')
  if (container) {
    const vueApp = app.mount(container)

    // Set up iframe height management with iframeResizer
    if (window.FinderV2Config?.iframeResize) {
      // Wait for iframeResizer to initialize
      setTimeout(() => {
        if (window.parentIFrame) {
          // Initial height notification
          notifyIframeResize()

          // Watch for content changes and notify parent
          const observer = new MutationObserver(() => {
            setTimeout(notifyIframeResize, 50)
          })

          observer.observe(container, {
            childList: true,
            subtree: true,
            attributes: true
          })

          // Also notify on window resize
          window.addEventListener('resize', notifyIframeResize)
        }
      }, 100)
    }
  } else {
    console.error('Finder-v2 widget container not found')
  }
})

// Export for potential external access
window.FinderV2App = app
