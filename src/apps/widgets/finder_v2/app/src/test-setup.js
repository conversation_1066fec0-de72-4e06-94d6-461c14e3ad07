/**
 * Test setup for Vitest
 */

import { vi } from 'vitest'

// Mock global objects
global.window = global.window || {}
global.document = global.document || {}

// Mock FinderV2Config
global.window.FinderV2Config = {
  widgetResources: {
    make: ['Make', '/api/makes/'],
    model: ['Model', '/api/models/'],
    year: ['Year', '/api/years/'],
    modification: ['Modification', '/api/modifications/'],
    generation: ['Generation', '/api/generations/'],
    search_by_model: ['', '/api/search/by_model/'],
    tire_width: ['Tire Width', '/api/tire-widths/'],
    aspect_ratio: ['Aspect Ratio', '/api/aspect-ratios/'],
    rim_diameter: ['Rim Diameter', '/api/rim-diameters/'],
    search_by_tire: ['', '/api/search/by_tire/'],
    rim_width: ['Rim Width', '/api/rim-widths/'],
    bolt_pattern: ['Bolt Pattern', '/api/bolt-patterns/'],
    search_by_rim: ['', '/api/search/by_rim/']
  },
  flowType: 'primary',
  apiVersion: 'v2',
  csrfToken: 'test-csrf-token',
  widgetConfig: {
    markets: [],
    utm: '?utm_campaign=widget&utm_medium=web&utm_source=test',
    slug: 'test-widget',
    flowType: 'primary',
    apiVersion: 'v2',
    tabs: {
      visible: ['by_vehicle', 'by_tire', 'by_rim'],
      primary: 'by_vehicle'
    },
    blocks: {
      button_to_ws: {
        hide: false
      }
    }
  }
}

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
}
