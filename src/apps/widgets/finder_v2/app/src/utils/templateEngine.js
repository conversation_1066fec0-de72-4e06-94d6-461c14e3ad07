/**
 * Lightweight template engine for finder-v2 custom output templates
 * Supports variables, loops, and basic conditionals
 */

// HTML escape for security
const escapeHtml = (text) => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

// Get nested object property by dot notation
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : ''
  }, obj)
}

// Main template rendering function
export const renderTemplate = (template, data) => {
  try {
    let result = template

    // Process loops first ({% for item in array %})
    result = processLoops(result, data)
    
    // Process conditionals ({% if condition %})
    result = processConditionals(result, data)
    
    // Process variables ({{ variable.path }})
    result = processVariables(result, data)

    return result
  } catch (error) {
    console.warn('Template rendering error:', error)
    return `<div class="text-red-500 text-sm">Template error: ${error.message}</div>`
  }
}

// Process {% for item in array %} loops
const processLoops = (template, data) => {
  const loopRegex = /\{\%\s*for\s+(\w+)\s+in\s+(\w+)\s*\%\}([\s\S]*?)\{\%\s*endfor\s*\%\}/g
  
  return template.replace(loopRegex, (match, itemVar, arrayVar, content) => {
    const array = data[arrayVar]
    if (!Array.isArray(array)) return ''
    
    return array.map(item => {
      let itemContent = content
      const itemData = { [itemVar]: item, ...data }
      
      // Process conditionals within the loop content first
      itemContent = processConditionals(itemContent, itemData)
      
      // Then process variables
      itemContent = processVariables(itemContent, itemData)
      return itemContent
    }).join('')
  })
}

// Process {% if condition %} conditionals (handles nested conditionals)
const processConditionals = (template, data) => {
  let result = template
  let hasChanges = true
  
  // Process conditionals iteratively to handle nested structures
  while (hasChanges) {
    hasChanges = false
    
    // Process innermost conditionals first (no nested if statements inside)
    const ifRegex = /\{\%\s*if\s+(.*?)\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?)(?:\{\%\s*else\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?))?\{\%\s*endif\s*\%\}/g
    
    result = result.replace(ifRegex, (match, condition, ifContent, elseContent = '') => {
      hasChanges = true
      const isTrue = evaluateCondition(condition, data)
      let processedContent = isTrue ? ifContent : elseContent
      
      // Recursively process any remaining conditionals in the content
      if (processedContent.includes('{% if')) {
        processedContent = processConditionals(processedContent, data)
      }
      
      return processedContent
    })
  }
  
  return result
}

// Process {{ variable }} placeholders
const processVariables = (template, data) => {
  const varRegex = /\{\{\s*(.*?)\s*\}\}/g
  
  return template.replace(varRegex, (match, variable) => {
    const trimmedVar = variable.trim()
    
    // Check for ternary operator: condition ? valueIfTrue : valueIfFalse
    const ternaryMatch = trimmedVar.match(/^(.+?)\s*\?\s*['"]?(.*?)['"]?\s*:\s*['"]?(.*?)['"]?$/)
    if (ternaryMatch) {
      const [, condition, trueValue, falseValue] = ternaryMatch
      const conditionResult = evaluateCondition(condition.trim(), data)
      // Remove quotes if present
      const cleanTrueValue = trueValue.replace(/^['"]|['"]$/g, '')
      const cleanFalseValue = falseValue.replace(/^['"]|['"]$/g, '')
      return escapeHtml(conditionResult ? cleanTrueValue : cleanFalseValue)
    }
    
    // Regular variable lookup
    const value = getNestedValue(data, trimmedVar)
    return escapeHtml(String(value || ''))
  })
}

// Simple condition evaluation (supports basic boolean checks)
const evaluateCondition = (condition, data) => {
  try {
    // Handle 'not' operator
    if (condition.startsWith('not ')) {
      const innerCondition = condition.slice(4).trim()
      return !evaluateCondition(innerCondition, data)
    }
    
    // Get the value and check truthiness
    const value = getNestedValue(data, condition.trim())
    return Boolean(value)
  } catch {
    return false
  }
} 