// Third-party library imports for finder-v2 widget
// This file is bundled separately to optimize caching

// Vue 3 runtime (if needed for external access)
import { createApp } from 'vue'

// Pinia for state management
import { createPinia } from 'pinia'

// HeadlessUI components
import * as HeadlessUI from '@headlessui/vue'

// Heroicons
import * as HeroIcons from '@heroicons/vue/24/outline'

// Axios for HTTP requests
import axios from 'axios'

// Export libraries for potential external use
window.Vue = { createApp, createPinia }
window.HeadlessUI = HeadlessUI
window.HeroIcons = HeroIcons
window.axios = axios

// Configure axios defaults for CSRF protection
axios.defaults.xsrfCookieName = 'csrftoken'
axios.defaults.xsrfHeaderName = 'X-CSRFToken'

// Add request interceptor for CSRF token
axios.interceptors.request.use((config) => {
  const token = window.FinderV2Config?.csrfToken
  if (token) {
    config.headers['X-CSRFToken'] = token
  }
  return config
})

console.log('Finder-v2 widget libraries loaded')
