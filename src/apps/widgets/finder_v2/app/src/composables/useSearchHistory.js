import { ref, readonly, computed } from 'vue'

/**
 * Search History Composable
 * 
 * Manages localStorage operations for search history functionality.
 * Provides reactive search history state and handles search serialization/deserialization.
 * Implements storage limits and cleanup with error handling.
 */
export function useSearchHistory(widgetId) {
  const searches = ref([])
  const isEnabled = ref(true)
  const maxItems = ref(10)
  const displayItems = ref(5)
  
  // Generate storage key unique to widget instance
  const storageKey = `finder_v2_search_history_${widgetId}`
  
  // Check localStorage availability
  const isLocalStorageAvailable = computed(() => {
    try {
      const test = '__localStorage_test__'
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch (e) {
      console.warn('localStorage not available:', e)
      return false
    }
  })
  
  /**
   * Initialize search history from localStorage
   */
  function initialize() {
    console.log('useSearchHistory initialize called for:', storageKey)
    if (!isLocalStorageAvailable.value) {
      console.log('localStorage not available')
      isEnabled.value = false
      return
    }
    
    try {
      const stored = localStorage.getItem(storageKey)
      console.log('localStorage data for', storageKey, ':', stored)
      if (stored) {
        const data = JSON.parse(stored)
        console.log('Parsed data:', data)
        
        // Validate stored data structure
        if (validateStoredData(data)) {
          console.log('Data validation passed, setting searches:', data.searches)
          // NEW: normalize legacy items and ensure validity
          isEnabled.value = data.enabled !== false

          // Normalize stored searches (handles legacy formats) and filter invalid entries
          const normalized = (data.searches || []).map(normalizeSearchItem).filter(validateSearchItem)

          // Sort by timestamp (newest first)
          normalized.sort((a, b) => b.timestamp - a.timestamp)

          // Assign to reactive state
          searches.value = normalized

          // DIRECT STATE CHECK
          console.log('DIRECT STATE CHECK AFTER ASSIGNMENT:', {
            searchesValue: searches.value,
            searchesLength: searches.value.length,
            isEnabledValue: isEnabled.value,
            rawSearchesRef: searches,
            rawEnabledRef: isEnabled
          })

          // Persist normalized data back to storage in case it was upgraded
          saveToStorage()
        } else {
          // Invalid data format, reset
          clearHistory()
        }
      }
    } catch (error) {
      console.warn('Failed to load search history:', error)
      clearHistory()
    }
  }
  
  /**
   * Add a new search to history
   * @param {Object} searchParams - Search parameters from the form
   */
  function addSearch(searchParams) {
    if (!isEnabled.value || !isLocalStorageAvailable.value) {
      return
    }
    
    try {
      const searchItem = createSearchItem(searchParams)
      
      // Remove duplicate if exists (same parameters)
      const existingIndex = searches.value.findIndex(item => 
        compareSearchParameters(item.parameters, searchItem.parameters)
      )
      
      if (existingIndex !== -1) {
        // Update existing search timestamp and move to top
        searches.value.splice(existingIndex, 1)
      }
      
      // Add to beginning of array
      searches.value.unshift(searchItem)
      
      // Enforce storage limit
      if (searches.value.length > maxItems.value) {
        searches.value = searches.value.slice(0, maxItems.value)
      }
      
      // Save to localStorage
      saveToStorage()
      
    } catch (error) {
      console.warn('Failed to add search to history:', error)
    }
  }
  
  /**
   * Remove a specific search from history
   * @param {string} searchId - ID of the search to remove
   */
  function removeSearch(searchId) {
    if (!isEnabled.value) {
      return
    }
    
    try {
      const index = searches.value.findIndex(item => item.id === searchId)
      if (index !== -1) {
        searches.value.splice(index, 1)
        saveToStorage()
      }
    } catch (error) {
      console.warn('Failed to remove search from history:', error)
    }
  }
  
  /**
   * Get a search item by ID
   * @param {string} searchId - ID of the search to retrieve
   * @returns {Object|null} Search item or null if not found
   */
  function getSearch(searchId) {
    return searches.value.find(item => item.id === searchId) || null
  }
  
  /**
   * Update search timestamp (move to top)
   * @param {string} searchId - ID of the search to update
   */
  function updateSearchTimestamp(searchId) {
    if (!isEnabled.value) {
      return
    }
    
    try {
      const index = searches.value.findIndex(item => item.id === searchId)
      if (index !== -1) {
        const searchItem = searches.value.splice(index, 1)[0]
        searchItem.timestamp = Date.now()
        searches.value.unshift(searchItem)
        saveToStorage()
      }
    } catch (error) {
      console.warn('Failed to update search timestamp:', error)
    }
  }
  
  /**
   * Clear all search history
   */
  function clearHistory() {
    searches.value = []
    
    if (isLocalStorageAvailable.value) {
      try {
        localStorage.removeItem(storageKey)
      } catch (error) {
        console.warn('Failed to clear search history:', error)
      }
    }
  }
  
  /**
   * Enable or disable search history
   * @param {boolean} enabled - Whether to enable search history
   */
  function setEnabled(enabled) {
    isEnabled.value = enabled
    
    if (!enabled) {
      clearHistory()
    }
    
    saveToStorage()
  }
  
  /**
   * Configure maximum items and display items
   * @param {Object} config - Configuration object
   */
  function configure(config) {
    if (config.maxItems !== undefined) {
      maxItems.value = Math.max(1, Math.min(50, config.maxItems))
    }
    
    if (config.displayItems !== undefined) {
      displayItems.value = Math.max(1, Math.min(maxItems.value, config.displayItems))
    }
    
    if (config.enabled !== undefined) {
      setEnabled(config.enabled)
    }
    
    // Trim searches if new max is smaller
    if (searches.value.length > maxItems.value) {
      searches.value = searches.value.slice(0, maxItems.value)
      saveToStorage()
    }
  }
  
  /**
   * Get searches for display (limited by displayItems)
   * @returns {Array} Array of search items for display
   */
  const displaySearches = computed(() => {
    return searches.value.slice(0, displayItems.value)
  })
  
  /**
   * Check if there are more searches beyond display limit
   * @returns {boolean} True if there are more searches
   */
  const hasMoreSearches = computed(() => {
    return searches.value.length > displayItems.value
  })
  
  /**
   * Create a search item from search parameters
   * @param {Object} searchParams - Search parameters
   * @returns {Object} Formatted search item
   */
  function createSearchItem(searchParams) {
    const timestamp = Date.now()
    const id = `${timestamp}_${Math.random().toString(36).substr(2, 9)}`
    
    return {
      id,
      timestamp,
      description: generateSearchDescription(searchParams),
      flowType: searchParams.flowType || 'primary',
      parameters: {
        year: searchParams.year || '',
        make: searchParams.make || '',
        model: searchParams.model || '',
        modification: searchParams.modification || '',
        generation: searchParams.generation || ''
      },
      // Store option objects for regenerating display text
      options: searchParams.options || {}
    }
  }
  
  /**
   * Generate human-readable search description using display text from option objects
   * @param {Object} searchParams - Search parameters
   * @returns {string} Human-readable description
   */
  function generateSearchDescription(searchParams) {
    const parts = []
    const options = searchParams.options || {}
    
    // Helper function to get display text similar to CustomSelector logic
    function getOptionDisplayText(option, optionType) {
      if (!option) return null
      
      // For year options, use the name or year value
      if (optionType === 'year') {
        return option.name || option.year || option.slug || option.id
      }
      
      // For make and model options, capitalize the name
      if (optionType === 'make' || optionType === 'model') {
        const name = option.name || option.slug || option.id
        if (typeof name === 'string') {
          // Capitalize first letter of each word
          return name.split(' ').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          ).join(' ')
        }
        return name
      }
      
      // For modification options, use the full descriptive text
      if (optionType === 'modification') {
        if (option.engine || option.trim || option.generation) {
          const details = []
          let primaryName = option.name || ''
          
          // Add generation information for primary and year_select flows
          const showGenerationInfo = searchParams.flowType === 'primary' || searchParams.flowType === 'year_select'
          if (showGenerationInfo && option.generation) {
            const generation = option.generation
            if (generation.name && generation.name.trim() !== '') {
              const years = `${generation.start}-${generation.end}`
              details.push(`${generation.name} (${years})`)
            } else {
              details.push(`${generation.start}-${generation.end}`)
            }
          }
          
          // Add engine specifications
          if (option.engine) {
            const engineParts = []
            
            if (option.engine.capacity) {
              engineParts.push(option.engine.capacity)
            }
            
            if (option.engine.type) {
              engineParts.push(option.engine.type)
            }
            
            if (option.engine.fuel) {
              engineParts.push(option.engine.fuel)
            }
            
            if (option.engine.power && option.engine.power.hp) {
              engineParts.push(`${option.engine.power.hp}HP`)
            }
            
            if (engineParts.length > 0) {
              details.push(engineParts.join(' '))
            }
          }
          
          return details.length > 0 ? `${primaryName} ${details.join(' • ')}`.trim() : primaryName
        }
        
        return option.name || option.slug || option.id
      }
      
      // For generation options
      if (optionType === 'generation') {
        if (option.year_ranges && Array.isArray(option.year_ranges)) {
          const yearRanges = option.year_ranges.join(', ')
          if (!option.name || option.name.trim() === '') {
            return yearRanges
          }
          return `${option.name} (${yearRanges})`
        }
        return option.name || option.slug || option.id
      }
      
      return option.name || option.slug || option.id
    }
    
    // Build description using display text from options or fallback to raw values
    if (searchParams.year) {
      const displayText = getOptionDisplayText(options.year, 'year') || searchParams.year
      parts.push(displayText)
    }
    
    if (searchParams.make) {
      const displayText = getOptionDisplayText(options.make, 'make') || searchParams.make
      parts.push(displayText)
    }
    
    if (searchParams.model) {
      const displayText = getOptionDisplayText(options.model, 'model') || searchParams.model
      parts.push(displayText)
    }
    
    if (searchParams.modification) {
      const displayText = getOptionDisplayText(options.modification, 'modification') || searchParams.modification
      parts.push(displayText)
    }
    
    if (searchParams.generation && searchParams.flowType === 'alternative') {
      const displayText = getOptionDisplayText(options.generation, 'generation') || searchParams.generation
      parts.push(`(${displayText})`)
    }
    
    return parts.join(' ') || 'Unknown Search'
  }
  
  /**
   * Compare search parameters for duplicates
   * @param {Object} params1 - First parameter set
   * @param {Object} params2 - Second parameter set
   * @returns {boolean} True if parameters are the same
   */
  function compareSearchParameters(params1, params2) {
    const keys = ['year', 'make', 'model', 'modification', 'generation']
    return keys.every(key => (params1[key] || '') === (params2[key] || ''))
  }

  /**
   * Normalize a stored search item to the latest schema. Handles legacy flat
   * structures where search parameters were stored at the top level instead of
   * inside a dedicated `parameters` object. Also guarantees that `id` and
   * `timestamp` fields exist and are of the correct types.
   * @param {any} item - Raw search item from storage
   * @returns {Object} Normalized search item (may still be invalid and should
   *                   be passed through validateSearchItem)
   */
  function normalizeSearchItem(item) {
    if (!item || typeof item !== 'object') {
      return {}
    }

    const normalized = { ...item }

    // Legacy schema: parameters stored at the root level
    if (!normalized.parameters) {
      normalized.parameters = {
        year: normalized.year || '',
        make: normalized.make || '',
        model: normalized.model || '',
        modification: normalized.modification || '',
        generation: normalized.generation || ''
      }
    }

    // Ensure timestamp is a number
    if (normalized.timestamp && typeof normalized.timestamp !== 'number') {
      normalized.timestamp = Number(normalized.timestamp) || Date.now()
    } else if (!normalized.timestamp) {
      normalized.timestamp = Date.now()
    }

    // Ensure id exists
    if (!normalized.id) {
      normalized.id = `${normalized.timestamp}_${Math.random().toString(36).substr(2, 9)}`
    }

    return normalized
  }
  
  /**
   * Validate stored data structure
   * @param {any} data - Data to validate
   * @returns {boolean} True if data is valid
   */
  function validateStoredData(data) {
    if (!data || typeof data !== 'object') {
      return false
    }
    
    if (!Array.isArray(data.searches)) {
      return false
    }
    
    return true
  }
  
  /**
   * Validate individual search item
   * @param {any} item - Search item to validate
   * @returns {boolean} True if item is valid
   */
  function validateSearchItem(item) {
    if (!item || typeof item !== 'object') {
      return false
    }
    
    if (!item.id || !item.timestamp || !item.parameters) {
      return false
    }
    
    if (typeof item.timestamp !== 'number' || item.timestamp <= 0) {
      return false
    }
    
    if (!item.parameters || typeof item.parameters !== 'object') {
      return false
    }
    
    return true
  }
  
  /**
   * Save current searches to localStorage
   */
  function saveToStorage() {
    if (!isLocalStorageAvailable.value) {
      return
    }
    
    try {
      const data = {
        version: '1.0',
        enabled: isEnabled.value,
        searches: searches.value
      }
      
      localStorage.setItem(storageKey, JSON.stringify(data))
    } catch (error) {
      console.warn('Failed to save search history:', error)
      
      // Handle quota exceeded by removing oldest entries
      if (error.name === 'QuotaExceededError') {
        handleQuotaExceeded()
      }
    }
  }
  
  /**
   * Handle localStorage quota exceeded
   */
  function handleQuotaExceeded() {
    try {
      // Remove half of the searches and try again
      const reducedSearches = searches.value.slice(0, Math.floor(searches.value.length / 2))
      searches.value = reducedSearches
      
      // Try saving again
      saveToStorage()
    } catch (error) {
      console.warn('Failed to handle quota exceeded:', error)
      // If still failing, clear all history
      clearHistory()
    }
  }
  
  /**
   * Get relative time string for display
   * @param {number} timestamp - Unix timestamp
   * @returns {string} Relative time string
   */
  function getRelativeTime(timestamp) {
    const now = Date.now()
    const diff = now - timestamp
    
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
    } else {
      return 'Just now'
    }
  }
  
  // Initialize on creation
  initialize()
  
  return {
    // Reactive state as refs (not raw arrays)
    searches,
    isEnabled,
    displaySearches,
    hasMoreSearches,
    maxItems,
    displayItems,
    isLocalStorageAvailable,
    
    // Methods
    addSearch,
    removeSearch,
    getSearch,
    updateSearchTimestamp,
    clearHistory,
    setEnabled,
    configure,
    getRelativeTime
  }
}