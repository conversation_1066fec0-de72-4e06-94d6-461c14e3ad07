import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { useSearchHistory } from '../useSearchHistory.js'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('useSearchHistory', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with empty searches when no stored data', () => {
    const { searches, isEnabled } = useSearchHistory('test-widget')
    
    expect(searches.value).toEqual([])
    expect(isEnabled.value).toBe(true)
  })

  it('should load stored searches on initialization', () => {
    const storedData = {
      version: '1.0',
      enabled: true,
      searches: [
        {
          id: '123',
          timestamp: 1642678800000,
          description: '2020 BMW X5 xDrive40i',
          flowType: 'primary',
          parameters: {
            year: '2020',
            make: 'BMW',
            model: 'X5',
            modification: 'xDrive40i'
          }
        }
      ]
    }
    
    localStorageMock.getItem.mockReturnValue(JSON.stringify(storedData))
    
    const { searches } = useSearchHistory('test-widget')
    
    expect(searches.value).toHaveLength(1)
    expect(searches.value[0].description).toBe('2020 BMW X5 xDrive40i')
  })

  it('should add new search to history', () => {
    const { addSearch, searches } = useSearchHistory('test-widget')
    
    const searchParams = {
      flowType: 'primary',
      year: '2021',
      make: 'Toyota',
      model: 'Camry',
      modification: 'LE'
    }
    
    addSearch(searchParams)
    
    expect(searches.value).toHaveLength(1)
    expect(searches.value[0].description).toBe('2021 Toyota Camry LE')
    expect(searches.value[0].parameters).toEqual({
      year: '2021',
      make: 'Toyota',
      model: 'Camry',
      modification: 'LE',
      generation: ''
    })
  })

  it('should remove search from history', () => {
    const { addSearch, removeSearch, searches } = useSearchHistory('test-widget')
    
    const searchParams = {
      flowType: 'primary',
      year: '2021',
      make: 'Toyota',
      model: 'Camry',
      modification: 'LE'
    }
    
    addSearch(searchParams)
    expect(searches.value).toHaveLength(1)
    
    const searchId = searches.value[0].id
    removeSearch(searchId)
    
    expect(searches.value).toHaveLength(0)
  })

  it('should update search timestamp when moved to top', () => {
    const { addSearch, updateSearchTimestamp, searches } = useSearchHistory('test-widget')
    
    // Add two searches
    addSearch({
      flowType: 'primary',
      year: '2021',
      make: 'Toyota',
      model: 'Camry',
      modification: 'LE'
    })
    
    addSearch({
      flowType: 'primary', 
      year: '2020',
      make: 'Honda',
      model: 'Civic',
      modification: 'LX'
    })
    
    expect(searches.value).toHaveLength(2)
    expect(searches.value[0].description).toBe('2020 Honda Civic LX')
    expect(searches.value[1].description).toBe('2021 Toyota Camry LE')
    
    // Update timestamp of the second search (should move to top)
    const secondSearchId = searches.value[1].id
    updateSearchTimestamp(secondSearchId)
    
    expect(searches.value[0].description).toBe('2021 Toyota Camry LE')
    expect(searches.value[1].description).toBe('2020 Honda Civic LX')
  })

  it('should clear all history', () => {
    const { addSearch, clearHistory, searches } = useSearchHistory('test-widget')
    
    addSearch({
      flowType: 'primary',
      year: '2021',
      make: 'Toyota',
      model: 'Camry',
      modification: 'LE'
    })
    
    expect(searches.value).toHaveLength(1)
    
    clearHistory()
    
    expect(searches.value).toHaveLength(0)
  })

  it('should handle localStorage unavailability gracefully', () => {
    // Mock localStorage to throw error
    localStorageMock.getItem.mockImplementation(() => {
      throw new Error('localStorage not available')
    })
    
    const { searches, isEnabled, addSearch } = useSearchHistory('test-widget')
    
    expect(searches.value).toEqual([])
    expect(isEnabled.value).toBe(false)
    
    // Adding search should not throw error
    expect(() => {
      addSearch({
        flowType: 'primary',
        year: '2021',
        make: 'Toyota',
        model: 'Camry',
        modification: 'LE'
      })
    }).not.toThrow()
  })

  it('should respect maximum items limit', () => {
    const { addSearch, searches, configure } = useSearchHistory('test-widget')
    
    // Set max items to 3
    configure({ maxItems: 3 })
    
    // Add 5 searches
    for (let i = 1; i <= 5; i++) {
      addSearch({
        flowType: 'primary',
        year: `202${i}`,
        make: 'Toyota',
        model: 'Camry',
        modification: 'LE'
      })
    }
    
    // Should only keep latest 3
    expect(searches.value).toHaveLength(3)
    expect(searches.value[0].description).toBe('2025 Toyota Camry LE')
    expect(searches.value[1].description).toBe('2024 Toyota Camry LE')
    expect(searches.value[2].description).toBe('2023 Toyota Camry LE')
  })

  it('should generate human-readable descriptions for different flow types', () => {
    const { addSearch, searches } = useSearchHistory('test-widget')
    
    // Primary flow
    addSearch({
      flowType: 'primary',
      year: '2021',
      make: 'Toyota',
      model: 'Camry',
      modification: 'LE'
    })
    
    // Alternative flow with generation
    addSearch({
      flowType: 'alternative',
      make: 'BMW',
      model: 'X5',
      modification: 'xDrive40i',
      generation: 'G05'
    })
    
    // Year select flow
    addSearch({
      flowType: 'year_select',
      make: 'Honda',
      model: 'Civic',
      year: '2020',
      modification: 'LX'
    })
    
    expect(searches.value).toHaveLength(3)
    expect(searches.value[2].description).toBe('2021 Toyota Camry LE')
    expect(searches.value[1].description).toBe('BMW X5 xDrive40i (G05)')
    expect(searches.value[0].description).toBe('Honda Civic 2020 LX')
  })

  it('should prevent duplicate searches', () => {
    const { addSearch, searches } = useSearchHistory('test-widget')
    
    const searchParams = {
      flowType: 'primary',
      year: '2021',
      make: 'Toyota',
      model: 'Camry',
      modification: 'LE'
    }
    
    // Add same search twice
    addSearch(searchParams)
    addSearch(searchParams)
    
    // Should only have one search but with updated timestamp
    expect(searches.value).toHaveLength(1)
  })

  it('should format relative time correctly', () => {
    const { getRelativeTime } = useSearchHistory('test-widget')
    
    const now = Date.now()
    
    expect(getRelativeTime(now)).toBe('Just now')
    expect(getRelativeTime(now - 30 * 1000)).toBe('Just now')
    expect(getRelativeTime(now - 2 * 60 * 1000)).toBe('2 minutes ago')
    expect(getRelativeTime(now - 1 * 60 * 60 * 1000)).toBe('1 hour ago')
    expect(getRelativeTime(now - 3 * 60 * 60 * 1000)).toBe('3 hours ago')
    expect(getRelativeTime(now - 1 * 24 * 60 * 60 * 1000)).toBe('1 day ago')
    expect(getRelativeTime(now - 5 * 24 * 60 * 60 * 1000)).toBe('5 days ago')
  })
})