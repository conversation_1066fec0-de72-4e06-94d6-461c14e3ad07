<template>
  <div class="flex items-start">
    <Listbox v-model="selectedValue" :disabled="disabled" class="flex-1">
      <div class="relative">
        <ListboxButton
        as="button"
        :class="[
          'grid w-full cursor-default grid-cols-1 theme-rounded-md bg-white py-1.5 pr-2 pl-3 text-left text-gray-900 border border-gray-300 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-primary-color focus:border-primary-color sm:text-sm/6',
          disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : ''
        ]"
        ref="buttonRef"
      >
        <span v-if="loading" class="col-start-1 row-start-1 flex w-full gap-2 pr-6">
          <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="truncate">Loading...</span>
        </span>
        <span v-else-if="selectedOption" class="col-start-1 row-start-1 flex w-full gap-2 pr-6">
          <template v-if="getDisplayData(selectedOption).isGeneration">
            <span class="truncate">{{ getDisplayData(selectedOption).name }}</span>
            <span class="truncate text-ws-secondary-500">{{ getDisplayData(selectedOption).yearRanges }}</span>
          </template>
          <template v-else-if="getDisplayData(selectedOption).isModification">
            <div class="flex flex-col min-w-0">
              <span class="truncate font-medium">{{ getDisplayData(selectedOption).name }}</span>
              <span v-if="getDisplayData(selectedOption).details" class="truncate text-xs text-ws-secondary-500">{{ getDisplayData(selectedOption).details }}</span>
            </div>
          </template>
          <template v-else>
            <span class="truncate">{{ getDisplayText(selectedOption) }}</span>
          </template>
        </span>
        <span v-else class="col-start-1 row-start-1 flex w-full gap-2 pr-6">
          <span :class="['truncate', disabled ? 'text-gray-500' : 'text-gray-900']">{{ placeholder }}</span>
        </span>
        <svg class="col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" />
        </svg>
      </ListboxButton>

      <transition
        leave-active-class="transition duration-100 ease-in"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
      >
        <ListboxOptions
          class="z-10 mt-1 max-h-60 w-full overflow-auto theme-rounded-md bg-white py-1 text-base ring-1 shadow-lg ring-black/5 focus:outline-hidden sm:text-sm"
        >
          <ListboxOption
            v-if="!options.length && !loading"
            :value="null"
            class="relative cursor-default select-none py-2 pr-9 pl-3 text-gray-500"
            disabled
          >
            No options available
          </ListboxOption>
          <ListboxOption
            v-for="option in options"
            v-slot="{ active, selected }"
            :key="option.slug || option.id"
            :value="option.slug || option.id"
            as="template"
          >
            <li
              :class="[
                active ? 'bg-primary-color text-white outline-hidden' : 'text-gray-900 hover:bg-gray-50',
                'relative cursor-default select-none py-2 pr-9 pl-3',
              ]"
            >
              <div class="flex">
                <template v-if="getDisplayData(option).isGeneration">
                  <span
                    :class="[
                      selected ? 'font-semibold' : 'font-normal',
                      'truncate'
                    ]"
                  >
                    {{ getDisplayData(option).name }}
                  </span>
                  <span
                    :class="[
                      active ? 'text-white/80' : 'text-ws-secondary-500',
                      'ml-2 truncate'
                    ]"
                  >
                    {{ getDisplayData(option).yearRanges }}
                  </span>
                </template>
                <template v-else-if="getDisplayData(option).isModification">
                  <div class="flex flex-col min-w-0 w-full">
                    <span
                      :class="[
                        selected ? 'font-semibold' : 'font-medium',
                        'truncate'
                      ]"
                    >
                      {{ getDisplayData(option).name }}
                    </span>
                    <span
                      v-if="getDisplayData(option).details"
                      :class="[
                        active ? 'text-white/80' : 'text-ws-secondary-500',
                        'truncate text-xs'
                      ]"
                    >
                      {{ getDisplayData(option).details }}
                    </span>
                  </div>
                </template>
                <template v-else>
                  <span
                    :class="[
                      selected ? 'font-semibold' : 'font-normal',
                      'truncate'
                    ]"
                  >
                    {{ getDisplayText(option) }}
                  </span>
                </template>
              </div>
              <span
                v-if="selected"
                :class="[
                  active ? 'text-white' : 'text-primary-color',
                  'absolute inset-y-0 right-0 flex items-center pr-4'
                ]"
              >
                <CheckIcon class="size-5" aria-hidden="true" />
              </span>
            </li>
          </ListboxOption>
        </ListboxOptions>
      </transition>
      </div>
    </Listbox>

    <!-- External Loading Spinner -->
    <div class="min-w-[32px] mt-2 ml-1">
      <div v-if="preloader" class="spinner-external">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-5 h-5 text-gray-400">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, watch, ref, nextTick, onMounted, onUnmounted } from 'vue'
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue'
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'

export default {
  name: 'CustomSelector',
  components: {
    Listbox,
    ListboxButton,
    ListboxOptions,
    ListboxOption,
    CheckIcon,
    ChevronUpDownIcon,
  },
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: 'Select option'
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    preloader: {
      type: Boolean,
      default: false
    },
    stateLoaded: {
      type: Boolean,
      default: false
    },
    autoExpand: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    // Reference to ListboxButton for programmatic opening of dropdown
    const buttonRef = ref(null)

    const selectedValue = computed({
      get: () => props.modelValue,
      set: (value) => {
        emit('update:modelValue', value)
        emit('change', value)
      }
    })

    const selectedOption = computed(() => {
      if (!props.modelValue) return null
      return props.options.find(option => 
        (option.slug || option.id) === props.modelValue
      )
    })

    // Watch for options changes and clear selection if current value is not in new options
    watch(() => props.options, (newOptions) => {
      if (props.modelValue && newOptions.length > 0) {
        const exists = newOptions.some(option =>
          (option.slug || option.id) === props.modelValue
        )
        if (!exists) {
          selectedValue.value = ''
        }
      }
    })

    // Format display text for options, with special handling for generations
    const getDisplayText = (option) => {
      // Check if this is a generation option (has start/end properties unique to generations)
      // Models also have year_ranges, but only generations have start/end integer properties
      if (option.year_ranges && Array.isArray(option.year_ranges) &&
          typeof option.start === 'number' && typeof option.end === 'number') {
        const yearRanges = option.year_ranges.join(', ')

        // If generation name is empty, null, or undefined, show only year ranges
        if (!option.name || option.name.trim() === '') {
          return yearRanges
        }

        // If generation name exists, show "name, year_ranges"
        return `${option.name}, ${yearRanges}`
      }

      // For non-generation options (models, makes, years, modifications), use the name as before
      return option.name || ''
    }

    // Get structured display data for options, with special handling for generations and modifications
    const getDisplayData = (option) => {
      // Check if this is a generation option (has start/end properties unique to generations)
      // Models also have year_ranges, but only generations have start/end integer properties
      if (option.year_ranges && Array.isArray(option.year_ranges) &&
          typeof option.start === 'number' && typeof option.end === 'number') {
        const yearRanges = option.year_ranges.join(', ')

        // If generation name is empty, null, or undefined, show only year ranges as name
        if (!option.name || option.name.trim() === '') {
          return {
            isGeneration: false, // Treat as simple text when no name
            isModification: false,
            name: yearRanges,
            yearRanges: ''
          }
        }

        // If generation name exists, return structured data
        return {
          isGeneration: true,
          isModification: false,
          name: option.name,
          yearRanges: yearRanges
        }
      }

      // Check if this is a modification option (has engine and/or trim properties)
      if (option.engine || option.trim || option.generation) {
        const details = []
        let primaryName = option.name || ''

        // In primary and year_select flows, add generation information if available since users don't see generation selector
        const showGenerationInfo = window.FinderV2Config?.flowType === 'primary' || 
                                   window.FinderV2Config?.widgetConfig?.flowType === 'primary' ||
                                   window.FinderV2Config?.flowType === 'year_select' || 
                                   window.FinderV2Config?.widgetConfig?.flowType === 'year_select'
        
        if (showGenerationInfo && option.generation) {
          const generation = option.generation
          let generationInfo = ''
          
          if (generation.name && generation.name.trim() !== '') {
            // Generation has a name - show name with years
            const years = `${generation.start}-${generation.end}`
            generationInfo = `${generation.name} (${years})`
          } else {
            // Generation has no name - show only years  
            generationInfo = `${generation.start}-${generation.end}`
          }
          
          if (generationInfo) {
            details.push(generationInfo)
          }
        }

        // Add trim if available and different from name
        if (option.trim && option.trim.trim() !== '' && option.trim !== option.name) {
          //details.push(option.trim)
        }

        // Add engine specifications if available
        if (option.engine) {
          const engineParts = []
          
          // Add capacity (e.g., "3.0")
          if (option.engine.capacity) {
            engineParts.push(option.engine.capacity)
          }
          
          // Add engine type (e.g., "V6")
          if (option.engine.type) {
            engineParts.push(option.engine.type)
          }
          
          // Add fuel type with abbreviations
          if (option.engine.fuel) {
            let fuelAbbrev = option.engine.fuel
            if (option.engine.fuel === 'Petrol') fuelAbbrev = 'Petrol'
            else if (option.engine.fuel === 'Diesel') fuelAbbrev = 'Diesel'
            else if (option.engine.fuel === 'Hybrid') fuelAbbrev = 'Hybrid'
            engineParts.push(fuelAbbrev)
          }
          
          // Add horsepower if available
          if (option.engine.power && option.engine.power.hp) {
            engineParts.push(`${option.engine.power.hp}HP`)
          }
          
          if (engineParts.length > 0) {
            details.push(engineParts.join(' '))
          }
        }

        return {
          isGeneration: false,
          isModification: true,
          name: primaryName,
          details: details.join(' • ')
        }
      }

      // For other options (models, makes, years)
      return {
        isGeneration: false,
        isModification: false,
        name: option.name || '',
        yearRanges: ''
      }
    }

    // Respect user's reduced-motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches

    // -------------------------------------------------------
    watch(
      () => [props.stateLoaded, props.options.length],
      ([loaded, len], [prevLoaded]) => {
        if (!prevLoaded && loaded && len > 0 && !props.disabled && props.autoExpand && !prefersReducedMotion) {
          // Skip if user is already interacting with another control
          const activeEl = document.activeElement
          const btnEl = buttonRef.value?.$el ?? buttonRef.value
          const userBusy = activeEl && activeEl !== document.body && btnEl && !btnEl.contains(activeEl)

          // Allow auto-expand unless user is inside another input or textarea (text entry)
          if (userBusy && ['INPUT','TEXTAREA'].includes(activeEl.tagName)) {
            return
          }
          console.debug('AutoExpand →', { placeholder: props.placeholder, len })
          // Delay slightly to allow spinner removal & DOM update
          setTimeout(async () => {
            await nextTick()
            const el = buttonRef.value?.$el ?? buttonRef.value
            try {
              if (el && typeof el.click === 'function') {
                el.focus({ preventScroll: true })
                el.click()
                console.debug('AutoExpand click dispatched', { placeholder: props.placeholder })
                triggerResize()
              }
            } catch (error) {
              console.warn('Auto-expand failed:', error)
            }
          }, 200)
        }
      },
      { flush: 'post' }
    )

    /** Trigger iframeResizer size recalculation */
    const triggerResize = () => {
      if (window.parentIFrame && window.parentIFrame.size) {
        window.parentIFrame.size()
        // second call after dropdown animation completes (~300ms)
        setTimeout(() => {
          window.parentIFrame && window.parentIFrame.size()
        }, 350)
      }
    }

    // Resize after value change (dropdown closes)
    watch(() => props.modelValue, () => {
      triggerResize()
    })

    onMounted(() => {
      if (buttonRef.value) {
        (buttonRef.value.$el ?? buttonRef.value).addEventListener('click', triggerResize)
      }
    })

    onUnmounted(() => {
      if (buttonRef.value) {
        (buttonRef.value.$el ?? buttonRef.value).removeEventListener('click', triggerResize)
      }
    })

    return {
      selectedValue,
      selectedOption,
      getDisplayText,
      getDisplayData,
      buttonRef,
      triggerResize
    }
  }
}
</script>

<style scoped>
.spinner-external svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
