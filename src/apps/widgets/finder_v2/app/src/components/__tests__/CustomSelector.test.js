/**
 * Unit tests for CustomSelector component
 * Tests the generation display logic for empty generation names
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import CustomSelector from '../CustomSelector.vue'

describe('CustomSelector', () => {
  let wrapper

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('Generation Display Logic', () => {
    it('displays generation name with year ranges when name is not empty', async () => {
      const generationWithName = {
        slug: 'gen1',
        name: 'Generation 1',
        year_ranges: ['2020-2024'],
        start: 2020,
        end: 2024
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [generationWithName],
          modelValue: '',
          placeholder: 'Select Generation'
        }
      })

      // Open the dropdown
      await wrapper.find('.form-control').trigger('click')
      
      // Check that the option displays "Generation 1, 2020-2024"
      const option = wrapper.find('[role="option"]')
      expect(option.text()).toBe('Generation 1, 2020-2024')
    })

    it('displays only year ranges when generation name is empty', async () => {
      const generationWithEmptyName = {
        slug: '4d8bd3f735',
        name: '',
        year_ranges: ['2020-2024'],
        start: 2020,
        end: 2026
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [generationWithEmptyName],
          modelValue: '',
          placeholder: 'Select Generation'
        }
      })

      // Open the dropdown
      await wrapper.find('.form-control').trigger('click')
      
      // Check that the option displays only "2020-2024"
      const option = wrapper.find('[role="option"]')
      expect(option.text()).toBe('2020-2024')
    })

    it('displays only year ranges when generation name is null', async () => {
      const generationWithNullName = {
        slug: 'gen-null',
        name: null,
        year_ranges: ['2018-2022'],
        start: 2018,
        end: 2022
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [generationWithNullName],
          modelValue: '',
          placeholder: 'Select Generation'
        }
      })

      // Open the dropdown
      await wrapper.find('.form-control').trigger('click')
      
      // Check that the option displays only "2018-2022"
      const option = wrapper.find('[role="option"]')
      expect(option.text()).toBe('2018-2022')
    })

    it('displays only year ranges when generation name is whitespace', async () => {
      const generationWithWhitespaceName = {
        slug: 'gen-ws',
        name: '   ',
        year_ranges: ['2019-2023'],
        start: 2019,
        end: 2023
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [generationWithWhitespaceName],
          modelValue: '',
          placeholder: 'Select Generation'
        }
      })

      // Open the dropdown
      await wrapper.find('.form-control').trigger('click')
      
      // Check that the option displays only "2019-2023"
      const option = wrapper.find('[role="option"]')
      expect(option.text()).toBe('2019-2023')
    })

    it('handles multiple year ranges correctly', async () => {
      const generationWithMultipleRanges = {
        slug: 'gen-multi',
        name: 'Multi Gen',
        year_ranges: ['2015-2018', '2020-2024'],
        start: 2015,
        end: 2024
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [generationWithMultipleRanges],
          modelValue: '',
          placeholder: 'Select Generation'
        }
      })

      // Open the dropdown
      await wrapper.find('.form-control').trigger('click')
      
      // Check that the option displays "Multi Gen, 2015-2018, 2020-2024"
      const option = wrapper.find('[role="option"]')
      expect(option.text()).toBe('Multi Gen, 2015-2018, 2020-2024')
    })

    it('displays selected generation correctly in button when name is empty', async () => {
      const generationWithEmptyName = {
        slug: '4d8bd3f735',
        name: '',
        year_ranges: ['2020-2024'],
        start: 2020,
        end: 2026
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [generationWithEmptyName],
          modelValue: '4d8bd3f735',
          placeholder: 'Select Generation'
        }
      })

      // Check that the button displays only "2020-2024"
      const buttonText = wrapper.find('.form-control span').text()
      expect(buttonText).toBe('2020-2024')
    })

    it('displays selected generation correctly in button when name exists', async () => {
      const generationWithName = {
        slug: 'gen1',
        name: 'Generation 1',
        year_ranges: ['2020-2024'],
        start: 2020,
        end: 2024
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [generationWithName],
          modelValue: 'gen1',
          placeholder: 'Select Generation'
        }
      })

      // Check that the button displays "Generation 1, 2020-2024"
      const buttonText = wrapper.find('.form-control span').text()
      expect(buttonText).toBe('Generation 1, 2020-2024')
    })
  })

  describe('Non-Generation Options', () => {
    it('displays regular options normally (make, model, etc.)', async () => {
      const makeOption = {
        slug: 'toyota',
        name: 'Toyota'
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [makeOption],
          modelValue: '',
          placeholder: 'Select Make'
        }
      })

      // Open the dropdown
      await wrapper.find('.form-control').trigger('click')

      // Check that the option displays just "Toyota"
      const option = wrapper.find('[role="option"]')
      expect(option.text()).toBe('Toyota')
    })

    it('handles empty name for non-generation options', async () => {
      const optionWithEmptyName = {
        slug: 'empty',
        name: ''
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [optionWithEmptyName],
          modelValue: '',
          placeholder: 'Select Option'
        }
      })

      // Open the dropdown
      await wrapper.find('.form-control').trigger('click')

      // Check that the option displays empty string
      const option = wrapper.find('[role="option"]')
      expect(option.text()).toBe('')
    })
  })

  describe('API Endpoint Scoping Verification', () => {
    it('makes endpoint options display normally without year_ranges formatting', async () => {
      const makesOptions = [
        { slug: 'toyota', name: 'Toyota' },
        { slug: 'honda', name: 'Honda' },
        { slug: 'aion', name: 'Aion' }
      ]

      wrapper = mount(CustomSelector, {
        props: {
          options: makesOptions,
          modelValue: '',
          placeholder: 'Select Make'
        }
      })

      await wrapper.find('.form-control').trigger('click')

      const options = wrapper.findAll('[role="option"]')
      expect(options[0].text()).toBe('Toyota')
      expect(options[1].text()).toBe('Honda')
      expect(options[2].text()).toBe('Aion')
    })

    it('models endpoint options display normally without year_ranges formatting', async () => {
      const modelsOptions = [
        { slug: 'camry', name: 'Camry' },
        { slug: 'corolla', name: 'Corolla' },
        { slug: 'u5', name: 'U5' }
      ]

      wrapper = mount(CustomSelector, {
        props: {
          options: modelsOptions,
          modelValue: '',
          placeholder: 'Select Model'
        }
      })

      await wrapper.find('.form-control').trigger('click')

      const options = wrapper.findAll('[role="option"]')
      expect(options[0].text()).toBe('Camry')
      expect(options[1].text()).toBe('Corolla')
      expect(options[2].text()).toBe('U5')
    })

    it('years endpoint options display normally without year_ranges formatting', async () => {
      const yearsOptions = [
        { slug: '2024', name: '2024' },
        { slug: '2023', name: '2023' },
        { slug: '2022', name: '2022' }
      ]

      wrapper = mount(CustomSelector, {
        props: {
          options: yearsOptions,
          modelValue: '',
          placeholder: 'Select Year'
        }
      })

      await wrapper.find('.form-control').trigger('click')

      const options = wrapper.findAll('[role="option"]')
      expect(options[0].text()).toBe('2024')
      expect(options[1].text()).toBe('2023')
      expect(options[2].text()).toBe('2022')
    })

    it('modifications endpoint options display normally without year_ranges formatting', async () => {
      const modificationsOptions = [
        { slug: 'mod1', name: '2.0L Turbo AWD' },
        { slug: 'mod2', name: '1.8L FWD' },
        { slug: 'mod3', name: '2.5L Hybrid' }
      ]

      wrapper = mount(CustomSelector, {
        props: {
          options: modificationsOptions,
          modelValue: '',
          placeholder: 'Select Modification'
        }
      })

      await wrapper.find('.form-control').trigger('click')

      const options = wrapper.findAll('[role="option"]')
      expect(options[0].text()).toBe('2.0L Turbo AWD')
      expect(options[1].text()).toBe('1.8L FWD')
      expect(options[2].text()).toBe('2.5L Hybrid')
    })

    it('does not apply generation formatting to models with year_ranges property', async () => {
      // Test critical case: model option that has year_ranges but lacks start/end properties
      const modelWithYearRanges = {
        slug: 'u5',
        name: 'U5',
        name_en: 'U5',
        regions: ['chdm'],
        year_ranges: ['2020-2024'] // Models have year_ranges but no start/end
      }

      wrapper = mount(CustomSelector, {
        props: {
          options: [modelWithYearRanges],
          modelValue: '',
          placeholder: 'Select Model'
        }
      })

      await wrapper.find('.form-control').trigger('click')

      // Should NOT apply generation formatting - models should display only name
      const option = wrapper.find('[role="option"]')
      expect(option.text()).toBe('U5')
    })

    it('mixed options with models and generations display correctly', async () => {
      const mixedOptions = [
        { slug: 'make1', name: 'Toyota' }, // Make - no year_ranges
        {
          slug: 'model1',
          name: 'U5',
          name_en: 'U5',
          regions: ['chdm'],
          year_ranges: ['2020-2024']
        }, // Model - has year_ranges but no start/end
        { slug: 'year1', name: '2024' }, // Year - no year_ranges
        {
          slug: 'gen1',
          name: 'Generation 1',
          year_ranges: ['2020-2024'],
          start: 2020,
          end: 2024,
          platform: 'CMF-CD'
        }, // Generation - has year_ranges AND start/end
        {
          slug: 'gen2',
          name: '',
          year_ranges: ['2018-2022'],
          start: 2018,
          end: 2022,
          platform: ''
        } // Generation - has year_ranges AND start/end, empty name
      ]

      wrapper = mount(CustomSelector, {
        props: {
          options: mixedOptions,
          modelValue: '',
          placeholder: 'Select Option'
        }
      })

      await wrapper.find('.form-control').trigger('click')

      const options = wrapper.findAll('[role="option"]')
      expect(options[0].text()).toBe('Toyota') // Make - normal display
      expect(options[1].text()).toBe('U5') // Model - normal display (NOT generation formatting)
      expect(options[2].text()).toBe('2024') // Year - normal display
      expect(options[3].text()).toBe('Generation 1, 2020-2024') // Generation with name
      expect(options[4].text()).toBe('2018-2022') // Generation without name
    })

    it('verifies generation objects must have both start AND end properties', async () => {
      const optionsWithPartialGenerationProps = [
        {
          slug: 'partial1',
          name: 'Has Start Only',
          year_ranges: ['2020-2024'],
          start: 2020
          // Missing end property
        },
        {
          slug: 'partial2',
          name: 'Has End Only',
          year_ranges: ['2020-2024'],
          end: 2024
          // Missing start property
        },
        {
          slug: 'complete',
          name: 'Complete Generation',
          year_ranges: ['2020-2024'],
          start: 2020,
          end: 2024
        }
      ]

      wrapper = mount(CustomSelector, {
        props: {
          options: optionsWithPartialGenerationProps,
          modelValue: '',
          placeholder: 'Select Option'
        }
      })

      await wrapper.find('.form-control').trigger('click')

      const options = wrapper.findAll('[role="option"]')
      expect(options[0].text()).toBe('Has Start Only') // No generation formatting
      expect(options[1].text()).toBe('Has End Only') // No generation formatting
      expect(options[2].text()).toBe('Complete Generation, 2020-2024') // Generation formatting
    })
  })

  describe('Basic Functionality', () => {
    it('renders with placeholder when no option is selected', () => {
      wrapper = mount(CustomSelector, {
        props: {
          options: [],
          modelValue: '',
          placeholder: 'Select Option'
        }
      })

      expect(wrapper.find('.form-control span').text()).toBe('Select Option')
    })

    it('shows loading state', () => {
      wrapper = mount(CustomSelector, {
        props: {
          options: [],
          modelValue: '',
          loading: true
        }
      })

      expect(wrapper.find('.form-control span').text()).toContain('Loading...')
    })

    it('is disabled when disabled prop is true', () => {
      wrapper = mount(CustomSelector, {
        props: {
          options: [],
          modelValue: '',
          disabled: true
        }
      })

      expect(wrapper.find('.form-control').classes()).toContain('cursor-not-allowed')
    })
  })
})
