/**
 * Unit tests for FinderV2Widget component
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import FinderV2Widget from '../FinderV2Widget.vue'

// Mock child components
vi.mock('../VehicleSearch.vue', () => ({
  default: {
    name: 'VehicleSearch',
    template: '<div data-testid="vehicle-search">Vehicle Search</div>'
  }
}))

vi.mock('../TireSearch.vue', () => ({
  default: {
    name: 'TireSearch', 
    template: '<div data-testid="tire-search">Tire Search</div>'
  }
}))

vi.mock('../RimSearch.vue', () => ({
  default: {
    name: 'RimSearch',
    template: '<div data-testid="rim-search">Rim Search</div>'
  }
}))

vi.mock('../ResultsDisplay.vue', () => ({
  default: {
    name: 'ResultsDisplay',
    template: '<div data-testid="results-display">Results Display</div>'
  }
}))

describe('FinderV2Widget', () => {
  let wrapper
  let pinia

  beforeEach(() => {
    // Set up Pinia
    pinia = createPinia()
    setActivePinia(pinia)

    // Mock global config
    global.window = {
      FinderV2Config: {
        widgetConfig: {
          tabs: {
            visible: ['by_vehicle', 'by_tire', 'by_rim'],
            primary: 'by_vehicle'
          }
        }
      }
    }
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('renders correctly with default configuration', () => {
    wrapper = mount(FinderV2Widget, {
      global: {
        plugins: [pinia]
      }
    })

    expect(wrapper.find('.finder-v2-widget').exists()).toBe(true)
  })

  it('displays tabs when multiple tabs are configured', () => {
    wrapper = mount(FinderV2Widget, {
      global: {
        plugins: [pinia]
      }
    })

    const tabs = wrapper.findAll('.nav-link')
    expect(tabs.length).toBe(3)
    expect(tabs[0].text()).toBe('By Vehicle')
    expect(tabs[1].text()).toBe('By Tire Size')
    expect(tabs[2].text()).toBe('By Rim Size')
  })

  it('hides tabs when only one tab is configured', async () => {
    global.window.FinderV2Config.widgetConfig.tabs.visible = ['by_vehicle']

    wrapper = mount(FinderV2Widget, {
      global: {
        plugins: [pinia]
      }
    })

    const tabsContainer = wrapper.find('.widget-tabs')
    expect(tabsContainer.exists()).toBe(false)
  })

  it('sets active tab to primary tab from configuration', () => {
    global.window.FinderV2Config.widgetConfig.tabs.primary = 'by_tire'

    wrapper = mount(FinderV2Widget, {
      global: {
        plugins: [pinia]
      }
    })

    // Check that tire search is displayed
    expect(wrapper.find('[data-testid="tire-search"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="vehicle-search"]').exists()).toBe(false)
  })

  it('switches tabs when tab is clicked', async () => {
    wrapper = mount(FinderV2Widget, {
      global: {
        plugins: [pinia]
      }
    })

    // Initially should show vehicle search
    expect(wrapper.find('[data-testid="vehicle-search"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="tire-search"]').exists()).toBe(false)

    // Click tire search tab
    const tireTab = wrapper.findAll('.nav-link')[1]
    await tireTab.trigger('click')

    // Should now show tire search
    expect(wrapper.find('[data-testid="vehicle-search"]').exists()).toBe(false)
    expect(wrapper.find('[data-testid="tire-search"]').exists()).toBe(true)
  })

  it('applies active class to current tab', async () => {
    wrapper = mount(FinderV2Widget, {
      global: {
        plugins: [pinia]
      }
    })

    // Initially first tab should be active
    const tabs = wrapper.findAll('.nav-link')
    expect(tabs[0].classes()).toContain('active')
    expect(tabs[1].classes()).not.toContain('active')

    // Click second tab
    await tabs[1].trigger('click')

    // Second tab should now be active
    expect(tabs[0].classes()).not.toContain('active')
    expect(tabs[1].classes()).toContain('active')
  })

  it('shows results section when results are available', () => {
    // Mock store with results
    const mockStore = {
      results: [{ id: 1, make: 'Toyota', model: 'Camry' }]
    }

    wrapper = mount(FinderV2Widget, {
      global: {
        plugins: [pinia],
        mocks: {
          hasResults: true
        }
      }
    })

    // This test would need proper store mocking
    // For now, just verify the component structure
    expect(wrapper.find('.finder-v2-widget').exists()).toBe(true)
  })

  it('handles missing configuration gracefully', () => {
    global.window.FinderV2Config = {}

    wrapper = mount(FinderV2Widget, {
      global: {
        plugins: [pinia]
      }
    })

    // Should still render without errors
    expect(wrapper.find('.finder-v2-widget').exists()).toBe(true)
  })

  it('initializes with correct component structure', () => {
    wrapper = mount(FinderV2Widget, {
      global: {
        plugins: [pinia]
      }
    })

    // Check main structure
    expect(wrapper.find('.finder-v2-widget').exists()).toBe(true)
    expect(wrapper.find('.tab-content').exists()).toBe(true)
    expect(wrapper.find('.tab-pane').exists()).toBe(true)
  })
})
