<template>
  <div class="finder-v2-widget p-1" data-iframe-height :class="themeClasses" :style="themeStyles">
    <!-- Vehicle Search (finder-v2 only supports by_vehicle search type) -->
    <div class="search-content">
      <VehicleSearch />
    </div>

    <!-- Results Display -->
    <div v-if="hasResults || loadingResults" class="results-section">
      <ResultsDisplay />
    </div>
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/finder'
import VehicleSearch from './VehicleSearch.vue'
import ResultsDisplay from './ResultsDisplay.vue'

export default {
  name: 'FinderV2Widget',
  components: {
    VehicleSearch,
    ResultsDisplay
  },
  setup() {
    const finderStore = useFinderStore()
    const { loadingResults, results } = storeToRefs(finderStore)

    // Widget configuration from global config
    const config = window.FinderV2Config || {}
    const theme = config.theme || {}

    // Check if there are search results
    const hasResults = computed(() => {
      return results.value.length > 0
    })

    // Generate theme classes based on configuration
    const themeClasses = computed(() => {
      const classes = []
      if (theme.name) {
        classes.push(`theme-${theme.name.toLowerCase().replace(/\s+/g, '-')}`)
      }
      if (theme.effects?.hoverEffect) {
        classes.push(`hover-${theme.effects.hoverEffect}`)
      }
      return classes.join(' ')
    })

    // Generate theme styles based on configuration
    const themeStyles = computed(() => {
      const styles = {}
      
      // Apply theme colors
      if (theme.colors) {
        styles['--theme-primary'] = theme.colors.primary
        styles['--theme-secondary'] = theme.colors.secondary
        styles['--theme-accent'] = theme.colors.accent
        styles['--theme-background'] = theme.colors.background
        styles['--theme-text'] = theme.colors.text
        
        // Convert hex colors to RGB for rgba() usage
        styles['--theme-primary-rgb'] = hexToRgb(theme.colors.primary)
        styles['--theme-secondary-rgb'] = hexToRgb(theme.colors.secondary)
        styles['--theme-accent-rgb'] = hexToRgb(theme.colors.accent)
      }
      
      // Apply typography
      if (theme.typography) {
        styles['--theme-font-family'] = theme.typography.fontFamily
        styles['--theme-font-size'] = theme.typography.fontSize
        styles['--theme-font-weight'] = theme.typography.fontWeight
        styles['--theme-line-height'] = theme.typography.lineHeight
        styles['--theme-letter-spacing'] = theme.typography.letterSpacing
      }
      
      // Apply spacing
      if (theme.spacing) {
        styles['--theme-padding'] = theme.spacing.padding
        styles['--theme-margin'] = theme.spacing.margin
      }
      
      // Apply effects
      if (theme.effects) {
        styles['--theme-border-radius'] = theme.effects.borderRadius
        styles['--theme-border-width'] = theme.effects.borderWidth
        styles['--theme-animation-speed'] = theme.effects.animationSpeed
        
        // Apply shadow based on intensity
        const shadowIntensity = theme.effects.shadowIntensity
        const shadows = {
          'none': 'none',
          'light': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
          'medium': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          'heavy': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        }
        styles['--theme-shadow'] = shadows[shadowIntensity] || shadows['medium']
      }
      
      return styles
    })

    // Helper function to convert hex to RGB
    function hexToRgb(hex) {
      if (!hex) return '0, 0, 0'
      
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result 
        ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
        : '0, 0, 0'
    }

    onMounted(() => {
      // Initialize finder store with configuration
      finderStore.initialize(config)
      
      // Apply theme styles to document root for global access
      if (theme.colors || theme.typography || theme.spacing || theme.effects) {
        const root = document.documentElement
        Object.entries(themeStyles.value).forEach(([property, value]) => {
          root.style.setProperty(property, value)
        })
      }
    })

    return {
      loadingResults,
      hasResults,
      themeClasses,
      themeStyles
    }
  }
}
</script>

<style scoped>
.finder-v2-widget {
  width: 100%;
}

.search-content {
  padding: 1rem 0;
}

.results-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}
</style>
