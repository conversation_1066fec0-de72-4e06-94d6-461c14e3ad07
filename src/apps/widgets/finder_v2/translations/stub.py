from collections import OrderedDict

FINDER_V2_TRANSLATION_STUB = OrderedDict([
    ("Make", ""),
    ("Year", ""),
    ("Model", ""),
    ("Generation", ""),  # New for alternative flow
    ("Modification", ""),  # New for v2 API
    ("Tire Width", ""),
    ("Aspect Ratio", ""),
    ("<PERSON>im <PERSON>", ""),
    ("<PERSON>im <PERSON>id<PERSON>", ""),
    ("Bolt Pattern", ""),
    ("By Vehicle", ""),
    ("By Tire Size", ""),
    ("By Rim Size", ""),
    ("Specify make, year and model to find matching wheels:", ""),
    ("Specify values for fields below to find matching vehicles:", ""),
    ("Wheels with these parameters may fit the following vehicles:", ""),
    ("Please, note:", ""),
    ("highlighted", ""),
    ("items are for OEM wheels", ""),
    ("on", ""),
    ("Trim", ""),
    ("Tire", ""),
    ("Rim", ""),
    ("THD", ""),
    ("CB", ""),
    ("Trims and Years for", ""),
    ("Loading...", ""),  # New for Vue 3 loading states
    ("Select option", ""),  # New for Vue 3 selectors
    ("No results found", ""),  # New for Vue 3 error states

    ("Japanese domestic market", ""),
    ("US domestic market", ""),
    ("European domestic market", ""),
    ("Southeast Asian Market", ""),
    ("Australian Domestic Market", ""),
    ("Latin American domestic market", ""),
    ("Middle East Domestic Market", ""),
    ("Mexican domestic market", ""),
    ("South Korean Domestic Market", ""),
    ("Canadian domestic market", ""),
    ("South African Domestic Market", ""),
    ("Chinese Domestic Market", ""),
])
