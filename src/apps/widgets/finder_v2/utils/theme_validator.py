"""
Theme validator utility for finder-v2 widgets.

This module provides comprehensive validation for theme configurations,
ensuring security, accessibility, and performance compliance.
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


class ThemeValidator:
    """
    Validates theme configurations for security, accessibility, and performance.
    
    This class provides comprehensive validation including:
    - Color format validation and accessibility checks
    - CSS property whitelisting and sanitization
    - Performance impact assessment
    - Security vulnerability detection
    """
    
    # Allowed CSS properties for advanced configuration
    ALLOWED_CSS_PROPERTIES = {
        'color', 'background-color', 'border-color', 'font-family', 'font-size',
        'font-weight', 'line-height', 'padding', 'margin', 'border-radius',
        'box-shadow', 'text-align', 'text-decoration', 'text-transform',
        'letter-spacing', 'word-spacing', 'opacity', 'transition',
        'animation-duration', 'animation-timing-function'
    }
    
    # Dangerous CSS patterns that should be blocked
    DANGEROUS_PATTERNS = [
        r'javascript\s*:',
        r'expression\s*\(',
        r'behavior\s*:',
        r'binding\s*:',
        r'@import\s+url\s*\(',
        r'url\s*\(\s*["\']?\s*javascript:',
        r'vbscript\s*:',
        r'data\s*:\s*text/html',
        r'<script',
        r'</script',
        r'on\w+\s*=',
        r'\\[0-9a-f]{1,6}',  # Unicode escapes
    ]
    
    # Minimum contrast ratios for accessibility
    WCAG_AA_CONTRAST_RATIO = 4.5
    WCAG_AAA_CONTRAST_RATIO = 7.0
    
    def __init__(self):
        """Initialize the theme validator."""
        self.errors = []
        self.warnings = []
    
    def validate_theme(self, theme_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate complete theme configuration.
        
        Args:
            theme_data: Theme configuration dictionary
            
        Returns:
            Validation result with errors and warnings
        """
        self.errors = []
        self.warnings = []
        
        # Validate color configuration
        if 'colors' in theme_data:
            self._validate_colors(theme_data['colors'])
        
        # Validate typography configuration
        if 'typography' in theme_data:
            self._validate_typography(theme_data['typography'])
        
        # Validate effects configuration
        if 'effects' in theme_data:
            self._validate_effects(theme_data['effects'])
        
        # Validate advanced configuration
        if 'advanced_config' in theme_data:
            self._validate_advanced_config(theme_data['advanced_config'])
        
        # Perform accessibility checks
        self._validate_accessibility(theme_data)
        
        # Perform security checks
        self._validate_security(theme_data)
        
        return {
            'valid': len(self.errors) == 0,
            'errors': self.errors,
            'warnings': self.warnings,
            'accessibility_score': self._calculate_accessibility_score(theme_data),
            'security_score': self._calculate_security_score(theme_data),
        }
    
    def validate_color(self, color_value: str) -> bool:
        """
        Validate a single color value.
        
        Args:
            color_value: Color value to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not color_value:
            return False
        
        # Check hex color format
        hex_pattern = r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$'
        if re.match(hex_pattern, color_value):
            return True
        
        # Check for named colors (basic set)
        named_colors = {
            'transparent', 'currentColor', 'inherit', 'initial', 'unset',
            'black', 'white', 'red', 'green', 'blue', 'yellow', 'cyan',
            'magenta', 'gray', 'orange', 'purple', 'pink', 'brown'
        }
        
        if color_value.lower() in named_colors:
            return True
        
        # Check RGB/RGBA format
        rgb_pattern = r'^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(?:,\s*[\d.]+\s*)?\)$'
        if re.match(rgb_pattern, color_value):
            return True
        
        # Check HSL/HSLA format
        hsl_pattern = r'^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(?:,\s*[\d.]+\s*)?\)$'
        if re.match(hsl_pattern, color_value):
            return True
        
        return False
    
    def calculate_contrast_ratio(self, color1: str, color2: str) -> float:
        """
        Calculate contrast ratio between two colors.
        
        Args:
            color1: First color (hex format)
            color2: Second color (hex format)
            
        Returns:
            Contrast ratio (1.0 to 21.0)
        """
        try:
            luminance1 = self._calculate_luminance(color1)
            luminance2 = self._calculate_luminance(color2)
            
            # Ensure lighter color is first
            if luminance1 < luminance2:
                luminance1, luminance2 = luminance2, luminance1
            
            return (luminance1 + 0.05) / (luminance2 + 0.05)
        except:
            return 1.0
    
    def validate_css_property(self, property_name: str, property_value: str) -> bool:
        """
        Validate a CSS property name and value.
        
        Args:
            property_name: CSS property name
            property_value: CSS property value
            
        Returns:
            True if valid, False otherwise
        """
        # Check if property is allowed
        if property_name not in self.ALLOWED_CSS_PROPERTIES:
            return False
        
        # Check for dangerous patterns
        combined_value = f"{property_name}: {property_value}"
        for pattern in self.DANGEROUS_PATTERNS:
            if re.search(pattern, combined_value, re.IGNORECASE):
                return False
        
        return True
    
    def _validate_colors(self, colors: Dict[str, str]) -> None:
        """Validate color configuration."""
        required_colors = ['primary', 'secondary', 'accent', 'background', 'text']
        
        for color_name in required_colors:
            if color_name not in colors:
                self.errors.append(f"Missing required color: {color_name}")
                continue
            
            color_value = colors[color_name]
            if not self.validate_color(color_value):
                self.errors.append(f"Invalid color format for {color_name}: {color_value}")
    
    def _validate_typography(self, typography: Dict[str, str]) -> None:
        """Validate typography configuration."""
        if 'font_family' in typography:
            font_family = typography['font_family']
            if not isinstance(font_family, str) or len(font_family) > 200:
                self.errors.append("Invalid font family specification")
        
        if 'base_font_size' in typography:
            font_size = typography['base_font_size']
            if not re.match(r'^\d+px$', font_size):
                self.errors.append("Font size must be specified in pixels (e.g., '16px')")
            else:
                size_value = int(font_size[:-2])
                if size_value < 10 or size_value > 24:
                    self.warnings.append(f"Font size {font_size} may impact readability")
    
    def _validate_effects(self, effects: Dict[str, str]) -> None:
        """Validate effects configuration."""
        if 'border_radius' in effects:
            border_radius = effects['border_radius']
            if not re.match(r'^[\d.]+r?em$|^\d+px$|^0$', border_radius):
                self.errors.append("Invalid border radius format")
        
        if 'shadow_intensity' in effects:
            shadow_intensity = effects['shadow_intensity']
            allowed_intensities = ['none', 'light', 'medium', 'heavy']
            if shadow_intensity not in allowed_intensities:
                self.errors.append(f"Invalid shadow intensity: {shadow_intensity}")
    
    def _validate_advanced_config(self, advanced_config: Dict[str, Any]) -> None:
        """Validate advanced configuration."""
        if not isinstance(advanced_config, dict):
            self.errors.append("Advanced configuration must be a JSON object")
            return
        
        # Check for allowed keys
        allowed_keys = {
            'custom_font_family', 'custom_font_sizes', 'responsive_overrides',
            'animation_speed', 'transition_timing', 'hover_effects',
            'focus_styles', 'custom_shadows', 'gradient_colors'
        }
        
        for key in advanced_config.keys():
            if key not in allowed_keys:
                self.warnings.append(f"Unknown advanced configuration key: {key}")
        
        # Validate responsive overrides
        if 'responsive_overrides' in advanced_config:
            self._validate_responsive_overrides(advanced_config['responsive_overrides'])
    
    def _validate_responsive_overrides(self, overrides: Dict[str, Any]) -> None:
        """Validate responsive override configuration."""
        allowed_breakpoints = ['mobile', 'tablet', 'desktop']
        
        for breakpoint, config in overrides.items():
            if breakpoint not in allowed_breakpoints:
                self.errors.append(f"Invalid responsive breakpoint: {breakpoint}")
                continue
            
            if not isinstance(config, dict):
                self.errors.append(f"Responsive override for {breakpoint} must be an object")
                continue
            
            # Validate each override property
            for prop_name, prop_value in config.items():
                if not self.validate_css_property(prop_name, str(prop_value)):
                    self.errors.append(f"Invalid responsive override: {prop_name}")
    
    def _validate_accessibility(self, theme_data: Dict[str, Any]) -> None:
        """Validate accessibility compliance."""
        if 'colors' not in theme_data:
            return
        
        colors = theme_data['colors']
        
        # Check background-text contrast
        if 'background' in colors and 'text' in colors:
            contrast_ratio = self.calculate_contrast_ratio(
                colors['background'], colors['text']
            )
            
            if contrast_ratio < self.WCAG_AA_CONTRAST_RATIO:
                self.errors.append(
                    f"Background-text contrast ratio ({contrast_ratio:.2f}) "
                    f"does not meet WCAG AA standards (minimum {self.WCAG_AA_CONTRAST_RATIO})"
                )
            elif contrast_ratio < self.WCAG_AAA_CONTRAST_RATIO:
                self.warnings.append(
                    f"Background-text contrast ratio ({contrast_ratio:.2f}) "
                    f"does not meet WCAG AAA standards (minimum {self.WCAG_AAA_CONTRAST_RATIO})"
                )
        
        # Check primary-background contrast
        if 'primary' in colors and 'background' in colors:
            contrast_ratio = self.calculate_contrast_ratio(
                colors['primary'], colors['background']
            )
            
            if contrast_ratio < 3.0:  # Minimum for UI elements
                self.warnings.append(
                    f"Primary-background contrast ratio ({contrast_ratio:.2f}) "
                    "may not be sufficient for UI elements"
                )
    
    def _validate_security(self, theme_data: Dict[str, Any]) -> None:
        """Validate security aspects of theme configuration."""
        # Check for script injection attempts
        json_string = json.dumps(theme_data)
        
        for pattern in self.DANGEROUS_PATTERNS:
            if re.search(pattern, json_string, re.IGNORECASE):
                self.errors.append("Potentially dangerous content detected in theme configuration")
                break
        
        # Check for excessively long values that might cause DoS
        def check_length_recursive(obj, path=""):
            if isinstance(obj, str):
                if len(obj) > 1000:
                    self.warnings.append(f"Excessively long value at {path}: {len(obj)} characters")
            elif isinstance(obj, dict):
                for key, value in obj.items():
                    check_length_recursive(value, f"{path}.{key}")
            elif isinstance(obj, list):
                for i, value in enumerate(obj):
                    check_length_recursive(value, f"{path}[{i}]")
        
        check_length_recursive(theme_data)
    
    def _calculate_luminance(self, hex_color: str) -> float:
        """Calculate relative luminance of a color."""
        # Remove # if present
        hex_color = hex_color.lstrip('#')
        
        # Handle 3-digit hex
        if len(hex_color) == 3:
            hex_color = ''.join([c*2 for c in hex_color])
        
        # Convert to RGB
        r = int(hex_color[0:2], 16) / 255.0
        g = int(hex_color[2:4], 16) / 255.0
        b = int(hex_color[4:6], 16) / 255.0
        
        # Apply gamma correction
        def gamma_correct(c):
            if c <= 0.03928:
                return c / 12.92
            else:
                return pow((c + 0.055) / 1.055, 2.4)
        
        r = gamma_correct(r)
        g = gamma_correct(g)
        b = gamma_correct(b)
        
        return 0.2126 * r + 0.7152 * g + 0.0722 * b
    
    def _calculate_accessibility_score(self, theme_data: Dict[str, Any]) -> float:
        """Calculate accessibility score (0-100)."""
        score = 100.0
        
        if 'colors' in theme_data:
            colors = theme_data['colors']
            
            # Check contrast ratios
            if 'background' in colors and 'text' in colors:
                contrast_ratio = self.calculate_contrast_ratio(
                    colors['background'], colors['text']
                )
                
                if contrast_ratio < self.WCAG_AA_CONTRAST_RATIO:
                    score -= 30.0  # Major penalty for failing AA
                elif contrast_ratio < self.WCAG_AAA_CONTRAST_RATIO:
                    score -= 10.0  # Minor penalty for not meeting AAA
        
        # Penalty for accessibility errors
        accessibility_errors = [e for e in self.errors if 'contrast' in e.lower()]
        score -= len(accessibility_errors) * 20.0
        
        # Penalty for accessibility warnings
        accessibility_warnings = [w for w in self.warnings if 'contrast' in w.lower()]
        score -= len(accessibility_warnings) * 5.0
        
        return max(0.0, min(100.0, score))
    
    def _calculate_security_score(self, theme_data: Dict[str, Any]) -> float:
        """Calculate security score (0-100)."""
        score = 100.0
        
        # Major penalty for security errors
        security_errors = [e for e in self.errors if 'dangerous' in e.lower()]
        score -= len(security_errors) * 50.0
        
        # Minor penalty for security warnings
        security_warnings = [w for w in self.warnings if any(
            keyword in w.lower() for keyword in ['long', 'unknown', 'invalid']
        )]
        score -= len(security_warnings) * 5.0
        
        return max(0.0, min(100.0, score))
    
    @staticmethod
    def create_safe_theme_defaults() -> Dict[str, Any]:
        """
        Create a safe default theme configuration.
        
        Returns:
            Safe theme configuration dictionary
        """
        return {
            'theme_name': 'Safe Default',
            'colors': {
                'primary': '#3B82F6',
                'secondary': '#6B7280',
                'accent': '#10B981',
                'background': '#FFFFFF',
                'text': '#1F2937',
            },
            'typography': {
                'font_family': 'system-ui',
                'base_font_size': '16px',
            },
            'effects': {
                'border_radius': '0.375rem',
                'shadow_intensity': 'medium',
            },
            'advanced_config': {},
        }
    
    @staticmethod
    def sanitize_theme_data(theme_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize theme data by removing dangerous content.
        
        Args:
            theme_data: Theme configuration to sanitize
            
        Returns:
            Sanitized theme configuration
        """
        validator = ThemeValidator()
        sanitized = {}
        
        # Copy safe values
        for key, value in theme_data.items():
            if key in ['colors', 'typography', 'effects']:
                sanitized[key] = value
            elif key == 'advanced_config' and isinstance(value, dict):
                # Sanitize advanced config
                sanitized_advanced = {}
                for adv_key, adv_value in value.items():
                    if adv_key in ['custom_font_family', 'custom_font_sizes', 'responsive_overrides']:
                        sanitized_advanced[adv_key] = adv_value
                sanitized[key] = sanitized_advanced
            else:
                sanitized[key] = value
        
        return sanitized