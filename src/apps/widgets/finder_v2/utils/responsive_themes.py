"""
Responsive theme utilities for finder-v2 widgets.

This module provides utilities for responsive theme configuration and testing,
ensuring themes work correctly across different device sizes and screen resolutions.
"""

from typing import Dict, List, Any, Optional, Tuple
import json


class ResponsiveThemeManager:
    """
    Manages responsive theme configurations and testing.
    
    This class provides functionality to:
    - Configure responsive theme overrides
    - Test themes across different breakpoints
    - Validate responsive design compliance
    - Generate responsive CSS
    """
    
    # Standard breakpoints for responsive design
    BREAKPOINTS = {
        'mobile': {
            'name': 'Mobile',
            'min_width': 0,
            'max_width': 767,
            'media_query': '(max-width: 767px)',
            'viewport': {'width': 375, 'height': 667},
            'icon': 'fa-mobile-alt',
            'description': 'Mobile phones and small screens'
        },
        'tablet': {
            'name': 'Tablet',
            'min_width': 768,
            'max_width': 1023,
            'media_query': '(min-width: 768px) and (max-width: 1023px)',
            'viewport': {'width': 768, 'height': 1024},
            'icon': 'fa-tablet-alt',
            'description': 'Tablets and medium screens'
        },
        'desktop': {
            'name': 'Desktop',
            'min_width': 1024,
            'max_width': None,
            'media_query': '(min-width: 1024px)',
            'viewport': {'width': 1200, 'height': 800},
            'icon': 'fa-desktop',
            'description': 'Desktop computers and large screens'
        }
    }
    
    # Common responsive theme adjustments
    RESPONSIVE_ADJUSTMENTS = {
        'mobile': {
            'base_font_size': '14px',
            'border_radius': '0.25rem',
            'spacing_scale': 0.8,
            'touch_target_min': '44px'
        },
        'tablet': {
            'base_font_size': '16px',
            'border_radius': '0.375rem',
            'spacing_scale': 1.0,
            'touch_target_min': '44px'
        },
        'desktop': {
            'base_font_size': '16px',
            'border_radius': '0.5rem',
            'spacing_scale': 1.2,
            'touch_target_min': '32px'
        }
    }
    
    def __init__(self, theme=None):
        """
        Initialize responsive theme manager.
        
        Args:
            theme: WidgetTheme instance or None
        """
        self.theme = theme
        self.responsive_config = self._load_responsive_config()
    
    def _load_responsive_config(self) -> Dict[str, Any]:
        """Load responsive configuration from theme."""
        if not self.theme or not self.theme.advanced_config:
            return {}
        
        return self.theme.advanced_config.get('responsive_overrides', {})
    
    def get_breakpoint_config(self, breakpoint: str) -> Dict[str, Any]:
        """
        Get configuration for a specific breakpoint.
        
        Args:
            breakpoint: Breakpoint name ('mobile', 'tablet', 'desktop')
            
        Returns:
            Breakpoint configuration dictionary
        """
        return self.BREAKPOINTS.get(breakpoint, {})
    
    def get_responsive_overrides(self, breakpoint: str) -> Dict[str, Any]:
        """
        Get responsive overrides for a specific breakpoint.
        
        Args:
            breakpoint: Breakpoint name
            
        Returns:
            Dictionary of responsive overrides
        """
        base_overrides = self.RESPONSIVE_ADJUSTMENTS.get(breakpoint, {})
        custom_overrides = self.responsive_config.get(breakpoint, {})
        
        # Merge base and custom overrides
        return {**base_overrides, **custom_overrides}
    
    def generate_responsive_css(self, theme_data: Dict[str, Any]) -> str:
        """
        Generate responsive CSS for theme.
        
        Args:
            theme_data: Theme configuration data
            
        Returns:
            CSS string with responsive rules
        """
        css_rules = []
        
        # Generate CSS for each breakpoint
        for breakpoint, config in self.BREAKPOINTS.items():
            overrides = self.get_responsive_overrides(breakpoint)
            
            if not overrides:
                continue
            
            # Create media query
            media_query = config['media_query']
            css_rules.append(f"@media {media_query} {{")
            css_rules.append("  :root {")
            
            # Apply overrides
            for prop_name, prop_value in overrides.items():
                css_prop = self._convert_to_css_property(prop_name, prop_value)
                if css_prop:
                    css_rules.append(f"    {css_prop};")
            
            css_rules.append("  }")
            css_rules.append("}")
            css_rules.append("")
        
        return "\n".join(css_rules)
    
    def _convert_to_css_property(self, prop_name: str, prop_value: Any) -> Optional[str]:
        """Convert responsive property to CSS custom property."""
        property_mapping = {
            'base_font_size': ('--theme-font-size', prop_value),
            'border_radius': ('--theme-border-radius', prop_value),
            'spacing_scale': ('--theme-spacing-scale', str(prop_value)),
            'touch_target_min': ('--theme-touch-target-min', prop_value),
        }
        
        if prop_name in property_mapping:
            css_prop, css_value = property_mapping[prop_name]
            return f"{css_prop}: {css_value}"
        
        return None
    
    def validate_responsive_theme(self, theme_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate theme for responsive design compliance.
        
        Args:
            theme_data: Theme configuration data
            
        Returns:
            Validation results dictionary
        """
        results = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'breakpoint_scores': {}
        }
        
        # Test each breakpoint
        for breakpoint, config in self.BREAKPOINTS.items():
            breakpoint_result = self._validate_breakpoint(breakpoint, theme_data)
            results['breakpoint_scores'][breakpoint] = breakpoint_result
            
            # Aggregate warnings and errors
            results['warnings'].extend(breakpoint_result.get('warnings', []))
            results['errors'].extend(breakpoint_result.get('errors', []))
        
        # Update overall validity
        results['valid'] = len(results['errors']) == 0
        
        return results
    
    def _validate_breakpoint(self, breakpoint: str, theme_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate theme for a specific breakpoint."""
        result = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'score': 100.0
        }
        
        overrides = self.get_responsive_overrides(breakpoint)
        
        # Check touch target sizes for mobile/tablet
        if breakpoint in ['mobile', 'tablet']:
            min_touch_target = int(overrides.get('touch_target_min', '44px')[:-2])
            if min_touch_target < 44:
                result['warnings'].append(
                    f"{breakpoint.title()}: Touch targets should be at least 44px for accessibility"
                )
                result['score'] -= 10
        
        # Check font size appropriateness
        font_size = overrides.get('base_font_size', '16px')
        font_size_value = int(font_size[:-2])
        
        if breakpoint == 'mobile' and font_size_value < 14:
            result['warnings'].append(
                f"{breakpoint.title()}: Font size may be too small for mobile readability"
            )
            result['score'] -= 15
        
        # Check border radius scaling
        border_radius = overrides.get('border_radius', '0.375rem')
        if breakpoint == 'mobile' and 'rem' in border_radius:
            radius_value = float(border_radius[:-3])
            if radius_value > 0.5:
                result['warnings'].append(
                    f"{breakpoint.title()}: Large border radius may look odd on small screens"
                )
                result['score'] -= 5
        
        result['valid'] = len(result['errors']) == 0
        
        return result
    
    def get_responsive_test_cases(self) -> List[Dict[str, Any]]:
        """
        Get responsive test cases for theme testing.
        
        Returns:
            List of test case configurations
        """
        test_cases = []
        
        for breakpoint, config in self.BREAKPOINTS.items():
            test_cases.append({
                'name': f"{config['name']} Test",
                'breakpoint': breakpoint,
                'viewport': config['viewport'],
                'media_query': config['media_query'],
                'description': config['description'],
                'icon': config['icon'],
                'test_scenarios': self._get_breakpoint_test_scenarios(breakpoint)
            })
        
        return test_cases
    
    def _get_breakpoint_test_scenarios(self, breakpoint: str) -> List[Dict[str, Any]]:
        """Get test scenarios for a specific breakpoint."""
        common_scenarios = [
            {
                'name': 'Form Elements',
                'description': 'Test form inputs, buttons, and selectors',
                'elements': ['.form-control', '.btn', '.custom-select']
            },
            {
                'name': 'Text Readability',
                'description': 'Test text size and contrast across different content',
                'elements': ['.widget', 'h1', 'h2', 'h3', 'p', '.small']
            },
            {
                'name': 'Spacing and Layout',
                'description': 'Test padding, margins, and component spacing',
                'elements': ['.form-group', '.card', '.container']
            }
        ]
        
        # Add breakpoint-specific scenarios
        if breakpoint == 'mobile':
            common_scenarios.extend([
                {
                    'name': 'Touch Targets',
                    'description': 'Test touch target sizes and spacing',
                    'elements': ['.btn', '.form-control', '.custom-select', 'a']
                },
                {
                    'name': 'Text Input',
                    'description': 'Test text input on mobile keyboards',
                    'elements': ['input[type="text"]', 'input[type="email"]', 'textarea']
                }
            ])
        
        if breakpoint == 'tablet':
            common_scenarios.extend([
                {
                    'name': 'Orientation Changes',
                    'description': 'Test layout in portrait and landscape',
                    'elements': ['.container', '.row', '.col']
                }
            ])
        
        if breakpoint == 'desktop':
            common_scenarios.extend([
                {
                    'name': 'Hover States',
                    'description': 'Test hover effects and interactions',
                    'elements': ['.btn:hover', '.form-control:hover', 'a:hover']
                },
                {
                    'name': 'Focus States',
                    'description': 'Test keyboard focus indicators',
                    'elements': ['.btn:focus', '.form-control:focus', 'a:focus']
                }
            ])
        
        return common_scenarios
    
    def create_responsive_config_template(self) -> Dict[str, Any]:
        """
        Create a template for responsive configuration.
        
        Returns:
            Template configuration dictionary
        """
        template = {
            'responsive_overrides': {}
        }
        
        for breakpoint, adjustments in self.RESPONSIVE_ADJUSTMENTS.items():
            template['responsive_overrides'][breakpoint] = {
                'description': f"Responsive overrides for {breakpoint} devices",
                'enabled': True,
                **adjustments
            }
        
        return template
    
    def apply_responsive_preset(self, preset_name: str) -> Dict[str, Any]:
        """
        Apply a responsive preset configuration.
        
        Args:
            preset_name: Name of the preset to apply
            
        Returns:
            Applied configuration dictionary
        """
        presets = {
            'mobile_first': {
                'mobile': {
                    'base_font_size': '16px',  # Prevent zoom on iOS
                    'border_radius': '0.25rem',
                    'spacing_scale': 0.8,
                    'touch_target_min': '44px'
                },
                'tablet': {
                    'base_font_size': '16px',
                    'border_radius': '0.375rem',
                    'spacing_scale': 1.0,
                    'touch_target_min': '44px'
                },
                'desktop': {
                    'base_font_size': '16px',
                    'border_radius': '0.5rem',
                    'spacing_scale': 1.2,
                    'touch_target_min': '32px'
                }
            },
            'accessibility_focused': {
                'mobile': {
                    'base_font_size': '18px',
                    'border_radius': '0.5rem',
                    'spacing_scale': 1.0,
                    'touch_target_min': '48px'
                },
                'tablet': {
                    'base_font_size': '18px',
                    'border_radius': '0.5rem',
                    'spacing_scale': 1.2,
                    'touch_target_min': '48px'
                },
                'desktop': {
                    'base_font_size': '18px',
                    'border_radius': '0.5rem',
                    'spacing_scale': 1.4,
                    'touch_target_min': '36px'
                }
            },
            'compact': {
                'mobile': {
                    'base_font_size': '14px',
                    'border_radius': '0.25rem',
                    'spacing_scale': 0.6,
                    'touch_target_min': '40px'
                },
                'tablet': {
                    'base_font_size': '15px',
                    'border_radius': '0.25rem',
                    'spacing_scale': 0.8,
                    'touch_target_min': '40px'
                },
                'desktop': {
                    'base_font_size': '15px',
                    'border_radius': '0.25rem',
                    'spacing_scale': 1.0,
                    'touch_target_min': '32px'
                }
            }
        }
        
        return presets.get(preset_name, {})
    
    def generate_responsive_test_report(self, theme_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a comprehensive responsive test report.
        
        Args:
            theme_data: Theme configuration data
            
        Returns:
            Detailed test report dictionary
        """
        report = {
            'timestamp': self._get_timestamp(),
            'theme_name': theme_data.get('theme_name', 'Unknown'),
            'overall_score': 0.0,
            'breakpoint_results': {},
            'recommendations': [],
            'test_cases': self.get_responsive_test_cases()
        }
        
        # Test each breakpoint
        total_score = 0.0
        for breakpoint in self.BREAKPOINTS.keys():
            result = self._validate_breakpoint(breakpoint, theme_data)
            report['breakpoint_results'][breakpoint] = result
            total_score += result['score']
        
        # Calculate overall score
        report['overall_score'] = total_score / len(self.BREAKPOINTS)
        
        # Generate recommendations
        report['recommendations'] = self._generate_recommendations(report)
        
        return report
    
    def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Overall score recommendations
        if report['overall_score'] < 70:
            recommendations.append("Consider reviewing your responsive theme settings for better mobile compatibility")
        
        # Breakpoint-specific recommendations
        for breakpoint, result in report['breakpoint_results'].items():
            if result['score'] < 80:
                recommendations.append(f"Improve {breakpoint} compatibility by addressing the warnings listed")
        
        # Touch target recommendations
        mobile_result = report['breakpoint_results'].get('mobile', {})
        if any('touch target' in warning.lower() for warning in mobile_result.get('warnings', [])):
            recommendations.append("Increase touch target sizes for better mobile accessibility")
        
        # Font size recommendations
        if any('font size' in warning.lower() for warnings in [r.get('warnings', []) for r in report['breakpoint_results'].values()] for warning in warnings):
            recommendations.append("Consider adjusting font sizes for better readability across devices")
        
        return recommendations
    
    def _get_timestamp(self) -> str:
        """Get current timestamp for reporting."""
        from datetime import datetime
        return datetime.now().isoformat()


class ResponsiveThemeTestInterface:
    """
    Interface for responsive theme testing in admin.
    """
    
    def __init__(self, theme_manager: ResponsiveThemeManager):
        self.theme_manager = theme_manager
    
    def get_test_interface_data(self) -> Dict[str, Any]:
        """
        Get data for the responsive test interface.
        
        Returns:
            Dictionary with interface data
        """
        return {
            'breakpoints': self.theme_manager.BREAKPOINTS,
            'test_cases': self.theme_manager.get_responsive_test_cases(),
            'presets': ['mobile_first', 'accessibility_focused', 'compact'],
            'current_config': self.theme_manager.responsive_config
        }
    
    def generate_test_css(self, theme_data: Dict[str, Any]) -> str:
        """
        Generate CSS for testing responsive themes.
        
        Args:
            theme_data: Theme configuration data
            
        Returns:
            CSS string for testing
        """
        css_parts = []
        
        # Base theme CSS
        css_parts.append("/* Base theme styles */")
        css_parts.append(self._generate_base_theme_css(theme_data))
        
        # Responsive overrides
        css_parts.append("/* Responsive overrides */")
        css_parts.append(self.theme_manager.generate_responsive_css(theme_data))
        
        # Test-specific styles
        css_parts.append("/* Test interface styles */")
        css_parts.append(self._generate_test_interface_css())
        
        return "\n\n".join(css_parts)
    
    def _generate_base_theme_css(self, theme_data: Dict[str, Any]) -> str:
        """Generate base theme CSS."""
        # This would use the ThemeGenerator class
        return "/* Base theme CSS would be generated here */"
    
    def _generate_test_interface_css(self) -> str:
        """Generate CSS for test interface."""
        return """
        /* Responsive test interface styles */
        .responsive-test-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .responsive-test-container {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
        }
        
        .responsive-test-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .responsive-test-content {
            padding: 0;
            position: relative;
        }
        
        .responsive-test-iframe {
            border: none;
            display: block;
        }
        """