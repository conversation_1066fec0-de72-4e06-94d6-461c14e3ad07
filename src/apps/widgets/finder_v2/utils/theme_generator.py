"""
Theme CSS generator utility for finder-v2 widgets.

This module provides functionality to generate CSS custom properties and styles
based on theme configuration, enabling dynamic theme application to widgets.
Includes performance optimization features like caching and shared resources.
"""

import json
import time
import hashlib
from typing import Dict, Optional, Any
from django.utils.safestring import mark_safe
from django.template.loader import render_to_string
from django.core.cache import cache
from django.conf import settings


class ThemeGenerator:
    """
    Generates CSS custom properties and styles for widget themes.
    
    This class handles the conversion of theme configuration into CSS that can be
    injected into widget iframes for real-time theme application.
    Includes performance optimization features like caching and monitoring.
    """
    
    # Class-level cache for shared theme resources
    _shared_cache = {}
    _performance_metrics = {}
    
    def __init__(self, theme=None):
        """
        Initialize the theme generator.
        
        Args:
            theme: WidgetTheme instance or None for default theme
        """
        self.theme = theme
        self._css_cache = {}
        self._generation_start_time = None
    
    def generate_css_custom_properties(self) -> str:
        """
        Generate CSS custom properties for the theme with performance monitoring.
        
        Returns:
            CSS string with custom properties definitions
        """
        self._generation_start_time = time.time()
        
        if not self.theme:
            result = self._get_default_css_properties()
            self._record_performance_metric('default_css_generation', time.time() - self._generation_start_time)
            return result
        
        # Create cache key with theme hash for better cache invalidation
        theme_hash = self._generate_theme_hash()
        cache_key = f"theme_css_{theme_hash}"
        
        # Check Django cache first for shared resources
        cached_css = cache.get(cache_key)
        if cached_css:
            self._record_performance_metric('css_cache_hit', time.time() - self._generation_start_time)
            return cached_css
        
        # Check local cache
        if cache_key in self._css_cache:
            self._record_performance_metric('local_cache_hit', time.time() - self._generation_start_time)
            return self._css_cache[cache_key]
        
        # Generate CSS properties
        properties = self.theme.get_css_custom_properties()
        css_lines = [":root {"]
        
        for prop_name, prop_value in properties.items():
            css_lines.append(f"  {prop_name}: {prop_value};")
        
        # Add responsive overrides if configured
        if self.theme.advanced_config.get('responsive_overrides'):
            css_lines.extend(self._generate_responsive_overrides())
        
        css_lines.append("}")
        
        # Add theme-specific responsive CSS variables
        css_lines.extend(self._generate_theme_responsive_variables())
        
        css_content = "\n".join(css_lines)
        
        # Cache the result both locally and in Django cache
        self._css_cache[cache_key] = css_content
        cache.set(cache_key, css_content, timeout=3600)  # Cache for 1 hour
        
        self._record_performance_metric('css_generation', time.time() - self._generation_start_time)
        return css_content
    
    def generate_theme_css(self, include_fallbacks: bool = True) -> str:
        """
        Generate complete CSS for the theme including fallbacks.
        
        Args:
            include_fallbacks: Whether to include fallback CSS for older browsers
            
        Returns:
            Complete CSS string for the theme
        """
        css_parts = []
        
        # Add custom properties
        css_parts.append(self.generate_css_custom_properties())
        
        # Add component-specific styles
        css_parts.append(self._generate_component_styles())
        
        # Add fallback styles for older browsers
        if include_fallbacks:
            css_parts.append(self._generate_fallback_styles())
        
        return "\n\n".join(css_parts)
    
    def generate_inline_style_tag(self) -> str:
        """
        Generate an HTML style tag with the theme CSS.
        
        Returns:
            HTML style tag with theme CSS
        """
        css_content = self.generate_theme_css()
        return mark_safe(f'<style type="text/css">\n{css_content}\n</style>')
    
    def generate_css_file_content(self) -> str:
        """
        Generate CSS file content with proper headers and comments.
        
        Returns:
            CSS file content with theme styles
        """
        css_lines = [
            "/* Generated Theme CSS for Finder-v2 Widget */",
            f"/* Theme: {self.theme.theme_name if self.theme else 'Default'} */",
            f"/* Generated: {self._get_current_timestamp()} */",
            "",
            self.generate_theme_css(),
            "",
            "/* End of generated theme CSS */"
        ]
        
        return "\n".join(css_lines)
    
    def get_theme_preview_data(self) -> Dict[str, Any]:
        """
        Get theme data for preview purposes.
        
        Returns:
            Dictionary with theme preview data
        """
        if not self.theme:
            return self._get_default_theme_data()
        
        return {
            'theme_name': self.theme.theme_name,
            'colors': {
                'primary': self.theme.primary_color,
                'secondary': self.theme.secondary_color,
                'accent': self.theme.accent_color,
                'background': self.theme.background_color,
                'text': self.theme.text_color,
            },
            'typography': {
                'font_family': self.theme.font_family,
                'font_size': self.theme.base_font_size,
            },
            'effects': {
                'border_radius': self.theme.border_radius,
                'shadow_intensity': self.theme.shadow_intensity,
            },
            'css_properties': self.theme.get_css_custom_properties(),
        }
    
    def _get_default_css_properties(self) -> str:
        """
        Generate default CSS properties when no theme is configured.
        
        Returns:
            Default CSS custom properties
        """
        default_properties = {
            '--theme-primary': '#3B82F6',
            '--theme-secondary': '#6B7280',
            '--theme-accent': '#10B981',
            '--theme-background': '#FFFFFF',
            '--theme-text': '#1F2937',
            '--theme-font-family': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            '--theme-font-size': '16px',
            '--theme-border-radius': '0.375rem',
            '--theme-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        }
        
        css_lines = [":root {"]
        for prop_name, prop_value in default_properties.items():
            css_lines.append(f"  {prop_name}: {prop_value};")
        css_lines.append("}")
        
        return "\n".join(css_lines)
    
    def _generate_responsive_overrides(self) -> list:
        """
        Generate responsive CSS overrides from advanced configuration.
        
        Returns:
            List of CSS lines for responsive overrides
        """
        css_lines = []
        responsive_config = self.theme.advanced_config.get('responsive_overrides', {})
        
        breakpoints = {
            'mobile': '(max-width: 767px)',
            'tablet': '(min-width: 768px) and (max-width: 1023px)',
            'desktop': '(min-width: 1024px)'
        }
        
        for breakpoint, media_query in breakpoints.items():
            if breakpoint in responsive_config:
                css_lines.append(f"@media {media_query} {{")
                css_lines.append("  :root {")
                
                overrides = responsive_config[breakpoint]
                for prop_name, prop_value in overrides.items():
                    css_lines.append(f"    --theme-{prop_name}: {prop_value};")
                
                css_lines.append("  }")
                css_lines.append("}")
        
        return css_lines
    
    def _generate_theme_responsive_variables(self) -> list:
        """
        Generate responsive CSS variables for theme properties.
        
        Returns:
            List of CSS lines for responsive theme variables
        """
        css_lines = []
        
        if not self.theme:
            return css_lines
        
        # Mobile responsive variables
        css_lines.extend([
            "/* Mobile theme variables */",
            "@media (max-width: 767px) {",
            "  :root {",
            f"    --theme-mobile-font-size: {self.theme.base_font_size};",
            f"    --theme-mobile-padding: {self.theme.element_padding};",
            f"    --theme-mobile-margin: {self.theme.element_margin};",
            "  }",
            "}"
        ])
        
        # Tablet responsive variables
        css_lines.extend([
            "/* Tablet theme variables */",
            "@media (min-width: 768px) and (max-width: 1023px) {",
            "  :root {",
            f"    --theme-tablet-font-size: {self.theme.base_font_size};",
            f"    --theme-tablet-padding: {self.theme.element_padding};",
            f"    --theme-tablet-margin: {self.theme.element_margin};",
            "  }",
            "}"
        ])
        
        # Desktop responsive variables
        css_lines.extend([
            "/* Desktop theme variables */",
            "@media (min-width: 1024px) {",
            "  :root {",
            f"    --theme-desktop-font-size: {self.theme.base_font_size};",
            f"    --theme-desktop-padding: {self.theme.element_padding};",
            f"    --theme-desktop-margin: {self.theme.element_margin};",
            "  }",
            "}"
        ])
        
        return css_lines
    
    def _generate_component_styles(self) -> str:
        """
        Generate component-specific styles that use the custom properties.
        
        Returns:
            CSS string with component styles
        """
        css_parts = [
            "/* Component styles using theme custom properties */",
            ".finder-v2-widget {",
            "  background-color: var(--theme-background);",
            "  color: var(--theme-text);",
            "  font-family: var(--theme-font-family);",
            "  font-size: var(--theme-font-size);",
            "  font-weight: var(--theme-font-weight);",
            "  line-height: var(--theme-line-height);",
            "  letter-spacing: var(--theme-letter-spacing);",
            "}",
            "",
            ".finder-v2-widget .btn-primary {",
            "  background-color: var(--theme-primary);",
            "  border-color: var(--theme-primary);",
            "  border-width: var(--theme-border-width);",
            "  border-radius: var(--theme-border-radius);",
            "  box-shadow: var(--theme-shadow);",
            "  padding: var(--theme-padding);",
            "  margin: var(--theme-margin);",
            "  transition: all var(--theme-animation-speed) ease;",
            "}",
            "",
            ".finder-v2-widget .btn-primary:hover {",
            "  background-color: var(--theme-primary);",
            "  filter: var(--theme-hover-effect);",
            "  transform: translateY(-1px);",
            "}",
            "",
            ".finder-v2-widget .form-control {",
            "  border-color: var(--theme-secondary);",
            "  border-width: var(--theme-border-width);",
            "  border-radius: var(--theme-border-radius);",
            "  padding: var(--theme-padding);",
            "  margin: var(--theme-margin);",
            "  font-size: var(--theme-font-size);",
            "  font-weight: var(--theme-font-weight);",
            "  line-height: var(--theme-line-height);",
            "  letter-spacing: var(--theme-letter-spacing);",
            "  transition: all var(--theme-animation-speed) ease;",
            "}",
            "",
            ".finder-v2-widget .form-control:focus {",
            "  border-color: var(--theme-accent);",
            "  box-shadow: 0 0 0 0.2rem rgba(var(--theme-accent-rgb), 0.25);",
            "  transform: scale(1.02);",
            "}",
            "",
            ".finder-v2-widget .alert-success {",
            "  background-color: var(--theme-accent);",
            "  border-color: var(--theme-accent);",
            "  border-width: var(--theme-border-width);",
            "  border-radius: var(--theme-border-radius);",
            "  padding: var(--theme-padding);",
            "  margin: var(--theme-margin);",
            "}",
            "",
            ".finder-v2-widget .card {",
            "  background-color: var(--theme-background);",
            "  border-color: var(--theme-secondary);",
            "  border-width: var(--theme-border-width);",
            "  border-radius: var(--theme-border-radius);",
            "  box-shadow: var(--theme-shadow);",
            "  padding: var(--theme-padding);",
            "  margin: var(--theme-margin);",
            "}",
            "",
            "/* Advanced hover effects */",
            ".finder-v2-widget .btn-primary:hover {",
            "  background-color: var(--theme-primary);",
            "}",
            "",
            ".finder-v2-widget .btn-primary:hover[data-hover='shadow'] {",
            "  box-shadow: 0 8px 16px rgba(var(--theme-primary-rgb), 0.3);",
            "}",
            "",
            ".finder-v2-widget .btn-primary:hover[data-hover='glow'] {",
            "  box-shadow: 0 0 20px rgba(var(--theme-primary-rgb), 0.6);",
            "}",
            "",
            ".finder-v2-widget .btn-primary:hover[data-hover='scale'] {",
            "  transform: scale(1.05);",
            "}",
        ]
        
        return "\n".join(css_parts)
    
    def _generate_fallback_styles(self) -> str:
        """
        Generate fallback styles for browsers that don't support custom properties.
        
        Returns:
            CSS string with fallback styles
        """
        if not self.theme:
            return "/* No fallback styles for default theme */"
        
        css_parts = [
            "/* Fallback styles for older browsers */",
            "@supports not (--css: variables) {",
            f"  .finder-v2-widget {{ background-color: {self.theme.background_color}; }}",
            f"  .finder-v2-widget {{ color: {self.theme.text_color}; }}",
            f"  .finder-v2-widget {{ font-family: {self.theme._get_font_family_css()}; }}",
            f"  .finder-v2-widget {{ font-size: {self.theme.base_font_size}; }}",
            f"  .finder-v2-widget {{ font-weight: {self.theme.font_weight}; }}",
            f"  .finder-v2-widget {{ line-height: {self.theme.line_height}; }}",
            f"  .finder-v2-widget {{ letter-spacing: {self.theme.letter_spacing}; }}",
            f"  .finder-v2-widget .btn-primary {{ background-color: {self.theme.primary_color}; }}",
            f"  .finder-v2-widget .btn-primary {{ border-width: {self.theme.border_width}; }}",
            f"  .finder-v2-widget .btn-primary {{ border-radius: {self.theme.border_radius}; }}",
            f"  .finder-v2-widget .btn-primary {{ padding: {self.theme.element_padding}; }}",
            f"  .finder-v2-widget .form-control {{ border-color: {self.theme.secondary_color}; }}",
            f"  .finder-v2-widget .form-control {{ border-width: {self.theme.border_width}; }}",
            f"  .finder-v2-widget .form-control {{ border-radius: {self.theme.border_radius}; }}",
            f"  .finder-v2-widget .form-control {{ padding: {self.theme.element_padding}; }}",
            f"  .finder-v2-widget .alert-success {{ background-color: {self.theme.accent_color}; }}",
            f"  .finder-v2-widget .card {{ border-radius: {self.theme.border_radius}; }}",
            f"  .finder-v2-widget .card {{ border-width: {self.theme.border_width}; }}",
            "}"
        ]
        
        return "\n".join(css_parts)
    
    def _get_default_theme_data(self) -> Dict[str, Any]:
        """
        Get default theme data when no theme is configured.
        
        Returns:
            Default theme data dictionary
        """
        return {
            'theme_name': 'Default',
            'colors': {
                'primary': '#3B82F6',
                'secondary': '#6B7280',
                'accent': '#10B981',
                'background': '#FFFFFF',
                'text': '#1F2937',
            },
            'typography': {
                'font_family': 'system-ui',
                'font_size': '16px',
            },
            'effects': {
                'border_radius': '0.375rem',
                'shadow_intensity': 'medium',
            },
            'css_properties': {},
        }
    
    def _get_current_timestamp(self) -> str:
        """
        Get current timestamp for CSS file headers.
        
        Returns:
            Formatted timestamp string
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
    
    def _generate_theme_hash(self) -> str:
        """
        Generate a unique hash for the current theme configuration.
        Used for cache key generation and invalidation.
        
        Returns:
            SHA256 hash string of theme configuration
        """
        if not self.theme:
            return "default_theme"
        
        # Create hashable representation of theme configuration
        theme_data = {
            'primary_color': self.theme.primary_color,
            'secondary_color': self.theme.secondary_color,
            'accent_color': self.theme.accent_color,
            'background_color': self.theme.background_color,
            'text_color': self.theme.text_color,
            'font_family': self.theme.font_family,
            'base_font_size': self.theme.base_font_size,
            'border_radius': self.theme.border_radius,
            'shadow_intensity': self.theme.shadow_intensity,
            'advanced_config': json.dumps(self.theme.advanced_config, sort_keys=True),
            'theme_name': self.theme.theme_name,
        }
        
        # Create deterministic hash
        theme_string = json.dumps(theme_data, sort_keys=True)
        return hashlib.sha256(theme_string.encode()).hexdigest()[:16]
    
    def _record_performance_metric(self, metric_name: str, duration: float) -> None:
        """
        Record performance metrics for theme generation operations.
        
        Args:
            metric_name: Name of the performance metric
            duration: Duration in seconds
        """
        if metric_name not in self._performance_metrics:
            self._performance_metrics[metric_name] = {
                'count': 0,
                'total_time': 0.0,
                'min_time': float('inf'),
                'max_time': 0.0,
                'avg_time': 0.0
            }
        
        metrics = self._performance_metrics[metric_name]
        metrics['count'] += 1
        metrics['total_time'] += duration
        metrics['min_time'] = min(metrics['min_time'], duration)
        metrics['max_time'] = max(metrics['max_time'], duration)
        metrics['avg_time'] = metrics['total_time'] / metrics['count']
        
        # Log performance metrics if debug is enabled
        if getattr(settings, 'DEBUG', False):
            import logging
            logger = logging.getLogger(__name__)
            logger.debug(f"Theme performance metric '{metric_name}': {duration:.4f}s (avg: {metrics['avg_time']:.4f}s)")
    
    @classmethod
    def get_performance_metrics(cls) -> Dict[str, Any]:
        """
        Get aggregated performance metrics for all theme generation operations.
        
        Returns:
            Dictionary with performance metrics
        """
        return dict(cls._performance_metrics)
    
    @classmethod
    def clear_performance_metrics(cls) -> None:
        """
        Clear all accumulated performance metrics.
        """
        cls._performance_metrics.clear()
    
    def optimize_dom_updates(self, css_content: str) -> str:
        """
        Optimize CSS to prevent layout thrashing during DOM updates.
        
        Args:
            css_content: Original CSS content
            
        Returns:
            Optimized CSS content with performance improvements
        """
        # Add CSS containment for better performance
        optimization_rules = [
            "/* Performance optimizations */",
            ".finder-v2-widget {",
            "  contain: layout style paint;",
            "  will-change: auto;",
            "}",
            "",
            "/* Reduce paint complexity */",
            ".finder-v2-widget * {",
            "  backface-visibility: hidden;",
            "  transform: translateZ(0);",
            "}",
            "",
            "/* Optimize animations */",
            ".finder-v2-widget .btn-primary {",
            "  transition: transform 0.2s ease, opacity 0.2s ease;",
            "}",
            "",
            ".finder-v2-widget .btn-primary:hover {",
            "  transform: translateY(-1px);",
            "}",
            "",
            "/* Reduce reflow triggers */",
            ".finder-v2-widget .form-control:focus {",
            "  transform: scale(1.02);",
            "  transform-origin: center;",
            "}",
            ""
        ]
        
        return css_content + "\n\n" + "\n".join(optimization_rules)
    
    @staticmethod
    def validate_theme_css(css_content: str) -> Dict[str, Any]:
        """
        Validate generated CSS content for potential issues.
        
        Args:
            css_content: CSS content to validate
            
        Returns:
            Validation result with warnings and errors
        """
        warnings = []
        errors = []
        
        # Check for basic CSS syntax
        if not css_content.strip():
            errors.append("CSS content is empty")
        
        # Check for balanced braces
        open_braces = css_content.count('{')
        close_braces = css_content.count('}')
        if open_braces != close_braces:
            errors.append(f"Unbalanced braces: {open_braces} open, {close_braces} close")
        
        # Check for custom properties
        if '--theme-' not in css_content:
            warnings.append("No theme custom properties found")
        
        # Check for potentially dangerous content
        dangerous_patterns = ['javascript:', 'expression(', 'behavior:', 'binding:']
        for pattern in dangerous_patterns:
            if pattern.lower() in css_content.lower():
                errors.append(f"Potentially dangerous CSS pattern detected: {pattern}")
        
        return {
            'valid': len(errors) == 0,
            'warnings': warnings,
            'errors': errors,
        }


class ThemeInjector:
    """
    Handles CSS injection into widget iframes for real-time theme application.
    """
    
    def __init__(self, widget_config):
        """
        Initialize the theme injector.
        
        Args:
            widget_config: WidgetConfig instance
        """
        self.widget_config = widget_config
        self.theme_generator = ThemeGenerator(getattr(widget_config, 'theme', None))
    
    def get_iframe_css_injection(self) -> str:
        """
        Get CSS injection code for widget iframe.
        
        Returns:
            JavaScript code to inject CSS into iframe
        """
        css_content = self.theme_generator.generate_css_custom_properties()
        
        # Escape CSS content for JavaScript injection
        escaped_css = css_content.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')
        
        js_injection = f"""
        (function() {{
            var styleElement = document.createElement('style');
            styleElement.type = 'text/css';
            styleElement.innerHTML = "{escaped_css}";
            document.head.appendChild(styleElement);
        }})();
        """
        
        return js_injection.strip()
    
    def get_theme_data_json(self) -> str:
        """
        Get theme data as JSON for JavaScript consumption.
        
        Returns:
            JSON string with theme data
        """
        theme_data = self.theme_generator.get_theme_preview_data()
        return json.dumps(theme_data)
    
    def render_theme_style_tag(self) -> str:
        """
        Render theme CSS as HTML style tag.
        
        Returns:
            HTML style tag with theme CSS
        """
        return self.theme_generator.generate_inline_style_tag()