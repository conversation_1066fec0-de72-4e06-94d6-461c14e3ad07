"""
Django management command for migrating themes between different configurations.

This command provides utilities to:
- Migrate themes from old format to new format
- Bulk update themes with new properties
- Convert between different theme formats
- Fix theme data inconsistencies
"""

import json
import logging
from typing import Dict, List, Optional

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.core.exceptions import ValidationError

from src.apps.widgets.common.models import WidgetConfig, WidgetTheme
from src.apps.widgets.finder_v2.utils.theme_generator import ThemeGenerator
from src.apps.widgets.finder_v2.default_config.predefined_themes import PredefinedThemes


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Django management command for theme migration operations.
    """
    
    help = 'Migrate themes between different configurations and formats'
    
    def add_arguments(self, parser):
        """Add command line arguments."""
        parser.add_argument(
            '--action',
            type=str,
            choices=['migrate', 'update', 'validate', 'fix', 'export', 'import'],
            default='migrate',
            help='Action to perform (default: migrate)'
        )
        
        parser.add_argument(
            '--widget-type',
            type=str,
            default='finder-v2',
            help='Widget type to process (default: finder-v2)'
        )
        
        parser.add_argument(
            '--theme-id',
            type=int,
            help='Specific theme ID to process'
        )
        
        parser.add_argument(
            '--config-id',
            type=int,
            help='Specific widget config ID to process'
        )
        
        parser.add_argument(
            '--batch-size',
            type=int,
            default=50,
            help='Batch size for bulk operations (default: 50)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform dry run without making changes'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force operation even if validation fails'
        )
        
        parser.add_argument(
            '--export-file',
            type=str,
            help='File path for theme export'
        )
        
        parser.add_argument(
            '--import-file',
            type=str,
            help='File path for theme import'
        )
        
        parser.add_argument(
            '--add-missing-fields',
            action='store_true',
            help='Add missing advanced theme fields with default values'
        )
        
        parser.add_argument(
            '--update-predefined',
            action='store_true',
            help='Update themes to match latest predefined theme configurations'
        )
        
        parser.add_argument(
            '--validate-only',
            action='store_true',
            help='Only validate themes without making changes'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output'
        )
    
    def handle(self, *args, **options):
        """Handle the command execution."""
        self.verbosity = options.get('verbosity', 1)
        self.dry_run = options.get('dry_run', False)
        self.verbose = options.get('verbose', False)
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        action = options.get('action', 'migrate')
        
        try:
            if action == 'migrate':
                self._handle_migrate(options)
            elif action == 'update':
                self._handle_update(options)
            elif action == 'validate':
                self._handle_validate(options)
            elif action == 'fix':
                self._handle_fix(options)
            elif action == 'export':
                self._handle_export(options)
            elif action == 'import':
                self._handle_import(options)
            else:
                raise CommandError(f'Unknown action: {action}')
                
        except Exception as e:
            logger.exception(f'Theme migration command failed: {str(e)}')
            raise CommandError(f'Command failed: {str(e)}')
    
    def _handle_migrate(self, options):
        """Handle theme migration from old format to new format."""
        self.stdout.write('Starting theme migration...')
        
        widget_type = options.get('widget_type', 'finder-v2')
        batch_size = options.get('batch_size', 50)
        
        # Get all widget configs for the specified type
        configs = WidgetConfig.objects.filter(type=widget_type)
        
        if options.get('config_id'):
            configs = configs.filter(id=options['config_id'])
        
        total_configs = configs.count()
        self.stdout.write(f'Found {total_configs} widget configs to process')
        
        processed = 0
        migrated = 0
        errors = 0
        
        # Process configs in batches
        for i in range(0, total_configs, batch_size):
            batch = configs[i:i + batch_size]
            
            for config in batch:
                try:
                    result = self._migrate_config_theme(config, options)
                    processed += 1
                    
                    if result:
                        migrated += 1
                        if self.verbose:
                            self.stdout.write(f'Migrated theme for config {config.uuid}')
                    
                except Exception as e:
                    errors += 1
                    self.stdout.write(
                        self.style.ERROR(f'Error migrating config {config.uuid}: {str(e)}')
                    )
                    if not options.get('force', False):
                        raise
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Migration completed: {processed} processed, {migrated} migrated, {errors} errors'
            )
        )
    
    def _migrate_config_theme(self, config: WidgetConfig, options) -> bool:
        """
        Migrate a single widget config's theme.
        
        Args:
            config: WidgetConfig instance
            options: Command options
            
        Returns:
            bool: True if migration was performed
        """
        try:
            # Check if theme already exists
            theme = getattr(config, 'theme', None)
            
            if theme:
                # Theme exists, check if it needs migration
                if self._needs_migration(theme):
                    if not self.dry_run:
                        self._update_theme_fields(theme, options)
                    return True
                else:
                    if self.verbose:
                        self.stdout.write(f'Theme for config {config.uuid} already up to date')
                    return False
            else:
                # No theme exists, create one with default values
                if not self.dry_run:
                    self._create_default_theme(config)
                if self.verbose:
                    self.stdout.write(f'Created default theme for config {config.uuid}')
                return True
                
        except Exception as e:
            logger.exception(f'Failed to migrate theme for config {config.uuid}')
            raise
    
    def _needs_migration(self, theme: WidgetTheme) -> bool:
        """
        Check if a theme needs migration.
        
        Args:
            theme: WidgetTheme instance
            
        Returns:
            bool: True if migration is needed
        """
        # Check for missing advanced fields
        required_fields = [
            'font_weight', 'line_height', 'letter_spacing',
            'element_padding', 'element_margin', 'border_width',
            'animation_speed', 'hover_effect'
        ]
        
        for field in required_fields:
            if not hasattr(theme, field):
                return True
        
        # Check for empty or None values
        for field in required_fields:
            value = getattr(theme, field, None)
            if value is None or value == '':
                return True
        
        return False
    
    def _update_theme_fields(self, theme: WidgetTheme, options):
        """
        Update theme with missing fields.
        
        Args:
            theme: WidgetTheme instance
            options: Command options
        """
        updated = False
        
        # Default values for missing fields
        defaults = {
            'font_weight': '400',
            'line_height': '1.5',
            'letter_spacing': '0',
            'element_padding': '0.75rem',
            'element_margin': '0.5rem',
            'border_width': '1px',
            'animation_speed': '0.3s',
            'hover_effect': 'darken'
        }
        
        for field, default_value in defaults.items():
            if not hasattr(theme, field) or getattr(theme, field) in [None, '']:
                setattr(theme, field, default_value)
                updated = True
                if self.verbose:
                    self.stdout.write(f'Updated {field} to {default_value}')
        
        if updated:
            theme.save()
    
    def _create_default_theme(self, config: WidgetConfig):
        """
        Create a default theme for a widget config.
        
        Args:
            config: WidgetConfig instance
        """
        theme = WidgetTheme.objects.create(
            widget=config,
            theme_name='Default',
            primary_color='#3B82F6',
            secondary_color='#6B7280',
            accent_color='#10B981',
            background_color='#FFFFFF',
            text_color='#1F2937',
            font_family='system-ui',
            base_font_size='16px',
            font_weight='400',
            line_height='1.5',
            letter_spacing='0',
            element_padding='0.75rem',
            element_margin='0.5rem',
            border_radius='0.375rem',
            border_width='1px',
            shadow_intensity='medium',
            animation_speed='0.3s',
            hover_effect='darken'
        )
        
        if self.verbose:
            self.stdout.write(f'Created default theme for config {config.uuid}')
        
        return theme
    
    def _handle_update(self, options):
        """Handle theme update operations."""
        self.stdout.write('Starting theme update...')
        
        themes = self._get_themes_queryset(options)
        total_themes = themes.count()
        
        self.stdout.write(f'Found {total_themes} themes to update')
        
        updated = 0
        errors = 0
        
        for theme in themes:
            try:
                if self._update_theme(theme, options):
                    updated += 1
                    if self.verbose:
                        self.stdout.write(f'Updated theme {theme.id}')
                        
            except Exception as e:
                errors += 1
                self.stdout.write(
                    self.style.ERROR(f'Error updating theme {theme.id}: {str(e)}')
                )
                if not options.get('force', False):
                    raise
        
        self.stdout.write(
            self.style.SUCCESS(f'Update completed: {updated} updated, {errors} errors')
        )
    
    def _update_theme(self, theme: WidgetTheme, options) -> bool:
        """
        Update a single theme.
        
        Args:
            theme: WidgetTheme instance
            options: Command options
            
        Returns:
            bool: True if theme was updated
        """
        updated = False
        
        if options.get('add_missing_fields'):
            if self._needs_migration(theme):
                if not self.dry_run:
                    self._update_theme_fields(theme, options)
                updated = True
        
        if options.get('update_predefined'):
            if self._update_predefined_theme(theme, options):
                updated = True
        
        return updated
    
    def _update_predefined_theme(self, theme: WidgetTheme, options) -> bool:
        """
        Update theme to match predefined theme configuration.
        
        Args:
            theme: WidgetTheme instance
            options: Command options
            
        Returns:
            bool: True if theme was updated
        """
        # Check if theme matches a predefined theme
        predefined_themes = PredefinedThemes.get_all_themes()
        
        for theme_key, theme_data in predefined_themes.items():
            if self._theme_matches_predefined(theme, theme_data):
                if not self.dry_run:
                    self._apply_predefined_theme(theme, theme_data)
                if self.verbose:
                    self.stdout.write(f'Updated theme {theme.id} to match {theme_key}')
                return True
        
        return False
    
    def _theme_matches_predefined(self, theme: WidgetTheme, theme_data: Dict) -> bool:
        """
        Check if a theme matches a predefined theme configuration.
        
        Args:
            theme: WidgetTheme instance
            theme_data: Predefined theme data
            
        Returns:
            bool: True if theme matches
        """
        colors = theme_data.get('colors', {})
        
        return (
            theme.primary_color == colors.get('primary') and
            theme.secondary_color == colors.get('secondary') and
            theme.accent_color == colors.get('accent')
        )
    
    def _apply_predefined_theme(self, theme: WidgetTheme, theme_data: Dict):
        """
        Apply predefined theme data to a theme.
        
        Args:
            theme: WidgetTheme instance
            theme_data: Predefined theme data
        """
        colors = theme_data.get('colors', {})
        typography = theme_data.get('typography', {})
        effects = theme_data.get('effects', {})
        
        # Update colors
        if colors:
            theme.primary_color = colors.get('primary', theme.primary_color)
            theme.secondary_color = colors.get('secondary', theme.secondary_color)
            theme.accent_color = colors.get('accent', theme.accent_color)
            theme.background_color = colors.get('background', theme.background_color)
            theme.text_color = colors.get('text', theme.text_color)
        
        # Update typography
        if typography:
            theme.font_family = typography.get('font_family', theme.font_family)
            theme.base_font_size = typography.get('base_font_size', theme.base_font_size)
        
        # Update effects
        if effects:
            theme.border_radius = effects.get('border_radius', theme.border_radius)
            theme.shadow_intensity = effects.get('shadow_intensity', theme.shadow_intensity)
        
        theme.save()
    
    def _handle_validate(self, options):
        """Handle theme validation."""
        self.stdout.write('Starting theme validation...')
        
        themes = self._get_themes_queryset(options)
        total_themes = themes.count()
        
        self.stdout.write(f'Found {total_themes} themes to validate')
        
        valid = 0
        invalid = 0
        errors = []
        
        for theme in themes:
            try:
                validation_result = self._validate_theme(theme)
                if validation_result['valid']:
                    valid += 1
                    if self.verbose:
                        self.stdout.write(f'Theme {theme.id} is valid')
                else:
                    invalid += 1
                    error_msg = f'Theme {theme.id} is invalid: {validation_result["errors"]}'
                    errors.append(error_msg)
                    self.stdout.write(self.style.ERROR(error_msg))
                    
            except Exception as e:
                invalid += 1
                error_msg = f'Error validating theme {theme.id}: {str(e)}'
                errors.append(error_msg)
                self.stdout.write(self.style.ERROR(error_msg))
        
        self.stdout.write(
            self.style.SUCCESS(f'Validation completed: {valid} valid, {invalid} invalid')
        )
        
        if errors and not options.get('force', False):
            raise CommandError(f'Found {len(errors)} validation errors')
    
    def _validate_theme(self, theme: WidgetTheme) -> Dict:
        """
        Validate a single theme.
        
        Args:
            theme: WidgetTheme instance
            
        Returns:
            Dict: Validation result with 'valid' boolean and 'errors' list
        """
        try:
            # Validate using Django model validation
            theme.full_clean()
            
            # Validate CSS generation
            generator = ThemeGenerator(theme)
            css = generator.generate_theme_css()
            css_validation = ThemeGenerator.validate_theme_css(css)
            
            return {
                'valid': css_validation['valid'],
                'errors': css_validation['errors'],
                'warnings': css_validation['warnings']
            }
            
        except ValidationError as e:
            return {
                'valid': False,
                'errors': [str(e)],
                'warnings': []
            }
    
    def _handle_fix(self, options):
        """Handle theme fixing operations."""
        self.stdout.write('Starting theme fixing...')
        
        themes = self._get_themes_queryset(options)
        total_themes = themes.count()
        
        self.stdout.write(f'Found {total_themes} themes to fix')
        
        fixed = 0
        errors = 0
        
        for theme in themes:
            try:
                if self._fix_theme(theme, options):
                    fixed += 1
                    if self.verbose:
                        self.stdout.write(f'Fixed theme {theme.id}')
                        
            except Exception as e:
                errors += 1
                self.stdout.write(
                    self.style.ERROR(f'Error fixing theme {theme.id}: {str(e)}')
                )
                if not options.get('force', False):
                    raise
        
        self.stdout.write(
            self.style.SUCCESS(f'Fix completed: {fixed} fixed, {errors} errors')
        )
    
    def _fix_theme(self, theme: WidgetTheme, options) -> bool:
        """
        Fix a single theme.
        
        Args:
            theme: WidgetTheme instance
            options: Command options
            
        Returns:
            bool: True if theme was fixed
        """
        fixed = False
        
        # Fix missing fields
        if self._needs_migration(theme):
            if not self.dry_run:
                self._update_theme_fields(theme, options)
            fixed = True
        
        # Fix validation errors
        validation_result = self._validate_theme(theme)
        if not validation_result['valid']:
            if not self.dry_run:
                self._fix_validation_errors(theme, validation_result['errors'])
            fixed = True
        
        return fixed
    
    def _fix_validation_errors(self, theme: WidgetTheme, errors: List[str]):
        """
        Fix validation errors in a theme.
        
        Args:
            theme: WidgetTheme instance
            errors: List of validation errors
        """
        for error in errors:
            if 'contrast' in error.lower():
                # Fix contrast issues
                self._fix_contrast_issues(theme)
            elif 'color' in error.lower():
                # Fix color format issues
                self._fix_color_format_issues(theme)
    
    def _fix_contrast_issues(self, theme: WidgetTheme):
        """
        Fix color contrast issues in a theme.
        
        Args:
            theme: WidgetTheme instance
        """
        # If background is light, ensure text is dark
        if theme._calculate_luminance(theme.background_color) > 0.5:
            if theme._calculate_contrast_ratio(theme.background_color, theme.text_color) < 4.5:
                theme.text_color = '#000000'  # Black text
        else:
            # If background is dark, ensure text is light
            if theme._calculate_contrast_ratio(theme.background_color, theme.text_color) < 4.5:
                theme.text_color = '#FFFFFF'  # White text
        
        theme.save()
    
    def _fix_color_format_issues(self, theme: WidgetTheme):
        """
        Fix color format issues in a theme.
        
        Args:
            theme: WidgetTheme instance
        """
        # Ensure all colors are proper hex format
        color_fields = ['primary_color', 'secondary_color', 'accent_color', 
                       'background_color', 'text_color']
        
        for field in color_fields:
            color = getattr(theme, field, '')
            if color and not color.startswith('#'):
                # Add # prefix if missing
                setattr(theme, field, f'#{color}')
            elif color and len(color) == 4:
                # Convert 3-digit hex to 6-digit
                color = color[1:]  # Remove #
                expanded = ''.join([c*2 for c in color])
                setattr(theme, field, f'#{expanded}')
        
        theme.save()
    
    def _handle_export(self, options):
        """Handle theme export operations."""
        export_file = options.get('export_file')
        if not export_file:
            raise CommandError('Export file path is required for export action')
        
        self.stdout.write(f'Exporting themes to {export_file}...')
        
        themes = self._get_themes_queryset(options)
        total_themes = themes.count()
        
        export_data = {
            'version': '1.0',
            'export_date': str(timezone.now()),
            'total_themes': total_themes,
            'themes': []
        }
        
        for theme in themes:
            theme_data = theme.export_to_json()
            theme_data['widget_config_id'] = theme.widget.id
            theme_data['widget_type'] = theme.widget.type
            export_data['themes'].append(theme_data)
        
        try:
            with open(export_file, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            self.stdout.write(
                self.style.SUCCESS(f'Exported {total_themes} themes to {export_file}')
            )
            
        except Exception as e:
            raise CommandError(f'Failed to export themes: {str(e)}')
    
    def _handle_import(self, options):
        """Handle theme import operations."""
        import_file = options.get('import_file')
        if not import_file:
            raise CommandError('Import file path is required for import action')
        
        self.stdout.write(f'Importing themes from {import_file}...')
        
        try:
            with open(import_file, 'r') as f:
                import_data = json.load(f)
            
            themes_data = import_data.get('themes', [])
            total_themes = len(themes_data)
            
            self.stdout.write(f'Found {total_themes} themes to import')
            
            imported = 0
            errors = 0
            
            for theme_data in themes_data:
                try:
                    if self._import_theme(theme_data, options):
                        imported += 1
                        if self.verbose:
                            self.stdout.write(f'Imported theme: {theme_data.get("theme_name", "Unknown")}')
                            
                except Exception as e:
                    errors += 1
                    self.stdout.write(
                        self.style.ERROR(f'Error importing theme: {str(e)}')
                    )
                    if not options.get('force', False):
                        raise
            
            self.stdout.write(
                self.style.SUCCESS(f'Import completed: {imported} imported, {errors} errors')
            )
            
        except Exception as e:
            raise CommandError(f'Failed to import themes: {str(e)}')
    
    def _import_theme(self, theme_data: Dict, options) -> bool:
        """
        Import a single theme.
        
        Args:
            theme_data: Theme data dictionary
            options: Command options
            
        Returns:
            bool: True if theme was imported
        """
        config_id = theme_data.get('widget_config_id')
        if not config_id:
            raise ValueError('Theme data missing widget_config_id')
        
        try:
            config = WidgetConfig.objects.get(uuid=config_id)
        except WidgetConfig.DoesNotExist:
            raise ValueError(f'Widget config {config_id} not found')
        
        if not self.dry_run:
            # Check if theme already exists
            existing_theme = getattr(config, 'theme', None)
            if existing_theme:
                if options.get('force', False):
                    existing_theme.delete()
                else:
                    raise ValueError(f'Theme already exists for config {config_id}')
            
            # Create new theme
            theme = WidgetTheme.import_from_json(theme_data, config)
            theme.save()
        
        return True
    
    def _get_themes_queryset(self, options):
        """
        Get themes queryset based on options.
        
        Args:
            options: Command options
            
        Returns:
            QuerySet: Filtered themes queryset
        """
        themes = WidgetTheme.objects.all()
        
        if options.get('theme_id'):
            themes = themes.filter(id=options['theme_id'])
        
        if options.get('config_id'):
            themes = themes.filter(widget__uuid=options['config_id'])
        
        if options.get('widget_type'):
            themes = themes.filter(widget__type=options['widget_type'])
        
        return themes