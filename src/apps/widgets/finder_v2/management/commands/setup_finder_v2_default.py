"""
Django management command to set up finder-v2 default configuration.

This command creates the default widget configuration for finder-v2 widgets,
which is required for the public creation interface to work.

Usage:
    python manage.py setup_finder_v2_default
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command

from src.apps.widgets.common.config_loader import DefaultConfigLoader
from src.apps.widgets.finder_v2.widget_type import FinderV2WidgetType


class Command(BaseCommand):
    help = 'Set up finder-v2 default widget configuration'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Setting up finder-v2 default configuration...')
        )

        try:
            from src.apps.widgets.common.models import WidgetConfig

            # Check if default config already exists
            existing_config = WidgetConfig.get_default('finder-v2', None)
            if existing_config:
                self.stdout.write(
                    self.style.WARNING(f'Default configuration already exists: {existing_config}')
                )
                return

            # Create default configuration manually
            config = WidgetConfig(
                user=None,
                name='Finder-v2 Default',
                is_default=True,
                type='finder-v2',
                lang='en',
                raw_params=FinderV2WidgetType.default_config
            )
            config.save()

            self.stdout.write(
                self.style.SUCCESS(f'✅ Finder-v2 default configuration created: {config}')
            )

            # Load translations
            try:
                call_command('ws_load_translations',
                             type=FinderV2WidgetType.type,
                             initial=True)
                self.stdout.write(
                    self.style.SUCCESS('✅ Translations loaded successfully!')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'⚠️ Translation loading failed (non-critical): {e}')
                )

            self.stdout.write(
                self.style.SUCCESS('🌐 Public creation interface is now available at:')
            )
            self.stdout.write(
                self.style.SUCCESS('   http://development.local:8000/widget/finder-v2/try/')
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error setting up finder-v2 configuration: {e}')
            )
            raise
