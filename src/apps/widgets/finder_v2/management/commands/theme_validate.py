"""
Django management command for validating theme configurations.

This command provides comprehensive validation for:
- Theme configuration integrity
- CSS generation and validation
- Color accessibility compliance
- Performance optimization checks
- Theme compatibility testing
"""

import json
import logging
from typing import Dict, List, Optional, Tuple
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.core.exceptions import ValidationError
from django.template import Context, Template
from django.template.loader import get_template

from src.apps.widgets.common.models import WidgetConfig, WidgetTheme
from src.apps.widgets.finder_v2.utils.theme_generator import ThemeGenerator
from src.apps.widgets.finder_v2.default_config.predefined_themes import PredefinedThemes


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Django management command for comprehensive theme validation.
    """
    
    help = 'Validate theme configurations and CSS generation'
    
    def add_arguments(self, parser):
        """Add command line arguments."""
        parser.add_argument(
            '--theme-id',
            type=int,
            help='Specific theme ID to validate'
        )
        
        parser.add_argument(
            '--config-id',
            type=int,
            help='Specific widget config ID to validate'
        )
        
        parser.add_argument(
            '--widget-type',
            type=str,
            default='finder-v2',
            help='Widget type to validate (default: finder-v2)'
        )
        
        parser.add_argument(
            '--check-accessibility',
            action='store_true',
            help='Perform accessibility compliance checks'
        )
        
        parser.add_argument(
            '--check-performance',
            action='store_true',
            help='Perform performance optimization checks'
        )
        
        parser.add_argument(
            '--check-compatibility',
            action='store_true',
            help='Check theme compatibility with different browsers'
        )
        
        parser.add_argument(
            '--check-css-validation',
            action='store_true',
            help='Validate generated CSS syntax and structure'
        )
        
        parser.add_argument(
            '--check-template-rendering',
            action='store_true',
            help='Test theme template rendering'
        )
        
        parser.add_argument(
            '--all-checks',
            action='store_true',
            help='Run all validation checks'
        )
        
        parser.add_argument(
            '--export-report',
            type=str,
            help='Export validation report to file'
        )
        
        parser.add_argument(
            '--fail-fast',
            action='store_true',
            help='Stop on first validation failure'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output'
        )
    
    def handle(self, *args, **options):
        """Handle the command execution."""
        self.verbosity = options.get('verbosity', 1)
        self.verbose = options.get('verbose', False)
        self.fail_fast = options.get('fail_fast', False)
        
        # Determine which checks to run
        checks_to_run = self._get_checks_to_run(options)
        
        if not checks_to_run:
            raise CommandError('No validation checks specified. Use --all-checks or specific check options.')
        
        self.stdout.write(f'Running theme validation with checks: {", ".join(checks_to_run)}')
        
        try:
            # Get themes to validate
            themes = self._get_themes_queryset(options)
            total_themes = themes.count()
            
            if total_themes == 0:
                self.stdout.write(self.style.WARNING('No themes found to validate'))
                return
            
            self.stdout.write(f'Found {total_themes} themes to validate')
            
            # Run validation
            validation_results = self._run_validation(themes, checks_to_run, options)
            
            # Display results
            self._display_results(validation_results)
            
            # Export report if requested
            if options.get('export_report'):
                self._export_report(validation_results, options['export_report'])
            
            # Exit with error code if validation failed
            if validation_results['failed_themes'] > 0:
                raise CommandError(f'Validation failed for {validation_results["failed_themes"]} themes')
                
        except Exception as e:
            logger.exception(f'Theme validation command failed: {str(e)}')
            raise CommandError(f'Command failed: {str(e)}')
    
    def _get_checks_to_run(self, options) -> List[str]:
        """
        Determine which validation checks to run based on options.
        
        Args:
            options: Command options
            
        Returns:
            List of check names to run
        """
        checks = []
        
        if options.get('all_checks'):
            checks = [
                'accessibility',
                'performance',
                'compatibility',
                'css_validation',
                'template_rendering'
            ]
        else:
            if options.get('check_accessibility'):
                checks.append('accessibility')
            if options.get('check_performance'):
                checks.append('performance')
            if options.get('check_compatibility'):
                checks.append('compatibility')
            if options.get('check_css_validation'):
                checks.append('css_validation')
            if options.get('check_template_rendering'):
                checks.append('template_rendering')
        
        return checks
    
    def _get_themes_queryset(self, options):
        """
        Get themes queryset based on options.
        
        Args:
            options: Command options
            
        Returns:
            QuerySet: Filtered themes queryset
        """
        themes = WidgetTheme.objects.all()
        
        if options.get('theme_id'):
            themes = themes.filter(id=options['theme_id'])
        
        if options.get('config_id'):
            themes = themes.filter(widget__uuid=options['config_id'])
        
        if options.get('widget_type'):
            themes = themes.filter(widget__type=options['widget_type'])
        
        return themes
    
    def _run_validation(self, themes, checks_to_run: List[str], options) -> Dict:
        """
        Run validation checks on themes.
        
        Args:
            themes: QuerySet of themes to validate
            checks_to_run: List of check names to run
            options: Command options
            
        Returns:
            Dictionary with validation results
        """
        results = {
            'total_themes': themes.count(),
            'passed_themes': 0,
            'failed_themes': 0,
            'theme_results': [],
            'check_summaries': {check: {'passed': 0, 'failed': 0} for check in checks_to_run}
        }
        
        for theme in themes:
            theme_result = {
                'theme_id': theme.id,
                'theme_name': theme.theme_name,
                'widget_config_id': theme.widget.uuid,
                'passed': True,
                'checks': {},
                'errors': [],
                'warnings': []
            }
            
            # Run each validation check
            for check in checks_to_run:
                try:
                    check_result = self._run_single_check(theme, check, options)
                    theme_result['checks'][check] = check_result
                    
                    if not check_result['passed']:
                        theme_result['passed'] = False
                        theme_result['errors'].extend(check_result['errors'])
                        results['check_summaries'][check]['failed'] += 1
                        
                        if self.fail_fast:
                            break
                    else:
                        results['check_summaries'][check]['passed'] += 1
                    
                    theme_result['warnings'].extend(check_result['warnings'])
                    
                except Exception as e:
                    theme_result['passed'] = False
                    theme_result['errors'].append(f'Check {check} failed: {str(e)}')
                    results['check_summaries'][check]['failed'] += 1
                    
                    if self.fail_fast:
                        break
            
            # Update overall results
            if theme_result['passed']:
                results['passed_themes'] += 1
            else:
                results['failed_themes'] += 1
            
            results['theme_results'].append(theme_result)
            
            # Display progress
            if self.verbose:
                status = 'PASSED' if theme_result['passed'] else 'FAILED'
                self.stdout.write(f'Theme {theme.id} ({theme.theme_name}): {status}')
                
                if theme_result['errors']:
                    for error in theme_result['errors']:
                        self.stdout.write(f'  ERROR: {error}')
                        
                if theme_result['warnings']:
                    for warning in theme_result['warnings']:
                        self.stdout.write(f'  WARNING: {warning}')
            
            if self.fail_fast and not theme_result['passed']:
                break
        
        return results
    
    def _run_single_check(self, theme: WidgetTheme, check: str, options) -> Dict:
        """
        Run a single validation check on a theme.
        
        Args:
            theme: WidgetTheme instance
            check: Check name
            options: Command options
            
        Returns:
            Dictionary with check results
        """
        check_methods = {
            'accessibility': self._check_accessibility,
            'performance': self._check_performance,
            'compatibility': self._check_compatibility,
            'css_validation': self._check_css_validation,
            'template_rendering': self._check_template_rendering
        }
        
        check_method = check_methods.get(check)
        if not check_method:
            raise ValueError(f'Unknown check: {check}')
        
        return check_method(theme, options)
    
    def _check_accessibility(self, theme: WidgetTheme, options) -> Dict:
        """
        Check theme accessibility compliance.
        
        Args:
            theme: WidgetTheme instance
            options: Command options
            
        Returns:
            Dictionary with accessibility check results
        """
        errors = []
        warnings = []
        
        # Check color contrast ratios
        contrast_checks = [
            (theme.background_color, theme.text_color, 'background/text'),
            (theme.primary_color, '#FFFFFF', 'primary/white'),
            (theme.secondary_color, theme.text_color, 'secondary/text'),
            (theme.accent_color, '#FFFFFF', 'accent/white')
        ]
        
        for bg_color, text_color, pair_name in contrast_checks:
            try:
                contrast_ratio = self._calculate_contrast_ratio(bg_color, text_color)
                if contrast_ratio < 4.5:  # WCAG AA standard
                    errors.append(f'Poor contrast ratio for {pair_name}: {contrast_ratio:.2f} (minimum 4.5)')
                elif contrast_ratio < 7.0:  # WCAG AAA standard
                    warnings.append(f'Contrast ratio for {pair_name} could be improved: {contrast_ratio:.2f} (AAA standard is 7.0)')
            except Exception as e:
                errors.append(f'Failed to calculate contrast ratio for {pair_name}: {str(e)}')
        
        # Check font size accessibility
        try:
            font_size_px = self._parse_font_size(theme.base_font_size)
            if font_size_px < 16:
                warnings.append(f'Font size {theme.base_font_size} may be too small for accessibility (recommended: 16px+)')
        except Exception as e:
            errors.append(f'Failed to parse font size: {str(e)}')
        
        # Check line height
        try:
            line_height = float(theme.line_height)
            if line_height < 1.4:
                warnings.append(f'Line height {theme.line_height} may be too small for accessibility (recommended: 1.4+)')
        except Exception as e:
            errors.append(f'Failed to parse line height: {str(e)}')
        
        return {
            'passed': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def _check_performance(self, theme: WidgetTheme, options) -> Dict:
        """
        Check theme performance optimization.
        
        Args:
            theme: WidgetTheme instance
            options: Command options
            
        Returns:
            Dictionary with performance check results
        """
        errors = []
        warnings = []
        
        # Check CSS generation performance
        try:
            generator = ThemeGenerator(theme)
            
            # Test CSS generation time
            import time
            start_time = time.time()
            css_content = generator.generate_theme_css()
            generation_time = time.time() - start_time
            
            if generation_time > 0.1:  # 100ms threshold
                warnings.append(f'CSS generation took {generation_time:.3f}s (threshold: 0.1s)')
            
            # Check CSS size
            css_size = len(css_content.encode('utf-8'))
            if css_size > 50000:  # 50KB threshold
                warnings.append(f'Generated CSS is {css_size} bytes (threshold: 50KB)')
            
            # Check for CSS optimization opportunities
            if 'will-change' not in css_content:
                warnings.append('CSS could benefit from performance optimizations (will-change property)')
            
        except Exception as e:
            errors.append(f'Failed to generate CSS for performance testing: {str(e)}')
        
        # Check animation performance
        try:
            animation_speed = theme.animation_speed
            if animation_speed and 'ms' in animation_speed:
                speed_ms = float(animation_speed.replace('ms', ''))
                if speed_ms > 500:
                    warnings.append(f'Animation speed {animation_speed} may be too slow for good UX')
            elif animation_speed and 's' in animation_speed:
                speed_s = float(animation_speed.replace('s', ''))
                if speed_s > 0.5:
                    warnings.append(f'Animation speed {animation_speed} may be too slow for good UX')
        except Exception as e:
            errors.append(f'Failed to parse animation speed: {str(e)}')
        
        return {
            'passed': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def _check_compatibility(self, theme: WidgetTheme, options) -> Dict:
        """
        Check theme browser compatibility.
        
        Args:
            theme: WidgetTheme instance
            options: Command options
            
        Returns:
            Dictionary with compatibility check results
        """
        errors = []
        warnings = []
        
        # Check CSS custom properties usage
        try:
            generator = ThemeGenerator(theme)
            css_content = generator.generate_theme_css()
            
            # Check for CSS custom properties
            if '--' in css_content:
                warnings.append('Theme uses CSS custom properties (IE11 incompatible)')
            
            # Check for modern CSS features
            modern_features = [
                ('grid', 'CSS Grid'),
                ('flexbox', 'Flexbox'),
                ('rgba(', 'RGBA colors'),
                ('hsla(', 'HSLA colors'),
                ('calc(', 'CSS calc()'),
                ('var(', 'CSS variables')
            ]
            
            for feature, description in modern_features:
                if feature in css_content.lower():
                    warnings.append(f'Theme uses {description} (may need fallbacks for older browsers)')
            
        except Exception as e:
            errors.append(f'Failed to generate CSS for compatibility testing: {str(e)}')
        
        # Check color format compatibility
        color_fields = [
            ('primary_color', theme.primary_color),
            ('secondary_color', theme.secondary_color),
            ('accent_color', theme.accent_color),
            ('background_color', theme.background_color),
            ('text_color', theme.text_color)
        ]
        
        for field_name, color_value in color_fields:
            if color_value and not color_value.startswith('#'):
                warnings.append(f'{field_name} uses non-hex color format: {color_value}')
        
        return {
            'passed': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def _check_css_validation(self, theme: WidgetTheme, options) -> Dict:
        """
        Validate generated CSS syntax and structure.
        
        Args:
            theme: WidgetTheme instance
            options: Command options
            
        Returns:
            Dictionary with CSS validation results
        """
        errors = []
        warnings = []
        
        try:
            generator = ThemeGenerator(theme)
            css_content = generator.generate_theme_css()
            
            # Use ThemeGenerator's built-in validation
            validation_result = ThemeGenerator.validate_theme_css(css_content)
            
            errors.extend(validation_result['errors'])
            warnings.extend(validation_result['warnings'])
            
            # Additional CSS structure checks
            if not css_content.strip():
                errors.append('Generated CSS is empty')
            
            # Check for proper CSS structure
            if ':root' not in css_content:
                warnings.append('CSS does not contain :root selector for custom properties')
            
            # Check for selector organization
            if '.finder-v2-widget' not in css_content:
                warnings.append('CSS does not contain main widget selector')
            
        except Exception as e:
            errors.append(f'Failed to validate CSS: {str(e)}')
        
        return {
            'passed': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def _check_template_rendering(self, theme: WidgetTheme, options) -> Dict:
        """
        Test theme template rendering.
        
        Args:
            theme: WidgetTheme instance
            options: Command options
            
        Returns:
            Dictionary with template rendering results
        """
        errors = []
        warnings = []
        
        try:
            # Test theme tags template rendering
            from src.apps.widgets.finder_v2.templatetags.theme_tags import render_theme_css
            
            # Test basic template tag rendering
            rendered_css = render_theme_css(theme)
            
            if not rendered_css:
                errors.append('Theme template tag returned empty result')
            elif '<style' not in rendered_css:
                errors.append('Theme template tag did not return proper style tag')
            
            # Test theme data injection
            generator = ThemeGenerator(theme)
            theme_data = generator.get_theme_preview_data()
            
            if not theme_data:
                errors.append('Theme data generation failed')
            elif 'colors' not in theme_data:
                warnings.append('Theme data missing colors section')
            
        except Exception as e:
            errors.append(f'Failed to test template rendering: {str(e)}')
        
        return {
            'passed': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def _calculate_contrast_ratio(self, color1: str, color2: str) -> float:
        """
        Calculate contrast ratio between two colors.
        
        Args:
            color1: First color (hex format)
            color2: Second color (hex format)
            
        Returns:
            Contrast ratio (1.0 to 21.0)
        """
        def get_luminance(hex_color):
            """Calculate relative luminance of a color."""
            # Remove # if present
            hex_color = hex_color.lstrip('#')
            
            # Convert to RGB
            r = int(hex_color[0:2], 16) / 255.0
            g = int(hex_color[2:4], 16) / 255.0
            b = int(hex_color[4:6], 16) / 255.0
            
            # Apply gamma correction
            def gamma_correct(c):
                return c / 12.92 if c <= 0.03928 else ((c + 0.055) / 1.055) ** 2.4
            
            r = gamma_correct(r)
            g = gamma_correct(g)
            b = gamma_correct(b)
            
            # Calculate luminance
            return 0.2126 * r + 0.7152 * g + 0.0722 * b
        
        lum1 = get_luminance(color1)
        lum2 = get_luminance(color2)
        
        # Ensure lighter color is in numerator
        if lum1 > lum2:
            return (lum1 + 0.05) / (lum2 + 0.05)
        else:
            return (lum2 + 0.05) / (lum1 + 0.05)
    
    def _parse_font_size(self, font_size: str) -> float:
        """
        Parse font size string to pixels.
        
        Args:
            font_size: Font size string (e.g., '16px', '1rem', '1.2em')
            
        Returns:
            Font size in pixels
        """
        if not font_size:
            return 16.0  # Default
        
        font_size = font_size.lower().strip()
        
        if font_size.endswith('px'):
            return float(font_size[:-2])
        elif font_size.endswith('rem'):
            return float(font_size[:-3]) * 16  # Assume 16px base
        elif font_size.endswith('em'):
            return float(font_size[:-2]) * 16  # Assume 16px base
        else:
            try:
                return float(font_size)
            except ValueError:
                raise ValueError(f'Unable to parse font size: {font_size}')
    
    def _display_results(self, results: Dict):
        """
        Display validation results to stdout.
        
        Args:
            results: Validation results dictionary
        """
        self.stdout.write(f'\nValidation Results Summary:')
        self.stdout.write(f'Total themes: {results["total_themes"]}')
        self.stdout.write(f'Passed: {results["passed_themes"]}')
        self.stdout.write(f'Failed: {results["failed_themes"]}')
        
        # Check summaries
        self.stdout.write(f'\nCheck Results:')
        for check, summary in results['check_summaries'].items():
            self.stdout.write(f'  {check}: {summary["passed"]} passed, {summary["failed"]} failed')
        
        # Failed themes details
        if results['failed_themes'] > 0:
            self.stdout.write(f'\nFailed Themes:')
            for theme_result in results['theme_results']:
                if not theme_result['passed']:
                    self.stdout.write(f'  Theme {theme_result["theme_id"]} ({theme_result["theme_name"]}):')
                    for error in theme_result['errors']:
                        self.stdout.write(f'    ERROR: {error}')
                    for warning in theme_result['warnings']:
                        self.stdout.write(f'    WARNING: {warning}')
    
    def _export_report(self, results: Dict, export_file: str):
        """
        Export validation report to file.
        
        Args:
            results: Validation results dictionary
            export_file: Export file path
        """
        try:
            report_data = {
                'validation_report': {
                    'generated_at': self._get_current_timestamp(),
                    'summary': {
                        'total_themes': results['total_themes'],
                        'passed_themes': results['passed_themes'],
                        'failed_themes': results['failed_themes']
                    },
                    'check_summaries': results['check_summaries'],
                    'theme_results': results['theme_results']
                }
            }
            
            with open(export_file, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            self.stdout.write(
                self.style.SUCCESS(f'Validation report exported to {export_file}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to export report: {str(e)}')
            )
    
    def _get_current_timestamp(self) -> str:
        """
        Get current timestamp for report headers.
        
        Returns:
            Formatted timestamp string
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")