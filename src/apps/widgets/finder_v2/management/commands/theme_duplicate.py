"""
Django management command for duplicating themes.

This command provides utilities to:
- Duplicate themes between widget configurations
- Bulk copy themes across multiple widgets
- Create theme templates from existing themes
- Clone themes with variations
"""

import json
import logging
from typing import Dict, List, Optional

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.core.exceptions import ValidationError

from src.apps.widgets.common.models import WidgetConfig, WidgetTheme
from src.apps.widgets.finder_v2.utils.theme_generator import ThemeGenerator


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Django management command for theme duplication operations.
    """
    
    help = 'Duplicate themes between widget configurations'
    
    def add_arguments(self, parser):
        """Add command line arguments."""
        parser.add_argument(
            '--source-theme-id',
            type=int,
            help='Source theme ID to duplicate from'
        )
        
        parser.add_argument(
            '--source-config-id',
            type=int,
            help='Source widget config ID to duplicate theme from'
        )
        
        parser.add_argument(
            '--target-config-id',
            type=int,
            help='Target widget config ID to duplicate theme to'
        )
        
        parser.add_argument(
            '--target-config-ids',
            type=str,
            help='Comma-separated list of target widget config IDs'
        )
        
        parser.add_argument(
            '--widget-type',
            type=str,
            default='finder-v2',
            help='Widget type to filter by (default: finder-v2)'
        )
        
        parser.add_argument(
            '--new-theme-name',
            type=str,
            help='Name for the duplicated theme'
        )
        
        parser.add_argument(
            '--name-suffix',
            type=str,
            default=' (Copy)',
            help='Suffix to add to duplicated theme names (default: " (Copy)")'
        )
        
        parser.add_argument(
            '--overwrite',
            action='store_true',
            help='Overwrite existing themes in target configs'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform dry run without making changes'
        )
        
        parser.add_argument(
            '--batch-size',
            type=int,
            default=50,
            help='Batch size for bulk operations (default: 50)'
        )
        
        parser.add_argument(
            '--create-variations',
            action='store_true',
            help='Create color variations of the source theme'
        )
        
        parser.add_argument(
            '--variations-count',
            type=int,
            default=5,
            help='Number of variations to create (default: 5)'
        )
        
        parser.add_argument(
            '--export-template',
            type=str,
            help='Export theme as template to file'
        )
        
        parser.add_argument(
            '--apply-template',
            type=str,
            help='Apply theme template from file'
        )
        
        parser.add_argument(
            '--user-filter',
            type=str,
            help='Filter by user ID or username'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output'
        )
    
    def handle(self, *args, **options):
        """Handle the command execution."""
        self.verbosity = options.get('verbosity', 1)
        self.dry_run = options.get('dry_run', False)
        self.verbose = options.get('verbose', False)
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        try:
            if options.get('export_template'):
                self._handle_export_template(options)
            elif options.get('apply_template'):
                self._handle_apply_template(options)
            elif options.get('create_variations'):
                self._handle_create_variations(options)
            else:
                self._handle_duplicate(options)
                
        except Exception as e:
            logger.exception(f'Theme duplication command failed: {str(e)}')
            raise CommandError(f'Command failed: {str(e)}')
    
    def _handle_duplicate(self, options):
        """Handle theme duplication operations."""
        self.stdout.write('Starting theme duplication...')
        
        # Get source theme
        source_theme = self._get_source_theme(options)
        if not source_theme:
            raise CommandError('Source theme not found or not specified')
        
        # Get target configs
        target_configs = self._get_target_configs(options)
        if not target_configs:
            raise CommandError('No target configs found or specified')
        
        self.stdout.write(f'Duplicating theme "{source_theme.theme_name}" to {len(target_configs)} configs')
        
        duplicated = 0
        errors = 0
        
        # Process targets in batches
        batch_size = options.get('batch_size', 50)
        
        for i in range(0, len(target_configs), batch_size):
            batch = target_configs[i:i + batch_size]
            
            with transaction.atomic():
                for config in batch:
                    try:
                        if self._duplicate_theme_to_config(source_theme, config, options):
                            duplicated += 1
                            if self.verbose:
                                self.stdout.write(f'Duplicated theme to config {config.uuid}')
                        
                    except Exception as e:
                        errors += 1
                        self.stdout.write(
                            self.style.ERROR(f'Error duplicating to config {config.uuid}: {str(e)}')
                        )
                        if not options.get('force', False):
                            raise
        
        self.stdout.write(
            self.style.SUCCESS(f'Duplication completed: {duplicated} duplicated, {errors} errors')
        )
    
    def _get_source_theme(self, options) -> Optional[WidgetTheme]:
        """
        Get the source theme based on options.
        
        Args:
            options: Command options
            
        Returns:
            WidgetTheme or None: Source theme instance
        """
        source_theme_id = options.get('source_theme_id')
        source_config_id = options.get('source_config_id')
        
        if source_theme_id:
            try:
                return WidgetTheme.objects.get(id=source_theme_id)
            except WidgetTheme.DoesNotExist:
                raise CommandError(f'Source theme {source_theme_id} not found')
        
        elif source_config_id:
            try:
                config = WidgetConfig.objects.get(uuid=source_config_id)
                theme = getattr(config, 'theme', None)
                if not theme:
                    raise CommandError(f'No theme found for config {source_config_id}')
                return theme
            except WidgetConfig.DoesNotExist:
                raise CommandError(f'Source config {source_config_id} not found')
        
        return None
    
    def _get_target_configs(self, options) -> List[WidgetConfig]:
        """
        Get target widget configs based on options.
        
        Args:
            options: Command options
            
        Returns:
            List[WidgetConfig]: List of target configs
        """
        target_configs = []
        
        # Single target config
        target_config_id = options.get('target_config_id')
        if target_config_id:
            try:
                config = WidgetConfig.objects.get(uuid=target_config_id)
                target_configs.append(config)
            except WidgetConfig.DoesNotExist:
                raise CommandError(f'Target config {target_config_id} not found')
        
        # Multiple target configs
        target_config_ids = options.get('target_config_ids')
        if target_config_ids:
            config_ids = [int(id.strip()) for id in target_config_ids.split(',')]
            for config_id in config_ids:
                try:
                    config = WidgetConfig.objects.get(uuid=config_id)
                    target_configs.append(config)
                except WidgetConfig.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Target config {config_id} not found, skipping')
                    )
        
        # Filter by widget type and user if no specific targets
        if not target_configs:
            configs = WidgetConfig.objects.filter(type=options.get('widget_type', 'finder-v2'))
            
            # Apply user filter if specified
            user_filter = options.get('user_filter')
            if user_filter:
                if user_filter.isdigit():
                    configs = configs.filter(user_id=int(user_filter))
                else:
                    configs = configs.filter(user__username=user_filter)
            
            target_configs = list(configs)
        
        return target_configs
    
    def _duplicate_theme_to_config(self, source_theme: WidgetTheme, target_config: WidgetConfig, 
                                  options) -> bool:
        """
        Duplicate a theme to a target config.
        
        Args:
            source_theme: Source theme to duplicate
            target_config: Target config to duplicate to
            options: Command options
            
        Returns:
            bool: True if duplication was successful
        """
        # Check if target already has a theme
        existing_theme = getattr(target_config, 'theme', None)
        if existing_theme:
            if not options.get('overwrite', False):
                if self.verbose:
                    self.stdout.write(f'Config {target_config.uuid} already has theme, skipping')
                return False
            else:
                if not self.dry_run:
                    existing_theme.delete()
                if self.verbose:
                    self.stdout.write(f'Overwriting existing theme for config {target_config.uuid}')
        
        if not self.dry_run:
            # Create new theme
            new_theme = self._create_theme_copy(source_theme, target_config, options)
            if self.verbose:
                self.stdout.write(f'Created theme copy: {new_theme.theme_name}')
        
        return True
    
    def _create_theme_copy(self, source_theme: WidgetTheme, target_config: WidgetConfig, 
                          options) -> WidgetTheme:
        """
        Create a copy of a theme for a target config.
        
        Args:
            source_theme: Source theme to copy
            target_config: Target config for the new theme
            options: Command options
            
        Returns:
            WidgetTheme: New theme instance
        """
        # Determine new theme name
        new_theme_name = options.get('new_theme_name')
        if not new_theme_name:
            name_suffix = options.get('name_suffix', ' (Copy)')
            new_theme_name = source_theme.theme_name + name_suffix
        
        # Create new theme with copied data
        new_theme = WidgetTheme(\n            widget=target_config,\n            theme_name=new_theme_name,\n            primary_color=source_theme.primary_color,\n            secondary_color=source_theme.secondary_color,\n            accent_color=source_theme.accent_color,\n            background_color=source_theme.background_color,\n            text_color=source_theme.text_color,\n            font_family=source_theme.font_family,\n            base_font_size=source_theme.base_font_size,\n            font_weight=source_theme.font_weight,\n            line_height=source_theme.line_height,\n            letter_spacing=source_theme.letter_spacing,\n            element_padding=source_theme.element_padding,\n            element_margin=source_theme.element_margin,\n            border_radius=source_theme.border_radius,\n            border_width=source_theme.border_width,\n            shadow_intensity=source_theme.shadow_intensity,\n            animation_speed=source_theme.animation_speed,\n            hover_effect=source_theme.hover_effect,\n            advanced_config=source_theme.advanced_config.copy()\n        )\n        \n        new_theme.save()\n        return new_theme
    
    def _handle_create_variations(self, options):
        """Handle creating theme variations."""
        self.stdout.write('Creating theme variations...')
        
        # Get source theme
        source_theme = self._get_source_theme(options)
        if not source_theme:
            raise CommandError('Source theme not found or not specified')
        
        # Get target configs
        target_configs = self._get_target_configs(options)
        if not target_configs:
            raise CommandError('No target configs found or specified')
        
        variations_count = options.get('variations_count', 5)
        
        self.stdout.write(f'Creating {variations_count} variations of "{source_theme.theme_name}"')
        
        created = 0
        errors = 0
        
        for i, config in enumerate(target_configs[:variations_count]):
            try:
                if self._create_theme_variation(source_theme, config, i, options):
                    created += 1
                    if self.verbose:
                        self.stdout.write(f'Created variation {i+1} for config {config.uuid}')
                
            except Exception as e:
                errors += 1
                self.stdout.write(
                    self.style.ERROR(f'Error creating variation {i+1}: {str(e)}')
                )
                if not options.get('force', False):
                    raise
        
        self.stdout.write(
            self.style.SUCCESS(f'Variations created: {created} created, {errors} errors')
        )
    
    def _create_theme_variation(self, source_theme: WidgetTheme, target_config: WidgetConfig, 
                               variation_index: int, options) -> bool:
        """
        Create a theme variation.
        
        Args:
            source_theme: Source theme to vary
            target_config: Target config for the variation
            variation_index: Index of the variation
            options: Command options
            
        Returns:
            bool: True if variation was created
        """
        # Check if target already has a theme
        existing_theme = getattr(target_config, 'theme', None)
        if existing_theme and not options.get('overwrite', False):
            if self.verbose:
                self.stdout.write(f'Config {target_config.uuid} already has theme, skipping')
            return False
        
        if not self.dry_run:
            if existing_theme:
                existing_theme.delete()
            
            # Create variation
            variation = self._generate_color_variation(source_theme, variation_index)
            variation.widget = target_config
            variation.theme_name = f'{source_theme.theme_name} Variation {variation_index + 1}'
            variation.save()
        
        return True
    
    def _generate_color_variation(self, source_theme: WidgetTheme, variation_index: int) -> WidgetTheme:
        """
        Generate a color variation of a theme.
        
        Args:
            source_theme: Source theme to vary
            variation_index: Index of the variation
            
        Returns:
            WidgetTheme: Theme variation
        """
        # Color variation strategies
        variations = [
            self._create_darker_variation,
            self._create_lighter_variation,
            self._create_complementary_variation,
            self._create_monochromatic_variation,
            self._create_analogous_variation
        ]
        
        # Select variation strategy
        strategy = variations[variation_index % len(variations)]
        
        # Create variation
        variation = WidgetTheme(
            primary_color=source_theme.primary_color,
            secondary_color=source_theme.secondary_color,
            accent_color=source_theme.accent_color,
            background_color=source_theme.background_color,
            text_color=source_theme.text_color,
            font_family=source_theme.font_family,
            base_font_size=source_theme.base_font_size,
            font_weight=source_theme.font_weight,
            line_height=source_theme.line_height,
            letter_spacing=source_theme.letter_spacing,
            element_padding=source_theme.element_padding,
            element_margin=source_theme.element_margin,
            border_radius=source_theme.border_radius,
            border_width=source_theme.border_width,
            shadow_intensity=source_theme.shadow_intensity,
            animation_speed=source_theme.animation_speed,
            hover_effect=source_theme.hover_effect,
            advanced_config=source_theme.advanced_config.copy()
        )
        
        # Apply variation strategy
        strategy(variation)
        
        return variation
    
    def _create_darker_variation(self, theme: WidgetTheme):
        """Create a darker variation of a theme."""
        theme.primary_color = self._darken_color(theme.primary_color, 0.2)
        theme.secondary_color = self._darken_color(theme.secondary_color, 0.2)
        theme.accent_color = self._darken_color(theme.accent_color, 0.2)
    
    def _create_lighter_variation(self, theme: WidgetTheme):
        """Create a lighter variation of a theme."""
        theme.primary_color = self._lighten_color(theme.primary_color, 0.2)
        theme.secondary_color = self._lighten_color(theme.secondary_color, 0.2)
        theme.accent_color = self._lighten_color(theme.accent_color, 0.2)
    
    def _create_complementary_variation(self, theme: WidgetTheme):
        """Create a complementary color variation."""
        theme.primary_color = self._get_complementary_color(theme.primary_color)
        theme.accent_color = self._get_complementary_color(theme.accent_color)
    
    def _create_monochromatic_variation(self, theme: WidgetTheme):
        """Create a monochromatic variation."""
        base_hue = self._get_hue(theme.primary_color)
        theme.primary_color = self._hsl_to_hex(base_hue, 0.7, 0.4)
        theme.secondary_color = self._hsl_to_hex(base_hue, 0.3, 0.6)
        theme.accent_color = self._hsl_to_hex(base_hue, 0.8, 0.5)
    
    def _create_analogous_variation(self, theme: WidgetTheme):
        """Create an analogous color variation."""
        base_hue = self._get_hue(theme.primary_color)
        theme.primary_color = self._hsl_to_hex((base_hue + 30) % 360, 0.7, 0.4)
        theme.accent_color = self._hsl_to_hex((base_hue - 30) % 360, 0.8, 0.5)
    
    def _darken_color(self, hex_color: str, amount: float) -> str:
        """Darken a hex color by a given amount."""
        # Simple darkening by reducing lightness
        r, g, b = self._hex_to_rgb(hex_color)
        factor = 1 - amount
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        return f'#{r:02x}{g:02x}{b:02x}'
    
    def _lighten_color(self, hex_color: str, amount: float) -> str:
        """Lighten a hex color by a given amount."""
        # Simple lightening by increasing towards white
        r, g, b = self._hex_to_rgb(hex_color)
        r = int(r + (255 - r) * amount)
        g = int(g + (255 - g) * amount)
        b = int(b + (255 - b) * amount)
        return f'#{r:02x}{g:02x}{b:02x}'
    
    def _get_complementary_color(self, hex_color: str) -> str:
        """Get the complementary color."""
        h, s, l = self._hex_to_hsl(hex_color)
        h = (h + 180) % 360
        return self._hsl_to_hex(h, s, l)
    
    def _get_hue(self, hex_color: str) -> float:
        """Get the hue value from a hex color."""
        h, s, l = self._hex_to_hsl(hex_color)
        return h
    
    def _hex_to_rgb(self, hex_color: str) -> tuple:
        """Convert hex to RGB."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def _hex_to_hsl(self, hex_color: str) -> tuple:
        """Convert hex to HSL."""
        r, g, b = self._hex_to_rgb(hex_color)
        r, g, b = r/255.0, g/255.0, b/255.0
        
        max_val = max(r, g, b)
        min_val = min(r, g, b)
        h, s, l = 0, 0, (max_val + min_val) / 2
        
        if max_val == min_val:
            h = s = 0  # achromatic
        else:
            d = max_val - min_val
            s = d / (2 - max_val - min_val) if l > 0.5 else d / (max_val + min_val)
            if max_val == r:
                h = (g - b) / d + (6 if g < b else 0)
            elif max_val == g:
                h = (b - r) / d + 2
            elif max_val == b:
                h = (r - g) / d + 4
            h /= 6
        
        return h * 360, s, l
    
    def _hsl_to_hex(self, h: float, s: float, l: float) -> str:
        """Convert HSL to hex."""
        h = h / 360
        
        def hue_to_rgb(p, q, t):
            if t < 0:
                t += 1
            if t > 1:
                t -= 1
            if t < 1/6:
                return p + (q - p) * 6 * t
            if t < 1/2:
                return q
            if t < 2/3:
                return p + (q - p) * (2/3 - t) * 6
            return p
        
        if s == 0:
            r = g = b = l  # achromatic
        else:
            q = l * (1 + s) if l < 0.5 else l + s - l * s
            p = 2 * l - q
            r = hue_to_rgb(p, q, h + 1/3)
            g = hue_to_rgb(p, q, h)
            b = hue_to_rgb(p, q, h - 1/3)
        
        return f'#{int(r*255):02x}{int(g*255):02x}{int(b*255):02x}'
    
    def _handle_export_template(self, options):
        """Handle exporting theme as template."""
        export_file = options.get('export_template')
        if not export_file:
            raise CommandError('Export template file path is required')
        
        source_theme = self._get_source_theme(options)
        if not source_theme:
            raise CommandError('Source theme not found or not specified')
        
        self.stdout.write(f'Exporting theme template to {export_file}...')
        
        template_data = {
            'version': '1.0',
            'template_name': source_theme.theme_name,
            'description': f'Template created from theme {source_theme.id}',
            'theme_data': source_theme.export_to_json()
        }
        
        try:
            with open(export_file, 'w') as f:
                json.dump(template_data, f, indent=2)
            
            self.stdout.write(
                self.style.SUCCESS(f'Template exported to {export_file}')
            )
            
        except Exception as e:
            raise CommandError(f'Failed to export template: {str(e)}')
    
    def _handle_apply_template(self, options):
        """Handle applying theme template."""
        template_file = options.get('apply_template')
        if not template_file:
            raise CommandError('Template file path is required')
        
        target_configs = self._get_target_configs(options)
        if not target_configs:
            raise CommandError('No target configs found or specified')
        
        self.stdout.write(f'Applying theme template from {template_file}...')
        
        try:
            with open(template_file, 'r') as f:
                template_data = json.load(f)
            
            theme_data = template_data.get('theme_data', {})
            template_name = template_data.get('template_name', 'Template')
            
            applied = 0
            errors = 0
            
            for config in target_configs:
                try:
                    if self._apply_template_to_config(theme_data, config, template_name, options):
                        applied += 1
                        if self.verbose:
                            self.stdout.write(f'Applied template to config {config.uuid}')
                    
                except Exception as e:
                    errors += 1
                    self.stdout.write(
                        self.style.ERROR(f'Error applying template to config {config.uuid}: {str(e)}')
                    )
                    if not options.get('force', False):
                        raise
            
            self.stdout.write(
                self.style.SUCCESS(f'Template applied: {applied} applied, {errors} errors')
            )
            
        except Exception as e:
            raise CommandError(f'Failed to apply template: {str(e)}')
    
    def _apply_template_to_config(self, theme_data: Dict, config: WidgetConfig, 
                                 template_name: str, options) -> bool:
        """
        Apply theme template to a config.
        
        Args:
            theme_data: Theme data from template
            config: Target config
            template_name: Name of the template
            options: Command options
            
        Returns:
            bool: True if template was applied
        """
        # Check if target already has a theme
        existing_theme = getattr(config, 'theme', None)
        if existing_theme and not options.get('overwrite', False):
            if self.verbose:
                self.stdout.write(f'Config {config.uuid} already has theme, skipping')
            return False
        
        if not self.dry_run:
            if existing_theme:
                existing_theme.delete()
            
            # Create theme from template
            new_theme = WidgetTheme.import_from_json(theme_data, config)
            
            # Set template name
            new_theme_name = options.get('new_theme_name', template_name)
            new_theme.theme_name = new_theme_name
            
            new_theme.save()
        
        return True