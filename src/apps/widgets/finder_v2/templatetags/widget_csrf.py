"""
Custom template tags for widget CSRF token generation.

This module provides template tags that generate CSRF tokens using the same
algorithm as the WsProtectMixin.get_token() method, ensuring consistency
between template-generated tokens and API validation.
"""

import base64
from django import template

register = template.Library()


@register.simple_tag
def widget_csrf_token(request):
    """
    Generate CSRF token based on User-Agent header using the widget CSRF algorithm.

    This uses the same algorithm as WsProtectMixin.get_token() to ensure
    consistency between template-generated tokens and API validation.

    Args:
        request: Django HttpRequest object

    Returns:
        str: Generated CSRF token
    """
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    # GitHub master algorithm (same as WsProtectMixin.get_token)
    token = base64.b64encode(user_agent.encode('utf-8')).decode('utf-8')
    token_slice = token[:32]  # slice(0, 30 + 2)

    result = []
    for i in range(len(token_slice)):
        # JavaScript: token[27 + 11 - (7 + i * 11) % 39]
        index = (27 + 11 - (7 + i * 11) % 39) % len(token)
        result.append(token[index])

    return ''.join(result) 