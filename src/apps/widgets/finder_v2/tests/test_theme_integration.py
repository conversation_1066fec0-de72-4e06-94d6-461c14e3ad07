"""
End-to-end integration tests for the finder-v2 theming system.

This test suite validates the complete theming workflow including:
- Theme creation and configuration
- CSS generation and injection
- Template rendering with themes
- JavaScript theme integration
- Performance and accessibility compliance
"""

import json
import time
from unittest.mock import patch, MagicMock
from django.test import TestCase, Client, override_settings
from django.urls import reverse
from django.template import Context, Template
from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.test.utils import override_settings

from src.apps.widgets.common.models import WidgetConfig, WidgetTheme
from src.apps.widgets.finder_v2.utils.theme_generator import ThemeGenerator, ThemeInjector
from src.apps.widgets.finder_v2.default_config.predefined_themes import PredefinedThemes


User = get_user_model()


class ThemeIntegrationTestCase(TestCase):
    """End-to-end integration tests for the theming system."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test widget config
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Test Widget',
            configuration={}
        )
        
        # Create test theme
        self.theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Integration Test Theme',
            primary_color='#3B82F6',
            secondary_color='#6B7280',
            accent_color='#10B981',
            background_color='#FFFFFF',
            text_color='#1F2937',
            font_family='system-ui',
            base_font_size='16px',
            font_weight='400',
            line_height='1.5',
            letter_spacing='0',
            element_padding='0.75rem',
            element_margin='0.5rem',
            border_radius='0.375rem',
            border_width='1px',
            shadow_intensity='medium',
            animation_speed='0.3s',
            hover_effect='darken'
        )
    
    def test_complete_theme_workflow(self):
        """Test the complete theme workflow from creation to rendering."""
        
        # 1. Theme creation and validation
        self.assertIsNotNone(self.theme.id)
        self.assertEqual(self.theme.theme_name, 'Integration Test Theme')
        self.assertEqual(self.theme.primary_color, '#3B82F6')
        
        # 2. CSS generation
        generator = ThemeGenerator(self.theme)
        css_content = generator.generate_theme_css()
        
        self.assertIn(':root', css_content)
        self.assertIn('--theme-primary', css_content)
        self.assertIn('#3B82F6', css_content)
        self.assertIn('.finder-v2-widget', css_content)
        
        # 3. CSS validation
        validation_result = ThemeGenerator.validate_theme_css(css_content)
        self.assertTrue(validation_result['valid'])
        self.assertEqual(len(validation_result['errors']), 0)
        
        # 4. Template rendering
        injector = ThemeInjector(self.widget_config)
        style_tag = injector.render_theme_style_tag()
        
        self.assertIn('<style', style_tag)
        self.assertIn('--theme-primary', style_tag)
        self.assertIn('#3B82F6', style_tag)
        
        # 5. JavaScript theme data
        theme_data_json = injector.get_theme_data_json()
        theme_data = json.loads(theme_data_json)
        
        self.assertEqual(theme_data['colors']['primary'], '#3B82F6')
        self.assertEqual(theme_data['typography']['font_family'], 'system-ui')
        self.assertEqual(theme_data['theme_name'], 'Integration Test Theme')
    
    def test_theme_template_integration(self):
        """Test theme integration with Django templates."""
        
        # Test theme tags template tag
        from src.apps.widgets.finder_v2.templatetags.theme_tags import render_theme_css
        
        rendered_css = render_theme_css(self.theme)
        
        self.assertIn('<style', rendered_css)
        self.assertIn('--theme-primary: #3B82F6', rendered_css)
        self.assertIn('.finder-v2-widget', rendered_css)
        
        # Test template rendering with context
        template_content = '''
        {% load theme_tags %}
        <div class="widget-container">
            {% render_theme_css theme %}
            <div class="finder-v2-widget">Content</div>
        </div>
        '''
        
        template = Template(template_content)
        context = Context({'theme': self.theme})
        rendered = template.render(context)
        
        self.assertIn('<style', rendered)
        self.assertIn('--theme-primary: #3B82F6', rendered)
        self.assertIn('finder-v2-widget', rendered)
    
    def test_vue_component_theme_integration(self):
        """Test Vue component theme integration."""
        
        # Test theme data structure for Vue component
        generator = ThemeGenerator(self.theme)
        theme_data = generator.get_theme_preview_data()
        
        # Verify theme data structure matches Vue component expectations
        self.assertIn('colors', theme_data)
        self.assertIn('typography', theme_data)
        self.assertIn('effects', theme_data)
        
        # Test color structure
        colors = theme_data['colors']
        self.assertEqual(colors['primary'], '#3B82F6')
        self.assertEqual(colors['secondary'], '#6B7280')
        self.assertEqual(colors['accent'], '#10B981')
        self.assertEqual(colors['background'], '#FFFFFF')
        self.assertEqual(colors['text'], '#1F2937')
        
        # Test typography structure
        typography = theme_data['typography']
        self.assertEqual(typography['font_family'], 'system-ui')
        self.assertEqual(typography['font_size'], '16px')
        
        # Test effects structure
        effects = theme_data['effects']
        self.assertEqual(effects['border_radius'], '0.375rem')
        self.assertEqual(effects['shadow_intensity'], 'medium')
    
    def test_theme_performance_optimization(self):
        """Test theme performance optimization features."""
        
        generator = ThemeGenerator(self.theme)
        
        # Test CSS generation performance
        start_time = time.time()
        css_content = generator.generate_theme_css()
        generation_time = time.time() - start_time
        
        # CSS generation should be fast (< 100ms)
        self.assertLess(generation_time, 0.1)
        
        # Test CSS size optimization
        css_size = len(css_content.encode('utf-8'))
        self.assertLess(css_size, 50000)  # < 50KB
        
        # Test CSS caching
        cached_css = generator.generate_css_custom_properties()
        self.assertEqual(css_content.split('\n')[0], cached_css.split('\n')[0])
        
        # Test performance metrics
        metrics = ThemeGenerator.get_performance_metrics()
        self.assertIsInstance(metrics, dict)
    
    def test_theme_accessibility_compliance(self):
        """Test theme accessibility compliance."""
        
        # Test contrast ratios
        def calculate_contrast_ratio(color1, color2):
            """Helper to calculate contrast ratio."""
            def get_luminance(hex_color):
                hex_color = hex_color.lstrip('#')
                r = int(hex_color[0:2], 16) / 255.0
                g = int(hex_color[2:4], 16) / 255.0
                b = int(hex_color[4:6], 16) / 255.0
                
                def gamma_correct(c):
                    return c / 12.92 if c <= 0.03928 else ((c + 0.055) / 1.055) ** 2.4
                
                r = gamma_correct(r)
                g = gamma_correct(g)
                b = gamma_correct(b)
                
                return 0.2126 * r + 0.7152 * g + 0.0722 * b
            
            lum1 = get_luminance(color1)
            lum2 = get_luminance(color2)
            
            if lum1 > lum2:
                return (lum1 + 0.05) / (lum2 + 0.05)
            else:
                return (lum2 + 0.05) / (lum1 + 0.05)
        
        # Test background/text contrast
        bg_text_contrast = calculate_contrast_ratio(
            self.theme.background_color, 
            self.theme.text_color
        )
        self.assertGreaterEqual(bg_text_contrast, 4.5)  # WCAG AA
        
        # Test primary/white contrast
        primary_white_contrast = calculate_contrast_ratio(
            self.theme.primary_color, 
            '#FFFFFF'
        )
        self.assertGreaterEqual(primary_white_contrast, 3.0)  # WCAG AA for large text
        
        # Test font size accessibility
        font_size_px = float(self.theme.base_font_size.replace('px', ''))
        self.assertGreaterEqual(font_size_px, 14)  # Minimum readable size
        
        # Test line height accessibility
        line_height = float(self.theme.line_height)
        self.assertGreaterEqual(line_height, 1.2)  # Minimum readable line height
    
    def test_predefined_themes_integration(self):
        """Test integration with predefined themes."""
        
        # Get predefined themes
        predefined_themes = PredefinedThemes.get_all_themes()
        
        self.assertGreater(len(predefined_themes), 0)
        
        # Test each predefined theme
        for theme_key, theme_config in predefined_themes.items():
            
            # Create theme from predefined config
            test_theme = WidgetTheme.objects.create(
                widget=self.widget_config,
                theme_name=theme_config['name'],
                primary_color=theme_config['colors']['primary'],
                secondary_color=theme_config['colors']['secondary'],
                accent_color=theme_config['colors']['accent'],
                background_color=theme_config['colors']['background'],
                text_color=theme_config['colors']['text'],
                font_family=theme_config['typography']['font_family'],
                base_font_size=theme_config['typography']['base_font_size'],
                border_radius=theme_config['effects']['border_radius'],
                shadow_intensity=theme_config['effects']['shadow_intensity']
            )
            
            # Test CSS generation
            generator = ThemeGenerator(test_theme)
            css_content = generator.generate_theme_css()
            
            self.assertIn(':root', css_content)
            self.assertIn('--theme-primary', css_content)
            self.assertIn(theme_config['colors']['primary'], css_content)
            
            # Test CSS validation
            validation_result = ThemeGenerator.validate_theme_css(css_content)
            self.assertTrue(validation_result['valid'], 
                          f'CSS validation failed for {theme_key}: {validation_result["errors"]}')
            
            # Clean up
            test_theme.delete()
    
    def test_theme_management_commands(self):
        """Test theme management commands integration."""
        
        # Test theme validation command
        from io import StringIO
        from django.core.management import call_command
        
        # Create output buffer
        out = StringIO()
        
        # Test validation command
        call_command('theme_validate', 
                    theme_id=self.theme.id,
                    check_css_validation=True,
                    check_accessibility=True,
                    verbosity=0,
                    stdout=out)
        
        output = out.getvalue()
        self.assertIn('Validation Results Summary', output)
        self.assertIn('Passed:', output)
        
        # Test migration command
        out = StringIO()
        call_command('theme_migrate', 
                    config_id=self.widget_config.id,
                    dry_run=True,
                    verbosity=0,
                    stdout=out)
        
        output = out.getvalue()
        self.assertIn('Migration completed', output)
        
        # Test duplication command
        out = StringIO()
        call_command('theme_duplicate',
                    source_theme_id=self.theme.id,
                    target_config_id=self.widget_config.id,
                    dry_run=True,
                    verbosity=0,
                    stdout=out)
        
        output = out.getvalue()
        self.assertIn('Duplication completed', output)
    
    def test_theme_error_handling(self):
        """Test theme error handling and fallbacks."""
        
        # Test with invalid theme
        invalid_theme = WidgetTheme.objects.create(
            widget=self.widget_config,
            theme_name='Invalid Theme',
            primary_color='invalid-color',
            secondary_color='#INVALID',
            accent_color='',
            background_color='#FFFFFF',
            text_color='#000000',
            font_family='',
            base_font_size='invalid-size',
            border_radius='-1px',
            shadow_intensity='invalid'
        )
        
        # Test CSS generation with invalid theme
        generator = ThemeGenerator(invalid_theme)
        css_content = generator.generate_theme_css()
        
        # Should still generate CSS (with fallbacks)
        self.assertIn(':root', css_content)
        self.assertIn('--theme-primary', css_content)
        
        # Test CSS validation
        validation_result = ThemeGenerator.validate_theme_css(css_content)
        
        # Should identify validation issues
        self.assertGreater(len(validation_result['errors']) + len(validation_result['warnings']), 0)
        
        # Test template rendering with invalid theme
        from src.apps.widgets.finder_v2.templatetags.theme_tags import render_theme_css
        
        rendered_css = render_theme_css(invalid_theme)
        
        # Should still render something (fallback)
        self.assertIn('<style', rendered_css)
        
        # Clean up
        invalid_theme.delete()
    
    def test_theme_caching_integration(self):
        """Test theme caching system integration."""
        
        generator = ThemeGenerator(self.theme)
        
        # First generation (should cache)
        css1 = generator.generate_css_custom_properties()
        
        # Second generation (should use cache)
        css2 = generator.generate_css_custom_properties()
        
        # Should be identical
        self.assertEqual(css1, css2)
        
        # Test cache invalidation by modifying theme
        self.theme.primary_color = '#FF0000'
        self.theme.save()
        
        # New generator with modified theme
        new_generator = ThemeGenerator(self.theme)
        css3 = new_generator.generate_css_custom_properties()
        
        # Should be different
        self.assertNotEqual(css1, css3)
        self.assertIn('#FF0000', css3)
    
    def test_responsive_theme_integration(self):
        """Test responsive theme features integration."""
        
        # Add responsive configuration
        self.theme.advanced_config = {
            'responsive_overrides': {
                'mobile': {
                    'font-size': '14px',
                    'padding': '0.5rem'
                },
                'tablet': {
                    'font-size': '15px',
                    'padding': '0.625rem'
                },
                'desktop': {
                    'font-size': '16px',
                    'padding': '0.75rem'
                }
            }
        }
        self.theme.save()
        
        # Test CSS generation with responsive overrides
        generator = ThemeGenerator(self.theme)
        css_content = generator.generate_theme_css()
        
        # Should contain responsive media queries
        self.assertIn('@media (max-width: 767px)', css_content)
        self.assertIn('@media (min-width: 768px)', css_content)
        self.assertIn('@media (min-width: 1024px)', css_content)
        
        # Should contain responsive variables
        self.assertIn('--theme-mobile-font-size', css_content)
        self.assertIn('--theme-tablet-font-size', css_content)
        self.assertIn('--theme-desktop-font-size', css_content)
    
    def test_theme_export_import_integration(self):
        """Test theme export and import functionality."""
        
        # Test theme export
        theme_data = self.theme.export_to_json()
        
        self.assertIsInstance(theme_data, dict)
        self.assertEqual(theme_data['theme_name'], 'Integration Test Theme')
        self.assertEqual(theme_data['primary_color'], '#3B82F6')
        
        # Test theme import
        imported_theme = WidgetTheme.import_from_json(theme_data, self.widget_config)
        
        self.assertEqual(imported_theme.theme_name, self.theme.theme_name)
        self.assertEqual(imported_theme.primary_color, self.theme.primary_color)
        self.assertEqual(imported_theme.secondary_color, self.theme.secondary_color)
        
        # Test that imported theme generates identical CSS
        original_generator = ThemeGenerator(self.theme)
        imported_generator = ThemeGenerator(imported_theme)
        
        original_css = original_generator.generate_theme_css()
        imported_css = imported_generator.generate_theme_css()
        
        # Should be functionally identical (allowing for minor variations)
        self.assertIn(self.theme.primary_color, original_css)
        self.assertIn(self.theme.primary_color, imported_css)
        
        # Clean up
        imported_theme.delete()
    
    def tearDown(self):
        """Clean up test data."""
        # Clean up is automatic with Django's test database rollback
        pass


class ThemePerformanceTestCase(TestCase):
    """Performance-focused tests for the theming system."""
    
    def setUp(self):
        """Set up performance test data."""
        self.user = User.objects.create_user(
            username='perfuser',
            email='<EMAIL>',
            password='perfpass123'
        )
        
        # Create multiple themes for bulk testing
        self.themes = []
        for i in range(10):
            config = WidgetConfig.objects.create(
                user=self.user,
                type='finder-v2',
                name=f'Perf Test Widget {i}',
                configuration={}
            )
            
            theme = WidgetTheme.objects.create(
                widget=config,
                theme_name=f'Performance Test Theme {i}',
                primary_color=f'#{i:02x}{i:02x}{i:02x}',
                secondary_color='#6B7280',
                accent_color='#10B981',
                background_color='#FFFFFF',
                text_color='#1F2937',
                font_family='system-ui',
                base_font_size='16px'
            )
            
            self.themes.append(theme)
    
    def test_bulk_css_generation_performance(self):
        """Test CSS generation performance with multiple themes."""
        
        start_time = time.time()
        
        for theme in self.themes:
            generator = ThemeGenerator(theme)
            css_content = generator.generate_theme_css()
            self.assertIn('--theme-primary', css_content)
        
        total_time = time.time() - start_time
        
        # Should generate CSS for 10 themes in under 1 second
        self.assertLess(total_time, 1.0)
        
        # Average time per theme should be under 100ms
        avg_time = total_time / len(self.themes)
        self.assertLess(avg_time, 0.1)
    
    def test_css_caching_performance(self):
        """Test CSS caching performance benefits."""
        
        theme = self.themes[0]
        generator = ThemeGenerator(theme)
        
        # First generation (no cache)
        start_time = time.time()
        css1 = generator.generate_css_custom_properties()
        first_time = time.time() - start_time
        
        # Second generation (with cache)
        start_time = time.time()
        css2 = generator.generate_css_custom_properties()
        second_time = time.time() - start_time
        
        # Cached generation should be faster
        self.assertLess(second_time, first_time)
        
        # Results should be identical
        self.assertEqual(css1, css2)
    
    def test_memory_usage_optimization(self):
        """Test memory usage optimization."""
        
        import gc
        import sys
        
        # Force garbage collection
        gc.collect()
        
        # Get baseline memory usage
        baseline_memory = sys.getsizeof(gc.get_objects())
        
        # Generate CSS for all themes
        generators = []
        for theme in self.themes:
            generator = ThemeGenerator(theme)
            css_content = generator.generate_theme_css()
            generators.append((generator, css_content))
        
        # Force garbage collection again
        gc.collect()
        
        # Get final memory usage
        final_memory = sys.getsizeof(gc.get_objects())
        
        # Memory increase should be reasonable
        memory_increase = final_memory - baseline_memory
        
        # Should not increase memory by more than 10MB for 10 themes
        self.assertLess(memory_increase, 10 * 1024 * 1024)


class ThemeSecurityTestCase(TestCase):
    """Security-focused tests for the theming system."""
    
    def setUp(self):
        """Set up security test data."""
        self.user = User.objects.create_user(
            username='secuser',
            email='<EMAIL>',
            password='secpass123'
        )
        
        self.widget_config = WidgetConfig.objects.create(
            user=self.user,
            type='finder-v2',
            name='Security Test Widget',
            configuration={}
        )
    
    def test_css_injection_prevention(self):
        """Test prevention of CSS injection attacks."""
        
        # Test malicious CSS injection attempts
        malicious_inputs = [
            'javascript:alert("xss")',
            'expression(alert("xss"))',
            'behavior: url("evil.htc")',
            'binding: url("evil.xml")',
            '/**/alert("xss")/**/',
            'url("javascript:alert(\'xss\')")'
        ]
        
        for malicious_input in malicious_inputs:
            theme = WidgetTheme.objects.create(
                widget=self.widget_config,
                theme_name=f'Malicious Theme - {malicious_input[:10]}',
                primary_color=malicious_input,
                secondary_color='#6B7280',
                accent_color='#10B981',
                background_color='#FFFFFF',
                text_color='#1F2937',
                font_family='system-ui',
                base_font_size='16px'
            )
            
            # Test CSS generation
            generator = ThemeGenerator(theme)
            css_content = generator.generate_theme_css()
            
            # Test CSS validation
            validation_result = ThemeGenerator.validate_theme_css(css_content)
            
            # Should detect dangerous patterns
            self.assertGreater(len(validation_result['errors']), 0)
            
            # Clean up
            theme.delete()
    
    def test_xss_prevention_in_templates(self):
        """Test XSS prevention in template rendering."""
        
        xss_inputs = [
            '<script>alert("xss")</script>',
            '"><script>alert("xss")</script>',
            'javascript:alert("xss")',
            'onmouseover="alert(\'xss\')"',
            '&lt;script&gt;alert("xss")&lt;/script&gt;'
        ]
        
        for xss_input in xss_inputs:
            theme = WidgetTheme.objects.create(
                widget=self.widget_config,
                theme_name=xss_input,
                primary_color='#3B82F6',
                secondary_color='#6B7280',
                accent_color='#10B981',
                background_color='#FFFFFF',
                text_color='#1F2937',
                font_family=xss_input,
                base_font_size='16px'
            )
            
            # Test template rendering
            from src.apps.widgets.finder_v2.templatetags.theme_tags import render_theme_css
            
            rendered_css = render_theme_css(theme)
            
            # Should not contain unescaped script tags
            self.assertNotIn('<script>', rendered_css)
            self.assertNotIn('javascript:', rendered_css)
            self.assertNotIn('onmouseover=', rendered_css)
            
            # Clean up
            theme.delete()
    
    def test_color_validation_security(self):
        """Test color value validation for security."""
        
        # Test various color format attacks
        color_attacks = [
            'red; background: url("evil.com")',
            '#FF0000; color: expression(alert("xss"))',
            'rgb(255,0,0); background: url(javascript:alert("xss"))',
            'url("data:text/html,<script>alert(1)</script>")',
            '#FF0000/**/; background: url("evil.com")/**/',
        ]
        
        for color_attack in color_attacks:
            theme = WidgetTheme.objects.create(
                widget=self.widget_config,
                theme_name='Color Attack Theme',
                primary_color=color_attack,
                secondary_color='#6B7280',
                accent_color='#10B981',
                background_color='#FFFFFF',
                text_color='#1F2937',
                font_family='system-ui',
                base_font_size='16px'
            )
            
            # Test CSS generation
            generator = ThemeGenerator(theme)
            css_content = generator.generate_theme_css()
            
            # Test CSS validation
            validation_result = ThemeGenerator.validate_theme_css(css_content)
            
            # Should detect security issues
            if 'javascript:' in color_attack or 'expression(' in color_attack:
                self.assertGreater(len(validation_result['errors']), 0)
            
            # Clean up
            theme.delete()
    
    def test_advanced_config_security(self):
        """Test advanced configuration security."""
        
        # Test malicious advanced configurations
        malicious_configs = [
            {
                'custom_css': '<script>alert("xss")</script>',
                'javascript_injection': 'alert("xss")'
            },
            {
                'background_image': 'url("javascript:alert(\'xss\')")',
                'font_url': 'url("data:text/html,<script>alert(1)</script>")'
            },
            {
                'responsive_overrides': {
                    'mobile': {
                        'color': 'expression(alert("xss"))'
                    }
                }
            }
        ]
        
        for malicious_config in malicious_configs:
            theme = WidgetTheme.objects.create(
                widget=self.widget_config,
                theme_name='Malicious Config Theme',
                primary_color='#3B82F6',
                secondary_color='#6B7280',
                accent_color='#10B981',
                background_color='#FFFFFF',
                text_color='#1F2937',
                font_family='system-ui',
                base_font_size='16px',
                advanced_config=malicious_config
            )
            
            # Test CSS generation
            generator = ThemeGenerator(theme)
            css_content = generator.generate_theme_css()
            
            # Should not contain dangerous patterns
            self.assertNotIn('javascript:', css_content)
            self.assertNotIn('<script>', css_content)
            self.assertNotIn('expression(', css_content)
            
            # Clean up
            theme.delete()