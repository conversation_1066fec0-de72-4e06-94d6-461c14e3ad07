# Do not delete this file even if it's empty
# Otherwise post_migrate signal is not sent in AppConfig
from django.utils.functional import cached_property

from src.apps.widgets.common.json_wrapper import WidgetParamsJsonWrapper


class FinderV2InterfaceTabs(object):
    TABS = [
        dict(title='By Vehicle v2',
             name='by_vehicle',
             html_id='tab-finder-v2-by-model',
             resource='search_by_model',
             template=None),  # Vue 3 handles rendering, no Django template needed
        # Removed: by_tire and by_rim tabs to simplify interface and focus on vehicle search only
    ]

    TAB_CHOICES = [(tab['name'], tab['title']) for tab in TABS]

    def __init__(self, tabs):
        self.tabs = tabs

    @property
    def primary(self):
        for tab in self.TABS:
            if self.tabs['primary'] == tab['name']:
                return tab

    def __len__(self):
        return len(self.tabs['visible'])

    def __iter__(self):
        for tab in self.TABS:
            if tab['name'] in self.tabs['visible']:
                tab_obj = tab.copy()
                tab_obj['primary'] = (self.tabs['primary'] == tab['name'])
                yield tab_obj


class FinderV2JsonWrapper(WidgetParamsJsonWrapper):

    @cached_property
    def interface_tabs(self):
        return FinderV2InterfaceTabs(self['interface']['tabs'])

    @cached_property
    def flow_type(self):
        """Get the API flow type: 'primary' or 'alternative'"""
        return self['interface'].get('flow_type', default='primary')

    @cached_property
    def api_version(self):
        """Get the API version for this widget"""
        return self['interface'].get('api_version', default='v2')

    def get_filter_params(self):
        params = {}

        try:
            # Use bracket notation like the original finder widget
            # The JSON wrapper handles KeyError by falling back to default values
            filter_by = self['content']['filter']['by']
            if filter_by:
                filter_list = self['content']['filter'][filter_by]
                # Handle both string items and dict items with slug/value
                string_items = []
                for item in filter_list:
                    if isinstance(item, dict):
                        # Extract slug or value from dict items
                        string_items.append(str(item.get('slug', item.get('value', item))))
                    else:
                        string_items.append(str(item))
                params[filter_by] = ','.join(string_items)

            # Handle only_oem parameter
            if self['content']['only_oem']:
                params['only_oem'] = True

            # Add region parameters for API calls
            # Each region slug becomes a separate 'region' parameter
            regions = self['content'].get('regions', [])
            if not regions:
                # For backward compatibility, also check legacy 'regions_priority' key
                regions = self['content'].get('regions_priority', [])

            if regions:
                # Convert to list format that axios can handle as repeated parameters
                params['region'] = regions

        except (KeyError, TypeError, AttributeError):
            # If there's any error accessing the configuration, return empty params
            # This ensures the widget continues to work even with malformed config
            pass

        return params
