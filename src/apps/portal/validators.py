import time
import re
import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _
from .models import SpamAttempt

logger = logging.getLogger(__name__)


class EmailDomainValidator:
    """Service for validating email domains against spam lists"""
    
    # Common disposable email domains
    DISPOSABLE_DOMAINS = {
        '10minutemail.com', '2prong.com', '9ox.net', 'anonymbox.com',
        'armyspy.com', 'blogtoemail.com', 'boun.cr', 'bugmenot.com',
        'deadaddress.com', 'dispostable.com', 'fakemail.fr', 'guerrillamail.com',
        'incognitomail.org', 'jetable.org', 'legitmail.club', 'mailcatch.com',
        'mailhazard.com', 'mailinator.com', 'mailtothis.com', 'no-spam.ws',
        'nospam4me.com', 'nowmymail.com', 'putthisinyourspamdatabase.com',
        'quickinbox.com', 'rcpt.at', 'rtrtr.com', 'safe-mail.net',
        'sharklasers.com', 'spam4.me', 'spamherelots.com', 'supere-mail.com',
        'tempemail.com', 'tempinbox.com', 'tempomail.fr', 'throwaway.email',
        'trashmail.com', 'yopmail.com', 'yopmail.fr', 'yopmail.net',
        'temp-mail.org', 'mohmal.com', 'guerrillamailblock.com',
        'spamgourmet.com', 'mytrashmail.com', 'tempmail.net'
    }
    
    # Suspicious patterns in email addresses
    SUSPICIOUS_PATTERNS = [
        r'^[a-zA-Z0-9]{20,}@',  # Very long random-looking usernames
        r'^test\d+@',           # <EMAIL>
        r'^user\d+@',           # <EMAIL>  
        r'^admin\d+@',          # <EMAIL>
        r'^\d+@',               # Numeric only usernames
        r'^.+\+.+\+.+@',        # Multiple + signs (excessive aliasing)
    ]
    
    def __init__(self):
        # Allow custom disposable domains from settings
        custom_domains = getattr(settings, 'DISPOSABLE_EMAIL_DOMAINS', set())
        self.disposable_domains = self.DISPOSABLE_DOMAINS.union(custom_domains)
        
        # Allow disabling this validator in development
        self.enabled = getattr(settings, 'EMAIL_DOMAIN_VALIDATION_ENABLED', True)
    
    def is_disposable_email(self, email):
        """Check if email uses a disposable domain"""
        if not self.enabled:
            return False
            
        domain = email.split('@')[1].lower() if '@' in email else ''
        return domain in self.disposable_domains
    
    def has_suspicious_pattern(self, email):
        """Check if email matches suspicious patterns"""
        if not self.enabled:
            return False
            
        for pattern in self.SUSPICIOUS_PATTERNS:
            if re.match(pattern, email, re.IGNORECASE):
                return True
        return False
    
    def validate_email(self, email, ip_address='', request=None):
        """Validate email and raise ValidationError if suspicious"""
        if not self.enabled:
            return True
            
        if self.is_disposable_email(email):
            logger.warning(f"Disposable email domain detected: {email} from IP {ip_address}")
            
            # Record spam attempt
            SpamAttempt.record_spam_attempt(
                ip_address=ip_address,
                attempt_type='registration',
                blocked_reason='disposable_email',
                email=email,
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                additional_data={'domain': email.split('@')[1] if '@' in email else ''}
            )
            
            raise ValidationError(
                _('Please use a permanent email address for registration.'),
                code='disposable_email'
            )
        
        if self.has_suspicious_pattern(email):
            logger.warning(f"Suspicious email pattern detected: {email} from IP {ip_address}")
            
            # Record spam attempt
            SpamAttempt.record_spam_attempt(
                ip_address=ip_address,
                attempt_type='registration',
                blocked_reason='pattern',
                email=email,
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else ''
            )
            
            raise ValidationError(
                _('Please use a valid email address.'),
                code='suspicious_pattern'
            )
        
        return True


class FormTimingValidator:
    """Service for detecting automated form submissions based on timing"""
    
    def __init__(self):
        self.min_time = getattr(settings, 'FORM_MIN_SUBMISSION_TIME', 5)  # seconds
        self.enabled = getattr(settings, 'FORM_TIMING_VALIDATION_ENABLED', True)
    
    def generate_timing_token(self):
        """Generate timestamp token for form timing validation"""
        return str(int(time.time()))
    
    def validate_submission_timing(self, timing_token, ip_address='', request=None):
        """Validate form submission timing"""
        if not self.enabled:
            return True
            
        if not timing_token:
            logger.warning(f"Missing timing token from IP {ip_address}")
            return self._handle_timing_violation(ip_address, request, 'missing_token')
        
        try:
            start_time = int(timing_token)
            current_time = int(time.time())
            elapsed_time = current_time - start_time
            
            if elapsed_time < self.min_time:
                logger.warning(
                    f"Form submitted too quickly ({elapsed_time}s) from IP {ip_address}"
                )
                return self._handle_timing_violation(ip_address, request, 'too_fast', elapsed_time)
            
            # Also check if timing token is suspiciously old (more than 1 hour)
            if elapsed_time > 3600:
                logger.warning(
                    f"Form timing token too old ({elapsed_time}s) from IP {ip_address}"
                )
                return self._handle_timing_violation(ip_address, request, 'too_old', elapsed_time)
                
        except (ValueError, TypeError):
            logger.warning(f"Invalid timing token from IP {ip_address}")
            return self._handle_timing_violation(ip_address, request, 'invalid_token')
        
        return True
    
    def _handle_timing_violation(self, ip_address, request, violation_type, elapsed_time=None):
        """Handle timing validation violations"""
        # Record spam attempt
        additional_data = {'violation_type': violation_type}
        if elapsed_time is not None:
            additional_data['elapsed_time'] = elapsed_time
            additional_data['min_required'] = self.min_time
        
        SpamAttempt.record_spam_attempt(
            ip_address=ip_address,
            attempt_type='registration',
            blocked_reason='timing',
            user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
            additional_data=additional_data
        )
        
        raise ValidationError(
            _('Please take your time filling out the form.'),
            code='form_timing'
        )


class HoneypotValidator:
    """Service for honeypot field validation"""
    
    def __init__(self):
        self.enabled = getattr(settings, 'HONEYPOT_VALIDATION_ENABLED', True)
    
    def validate_honeypot(self, honeypot_value, ip_address='', request=None):
        """Validate honeypot field - should be empty"""
        if not self.enabled:
            return True
            
        if honeypot_value:
            logger.warning(f"Honeypot field filled from IP {ip_address}: '{honeypot_value}'")
            
            # Record spam attempt
            SpamAttempt.record_spam_attempt(
                ip_address=ip_address,
                attempt_type='registration',
                blocked_reason='honeypot',
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                additional_data={'honeypot_value': str(honeypot_value)[:100]}  # Limit length
            )
            
            # Silently reject - don't give feedback to bots
            raise ValidationError(
                _('Invalid form submission.'),
                code='honeypot'
            )
        
        return True


# Convenience functions for easy usage
def validate_email_domain(email, ip_address='', request=None):
    """Convenience function for email domain validation"""
    validator = EmailDomainValidator()
    return validator.validate_email(email, ip_address, request)


def validate_form_timing(timing_token, ip_address='', request=None):
    """Convenience function for form timing validation"""
    validator = FormTimingValidator()
    return validator.validate_submission_timing(timing_token, ip_address, request)


def validate_honeypot(honeypot_value, ip_address='', request=None):
    """Convenience function for honeypot validation"""
    validator = HoneypotValidator()
    return validator.validate_honeypot(honeypot_value, ip_address, request)