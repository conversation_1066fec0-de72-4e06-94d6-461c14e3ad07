# Generated by Django 2.2.14 on 2020-07-03 13:22

from django.db import migrations

from ws_django_helpers.migration_utils import SwapTableNames, DropOldTable


class Migration(migrations.Migration):

    dependencies = [
        ('portal', '0001_initial'),
        ('ws_live_settings', '0001_initial'),
    ]

    operations = [
        migrations.SeparateDatabaseAndState(
            database_operations=[
                SwapTableNames('ws_live_settings_wslivesettings',
                               'ws_settings_enginesettings'),
                DropOldTable('ws_settings_enginesettings'),
            ],
            state_operations=[]
        )
    ]
