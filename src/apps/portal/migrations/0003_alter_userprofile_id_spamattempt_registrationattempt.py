# Generated by Django 4.2.21 on 2025-07-24 17:24

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("portal", "0002_auto_20250526_0101"),
    ]

    operations = [
        migrations.AlterField(
            model_name="userprofile",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.CreateModel(
            name="SpamAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip_address", models.GenericIPAddressField()),
                ("email_attempted", models.EmailField(blank=True, max_length=254)),
                (
                    "attempt_type",
                    models.CharField(
                        choices=[
                            ("registration", "Registration"),
                            ("login", "Login"),
                            ("password_reset", "Password Reset"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "blocked_reason",
                    models.CharField(
                        choices=[
                            ("rate_limit", "Rate Limit Exceeded"),
                            ("disposable_email", "Disposable Email Domain"),
                            ("honeypot", "Honeypot Field Filled"),
                            ("timing", "Form Submitted Too Quickly"),
                            ("turnstile", "Turnstile Validation Failed"),
                            ("pattern", "Suspicious Email Pattern"),
                        ],
                        max_length=100,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("user_agent", models.TextField(blank=True)),
                ("additional_data", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["ip_address", "timestamp"],
                        name="portal_spam_ip_addr_9154bd_idx",
                    ),
                    models.Index(
                        fields=["attempt_type", "timestamp"],
                        name="portal_spam_attempt_0920fa_idx",
                    ),
                    models.Index(
                        fields=["blocked_reason", "timestamp"],
                        name="portal_spam_blocked_2fa28d_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="RegistrationAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip_address", models.GenericIPAddressField()),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("success", models.BooleanField(default=False)),
                ("user_agent", models.TextField(blank=True)),
                ("email_attempted", models.EmailField(blank=True, max_length=254)),
            ],
            options={
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["ip_address", "timestamp"],
                        name="portal_regi_ip_addr_b7a19f_idx",
                    ),
                    models.Index(
                        fields=["timestamp"], name="portal_regi_timesta_a87bf2_idx"
                    ),
                ],
            },
        ),
    ]
