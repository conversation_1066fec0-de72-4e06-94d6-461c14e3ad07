import logging
from django.http import HttpResponse
from django.conf import settings
from django.shortcuts import render
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin

from .models import RegistrationAttempt, SpamAttempt

logger = logging.getLogger(__name__)


class RegistrationRateLimitMiddleware(MiddlewareMixin):
    """Middleware for rate limiting registration attempts"""
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.max_attempts = getattr(settings, 'REGISTRATION_RATE_LIMIT_MAX', 3)
        self.time_window = getattr(settings, 'REGISTRATION_RATE_LIMIT_HOURS', 1)
        self.enabled = getattr(settings, 'REGISTRATION_RATE_LIMIT_ENABLED', True)
    
    def process_request(self, request):
        """Check rate limits before processing registration requests"""
        if not self.enabled:
            return None
            
        # Only check for registration POST requests
        if (request.method == 'POST' and 
            request.path in ['/accounts/register/', reverse('registration_register')]):
            
            ip_address = self.get_client_ip(request)
            
            # Check rate limit
            if RegistrationAttempt.is_rate_limited(
                ip_address, self.max_attempts, self.time_window
            ):
                # Log the blocked attempt
                logger.warning(
                    f"Registration rate limit exceeded for IP {ip_address}"
                )
                
                # Record spam attempt
                SpamAttempt.record_spam_attempt(
                    ip_address=ip_address,
                    attempt_type='registration',
                    blocked_reason='rate_limit',
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    additional_data={
                        'max_attempts': self.max_attempts,
                        'time_window': self.time_window
                    }
                )
                
                # Return rate limit response
                return self.rate_limit_response(request, ip_address)
        
        return None
    
    def process_response(self, request, response):
        """Record registration attempts after processing"""
        if not self.enabled:
            return response
            
        # Record registration attempts
        if (request.method == 'POST' and 
            request.path in ['/accounts/register/', reverse('registration_register')]):
            
            ip_address = self.get_client_ip(request)
            success = response.status_code == 302  # Redirect indicates success
            email = request.POST.get('email', '')
            
            # Record the attempt
            RegistrationAttempt.record_attempt(
                ip_address=ip_address,
                success=success,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                email=email
            )
            
            if success:
                logger.info(f"Successful registration from IP {ip_address}")
            else:
                logger.info(f"Failed registration attempt from IP {ip_address}")
        
        return response
    
    def get_client_ip(self, request):
        """Get the client's IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def rate_limit_response(self, request, ip_address):
        """Return rate limit exceeded response"""
        recent_attempts = RegistrationAttempt.get_recent_attempts_count(
            ip_address, self.time_window
        )
        
        context = {
            'max_attempts': self.max_attempts,
            'time_window': self.time_window,
            'attempts_made': recent_attempts,
            'ip_address': ip_address,
        }
        
        return render(
            request,
            'registration/rate_limit_exceeded.html',
            context,
            status=429
        )


def get_client_ip(request):
    """Utility function to get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip