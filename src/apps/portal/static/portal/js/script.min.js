/********************************************************
 *
 * Custom Javascript code for AppStrap Bootstrap theme
 * Written by Themelize.me (http://themelize.me)
 *
 *******************************************************//*global jRespond */var jPM={},PLUGINS_PATH="./plugins/",SLIDER_REV_VERSION="4.6";jQuery(document).ready(function(){"use strict";if($("[data-toggle=search-form]").length>0){var e=$("[data-toggle=search-form]"),t=e.data("target");$("[data-toggle=search-form]").click(function(){$(t).toggleClass("open");$(t+" .search").focus();$(e).toggleClass("open");$("html").toggleClass("search-form-open")});$("[data-toggle=search-form-close]").click(function(){$(t).removeClass("open");$(e).removeClass("open");$("html").removeClass("search-form-open")})}var n="green";jQuery(".colour-switcher a").click(function(){var e=jQuery(this).attr("href").replace("#",""),t=3*Math.floor(Math.random()*6);jQuery(".colour-switcher a").removeClass("active");jQuery(".colour-switcher a."+e).addClass("active");e!==n?jQuery("#colour-scheme").attr("href","css/colour-"+e+".css?x="+t):jQuery("#colour-scheme").attr("href","#")});navigator.userAgent.toLowerCase().indexOf("msie")>-1&&jQuery("[placeholder]").focus(function(){var e=jQuery(this);if(e.val()===e.attr("placeholder")){if(this.originalType){this.type=this.originalType;delete this.originalType}e.val("");e.removeClass("placeholder")}}).blur(function(){var e=jQuery(this);if(e.val()===""){e.addClass("placeholder");e.val(e.attr("placeholder"))}}).blur();jQuery('[data-hover="dropdown"]').length>0&&jQuery().themeLoadPlugin(["bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js"],[],null,"append");jQuery().tooltip&&jQuery("body").tooltip({selector:"[data-toggle=tooltip]",container:"body"});jQuery().popover&&jQuery("body").popover({selector:"[data-toggle=popover]",container:"body",trigger:"hover"});jQuery("html").themeSubMenus();jQuery("[data-page-class]").each(function(){jQuery("html").addClass(jQuery(this).data("page-class"))});jQuery(".navbar-fixed-top").size()>0&&jQuery("html").addClass("has-navbar-fixed-top");jQuery("[data-toggle=show-hide]").each(function(){jQuery(this).click(function(){var e="open",t=jQuery(this).attr("data-target"),n=jQuery(this).attr("data-target-state"),r=jQuery(this).attr("data-callback");typeof n!="undefined"&&n!==!1&&(e=n);e===undefined&&(e="open");jQuery(t).toggleClass("show-hide-"+e);jQuery(this).toggleClass(e);r&&typeof r=="function"&&r()})});if(jQuery("[data-toggle=switch]").length>0){var r=function(){jQuery("[data-toggle=switch]").bootstrapSwitch()};jQuery().themeLoadPlugin(["bootstrap-switch/build/js/bootstrap-switch.min.js"],["bootstrap-switch/build/css/bootstrap3/bootstrap-switch.min.css"],r)}if(jQuery("[data-toggle=jpanel-menu]").length>0){var i=function(){var e=jQuery("[data-toggle=jpanel-menu]");jPM=jQuery.jPanelMenu({menu:e.data("target"),direction:e.data("direction"),trigger:"."+e.attr("class"),excludedPanelContent:".jpanel-menu-exclude",openPosition:"280px",afterOpen:function(){e.addClass("open");jQuery("html").addClass("jpanel-menu-open")},afterClose:function(){e.removeClass("open");jQuery("html").removeClass("jpanel-menu-open")}});var t=jRespond([{label:"small",enter:0,exit:991}]);t.addFunc({breakpoint:"small",enter:function(){jPM.on();jQuery("html").themeSubMenus()},exit:function(){jPM.off();jQuery("html").themeSubMenus()}})};jQuery().themeLoadPlugin(["jPanelMenu/jquery.jpanelmenu.min.js","jRespond/js/jRespond.js"],[],i)}if(jQuery("[data-toggle=clingify]").length>0){var s=function(){jQuery("[data-toggle=clingify]").clingify({breakpoint:1010})};jQuery().themeLoadPlugin(["clingify/jquery.clingify.min.js"],["clingify/clingify.css"],s)}if(jQuery(".flexslider").length>0){var o=function(){jQuery(".flexslider").each(function(){var e={animation:jQuery(this).attr("data-transition"),selector:".slides > .slide",controlNav:!0,smoothHeight:!0,start:function(e){e.find("[data-animate-in]").each(function(){jQuery(this).css("visibility","hidden")});e.find(".slide-bg").each(function(){jQuery(this).css({"background-image":"url("+jQuery(this).data("bg-img")+")"});jQuery(this).css("visibility","visible").addClass("animated").addClass(jQuery(this).data("animate-in"))});e.find(".slide").eq(1).find("[data-animate-in]").each(function(){jQuery(this).css("visibility","hidden");jQuery(this).data("animate-delay")&&jQuery(this).addClass(jQuery(this).data("animate-delay"));jQuery(this).data("animate-duration")&&jQuery(this).addClass(jQuery(this).data("animate-duration"));jQuery(this).css("visibility","visible").addClass("animated").addClass(jQuery(this).data("animate-in"));jQuery(this).one("webkitAnimationEnd oanimationend msAnimationEnd animationend",function(){jQuery(this).removeClass(jQuery(this).data("animate-in"))})})},before:function(e){e.find(".slide-bg").each(function(){jQuery(this).removeClass(jQuery(this).data("animate-in")).removeClass("animated").css("visibility","hidden")});e.find(".slide").eq(e.animatingTo+1).find("[data-animate-in]").each(function(){jQuery(this).css("visibility","hidden")})},after:function(e){e.find(".slide").find("[data-animate-in]").each(function(){jQuery(this).css("visibility","hidden")});e.find(".slide").eq(e.animatingTo+1).find("[data-animate-in]").each(function(){jQuery(this).data("animate-delay")&&jQuery(this).addClass(jQuery(this).data("animate-delay"));jQuery(this).data("animate-duration")&&jQuery(this).addClass(jQuery(this).data("animate-duration"));jQuery(this).css("visibility","visible").addClass("animated").addClass(jQuery(this).data("animate-in"));jQuery(this).one("webkitAnimationEnd oanimationend msAnimationEnd animationend",function(){jQuery(this).removeClass(jQuery(this).data("animate-in"))})});$(window).trigger("resize")}},t=jQuery(this).attr("data-slidernav");t!=="auto"&&(e=$.extend({},e,{manualControls:t+" li a",controlsContainer:".flexslider-wrapper"}));jQuery("html").addClass("has-flexslider");jQuery(this).flexslider(e);jQuery(".flexslider").resize()})};jQuery().themeLoadPlugin(["flexslider/jquery.flexslider-min.js"],["flexslider/flexslider.css"],o)}if(jQuery("[data-js=quicksand]").length>0){var u=function(){jQuery("[data-js=quicksand]").each(function(){var e=jQuery(this).find(jQuery(this).data("quicksand-trigger")),t=jQuery(jQuery(this).data("quicksand-target")),n=t.clone(),r="all",i;e.click(function(s){r=jQuery(this).data("quicksand-fid");i="";e.parents("li").removeClass("active");jQuery(this).parents("li").addClass("active");r==="all"?i=n.find("[data-quicksand-id]"):i=n.find('[data-quicksand-tid="'+r+'"]');t.quicksand(i,{duration:600,attribute:"data-quicksand-id",adjustWidth:"auto"}).addClass("quicksand-target");s.preventDefault()})})};$.getScript("js/jquery-migrate-1.2.1.min.js");jQuery().themeLoadPlugin(["quicksand/jquery.quicksand.js"],[],u)}if(jQuery("[data-toggle=slider-rev]").length>0){var a=function(){jQuery("[data-toggle=slider-rev]").each(function(){var e=$(this),t,n={delay:9e3,startheight:500,startwidth:960,fullScreenAlignForce:"off",autoHeight:"off",hideThumbs:200,thumbWidth:100,thumbHeight:50,thumbAmount:3,navigationType:"bullet",navigationArrows:"solo",hideThumbsOnMobile:"off",hideBulletsOnMobile:"off",hideArrowsOnMobile:"off",hideThumbsUnderResoluition:0,navigationStyle:"round",navigationHAlign:"center",navigationVAlign:"bottom",navigationHOffset:0,navigationVOffset:20,soloArrowLeftHalign:"left",soloArrowLeftValign:"center",soloArrowLeftHOffset:20,soloArrowLeftVOffset:0,soloArrowRightHalign:"right",soloArrowRightValign:"center",soloArrowRightHOffset:20,soloArrowRightVOffset:0,keyboardNavigation:"on",touchenabled:"on",onHoverStop:"on",stopAtSlide:-1,stopAfterLoops:-1,hideCaptionAtLimit:0,hideAllCaptionAtLimit:0,hideSliderAtLimit:0,shadow:0,fullWidth:"off",fullScreen:"off",minFullScreenHeight:0,fullScreenOffsetContainer:"",dottedOverlay:"none",forceFullWidth:"off",spinner:"spinner0"};t=$.extend({},n,e.data());e.revolution(t)})};jQuery().themeLoadPlugin(["slider-revolution/rs-plugin/js/jquery.themepunch.tools.min.js?v="+SLIDER_REV_VERSION,"slider-revolution/rs-plugin/js/jquery.themepunch.revolution.min.js?v="+SLIDER_REV_VERSION],["slider-revolution/rs-plugin/css/settings.css?v="+SLIDER_REV_VERSION],a)}if(jQuery("[data-toggle=backstretch]").length>0){var f=function(){jQuery("[data-toggle=backstretch]").each(function(){var e=$(this),t=jQuery,n=[],r={fade:750,duration:4e3};jQuery.each(e.data("backstretch-imgs").split(","),function(e,t){n[e]=t});if(e.data("backstretch-target")){t=e.data("backstretch-target");t==="self"?t=e:$(t).length>0&&(t=$(t))}if(n){$("html").addClass("has-backstretch");r=$.extend({},r,e.data());t.backstretch(n,r);if(e.data("backstretch-overlay")!==!1){$(".backstretch").prepend('<div class="backstretch-overlay"></div>');e.data("backstretch-overlay-opacity")&&$(".backstretch").find(".backstretch-overlay").css("background","white").fadeTo(0,e.data("backstretch-overlay-opacity"))}}})};jQuery().themeLoadPlugin(["backstretch/jquery.backstretch.min.js"],[],f)}var l=["iframe[src*='player.vimeo.com']","iframe[src*='youtube.com']","iframe[src*='youtube-nocookie.com']","iframe[src*='kickstarter.com'][src*='video.html']","object","embed"];if($(this).find(l.join(",")).length>0){var c=function(){$("body").fitVids({ignore:".no-fitvids"})};jQuery().themeLoadPlugin(["fitvidsjs/jquery.fitvids.js"],[],c)}if(jQuery("[data-toggle=isotope-grid]").length>0){var h=function(){jQuery("[data-toggle=isotope-grid]").each(function(){var e=$(this),t=$(this).data("isotope-options");e.isotope(t);jQuery().imagesLoaded&&e.imagesLoaded(function(){e.isotope("layout")});$("body").addClass("has-isotope")})};jQuery().themeLoadPlugin(["imagesloaded/imagesloaded.pkgd.min.js","isotope/dist/isotope.pkgd.min.js"],[],h)}jQuery("code").length>0&&jQuery().themeLoadPlugin(["prism/prism.js"],["prism/prism.css"]);if(jQuery('[data-toggle="owl-carousel"]').length>0){var p=function(){var e={items:5,itemsCustom:!1,itemsDesktop:[1199,4],itemsDesktopSmall:[979,3],itemsTablet:[768,2],itemsTabletSmall:!1,itemsMobile:[479,1],singleItem:!1,itemsScaleUp:!1,slideSpeed:200,paginationSpeed:800,rewindSpeed:1e3,autoPlay:!1,stopOnHover:!1,navigation:!1,navigationText:["prev","next"],rewindNav:!0,scrollPerPage:!1,pagination:!0,paginationNumbers:!1,responsive:!0,responsiveRefreshRate:200,responsiveBaseWidth:window,baseClass:"owl-carousel",theme:"owl-theme",lazyLoad:!1,lazyFollow:!0,lazyEffect:"fade",autoHeight:!1,jsonPath:!1,jsonSuccess:!1,dragBeforeAnimFinish:!0,mouseDrag:!0,touchDrag:!0,addClassActive:!1,transitionStyle:!1,beforeUpdate:!1,afterUpdate:!1,beforeInit:!1,afterInit:!1,beforeMove:!1,afterMove:!1,afterAction:!1,startDragging:!1,afterLazyLoad:!1};jQuery('[data-toggle="owl-carousel"]').each(function(){var t,n={};$(this).data("owl-carousel-settings")!==""&&(n=$(this).data("owl-carousel-settings"));t=jQuery.extend(e,n);$(this).owlCarousel(t)})};jQuery().themeLoadPlugin(["owl-carousel/owl-carousel/owl.carousel.min.js"],["owl-carousel/owl-carousel/owl.carousel.css"],p)}if(jQuery('[data-toggle="magnific-popup"]').length>0){var d=function(){var e={disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close">&times;</button>',tClose:"Close (Esc)",tLoading:"Loading...",type:"image"};jQuery('[data-toggle="magnific-popup"]').each(function(){var t,n={};$(this).data("magnific-popup-settings")!==""&&(n=$(this).data("magnific-popup-settings"));t=jQuery.extend(e,n);$(this).magnificPopup(t)})};jQuery().themeLoadPlugin(["magnific-popup/dist/jquery.magnific-popup.min.js"],["magnific-popup/dist/magnific-popup.css"],d)}});jQuery.fn.extend({themeSubMenus:function(){jQuery("ul.dropdown-menu [data-toggle=dropdown]",jQuery(this)).on("click",function(e){e.preventDefault();e.stopPropagation();jQuery(this).parent().toggleClass("open")})},themeLoadPlugin:function(e,t,n,r){jQuery.ajaxPrefilter("script",function(e){e.crossDomain=!0});if(e){var i=0,s=function(){if(++i===e.length){$.each(t,function(e,t){jQuery("head").prepend('<link href="'+PLUGINS_PATH+t+'" rel="stylesheet" type="text/css" />')});n&&typeof n=="function"&&n()}};r===undefined?$.each(e,function(e,t){$.getScript(PLUGINS_PATH+t,s)}):r==="append"&&$.each(e,function(e,t){jQuery('script[src*="bootstrap.min.js"]').after('<script src="'+PLUGINS_PATH+t+'"></script>');s()})}}});