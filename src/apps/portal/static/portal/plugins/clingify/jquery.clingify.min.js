;(function($,window,document,undefined){var pluginName="clingify",defaults={breakpoint:0,extraClass:"",throttle:100,detached:$.noop,locked:$.noop,resized:$.noop},wrapperClass="js-clingify-wrapper",lockedClass="js-clingify-locked",placeholderClass="js-clingify-placeholder",$buildPlaceholder=$("<div>").addClass(placeholderClass),$buildWrapper=$("<div>").addClass(wrapperClass),$window=$(window);function Plugin(element,options){this.element=element;this.$element=$(element);this.options=$.extend({},defaults,
options);this._defaults=defaults;this._name=pluginName;this.vars={elemHeight:this.$element.height()};this.init()}Plugin.prototype={init:function(){var cling=this,scrollTimeout,throttle=cling.options.throttle,extraClass=cling.options.extraClass;cling.$element.wrap($buildPlaceholder.height(cling.vars.elemHeight)).wrap($buildWrapper);if(extraClass!==""&&typeof extraClass==="string"){cling.findWrapper().addClass(extraClass);cling.findPlaceholder().addClass(extraClass)}$window.on("scroll resize",function(event){if(!scrollTimeout)scrollTimeout=
setTimeout(function(){if(event.type==="resize"&&typeof cling.options.resized==="function")cling.options.resized();cling.checkElemStatus();scrollTimeout=null},throttle)})},checkCoords:function(){var coords={windowWidth:$window.width(),windowOffset:$window.scrollTop(),placeholderOffset:this.findPlaceholder().offset().top};return coords},detachElem:function(){if(typeof this.options.detached==="function")this.options.detached();this.findWrapper().removeClass(lockedClass)},lockElem:function(){if(typeof this.options.locked===
"function")this.options.locked();this.findWrapper().addClass(lockedClass)},findPlaceholder:function(){return this.$element.closest("."+placeholderClass)},findWrapper:function(){return this.$element.closest("."+wrapperClass)},checkElemStatus:function(){var cling=this,currentCoords=cling.checkCoords(),isScrolledPast=function(){if(currentCoords.windowOffset>=currentCoords.placeholderOffset)return true;else return false},isWideEnough=function(){if(currentCoords.windowWidth>=cling.options.breakpoint)return true;
else return false};if(isScrolledPast()&&isWideEnough())cling.lockElem();else if(!isScrolledPast()||!isWideEnough())cling.detachElem()}};$.fn[pluginName]=function(options){return this.each(function(){if(!$.data(this,"plugin_"+pluginName))$.data(this,"plugin_"+pluginName,new Plugin(this,options))})}})(jQuery,window,document);