<!doctype html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>Clingify Demo</title>
        <meta name="description" content="A flexible jQuery plugin for sticky elements">
        <meta name="viewport" content="width=device-width,initial-scale=1">
        <!-- build:css({.tmp,app}) styles/main.css -->
        <link rel="stylesheet" href="styles/clingify.css">
        <link rel="stylesheet" href="styles/demo.css">
        <!-- endbuild -->
        <!-- build:js scripts/vendor/modernizr.js -->
        <script src="bower_components/modernizr/modernizr.js"></script>
        <!-- endbuild -->
    </head>
    <body>
        <div class="wrapper">
            <div class="content">
                <a href="//github.com/theroux/clingify" class="github-link">&laquo; Back to GitHub Repo</a>
                <header>
                    <h1 class="title">Clingify Demo</h1>
                    <nav class="nav-persistent">
                        <ul>
                            <li>Jump to:</li>
                            <li><a href="#purpose">Purpose</a></li>
                            <li><a href="#features">Features</a></li>
                            <li><a href="#how-to-use">How to use</a></li>
                            <li><a href="#options">Options</a></li>
                            <li><a href="//github.com/theroux/clingify">Find on GitHub</a></li>
                        </ul>
                    </nav>
                </header>
                <section id="purpose">
                    <h2 >Purpose</h2>

                    <p>Clingify is a jQuery plugin that allows you to easily create &ldquo;sticky&rdquo; headers and navigation elements. Once you scroll past the targeted element, the plugin will toggle a CSS class that gives the element a fixed position, pinning it to the top of the page.
                </section>
                <section id="features">
                    <h2>Features</h2>
                    <p>There are many other &ldquo;sticky&rdquo; / &ldquo;clingy&rdquo; plugins out there, but here are a few reasons to consider Clingify:</p>
                    <ul class="advantages">
                        <li>No inline CSS. Everything is controlled via classes, so you don't need to understand Javascript to use this plugin. I have included some sensible default CSS, but you are free to edit it.</li>
                        <li>Seriously, though. Inline CSS is a pain in the butt.</li>
                        <li>It is possible to have multiple sticky elements on the same page. Simply pass an additional class via the "extraClass" parameter when initializing the plugin. By manipulating z-index and/or top positions, it's possible to achieve either an overlapping or a stacking effect.</li>
                        <li>The plugin wraps a placeholder div around your target element and matches its height. This keeps your content from shifting when the element becomes fixed position and is removed from the page flow.</li>
                        <li>It also creates a wrapper around your element to give you another hook for CSS position.</li>
                        <li>Optional breakpoint parameter makes it easy to suppress Clingify behavior below a certain screen width for smart phones and tablets.</li>
                        <li>Optional callback functions for when the element is attached or detached.</li>
                        <li>Optional callback function for when the page is resized. This is helpful for fluid widths and responsive design.</li>
                        <li>The window scroll and resize events are throttled to improve performance. The amount of throttle is a configurable option.</li>
                    </ul>
                </section>
                <section id="how-to-use">
                    <h2>How to use</h2>
                    <p>Just initialize the script somewhere after you&rsquo;ve included jQuery (1.7+) and the plugin.</p>
                    <p>Simple:</p>
                    <code class="initialization">
<pre>&lt;script type="text/javascript" src="/js/jquery.js">&lt;/script&gt;
&lt;script type="text/javascript" src="/js/jquery.clingify.js">&lt;/script&gt;                          
&lt;script type="text/javascript"&gt;
$(function() {
    $('.some-selector').clingify();
});
&lt;/script&gt;</pre>
                    </code>
                    <p>Here are the default settings:</p>
                     <code class="initialization">                       
<pre>$('.some-selector').clingify({
    breakpoint: 0,  // in pixels
    extraClass: '',
    throttle: 100,  // in milliseconds
    
    // Callback functions:
    detached: $.noop,
    locked: $.noop,
    resized: $.noop
});</pre>
                    </code>
                    <p>Example with callback functions:</p>
                     <code class="initialization">
<pre>$(function() {
    var $clingifyTarget = $('.some-selector'),
        $parent = $('.some-fluid-width-parent-container'),
        matchWidths = function($elem) {
            $elem.width($parent.width());
        };

    $clingifyTarget.clingify({
        locked : function() {
            matchWidths($clingifyTarget);
        },
        resized : function() {
            matchWidths($clingifyTarget);
        }
    });
});</pre>
                    </code>
                    <p>A version of the above example is being used on this page to keep the width of the clingy nav aligned with the main content. (Try resizing the browser window.)</p>
                </section>
                <section id="options">
                    <h2>Options</h2>
                    <p class="mobileonly">(If table data is cut off, scroll left or right.)</p>
                    <table class="table table-striped options">
                        <tr>
                            <th>Propery</th>
                            <th>Value Types</th>
                            <th>Default</th>
                            <th>Notes</th>
                        </tr>
                        <tr>
                            <td>breakpoint</td>
                            <td>integer</td>
                            <td>0</td>
                            <td>At screen sizes narrower than this number (of pixels), suppress Clingify behaviors.</td>
                        </tr>
                        <tr>
                            <td>extraClass</td>
                            <td>string</td>
                            <td>''</td>
                            <td>Add another class to the wrapper and placeholder divs.<br/>
                             This allows for different CSS rules for different Clingify elements.</td>
                        </tr>
                        <tr>
                            <td>throttle</td>
                            <td>integer</td>
                            <td>100</td>
                            <td>How often, in milliseconds, to check for scroll and resize events</td>
                        </tr>
                        <tr>
                            <td>detached</td>
                            <td>function</td>
                            <td>$.noop</td>
                            <td>Callback fires just before an element is un-Clingified.</td>
                        </tr>
                        <tr>
                            <td>locked</td>
                            <td>function</td>
                            <td>$.noop</td>
                            <td>Callback fires just before an element is Clingified.</td>
                        </tr>
                        <tr>
                            <td>resized</td>
                            <td>function</td>
                            <td>$.noop</td>
                            <td>Callback fires just after window is resized.</td>
                        </tr>

                    </table>
                </section>
                <section id="lorem-ipsum">
                    <h2>Some filler text to force a long, scrolling page</h2>

                    <div class="second-clingy">
                        <span class="message first-message">
                            <span class="star">&star;</span>
                            What if there was a second clingy element on the page? 
                            <span class="star">&star;</span>
                        </span>
                        <span class="message second-message catchadream">
                            &#9733; With some thoughtful CSS you can have both! &#9733;
                        </span>
                    </div>

                    <p>Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo. Quisque sit amet est et sapien ullamcorper pharetra. Vestibulum erat wisi, condimentum sed, commodo vitae, ornare sit amet, wisi. Aenean fermentum, elit eget tincidunt condimentum, eros ipsum rutrum orci, sagittis tempus lacus enim ac dui in turpis pulvinar facilisis. Ut felis.</p>

                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nec odio. Praesent libero. Sed cursus ante dapibus diam. Sed nisi. Nulla quis sem at nibh elementum imperdiet. Duis sagittis ipsum. Praesent mauris. Fusce nec tellus sed augue semper porta. Mauris massa. Vestibulum lacinia arcu eget nulla. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Curabitur sodales ligula in libero. Sed dignissim lacinia nunc. </p>

                    <p>Curabitur tortor. Pellentesque nibh. Aenean quam. In scelerisque sem at dolor. Maecenas mattis. Sed convallis tristique sem. Proin ut ligula vel nunc egestas porttitor. Morbi lectus risus, iaculis vel, suscipit quis, luctus non, massa. Fusce ac turpis quis ligula lacinia aliquet. Mauris ipsum. Nulla metus metus, ullamcorper vel, tincidunt sed, euismod in, nibh. </p>

                    <p>Quisque volutpat condimentum velit. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Nam nec ante. Sed lacinia, urna non tincidunt mattis, tortor neque adipiscing diam, a cursus ipsum ante quis turpis. Nulla facilisi. Ut fringilla. Suspendisse potenti. Nunc feugiat mi a tellus consequat imperdiet. Vestibulum sapien. Proin quam. Etiam ultrices. </p>

                    <p>Suspendisse in justo eu magna luctus suscipit. Sed lectus. Integer euismod lacus luctus magna. Quisque cursus, metus vitae pharetra auctor, sem massa mattis sem, at interdum magna augue eget diam. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Morbi lacinia molestie dui. Praesent blandit dolor. Sed non quam. In vel mi sit amet augue congue elementum. Morbi in ipsum sit amet pede facilisis laoreet. Donec lacus nunc, viverra nec, blandit vel, egestas et, augue. Vestibulum tincidunt malesuada tellus. Ut ultrices ultrices enim. Curabitur sit amet mauris. Morbi in dui quis est pulvinar ullamcorper. </p>

                    <p>Nulla facilisi. Integer lacinia sollicitudin massa. Cras metus. Sed aliquet risus a tortor. Integer id quam. Morbi mi. Quisque nisl felis, venenatis tristique, dignissim in, ultrices sit amet, augue. Proin sodales libero eget ante. Nulla quam. Aenean laoreet. Vestibulum nisi lectus, commodo ac, facilisis ac, ultricies eu, pede. Ut orci risus, accumsan porttitor, cursus quis, aliquet eget, justo. Sed pretium blandit orci. </p>

                    <p>Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo. Quisque sit amet est et sapien ullamcorper pharetra. Vestibulum erat wisi, condimentum sed, commodo vitae, ornare sit amet, wisi. Aenean fermentum, elit eget tincidunt condimentum, eros ipsum rutrum orci, sagittis tempus lacus enim ac dui in turpis pulvinar facilisis. Ut felis.</p>

                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nec odio. Praesent libero. Sed cursus ante dapibus diam. Sed nisi. Nulla quis sem at nibh elementum imperdiet. Duis sagittis ipsum. Praesent mauris. Fusce nec tellus sed augue semper porta. Mauris massa. Vestibulum lacinia arcu eget nulla. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Curabitur sodales ligula in libero. Sed dignissim lacinia nunc. </p>

                    <p>Curabitur tortor. Pellentesque nibh. Aenean quam. In scelerisque sem at dolor. Maecenas mattis. Sed convallis tristique sem. Proin ut ligula vel nunc egestas porttitor. Morbi lectus risus, iaculis vel, suscipit quis, luctus non, massa. Fusce ac turpis quis ligula lacinia aliquet. Mauris ipsum. Nulla metus metus, ullamcorper vel, tincidunt sed, euismod in, nibh. </p>

                    <p>Quisque volutpat condimentum velit. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Nam nec ante. Sed lacinia, urna non tincidunt mattis, tortor neque adipiscing diam, a cursus ipsum ante quis turpis. Nulla facilisi. Ut fringilla. Suspendisse potenti. Nunc feugiat mi a tellus consequat imperdiet. Vestibulum sapien. Proin quam. Etiam ultrices. </p>

                    <p>Suspendisse in justo eu magna luctus suscipit. Sed lectus. Integer euismod lacus luctus magna. Quisque cursus, metus vitae pharetra auctor, sem massa mattis sem, at interdum magna augue eget diam. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Morbi lacinia molestie dui. Praesent blandit dolor. Sed non quam. In vel mi sit amet augue congue elementum. Morbi in ipsum sit amet pede facilisis laoreet. Donec lacus nunc, viverra nec, blandit vel, egestas et, augue. Vestibulum tincidunt malesuada tellus. Ut ultrices ultrices enim. Curabitur sit amet mauris. Morbi in dui quis est pulvinar ullamcorper. </p>

                    <p>Nulla facilisi. Integer lacinia sollicitudin massa. Cras metus. Sed aliquet risus a tortor. Integer id quam. Morbi mi. Quisque nisl felis, venenatis tristique, dignissim in, ultrices sit amet, augue. Proin sodales libero eget ante. Nulla quam. Aenean laoreet. Vestibulum nisi lectus, commodo ac, facilisis ac, ultricies eu, pede. Ut orci risus, accumsan porttitor, cursus quis, aliquet eget, justo. Sed pretium blandit orci. </p>
                </section>
            </div>
        </div>

        <script>
            var _gaq=[['_setAccount','UA-********-1'],['_trackPageview']];
            (function(d,t){var g=d.createElement(t),s=d.getElementsByTagName(t)[0];
            g.src='//www.google-analytics.com/ga.js';
            s.parentNode.insertBefore(g,s)}(document,'script'));
        </script>

        <!-- build:js scripts/main.js -->
        <script src="bower_components/jquery/jquery.js"></script>
        <script src="scripts/jquery.clingify.js"></script>
        <script src="scripts/demo.js"></script>
        <!-- endbuild -->

</body>
</html>
