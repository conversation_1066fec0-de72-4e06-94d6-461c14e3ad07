{"name": "demo", "version": "0.0.0", "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-copy": "~0.4.1", "grunt-contrib-concat": "~0.1.3", "grunt-contrib-coffee": "~0.6.5", "grunt-contrib-uglify": "~0.2.0", "grunt-contrib-compass": "~0.2.0", "grunt-contrib-jshint": "~0.4.1", "grunt-contrib-cssmin": "~0.6.0", "grunt-contrib-connect": "~0.2.0", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-htmlmin": "~0.1.3", "grunt-contrib-imagemin": "~0.1.3", "grunt-contrib-watch": "~0.4.0", "grunt-rev": "~0.1.0", "grunt-autoprefixer": "~0.1.20130516", "grunt-usemin": "~0.1.10", "grunt-mocha": "~0.3.0", "grunt-open": "~0.2.0", "grunt-svgmin": "~0.1.0", "grunt-concurrent": "~0.1.0", "matchdep": "~0.1.1", "connect-livereload": "~0.2.0"}, "engines": {"node": ">=0.8.0"}}