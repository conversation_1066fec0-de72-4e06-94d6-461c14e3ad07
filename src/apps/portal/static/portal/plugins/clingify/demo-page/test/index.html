<!doctype html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Mocha Spec Runner</title>
    <link rel="stylesheet" href="lib/mocha/mocha.css">
</head>
<body>
    <div id="mocha"></div>
    <script src="lib/mocha/mocha.js"></script>
    <script>mocha.setup('bdd')</script>
    <!-- assertion framework -->
    <script src="lib/chai.js"></script>
    <script>var expect = chai.expect</script>

    <!-- include source files here... -->

    <!-- include spec files here... -->
    <script src="spec/test.js"></script>

    <script>mocha.run()</script>
</body>
</html>
