<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<svg id="svg548" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="512" width="512" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3188">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <g id="flag" fill-rule="evenodd" transform="matrix(.75530 0 0 1.1329 -28.678 -260.35)" stroke-width="1pt">
  <rect id="rect552" height="151.05" width="677.88" y="229.8" x="37.969" fill="#00c4ff"/>
  <rect id="rect554" height="151.05" width="677.88" y="380.23" x="37.969" fill="#fff"/>
  <rect id="rect555" height="151.05" width="677.88" y="530.67" x="37.969" fill="#00c4ff"/>
 </g>
 <g id="g904" transform="matrix(1.2386 0 0 1.2386 -158.39 -42.014)">
  <path id="path598" d="m382.49 221.33c0 14.564-11.864 26.37-26.499 26.37s-26.499-11.806-26.499-26.37 11.864-26.37 26.499-26.37 26.499 11.806 26.499 26.37z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(1.0193 0 0 1.0038 -26.198 18.075)" stroke="#000" stroke-width=".625" fill="#ffd600"/>
  <path id="path561" d="m364.43 195.28c-4.34-1.049-8.785 0.422-10.185 0.318-1.925 0-6.79-1.68-10.185 0" stroke-opacity=".38662" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".5" fill="none"/>
  <path id="path562" d="m338.71 200.49c4.305-3.01 9.115 1.086 10.394 0.315 3.492-2.294 6.736-1.868 10.08 0.21 2.155 1.272 5.914-3.71 10.289 0.315" stroke-opacity=".38662" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".5" fill="none"/>
  <path id="path564" d="m333.88 205.63c2.275-1.855 9.694-1.925 17.324 2.414 1.155-0.28 1.89-1.084 0.945-2.204-5.74-1.995-12.424-4.515-18.584-2.625-1.68 1.19-1.26 1.96 0.315 2.415z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".5" fill="#efc000"/>
  <path id="path565" d="m333.88 205.63c2.275-1.855 9.694-1.925 17.324 2.414 1.155-0.28 1.89-1.084 0.945-2.204-5.74-1.995-12.424-4.515-18.584-2.625-1.68 1.19-1.26 1.96 0.315 2.415z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.78705 0 0 .76270 614.99 72.384)" stroke="#000" stroke-width=".5" fill="#efc000"/>
  <path id="path569" stroke-linejoin="round" d="m330.84 211.83c7.525-4.83 17.464-2.31 21.629 0.315-6.09-1.155-6.195-1.68-10.605-1.785-3.115 0.106-7.699-0.21-11.024 1.47z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".625" fill="#f0bf00"/>
  <path id="path570" d="m348.06 211.3c-3.675 7.665-10.079 7.77-14.594-0.42" stroke-opacity=".38662" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path571" d="m345.54 212.88c0 0.92779-1.6453 1.6799-3.6748 1.6799s-3.6748-0.75212-3.6748-1.6799c0-0.92779 1.6453-1.6799 3.6748-1.6799s3.6748 0.75212 3.6748 1.6799z" fill-rule="evenodd" fill-opacity=".36803" transform="matrix(.85295 .12338 -.92423 1.011 231.88 -22.52)"/>
  <path id="path572" d="m344.07 212.56c0 0.23194-0.32906 0.41997-0.73497 0.41997s-0.73497-0.18803-0.73497-0.41997c0-0.23195 0.32906-0.41998 0.73497-0.41998s0.73497 0.18803 0.73497 0.41998z" fill-rule="evenodd" transform="matrix(1.0476 -.34283 .38138 .94175 -115.92 151.91)" fill="#ffd700"/>
  <path id="path573" d="m349.18 224.5c-4.239 7.127 1.537 2.1 2.475 4.164 1.65 1.913 3.301 1.462 4.276 0 0.976-1.651 7.127 3.113 2.926-3.938" stroke-opacity=".38662" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path574" stroke-linejoin="round" d="m341.64 236.31c3.638-0.413 9.753-3.188 11.929-0.9 1.875-2.063 8.477 0.6 12.715 0.9-3.076 1.875-9.302 0.6-12.265 2.588-2.889-1.763-9.266-0.15-12.379-2.588z" stroke-opacity=".38662" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-linecap="round" stroke-width=".625" fill="none"/>
  <path id="path575" stroke-linejoin="round" d="m347.5 239.58c5.514 2.251 6.752 1.913 12.716 0.225-1.238 3.264-4.398 3.951-6.19 3.826-1.857-0.121-4.388 0.113-6.526-4.051z" stroke-opacity=".38662" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path599" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path600" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.78705 0 0 .76270 64.243 72.907)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path605" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" transform="matrix(-.56256 -.53340 -.55044 .54515 657.14 309.12)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path607" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" transform="matrix(.55074 .54486 -.56226 .53369 265.32 -69.993)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path609" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" transform="matrix(-.0045306 -.76268 -.78704 .0043904 511.37 509.69)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path612" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" transform="matrix(-.72199 -.30364 -.31333 .69965 656.38 192.05)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path613" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" transform="matrix(-.56065 -.53529 -.55238 .54330 651.5 306.4)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path616" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" transform="matrix(-.30753 -.70206 -.72448 .29802 602.06 417.74)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path617" d="m349.18 224.5c-4.239 7.127 1.537 2.1 2.475 4.164 1.65 1.913 3.301 1.462 4.276 0 0.976-1.651 7.127 3.113 2.926-3.938" stroke-opacity=".081784" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path618" stroke-linejoin="round" d="m341.64 236.31c3.638-0.413 9.753-3.188 11.929-0.9 1.875-2.063 8.477 0.6 12.715 0.9-3.076 1.875-9.302 0.6-12.265 2.588-2.889-1.763-9.266-0.15-12.379-2.588z" stroke-opacity=".081784" fill-rule="evenodd" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-linecap="round" stroke-width=".625" fill="#f0bf00"/>
  <path id="path619" stroke-linejoin="round" d="m347.5 239.58c5.514 2.251 6.752 1.913 12.716 0.225-1.238 3.264-4.398 3.951-6.19 3.826-1.857-0.121-4.388 0.113-6.526-4.051z" stroke-opacity=".081784" fill-rule="evenodd" transform="matrix(.78705 0 0 .76270 57.458 72.384)" stroke="#000" stroke-width=".625" fill="#f0bf00"/>
  <path id="path630" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.56256 -.53340 -.55044 .54515 657.14 309.12)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path632" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(.55074 .54486 -.56226 .53369 265.32 -69.993)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path634" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.0045306 -.76268 -.78704 .0043904 511.37 509.69)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path637" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.72199 -.30364 -.31333 .69965 656.38 192.05)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path638" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.56065 -.53529 -.55238 .54330 651.5 306.4)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path641" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.30753 -.70206 -.72448 .29802 602.06 417.74)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path602" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(0 -.76270 .78705 0 164.06 504.41)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path604" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.78705 0 0 .76270 616.04 72.778)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path606" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.56256 -.53340 .55044 .54515 16.584 309.51)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path608" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.55074 .54486 .56226 .53369 408.41 -69.796)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path610" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.0045306 -.76268 .78704 .0043904 162.13 509.69)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path611" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.72199 -.30364 .31333 .69965 17.304 192.45)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path614" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.56065 -.53529 .55238 .54330 21.69 306.4)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path615" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.30753 -.70206 .72448 .29802 71.779 417.74)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path631" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(.56256 -.53340 .55044 .54515 16.584 309.51)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path633" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.55074 .54486 .56226 .53369 408.41 -69.796)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path635" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(.0045306 -.76268 .78704 .0043904 162.13 509.69)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path636" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(.72199 -.30364 .31333 .69965 17.304 192.45)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path639" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(.56065 -.53529 .55238 .54330 21.69 306.4)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path640" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(.30753 -.70206 .72448 .29802 71.779 417.74)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path686" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(0 .76270 -.78705 0 509.35 -23.965)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path687" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.78705 0 0 -.76270 57.38 407.66)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path688" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.56256 .53340 -.55044 -.54515 656.83 170.93)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path689" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.55074 -.54486 -.56226 -.53369 265.01 550.24)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path690" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.0045306 .76268 -.78704 -.0043904 511.29 -29.254)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path691" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.72199 .30364 -.31333 -.69965 656.11 288)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path692" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.56065 .53529 -.55238 -.54330 651.73 174.04)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path693" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.30753 .70206 -.72448 -.29802 601.64 62.696)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path694" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.56256 .53340 -.55044 -.54515 656.83 170.93)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path695" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(.55074 -.54486 -.56226 -.53369 265.01 550.24)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path696" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.0045306 .76268 -.78704 -.0043904 511.29 -29.254)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path697" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.72199 .30364 -.31333 -.69965 656.11 288)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path698" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.56065 .53529 -.55238 -.54330 651.73 174.04)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path699" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.30753 .70206 -.72448 -.29802 601.64 62.696)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path701" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.78705 0 0 -.76270 609.13 407.52)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path702" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(0 .76270 .78705 0 163.72 -30.464)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path703" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.55044 -.54515 .56256 -.53340 408.01 550.44)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path704" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(.56226 .53369 .55074 -.54486 16.589 170.74)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path705" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.78704 -.0043904 .0045306 -.76268 614.58 409.4)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path706" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.31333 -.69965 .72199 -.30364 287.2 549.74)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path707" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.55238 -.54330 .56065 -.53529 404.79 545.49)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path708" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.72448 -.29802 .30753 -.70206 519.7 496.95)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path709" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.55044 -.54515 .56256 -.53340 408.01 550.44)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path710" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(.56226 .53369 .55074 -.54486 16.589 170.74)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path711" stroke-linejoin="round" d="m343.17 258.06c-3.977 10.41 0.379 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.821 24.589 4.931 0.228-0.852-7.376 9.012-21.455 4.575-6.595-1.196-10.769 3.625-23.707 0.449-1.107-6.622-2.031-7.249-0.391z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.78704 -.0043904 .0045306 -.76268 614.58 409.4)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path712" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.31333 -.69965 .72199 -.30364 287.2 549.74)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path713" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.55238 -.54330 .56065 -.53529 404.79 545.49)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path714" stroke-linejoin="round" d="m342.97 258.16c-1.528 11.683-0.62 47.577 3.037 46.141 4.049 1.305 4.583-35.163 4.212-45.946-0.052-1.194-7.036-1.938-7.249-0.195z" stroke-opacity=".14870" fill-rule="evenodd" transform="matrix(-.72448 -.29802 .30753 -.70206 519.7 496.95)" stroke="#000" stroke-width=".625" fill="#ffd700"/>
  <path id="path555" d="m105.48 235.29" stroke-opacity=".078067" transform="matrix(.78705 0 0 .76270 57.865 72.976)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path577" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(.78705 0 0 .76270 57.865 72.976)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path578" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(.78705 0 0 .76270 57.245 74.52)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path579" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(.87138 0 0 .76270 29.308 75.979)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path580" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(.87137 0 0 .76270 28.514 77.353)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path581" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(1.04 0 0 .76270 -27.004 78.812)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path582" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(1.1524 0 0 .76270 -63.806 80.271)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path583" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(1.0962 0 0 .76270 -45.89 81.901)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path584" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(1.0681 0 0 .76270 -36.489 83.532)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path585" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(1.0962 0 0 .76270 -45.713 85.077)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path586" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(1.3773 0 0 .76270 -137.06 86.879)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path587" d="m328.14 202.55h-3.151" stroke-opacity="0.171" transform="matrix(1.1805 0 0 .76270 -72.849 88.939)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path588" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -72.76 90.398)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path589" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -72.14 92.372)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path590" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -72.052 94.089)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path591" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -70.989 95.977)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path592" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -69.926 97.522)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path593" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -68.952 99.324)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path594" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -67.8 101.04)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path595" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -67.003 102.67)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path596" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -65.321 104.65)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path597" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(.75892 0 0 .76270 73.738 106.45)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path620" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -72.76 90.398)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path621" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -72.14 92.372)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path622" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -72.052 94.089)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path623" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -70.989 95.977)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path624" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -69.926 97.522)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path625" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -68.952 99.324)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path626" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -67.8 101.04)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path627" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -67.003 102.67)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path628" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(1.1805 0 0 .76270 -65.321 104.65)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path629" d="m328.14 202.55h-3.151" stroke-opacity=".078067" transform="matrix(.75892 0 0 .76270 74.123 106.3)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path749" d="m105.48 235.29" stroke-opacity=".078067" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-.78705 0 0 .76270 615.44 72.581)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path750" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-.78705 0 0 .76270 615.44 72.581)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path751" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-.78705 0 0 .76270 616.06 74.126)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path752" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-.87138 0 0 .76270 643.99 75.585)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path753" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-.87137 0 0 .76270 644.79 76.958)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path754" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.04 0 0 .76270 700.31 78.417)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path755" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1524 0 0 .76270 737.11 79.876)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path756" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.0962 0 0 .76270 719.19 81.507)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path757" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.0681 0 0 .76270 709.79 83.138)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path758" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.0962 0 0 .76270 719.02 84.683)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path759" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.3773 0 0 .76270 810.36 86.485)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path760" d="m328.14 202.55h-3.151" stroke-opacity=".13383" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 746.15 88.545)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path761" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 746.06 90.004)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path762" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 745.44 91.978)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path763" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 745.35 93.694)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path764" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 744.29 95.583)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path765" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 743.23 97.128)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path766" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 742.25 98.93)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path767" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 741.1 100.65)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path768" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 740.31 102.28)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path769" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-1.1805 0 0 .76270 738.62 104.25)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path770" d="m328.14 202.55h-3.151" stroke-opacity=".063197" fill-rule="evenodd" fill-opacity=".86667" transform="matrix(-.75892 0 0 .76270 599.56 106.05)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path id="path771" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-1.1805 0 0 .76270 746.06 90.004)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path772" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-1.1805 0 0 .76270 745.44 91.978)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path773" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-1.1805 0 0 .76270 745.35 93.694)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path774" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-1.1805 0 0 .76270 744.29 95.583)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path775" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-1.1805 0 0 .76270 743.23 97.128)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path776" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-1.1805 0 0 .76270 742.25 98.93)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path777" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-1.1805 0 0 .76270 741.1 100.65)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path778" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-1.1805 0 0 .76270 740.31 102.28)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path779" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-1.1805 0 0 .76270 738.62 104.25)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path780" d="m328.14 202.55h-3.151" stroke-opacity=".063197" transform="matrix(-.75892 0 0 .76270 599.56 106.05)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path id="path781" stroke-linejoin="round" d="m330.84 211.83c7.525-4.83 17.464-2.31 21.629 0.315-6.09-1.155-6.195-1.68-10.605-1.785-3.115 0.106-7.699-0.21-11.024 1.47z" stroke-opacity=".38662" fill-rule="evenodd" transform="matrix(-.78705 0 0 .76270 615.8 72.187)" stroke="#000" stroke-width=".625" fill="#f0bf00"/>
  <path id="path782" d="m348.06 211.3c-3.675 7.665-10.079 7.77-14.594-0.42" stroke-opacity=".38662" transform="matrix(.78705 0 0 .76270 78.009 71.99)" stroke="#000" stroke-width=".625" fill="none"/>
  <path id="path783" d="m345.54 212.88c0 0.92779-1.6453 1.6799-3.6748 1.6799s-3.6748-0.75212-3.6748-1.6799c0-0.92779 1.6453-1.6799 3.6748-1.6799s3.6748 0.75212 3.6748 1.6799z" fill-rule="evenodd" fill-opacity=".36803" transform="matrix(.88114 .11814 -.95479 .96802 249.18 -11.902)"/>
  <path id="path784" d="m344.07 212.56c0 0.23194-0.32906 0.41997-0.73497 0.41997s-0.73497-0.18803-0.73497-0.41997c0-0.23195 0.32906-0.41998 0.73497-0.41998s0.73497 0.18803 0.73497 0.41998z" fill-rule="evenodd" transform="matrix(1.0476 -.34283 .38138 .94175 -95.366 151.52)" fill="#ffd700"/>
 </g>
</svg>
