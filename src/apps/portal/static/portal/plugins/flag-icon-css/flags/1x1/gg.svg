<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg id="svg2" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="512" width="512" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-18 -12 36 24" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata19">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs17">
  <clipPath id="clipPath3845" clipPathUnits="userSpaceOnUse">
   <rect id="rect3847" fill-opacity="0.67" height="36" width="36" y="-18" x="-18"/>
  </clipPath>
 </defs>
 <g id="flag">
  <rect id="rect3003" height="36" width="36" y="-18" x="-18" fill="#fff"/>
  <g id="g3814" clip-path="url(#clipPath3845)">
   <rect id="rect4" height="24" width="36" y="-12" x="-18" fill="#fff"/>
   <path id="path6" d="m0-18v36m-18-18h36" stroke="#e8112d" stroke-width="6" fill="none"/>
   <path id="arm" fill="#f9dd16" d="m-9 2 1-1h9v-2h-9l-1-1z"/>
   <use id="use9" xlink:href="#arm" transform="rotate(90)" height="24" width="36" y="0" x="0"/>
   <use id="use11" xlink:href="#arm" transform="rotate(-90)" height="24" width="36" y="0" x="0"/>
   <use id="use13" xlink:href="#arm" transform="scale(-1)" height="24" width="36" y="0" x="0"/>
  </g>
 </g>
</svg>
