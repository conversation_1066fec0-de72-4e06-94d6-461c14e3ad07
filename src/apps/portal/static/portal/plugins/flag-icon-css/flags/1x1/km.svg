<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<!--

<rdf:RDF xmlns="http://web.resource.org/cc/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
<Work rdf:about="">
   <dc:title>SVG graphic of Comoros' Flag</dc:title>
   <dc:rights><Agent>
      <dc:title><PERSON></dc:title>
   </Agent></dc:rights>
   <license rdf:resource="http://web.resource.org/cc/PublicDomain" />
</Work>

<License rdf:about="http://web.resource.org/cc/PublicDomain">
   <permits rdf:resource="http://web.resource.org/cc/Reproduction" />
   <permits rdf:resource="http://web.resource.org/cc/Distribution" />
   <permits rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
</License>

</rdf:RDF>

-->
<svg id="svg660" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="512" width="512" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata4850">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs662">
  <clipPath id="clipPath4980" clipPathUnits="userSpaceOnUse">
   <rect id="rect4982" fill-opacity="0.67" height="416.25" width="416.25" y="-.000013485" x="0"/>
  </clipPath>
 </defs>
 <g id="flag" clip-path="url(#clipPath4980)" fill-rule="evenodd" transform="matrix(1.23 0 0 1.23 0 .000016587)">
  <rect id="rect665" height="104.06" width="625" y="-.000015" x="0" stroke-width="1pt" fill="#ff0"/>
  <rect id="rect668" height="104.06" width="625" y="104.06" x="0" stroke-width="1pt" fill="#fff"/>
  <rect id="rect667" height="104.06" width="625" y="208.12" x="0" stroke-width="1pt" fill="#be0027"/>
  <rect id="rect666" height="104.06" width="625" y="312.19" x="0" stroke-width="1pt" fill="#3b5aa3"/>
  <path id="path664" d="m0 0.75619v415.49l310.45-207.16-310.45-208.33z" transform="matrix(1 0 0 1.0018 0 -.75756)" fill="#239e46"/>
  <path id="path673" d="m127.81 114.98c-69.183-3.51-100.7 51.6-100.63 94.2-0.159 50.38 47.582 91.93 91.71 89.43-23.794-11.26-52.85-42.82-53.117-89.64-0.2436-42.423 23.977-79.31 62.038-93.99z" fill="#fff"/>
  <polygon id="polygon674" d="m 126.77341,160.21338 -9.75793,-7.62986 -11.8172,3.71287 4.24106,-11.63809 -7.18286,-10.0915 12.37904,0.43712 7.37795,-9.94975 3.40961,11.90825 11.74269,3.94221 -10.27179,6.92258 z" points="126.77 160.21 117.02 152.58 105.2 156.3 109.44 144.66 102.26 134.57 114.64 135 122.01 125.05 125.42 136.96 137.17 140.9 126.89 147.83" stroke-width="1pt" fill="#fff"/>
  <polygon id="polygon675" d="m 126.77341,160.21338 -9.75793,-7.62986 -11.8172,3.71287 4.24106,-11.63809 -7.18286,-10.0915 12.37904,0.43712 7.37795,-9.94975 3.40961,11.90825 11.74269,3.94221 -10.27179,6.92258 z" points="126.77 160.21 117.02 152.58 105.2 156.3 109.44 144.66 102.26 134.57 114.64 135 122.01 125.05 125.42 136.96 137.17 140.9 126.89 147.83" transform="translate(-.20749 42.327)" stroke-width="1pt" fill="#fff"/>
  <polygon id="polygon676" d="m 126.77341,160.21338 -9.75793,-7.62986 -11.8172,3.71287 4.24106,-11.63809 -7.18286,-10.0915 12.37904,0.43712 7.37795,-9.94975 3.40961,11.90825 11.74269,3.94221 -10.27179,6.92258 z" points="126.77 160.21 117.02 152.58 105.2 156.3 109.44 144.66 102.26 134.57 114.64 135 122.01 125.05 125.42 136.96 137.17 140.9 126.89 147.83" transform="translate(-.0000096112 85.069)" stroke-width="1pt" fill="#fff"/>
  <polygon id="polygon677" d="m 126.77341,160.21338 -9.75793,-7.62986 -11.8172,3.71287 4.24106,-11.63809 -7.18286,-10.0915 12.37904,0.43712 7.37795,-9.94975 3.40961,11.90825 11.74269,3.94221 -10.27179,6.92258 z" points="126.77 160.21 117.02 152.58 105.2 156.3 109.44 144.66 102.26 134.57 114.64 135 122.01 125.05 125.42 136.96 137.17 140.9 126.89 147.83" transform="translate(-.20749 128.23)" stroke-width="1pt" fill="#fff"/>
 </g>
</svg>
