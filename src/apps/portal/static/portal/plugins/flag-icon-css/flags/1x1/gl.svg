<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<!-- /Creative Commons Public Domain -->
<!--
<rdf:RDF xmlns="http://web.resource.org/cc/"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
<Work rdf:about="">
    <dc:title>New Zealand, Australia, United Kingdom, United States, 
Bosnia and Herzegovina, Azerbaijan, Armenia, Bahamas, Belgium, Benin, 
Bulgaria, Estonia, Finland, Gabon, Gambia, Germany, Greece, Greenland, 
Guinea, Honduras, Israel, Jamaica, Jordan, and Romania Flags</dc:title>
    <dc:rights><Agent>
       <dc:title><PERSON></dc:title>
    </Agent></dc:rights>
    <license rdf:resource="http://web.resource.org/cc/PublicDomain" />
</Work>

<License rdf:about="http://web.resource.org/cc/PublicDomain">
    <permits rdf:resource="http://web.resource.org/cc/Reproduction" />
    <permits rdf:resource="http://web.resource.org/cc/Distribution" />
    <permits rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
</License>
</rdf:RDF>
-->
<svg id="svg378" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="512" width="512" version="1" y="0" x="0" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3289">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs380">
  <clipPath id="clipPath3033" clipPathUnits="userSpaceOnUse">
   <rect id="rect3035" fill-opacity="0.67" height="512" width="512" y="0" x="0"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath3033)">
  <rect id="rect149" transform="scale(-1)" height="512" width="766.65" y="-512" x="-661.1" fill="#fff"/>
  <rect id="rect148" transform="scale(-1)" height="255.55" width="766.65" y="-512" x="-661.1" stroke-width="1pt" fill="#df0000"/>
  <path id="path567" d="m628.27 506.38a217.36 217.36 0 1 1 -434.72 -0.00008" transform="matrix(.72122 0 0 -.72122 -105.55 621.06)" stroke-width="1pt" fill="#df0000"/>
  <path id="path568" d="m628.27 506.38a217.36 217.36 0 1 1 -434.72 -0.00008" transform="matrix(.72122 0 0 .72122 -105.55 -109.46)" stroke-width="1pt" fill="#fff"/>
 </g>
</svg>
