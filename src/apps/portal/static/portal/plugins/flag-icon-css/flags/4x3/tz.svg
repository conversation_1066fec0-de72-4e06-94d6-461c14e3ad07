<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<!-- /Creative Commons Public Domain -->
<!--
<rdf:RDF xmlns="http://web.resource.org/cc/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
<Work rdf:about="">
   <dc:title>SVG graphic of Tanzanian flag</dc:title>
   <dc:rights><Agent>
      <dc:title><PERSON></dc:title>
   </Agent></dc:rights>
   <license rdf:resource="http://web.resource.org/cc/PublicDomain" />
</Work>

<License rdf:about="http://web.resource.org/cc/PublicDomain">
   <permits rdf:resource="http://web.resource.org/cc/Reproduction" />
   <permits rdf:resource="http://web.resource.org/cc/Distribution" />
   <permits rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
</License>

</rdf:RDF>
-->
<svg id="svg548" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1" y="0" x="0" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata4166">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs550">
  <clipPath id="clipPath3665" clipPathUnits="userSpaceOnUse">
   <rect id="rect3667" fill-opacity="0.67" height="120" width="160" y="8.125e-8" x="10"/>
  </clipPath>
 </defs>
 <g id="flag" clip-path="url(#clipPath3665)" fill-rule="evenodd" transform="matrix(4 0 0 4 -40 -3.25e-7)" stroke-width="1pt">
  <rect id="rect583" height="120" width="180" y="0" x="0" fill="#09f"/>
  <path id="path584" d="m0 0h180l-180 120v-120z" fill="#090"/>
  <path id="path586" d="m0 120h40l140-95v-25h-40l-140 95v25z"/>
  <path id="path587" d="m0 91.456 137.18-91.456h13.52l-150.7 100.47v-9.014z" fill="#ff0"/>
  <path id="path588" d="m-0.7049 95.47 150.7-100.47v9.014l-137.18 91.456h-13.521z" transform="translate(30,24.53)" fill="#ff0"/>
 </g>
</svg>
