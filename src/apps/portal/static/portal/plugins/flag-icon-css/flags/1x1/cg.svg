<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with <PERSON><PERSON><PERSON><PERSON> ("http://www.sodipodi.com/") -->
<svg id="svg548" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="512" width="512" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3007">
  <rdf:RDF>
   <cc:Work rdf:about="">
    <dc:format>image/svg+xml</dc:format>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs550">
  <clipPath id="clipPath3031" clipPathUnits="userSpaceOnUse">
   <rect id="rect3033" fill-opacity="0.67" height="496.06" width="496.06" y="-.000080621" x="115.74"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath3031)" transform="matrix(1.0321 0 0 1.0321 -119.46 .000083212)" stroke-width="1pt">
  <rect id="rect555" height="496.06" width="744.09" y="-0.000059" x="0" fill="#ff0"/>
  <path id="path554" d="m0-0.000076294v496.06l496.06-496.06h-496.06z" fill="#00ca00"/>
  <path id="path553" d="m248.03 496.06h496.06v-496.06l-496.06 496.06z" fill="#f00"/>
 </g>
</svg>
