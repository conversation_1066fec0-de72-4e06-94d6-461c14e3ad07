## Submitting issues

### Reduced test case required

All bug reports and problem issues require a reduced test case. See [CSS Tricks - Reduced Test Cases](http://css-tricks.com/reduced-test-cases/) on why they _"are the absolute, ... number one way to troubleshoot bugs."_

+ A reduced test case is an isolated example that demonstrates the bug or issue.
+ It contains the bare minimum HTML, CSS, and JavaScript required to demonstrate the bug. No extra functionality or styling.
+ A link to your site is **not** a reduced test case.
+ A [CodePen](http://codepen.io) is preferred so we can help you fix an error.
+ Until you provide a reduced test case, your issue will be closed.

This guideline may seem a little harsh, but it helps dramatically. Reduced test cases help you identify the issue at hand and understand your own code. On our side, they greatly reduce the amount of time spent resolving the issue.