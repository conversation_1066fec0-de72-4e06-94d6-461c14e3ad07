<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Backstretch Test Suite</title>
  <!-- Load local jQuery. This can be overridden with a ?jquery=___ param. -->
  <script src="../libs/jquery-loader.js"></script>
  <!-- Load local QUnit (grunt requires v1.0.0 or newer). -->
  <link rel="stylesheet" href="../libs/qunit/qunit.css" media="screen">
  <script src="../libs/qunit/qunit.js"></script>
  <!-- Load local lib and tests. -->
  <script src="../src/jquery.backstretch.js"></script>
  <script src="backstretch_test.js"></script>
  <!-- Removing access to jQuery and $. But it'll still be available as _$, if
       you REALLY want to mess around with j<PERSON><PERSON><PERSON> in the console. REMEMBER WE
       ARE TESTING YOUR PLUGIN HERE -->
  <script>window._$ = jQuery.noConflict(true);</script>
</head>
<body>
  <h1 id="qunit-header">Backstretch Test Suite</h1>
  <h2 id="qunit-banner"></h2>
  <div id="qunit-testrunner-toolbar"></div>
  <h2 id="qunit-userAgent"></h2>
  <ol id="qunit-tests"></ol>
  <div id="qunit-fixture">
    <span>lame test markup</span>
    <span>normal test markup</span>
    <span>awesome test markup</span>
  </div>
</body>
</html>
