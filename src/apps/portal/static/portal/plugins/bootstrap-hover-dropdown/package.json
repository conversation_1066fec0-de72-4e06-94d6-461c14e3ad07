{"name": "bootstrap-hover-dropdown", "version": "2.1.3", "description": "An unofficial Bootstrap plugin to enable Bootstrap dropdowns to activate on hover and provide a nice user experience.", "main": "bootstrap-hover-dropdown.js", "dependencies": {"streamqueue": "^0.1.1"}, "devDependencies": {"gulp": "^3.8.10", "gulp-bump": "^0.1.13", "gulp-filter": "^2.0.0", "gulp-git": "^0.5.6", "gulp-rename": "^1.2.0", "gulp-tag-version": "^1.2.1", "gulp-uglify": "^1.1.0", "streamqueue": "^0.1.1"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/CWSpear/bootstrap-hover-dropdown.git"}, "keywords": ["twitter", "bootstrap", "hover", "dropdowns"], "author": {"name": "<PERSON>", "url": "https://cameronspear.com"}, "license": "MIT", "bugs": {"url": "https://github.com/CWSpear/bootstrap-hover-dropdown/issues"}, "homepage": "https://github.com/CWSpear/bootstrap-hover-dropdown"}