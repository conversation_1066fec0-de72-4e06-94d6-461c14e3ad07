@bootstrap-switch-base: bootstrap-switch;

.@{bootstrap-switch-base} {
  display: inline-block;
  direction: ltr;
  cursor: pointer;
  .border-radius(5px);
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  position: relative;
  text-align: left;
  overflow: hidden;
  line-height: 8px;
  z-index: 0;
  .user-select(none);
  vertical-align: middle;
  .transition(~"border-color ease-in-out .15s, box-shadow ease-in-out .15s");

  .@{bootstrap-switch-base}-container {
    display: inline-block;
    top: 0;
    .border-radius(4px);
    .translate3d(0, 0, 0);
  }

  .@{bootstrap-switch-base}-handle-on,
  .@{bootstrap-switch-base}-handle-off,
  .@{bootstrap-switch-base}-label {
    .box-sizing(border-box);
    cursor: pointer;
    display: inline-block !important;
    height: 100%;
    padding-top: 4px;
    padding-bottom: 4px;
    padding-left: 8px;
    padding-right: 8px;
    font-size: 14px;
    line-height: 20px;
  }

  .@{bootstrap-switch-base}-handle-on,
  .@{bootstrap-switch-base}-handle-off {
    text-align: center;
    z-index: 1;

    &.@{bootstrap-switch-base}-primary {
      .buttonBackground(@btnPrimaryBackgroundHighlight, @btnPrimaryBackground);
    }

    &.@{bootstrap-switch-base}-info {
      .buttonBackground(@btnInfoBackgroundHighlight, @btnInfoBackground);
    }

    &.@{bootstrap-switch-base}-success {
      .buttonBackground(@btnSuccessBackgroundHighlight, @btnSuccessBackground);
    }

    &.@{bootstrap-switch-base}-warning {
      .buttonBackground(@btnWarningBackgroundHighlight, @btnWarningBackground);
    }

    &.@{bootstrap-switch-base}-danger {
      .buttonBackground(@btnDangerBackgroundHighlight, @btnDangerBackground);
    }

    &.@{bootstrap-switch-base}-default {
      .buttonBackground(@btnBackgroundHighlight, @btnBackground, @grayDark, 0 1px 1px rgba(255,255,255,.75));
    }
  }

  .@{bootstrap-switch-base}-label {
    text-align: center;
    margin-top: -1px;
    margin-bottom: -1px;
    z-index: 100;
    border-left: 1px solid @btnBorder;
    border-right: 1px solid @btnBorder;
    .buttonBackground(@btnBackground, @btnBackgroundHighlight, @grayDark);
  }

  .@{bootstrap-switch-base}-handle-on {
    .border-left-radius(4px);
  }

  .@{bootstrap-switch-base}-handle-off {
    .border-right-radius(4px);
  }

  input[type='radio'],
  input[type='checkbox'] {
    position: absolute !important;
    top: 0;
    left: 0;
    .opacity(0);
    z-index: -1;

    &.form-control {
      height: auto;
    }
  }

  &.@{bootstrap-switch-base}-mini {
    min-width: 71px;

    .@{bootstrap-switch-base}-handle-on,
    .@{bootstrap-switch-base}-handle-off,
    .@{bootstrap-switch-base}-label {
      padding: 3px 6px;
      font-size: 10px;
      line-height: 9px;
    }
  }

  &.@{bootstrap-switch-base}-small {
    min-width: 79px;

    .@{bootstrap-switch-base}-handle-on,
    .@{bootstrap-switch-base}-handle-off,
    .@{bootstrap-switch-base}-label {
      padding: 3px 6px;
      font-size: 12px;
      line-height: 18px;
    }
  }

  &.@{bootstrap-switch-base}-large {
    min-width: 120px;

    .@{bootstrap-switch-base}-handle-on,
    .@{bootstrap-switch-base}-handle-off,
    .@{bootstrap-switch-base}-label {
      padding: 9px 12px;
      font-size: 16px;
      line-height: normal;
    }
  }

  &.@{bootstrap-switch-base}-disabled,
  &.@{bootstrap-switch-base}-readonly,
  &.@{bootstrap-switch-base}-indeterminate {
    cursor: default !important;

    .@{bootstrap-switch-base}-handle-on,
    .@{bootstrap-switch-base}-handle-off,
    .@{bootstrap-switch-base}-label {
      .opacity(50);
      cursor: default !important;
    }
  }

  &.@{bootstrap-switch-base}-animate {

    .@{bootstrap-switch-base}-container {
      .transition(margin-left .5s);
    }
  }

  &.@{bootstrap-switch-base}-inverse {

    .@{bootstrap-switch-base}-handle-on {
      .border-left-radius(0);
      .border-right-radius(4px);
    }

    .@{bootstrap-switch-base}-handle-off {
      .border-right-radius(0);
      .border-left-radius(4px);
    }
  }

  &.@{bootstrap-switch-base}-focused {
    border-color: rgba(82, 168, 236, .8);
    outline: 0;
    outline: thin dotted \9;
    .box-shadow(~"inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(82, 168, 236, .6)");
  }

  &.@{bootstrap-switch-base}-on,
  &.@{bootstrap-switch-base}-inverse.@{bootstrap-switch-base}-off {

    .@{bootstrap-switch-base}-label {
      .border-right-radius(4px);
    }
  }

  &.@{bootstrap-switch-base}-off,
  &.@{bootstrap-switch-base}-inverse.@{bootstrap-switch-base}-on {

    .@{bootstrap-switch-base}-label {
      .border-left-radius(4px);
    }
  }
}
