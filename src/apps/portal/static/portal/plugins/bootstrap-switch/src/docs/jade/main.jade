doctype html
html(lang='en')
  head
    meta(charset='utf-8')
    meta(http-equiv='X-UA-Compatible', content='IE=edge')
    meta(name='viewport', content='width=device-width, initial-scale=1.0')
    meta(name='description', content='Turn checkboxes and radio buttons in toggle switches.')
    meta(name='author', content='<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>')
    title Bootstrap Switch · Turn checkboxes and radio buttons in toggle switches
    link(href='docs/css/bootstrap.min.css', rel='stylesheet')
    link(href='docs/css/highlight.css', rel='stylesheet')
    link(href='dist/css/bootstrap3/bootstrap-switch.css', rel='stylesheet')
    link(href='http://getbootstrap.com/assets/css/docs.min.css', rel='stylesheet')
    link(href='docs/css/main.css', rel='stylesheet')
    script.
      var _gaq = _gaq || [];
      _gaq.push(['_setAccount', 'UA-********-1']);
      _gaq.push(['_trackPageview']);
      (function () {
        var ga = document.createElement('script');
        ga.type = 'text/javascript';
        ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(ga, s);
      })();
  body
    a(href='https://github.com/nostalgiaz/bootstrap-switch', id='github')
      img(src='https://s3.amazonaws.com/github/ribbons/forkme_right_gray_6d6d6d.png', alt='Fork me on GitHub')

    header.navbar.navbar-static-top.bs-docs-nav(role='banner')
      .container
        .navbar-header
          button.navbar-toggle(type='button', data-toggle='collapse', data-target='#collapse')
            span.sr-only Toggle navigation
            span.icon-bar
            span.icon-bar
            span.icon-bar
          a.navbar-brand(href='../') Bootstrap Switch
        nav#collapse.collapse.navbar-collapse.bs-navbar-collapse(role='navigation')
          ul.nav.navbar-nav
            li: a(href='https://github.com/nostalgiaz/bootstrap-switch/archive/master.zip', data-toggle='dropdown') Download
            li: a(href='examples.html') Examples
            li.dropdown
              a(href='#', data-toggle='dropdown')
                | Documentation
                = ' '
                span.caret
              ul.dropdown-menu
                li: a(href='/options.html') Options
                li: a(href='/methods.html') Methods
                li: a(href='/events.html') Events
                li.divider(role='presentation')
                li: a(href='/documentation-2.html') Documentation (v2.0.1)
            li: a(href='https://github.com/nostalgiaz/bootstrap-switch/issues') Bug reports

    main#content(role='main')
      block content

    footer.bs-docs-footer
      .container
        p
          | Code licensed under
          = ' '
          a(href='http://www.apache.org/licenses/LICENSE-2.0', target='_blank') Apache License, Version 2.0
          br
          | Created by
          = ' '
          a(href='https://github.com/nostalgiaz', target='_blank') Mattia Larentis
          br
          | Mantained by
          = ' '
          a(href='https://github.com/lostcrew', target='_blank') Emanuele Marchi

    script(src='docs/js/jquery.min.js')
    script(src='docs/js/bootstrap.min.js')
    script(src='docs/js/highlight.js')
    script(src='dist/js/bootstrap-switch.js')
    script(src='docs/js/main.js')
