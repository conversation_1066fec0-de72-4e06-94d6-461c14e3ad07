extends main

block content
  #content.bs-docs-header
    .container
      h1 Examples

  .container
    .row
      .col-sm-6.col-lg-4
        h2.h4 State
        p
          input#switch-state(type='checkbox', checked)
        .btn-group
          button.btn.btn-default(type='button' data-switch-toggle='state') Toggle
          button.btn.btn-default(type='button', data-switch-set='state', data-switch-value='true') Set true
          button.btn.btn-default(type='button', data-switch-set='state', data-switch-value='false') Set false
          button.btn.btn-default(type='button', data-switch-get='state') Get

      .col-sm-6.col-lg-4
        h2.h4 Size
        p
          input#switch-size(type='checkbox', checked, data-size='mini')
        .btn-group
          button.btn.btn-default(type='button', data-switch-set='size', data-switch-value='mini') Mini
          button.btn.btn-default(type='button', data-switch-set='size', data-switch-value='small') Small
          button.btn.btn-default(type='button', data-switch-set='size', data-switch-value='normal') Normal
          button.btn.btn-default(type='button', data-switch-set='size', data-switch-value='large') Large
          button.btn.btn-default(type='button', data-switch-get='size') Get

      .col-sm-6.col-lg-4
        h2.h4 Animate
        p
          input#switch-animate(type='checkbox', checked)
        p
          button.btn.btn-default(type='button', data-switch-toggle='animate') Toggle
          button.btn.btn-default(type='button', data-switch-get='animate') Get

      .col-sm-6.col-lg-4
        h2.h4 Disabled
        p
          input#switch-disabled(type='checkbox', checked, disabled)
        p
          button.btn.btn-default(type='button', data-switch-toggle='disabled') Toggle
          button.btn.btn-default(type='button', data-switch-get='disabled') Get

      .col-sm-6.col-lg-4
        h2.h4 Readonly
        p
          input#switch-readonly(type='checkbox', checked, readonly)
        p
          button.btn.btn-default(type='button', data-switch-toggle='readonly') Toggle
          button.btn.btn-default(type='button', data-switch-get='readonly') Get

      .col-sm-6.col-lg-4
        h2.h4 Indeterminate
        p
          input#switch-indeterminate(type='checkbox', checked, data-indeterminate='true')
        p
          button.btn.btn-default(type='button', data-switch-toggle='indeterminate') Toggle
          button.btn.btn-default(type='button', data-switch-get='indeterminate') Get

      .col-sm-6.col-lg-4
        h2.h4 Inverse
        p
          input#switch-inverse(type='checkbox', checked, data-inverse='true')
        p
          button.btn.btn-default(type='button', data-switch-toggle='inverse') Toggle
          button.btn.btn-default(type='button', data-switch-get='inverse') Get

      .col-sm-6.col-lg-4
        h2.h4 On Color
        p
          input#switch-onColor(type='checkbox', checked, data-on-color='info')
        p.btn-group
          .btn-group
            button.btn.btn-default.dropdown-toggle(type='button', data-toggle='dropdown')
              | Set &nbsp;
              span.caret
            .dropdown-menu(role='menu')
              li: a(data-switch-set='onColor', data-switch-value='primary') Primary
              li: a(data-switch-set='onColor', data-switch-value='info') Info
              li: a(data-switch-set='onColor', data-switch-value='success') Success
              li: a(data-switch-set='onColor', data-switch-value='warning') Warning
              li: a(data-switch-set='onColor', data-switch-value='default') Default
          button.btn.btn-default(type='button', data-switch-get='onColor') Get

      .col-sm-6.col-lg-4
        h2.h4 Off Color
        p
          input#switch-offColor(type='checkbox', data-off-color='warning')
        p.btn-group
          .btn-group
            button.btn.btn-default.dropdown-toggle(type='button', data-toggle='dropdown')
              | Set &nbsp;
              span.caret
            .dropdown-menu(role='menu')
              li: a(data-switch-set='offColor', data-switch-value='primary') Primary
              li: a(data-switch-set='offColor', data-switch-value='info') Info
              li: a(data-switch-set='offColor', data-switch-value='success') Success
              li: a(data-switch-set='offColor', data-switch-value='warning') Warning
              li: a(data-switch-set='offColor', data-switch-value='default') Default
          button.btn.btn-default(type='button', data-switch-get='offColor') Get

      .col-sm-6.col-lg-4
        h2.h4 On Text
        p
          input#switch-onText(type='checkbox', checked, data-on-text='Yes')
        .row
          .col-sm-6
            input.form-control(type='text', data-switch-set-value='onText', value='Yes')

      .col-sm-6.col-lg-4
        h2.h4 Off Text
        p
          input#switch-offText(type='checkbox', data-off-text='No')
        .row
          .col-sm-6
            input.form-control(type='text', data-switch-set-value='offText', value='No')

      .col-sm-6.col-lg-4
        h2.h4 Label Text
        p
          input#switch-labelText(type='checkbox', data-label-text='Label')
        .row
          .col-sm-6
            input.form-control(type='text', data-switch-set-value='labelText')

      .col-sm-6.col-lg-4
        h2.h4 Handle Width
        p
          input#switch-handleWidth(type='checkbox', data-handle-width='100')
        .row
          .col-sm-6
            input.form-control(type='number', data-switch-set-value='handleWidth', value='100')

      .col-sm-6.col-lg-4
        h2.h4 Label Width
        p
          input#switch-labelWidth(type='checkbox', data-label-width='100')
        .row
          .col-sm-6
            input.form-control(type='number', data-switch-set-value='labelWidth', value='100')

      .col-sm-6.col-lg-4
        h2.h4 Create | Destroy
        p
          input#switch-create-destroy(type='checkbox', checked, data-switch-no-init)
        .row
          .col-sm-6
            button.btn.btn-default(type='button', data-switch-create-destroy, data-destroy-text="Destroy") Create

    br
    br

    .text-center
      h2.h4 Radio All Off
      .row
        .col-sm-6
          h3.h5 Disabled
          input.switch-radio1(type='radio', name='radio1', checked)
          input.switch-radio1(type='radio', name='radio1')
          input.switch-radio1(type='radio', name='radio1')
        .col-sm-6
          h3.h5 Enabled
          input.switch-radio2(type='radio', name='radio2', checked, data-radio-all-off='true')
          input.switch-radio2(type='radio', name='radio2', data-radio-all-off='true')
          input.switch-radio2(type='radio', name='radio2', data-radio-all-off='true')

      br
      hr


      h2.h4 Inside Modals
      button.btn.btn-default(data-toggle='modal', data-target='#modal-switch') Open Modal
      .modal.fade#modal-switch(tabindex='-1', role='dialog', aria-labelledby='modal-switch-label')
        .modal-dialog
          .modal-content
            .modal-header
              button.close(type='button', data-dismiss='modal')
                span(aria-hidden='true') &times;
                span.sr-only Close
              .modal-title#modal-switch-label Title
            .modal-body
              input#switch-modal(type='checkbox', checked)
