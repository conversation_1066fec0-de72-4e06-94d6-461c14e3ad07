extends main

block content
  #content.bs-docs-header
    .container
      h1 Events

  .container
    p
      | All the events are namespaced, therefore always append <code>.bootstrapSwitch</code> when you
      | attach your handlers.<br>
      | You can register to the emitted events as follow:

    pre: code
      | $('input[name="my-checkbox"]').on('switchChange.bootstrapSwitch', function(event, state) {
      |   console.log(this); // DOM element
      |   console.log(event); // jQuery event
      |   console.log(state); // true | false
      | });

    table.table.table-bordered.table-striped.table-responsive
      thead
        tr
          th Name
          th Description
          th Parameters
      tbody
        tr
          td init
          td Triggered on initialization. 'this' refers to the DOM element.
          td
            | event (<a href="https://api.jquery.com/category/events/event-object/" target="_blank">jQuery Event object</a>)
        tr
          td switchChange
          td Triggered on switch state change. 'this' refers to the DOM element.
          td
            | event (<a href="https://api.jquery.com/category/events/event-object/" target="_blank">jQuery Event object</a>),
            | state (true | false)
