extends main

block content
  #content.bs-docs-header
    .container
      h1 Options

  .container
    table.table.table-bordered.table-striped.table-responsive
      thead
        tr
          th Name
          th Attribute
          th Type
          th Description
          th Values
          th Default
      tbody
        tr
          td state
          td checked
          td Boolean
          td The checkbox state
          td true, false
          td true
        tr
          td size
          td data-size
          td String
          td The checkbox size
          td null, 'mini', 'small', 'normal', 'large'
          td null
        tr
          td animate
          td data-animate
          td Boolean
          td Animate the switch
          td true, false
          td true
        tr
          td disabled
          td disabled
          td Boolean
          td Disable state
          td true, false
          td false
        tr
          td readonly
          td readonly
          td Boolean
          td Readonly state
          td true, false
          td false
        tr
          td indeterminate
          td data-indeterminate
          td Boolean
          td Indeterminate state
          td true, false
          td false
        tr
          td inverse
          td data-inverse
          td Boolean
          td Inverse switch direction
          td true, false
          td false
        tr
          td radioAllOff
          td data-radio-all-off
          td Boolean
          td Allow this radio button to be unchecked by the user
          td true, false
          td false
        tr
          td onColor
          td data-on-color
          td String
          td Color of the left side of the switch
          td 'primary', 'info', 'success', 'warning', 'danger', 'default'
          td 'primary'
        tr
          td offColor
          td data-off-color
          td String
          td Color of the right side of the switch
          td 'primary', 'info', 'success', 'warning', 'danger', 'default'
          td 'default'
        tr
          td onText
          td data-on-text
          td String
          td Text of the left side of the switch
          td String
          td 'ON'
        tr
          td offText
          td data-off-text
          td String
          td Text of the right side of the switch
          td String
          td 'OFF'
        tr
          td labelText
          td data-label-text
          td String
          td Text of the center handle of the switch
          td String
          td '&amp;nbsp;'
        tr
          td handleWidth
          td data-handle-width
          td String | Number
          td Width of the left and right sides in pixels
          td 'auto' or Number
          td 'auto'
        tr
          td labelWidth
          td data-label-width
          td String | Number
          td Width of the center handle in pixels
          td 'auto' or Number
          td 'auto'
        tr
          td baseClass
          td data-base-class
          td String
          td Global class prefix
          td String
          td 'bootstrap-switch'
        tr
          td wrapperClass
          td data-wrapper-class
          td String | Array
          td Container element class(es)
          td String | Array
          td 'wrapper'
        tr
          td onInit
          td
          td Function
          td Callback function to execute on initialization
          td Function
          td: pre: code.javascript function(event, state) {}
        tr
          td onSwitchChange
          td
          td Function
          td Callback function to execute on switch state change
          td Function
          td: pre: code.javascript function(event, state) {}

    h2 Global Defaults Overriding

    p Follow the jQuery convention to override the default options of the library. For instance:
    pre
      code
        | $.fn.bootstrapSwitch.defaults.size = 'large';
        | $.fn.bootstrapSwitch.defaults.onColor = 'success';
