extends main

block content
  .bs-docs-masthead
    .container
      h1.title Bootstrap Switch

      p.lead
        | Turn checkboxes &nbsp;
        input(type='checkbox', checked, data-switch-no-init)
        | &nbsp; and radio buttons &nbsp;
        input(type='radio', checked, data-switch-no-init)
        | &nbsp; in toggle switches &nbsp;
        input(type='checkbox', checked)

      p.lead
        a.btn.btn-outline-inverse.btn-lg(href='https://github.com/nostalgiaz/bootstrap-switch/archive/master.zip') Download Bootstrap Switch
      p.bs-docs-social
        iframe(src='http://ghbtns.com/github-btn.html?user=nostalgiaz&repo=bootstrap-switch&type=watch&count=true&size=large', allowtransparency='true', frameborder='0', scrolling='0', width='184', height='30')
        iframe(src='http://ghbtns.com/github-btn.html?user=nostalgiaz&repo=bootstrap-switch&type=fork&count=true&size=large', allowtransparency='true', frameborder='0', scrolling='0', width='144', height='30')
      br
      p.version
        | Currently v3.3.2 · Compatible with Bootstrap 2 and 3

  .container
    h2.page-header Getting Started

    p Include the dependencies: jQuery, Bootstrap and Bootstrap Switch CSS + Javascript.
    pre: code
      | [...]
      | &lt;link href="bootstrap.css" rel="stylesheet"&gt;
      | &lt;link href="bootstrap-switch.css" rel="stylesheet"&gt;
      | &lt;script src="jquery.js"&gt;&lt;/script&gt;
      | &lt;script src="bootstrap-switch.js"&gt;&lt;/script&gt;
      | [...]

    p Add your checkbox.
    pre: code &lt;input type="checkbox" name="my-checkbox" checked&gt;

    p Initialize Bootstrap Switch.
    pre: code $("[name='my-checkbox']").bootstrapSwitch();

    p Enjoy.

    .text-center
      a.btn.btn-lg.btn-primary(href='examples.html') See Examples
      | &nbsp;
      a.btn.btn-lg.btn-outline(href='options.html') Browse Documentation

