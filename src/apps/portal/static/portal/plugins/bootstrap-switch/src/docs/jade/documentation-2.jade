extends main

block content
  #content.bs-docs-header
    .container
      h1 Documentation
      p v2.0.1

  .container
    #options
      h2.page-header Options

      .alert.alert-warning
        p
          | All the options are accepted only using <code>data-*</code> attributes on the element.<br>
          | <code>checked</code>, <code>disabled</code> and <code>readonly</code> are exception to the rule, being
          | default HTML input attributes.<br>
          | Therefore, there is not any way to specify the options in JavaScript during initialization.

      table.table.table-bordered.table-striped.table-responsive
        thead
          tr
            th Name
            th Type
            th Description
            th Values
            th Default
        tbody
          tr
            td state
            td Boolean
            td The checkbox state
            td true, false
            td 'checked' attribute or true
          tr
            td size
            td String
            td The checkbox state
            td '', 'mini', 'small', 'normal', 'large'
            td ''
          tr
            td animate
            td Boolean
            td Animate the switch
            td true, false
            td true
          tr
            td disabled
            td Boolean
            td Disable state
            td true, false
            td 'disabled' attribute or false
          tr
            td readonly
            td Boolean
            td Readonly state
            td true, false
            td 'readonly' attribute or false
          tr
            td on
            td String
            td Color of the left side of the switch
            td 'primary', 'info', 'success', 'warning', 'danger', 'default'
            td null
          tr
            td off
            td String
            td Color of the right side of the switch
            td 'primary', 'info', 'success', 'warning', 'danger', 'default'
            td null
          tr
            td on-label
            td String
            td Text of the left side of the switch
            td String
            td 'ON'
          tr
            td off-label
            td String
            td Text of the right side of the switch
            td String
            td 'OFF'
          tr
            td text-label
            td String
            td Text of the center handle of the switch
            td String
            td '&amp;nbsp;'
          tr
            td label-icon
            td String
            td Text of the center handle of the switch. Use to include external services icons
            td String
            td null

    #methods
      h2.page-header Methods

      table.table.table-bordered.table-striped.table-responsive
        thead
          tr
            th Name
            th Description
            th Accepted Values
            th Returned Values
        tbody
          tr
            td state
            td Get checkbox state
            td
            td true, false
          tr
            td setState
            td Set checkbox state
            td (value: true, false)[, skip: true, <strong>false</strong>]
            td jQuery Object (input element)
          tr
            td toggleState
            td Toggle checkbox state
            td [skip: true, <strong>false</strong>]
            td jQuery Object (input element)
          tr
            td toggleRadioState
            td Toggle radio state
            td [skip: true, <strong>false</strong>]
            td jQuery Object (input element)
          tr
            td toggleRadioStateAllowUncheck
            td Toggle radio state allowing uncheck of the radio input
            td [uncheck: true, <strong>false</strong> | skip: true, <strong>false</strong>]
            td jQuery Object (input element)
          tr
            td setSizeClass
            td Set the size of the switch
            td '', 'mini', 'small', 'normal', 'large'
            td jQuery Object (input element)
          tr
            td setAnimated
            td Animate the switch
            td true, false
            td jQuery Object (input element)
          tr
            td isDisabled
            td Get disabled state
            td
            td true, false
          tr
            td setDisabled
            td Set disable state
            td true, false
            td jQuery Object (input element)
          tr
            td toggleDisabled
            td Toggle disabled state
            td
            td jQuery Object (input element)
          tr
            td isReadOnly
            td Get Readonly state
            td
            td true, false
          tr
            td setReadOnly
            td Set Readonly state
            td true, false
            td jQuery Object (input element)
          tr
            td toggleReadOnly
            td Toggle readonly state
            td
            td jQuery Object (input element)
          tr
            td setOnClass
            td Color of the left side of the switch
            td 'primary', 'info', 'success', 'warning', 'danger', 'default'
            td jQuery Object (input element)
          tr
            td setOffClass
            td Color of the right side of the switch
            td 'primary', 'info', 'success', 'warning', 'danger', 'default'
            td jQuery Object (input element)
          tr
            td setOnLabel
            td Text of the left side of the switch
            td String
            td jQuery Object (input element)
          tr
            td setOffLabel
            td Text of the right side of the switch
            td String
            td jQuery Object (input element)
          tr
            td setTextLabel
            td Text of the center handle of the switch
            td String
            td null
          tr
            td setTextIcon
            td Text of the center handle of the switch. Use to include external services icons
            td String
            td null
          tr
            td destroy
            td Destroy the instance of Bootstrap Switch
            td
            td jQuery Object (input element)

    #events
      h2.page-header Events

      p
        | The only event triggered it <code>switch-change</code>. It returns two parameters: <code>event</code> and
        | <code>data</code>.<br>
        | The latter is an object that include <code>el</code> (the input DOM element) and <code>value</code> (the
        | new input state)
