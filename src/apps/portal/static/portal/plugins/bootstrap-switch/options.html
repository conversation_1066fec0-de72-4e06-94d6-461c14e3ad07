<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Turn checkboxes and radio buttons in toggle switches.">
    <meta name="author" content="<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>">
    <title>Bootstrap Switch · Turn checkboxes and radio buttons in toggle switches</title>
    <link href="docs/css/bootstrap.min.css" rel="stylesheet">
    <link href="docs/css/highlight.css" rel="stylesheet">
    <link href="dist/css/bootstrap3/bootstrap-switch.css" rel="stylesheet">
    <link href="http://getbootstrap.com/assets/css/docs.min.css" rel="stylesheet">
    <link href="docs/css/main.css" rel="stylesheet">
    <script>
      var _gaq = _gaq || [];
      _gaq.push(['_setAccount', 'UA-********-1']);
      _gaq.push(['_trackPageview']);
      (function () {
        var ga = document.createElement('script');
        ga.type = 'text/javascript';
        ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(ga, s);
      })();
    </script>
  </head>
  <body><a href="https://github.com/nostalgiaz/bootstrap-switch" id="github"><img src="https://s3.amazonaws.com/github/ribbons/forkme_right_gray_6d6d6d.png" alt="Fork me on GitHub"></a>
    <header role="banner" class="navbar navbar-static-top bs-docs-nav">
      <div class="container">
        <div class="navbar-header">
          <button type="button" data-toggle="collapse" data-target="#collapse" class="navbar-toggle"><span class="sr-only">Toggle navigation</span><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></button><a href="../" class="navbar-brand">Bootstrap Switch</a>
        </div>
        <nav id="collapse" role="navigation" class="collapse navbar-collapse bs-navbar-collapse">
          <ul class="nav navbar-nav">
            <li><a href="https://github.com/nostalgiaz/bootstrap-switch/archive/master.zip" data-toggle="dropdown">Download</a></li>
            <li><a href="examples.html">Examples</a></li>
            <li class="dropdown"><a href="#" data-toggle="dropdown">Documentation <span class="caret"></span></a>
              <ul class="dropdown-menu">
                <li><a href="/options.html">Options</a></li>
                <li><a href="/methods.html">Methods</a></li>
                <li><a href="/events.html">Events</a></li>
                <li role="presentation" class="divider"></li>
                <li><a href="/documentation-2.html">Documentation (v2.0.1)</a></li>
              </ul>
            </li>
            <li><a href="https://github.com/nostalgiaz/bootstrap-switch/issues">Bug reports</a></li>
          </ul>
        </nav>
      </div>
    </header>
    <main id="content" role="main">
      <div id="content" class="bs-docs-header">
        <div class="container">
          <h1>Options</h1>
        </div>
      </div>
      <div class="container">
        <table class="table table-bordered table-striped table-responsive">
          <thead>
            <tr>
              <th>Name</th>
              <th>Attribute</th>
              <th>Type</th>
              <th>Description</th>
              <th>Values</th>
              <th>Default</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>state</td>
              <td>checked</td>
              <td>Boolean</td>
              <td>The checkbox state</td>
              <td>true, false</td>
              <td>true</td>
            </tr>
            <tr>
              <td>size</td>
              <td>data-size</td>
              <td>String</td>
              <td>The checkbox size</td>
              <td>null, 'mini', 'small', 'normal', 'large'</td>
              <td>null</td>
            </tr>
            <tr>
              <td>animate</td>
              <td>data-animate</td>
              <td>Boolean</td>
              <td>Animate the switch</td>
              <td>true, false</td>
              <td>true</td>
            </tr>
            <tr>
              <td>disabled</td>
              <td>disabled</td>
              <td>Boolean</td>
              <td>Disable state</td>
              <td>true, false</td>
              <td>false</td>
            </tr>
            <tr>
              <td>readonly</td>
              <td>readonly</td>
              <td>Boolean</td>
              <td>Readonly state</td>
              <td>true, false</td>
              <td>false</td>
            </tr>
            <tr>
              <td>indeterminate</td>
              <td>data-indeterminate</td>
              <td>Boolean</td>
              <td>Indeterminate state</td>
              <td>true, false</td>
              <td>false</td>
            </tr>
            <tr>
              <td>inverse</td>
              <td>data-inverse</td>
              <td>Boolean</td>
              <td>Inverse switch direction</td>
              <td>true, false</td>
              <td>false</td>
            </tr>
            <tr>
              <td>radioAllOff</td>
              <td>data-radio-all-off</td>
              <td>Boolean</td>
              <td>Allow this radio button to be unchecked by the user</td>
              <td>true, false</td>
              <td>false</td>
            </tr>
            <tr>
              <td>onColor</td>
              <td>data-on-color</td>
              <td>String</td>
              <td>Color of the left side of the switch</td>
              <td>'primary', 'info', 'success', 'warning', 'danger', 'default'</td>
              <td>'primary'</td>
            </tr>
            <tr>
              <td>offColor</td>
              <td>data-off-color</td>
              <td>String</td>
              <td>Color of the right side of the switch</td>
              <td>'primary', 'info', 'success', 'warning', 'danger', 'default'</td>
              <td>'default'</td>
            </tr>
            <tr>
              <td>onText</td>
              <td>data-on-text</td>
              <td>String</td>
              <td>Text of the left side of the switch</td>
              <td>String</td>
              <td>'ON'</td>
            </tr>
            <tr>
              <td>offText</td>
              <td>data-off-text</td>
              <td>String</td>
              <td>Text of the right side of the switch</td>
              <td>String</td>
              <td>'OFF'</td>
            </tr>
            <tr>
              <td>labelText</td>
              <td>data-label-text</td>
              <td>String</td>
              <td>Text of the center handle of the switch</td>
              <td>String</td>
              <td>'&amp;nbsp;'</td>
            </tr>
            <tr>
              <td>handleWidth</td>
              <td>data-handle-width</td>
              <td>String | Number</td>
              <td>Width of the left and right sides in pixels</td>
              <td>'auto' or Number</td>
              <td>'auto'</td>
            </tr>
            <tr>
              <td>labelWidth</td>
              <td>data-label-width</td>
              <td>String | Number</td>
              <td>Width of the center handle in pixels</td>
              <td>'auto' or Number</td>
              <td>'auto'</td>
            </tr>
            <tr>
              <td>baseClass</td>
              <td>data-base-class</td>
              <td>String</td>
              <td>Global class prefix</td>
              <td>String</td>
              <td>'bootstrap-switch'</td>
            </tr>
            <tr>
              <td>wrapperClass</td>
              <td>data-wrapper-class</td>
              <td>String | Array</td>
              <td>Container element class(es)</td>
              <td>String | Array</td>
              <td>'wrapper'</td>
            </tr>
            <tr>
              <td>onInit</td>
              <td></td>
              <td>Function</td>
              <td>Callback function to execute on initialization</td>
              <td>Function</td>
              <td>
                <pre><code class="javascript">function(event, state) {}</code></pre>
              </td>
            </tr>
            <tr>
              <td>onSwitchChange</td>
              <td></td>
              <td>Function</td>
              <td>Callback function to execute on switch state change</td>
              <td>Function</td>
              <td>
                <pre><code class="javascript">function(event, state) {}</code></pre>
              </td>
            </tr>
          </tbody>
        </table>
        <h2>Global Defaults Overriding</h2>
        <p>Follow the jQuery convention to override the default options of the library. For instance:</p>
        <pre><code>$.fn.bootstrapSwitch.defaults.size = 'large';
$.fn.bootstrapSwitch.defaults.onColor = 'success';</code></pre>
      </div>
    </main>
    <footer class="bs-docs-footer">
      <div class="container">
        <p>Code licensed under <a href="http://www.apache.org/licenses/LICENSE-2.0" target="_blank">Apache License, Version 2.0</a><br>Created by <a href="https://github.com/nostalgiaz" target="_blank">Mattia Larentis</a><br>Mantained by <a href="https://github.com/lostcrew" target="_blank">Emanuele Marchi</a>
        </p>
      </div>
    </footer>
    <script src="docs/js/jquery.min.js"></script>
    <script src="docs/js/bootstrap.min.js"></script>
    <script src="docs/js/highlight.js"></script>
    <script src="dist/js/bootstrap-switch.js"></script>
    <script src="docs/js/main.js"></script>
  </body>
</html>