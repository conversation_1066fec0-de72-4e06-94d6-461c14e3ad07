body {
  padding-top: 50px;
  padding-bottom: 50px;
}

h1 {
  font-size: 5em;
  letter-spacing: -2px;
}

.page-header {
  text-align: left;
}

#main {
  text-align: center;
}

.section {
  padding-top: 20px;
}

#github {
  display: none;
  position: fixed;
  width: 150px;
  height: 150px;
  top: 0;
  right: 0;
  z-index: 2000;
}

.footer {
  border-top: 1px solid #eee;
  margin-top: 80px;
  padding-top: 40px;
  padding-bottom: 40px;
}

@media (min-width: 768px) {
  #github {
    display: block;
  }
}
