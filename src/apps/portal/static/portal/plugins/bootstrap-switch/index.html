<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Turn checkboxes and radio buttons in toggle switches.">
    <meta name="author" content="<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>">
    <title>Bootstrap Switch · Turn checkboxes and radio buttons in toggle switches</title>
    <link href="docs/css/bootstrap.min.css" rel="stylesheet">
    <link href="docs/css/highlight.css" rel="stylesheet">
    <link href="dist/css/bootstrap3/bootstrap-switch.css" rel="stylesheet">
    <link href="http://getbootstrap.com/assets/css/docs.min.css" rel="stylesheet">
    <link href="docs/css/main.css" rel="stylesheet">
    <script>
      var _gaq = _gaq || [];
      _gaq.push(['_setAccount', 'UA-********-1']);
      _gaq.push(['_trackPageview']);
      (function () {
        var ga = document.createElement('script');
        ga.type = 'text/javascript';
        ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(ga, s);
      })();
    </script>
  </head>
  <body><a href="https://github.com/nostalgiaz/bootstrap-switch" id="github"><img src="https://s3.amazonaws.com/github/ribbons/forkme_right_gray_6d6d6d.png" alt="Fork me on GitHub"></a>
    <header role="banner" class="navbar navbar-static-top bs-docs-nav">
      <div class="container">
        <div class="navbar-header">
          <button type="button" data-toggle="collapse" data-target="#collapse" class="navbar-toggle"><span class="sr-only">Toggle navigation</span><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></button><a href="../" class="navbar-brand">Bootstrap Switch</a>
        </div>
        <nav id="collapse" role="navigation" class="collapse navbar-collapse bs-navbar-collapse">
          <ul class="nav navbar-nav">
            <li><a href="https://github.com/nostalgiaz/bootstrap-switch/archive/master.zip" data-toggle="dropdown">Download</a></li>
            <li><a href="examples.html">Examples</a></li>
            <li class="dropdown"><a href="#" data-toggle="dropdown">Documentation <span class="caret"></span></a>
              <ul class="dropdown-menu">
                <li><a href="/options.html">Options</a></li>
                <li><a href="/methods.html">Methods</a></li>
                <li><a href="/events.html">Events</a></li>
                <li role="presentation" class="divider"></li>
                <li><a href="/documentation-2.html">Documentation (v2.0.1)</a></li>
              </ul>
            </li>
            <li><a href="https://github.com/nostalgiaz/bootstrap-switch/issues">Bug reports</a></li>
          </ul>
        </nav>
      </div>
    </header>
    <main id="content" role="main">
      <div class="bs-docs-masthead">
        <div class="container">
          <h1 class="title">Bootstrap Switch</h1>
          <p class="lead">Turn checkboxes &nbsp;
            <input type="checkbox" checked data-switch-no-init>&nbsp; and radio buttons &nbsp;
            <input type="radio" checked data-switch-no-init>&nbsp; in toggle switches &nbsp;
            <input type="checkbox" checked>
          </p>
          <p class="lead"><a href="https://github.com/nostalgiaz/bootstrap-switch/archive/master.zip" class="btn btn-outline-inverse btn-lg">Download Bootstrap Switch</a></p>
          <p class="bs-docs-social">
            <iframe src="http://ghbtns.com/github-btn.html?user=nostalgiaz&amp;repo=bootstrap-switch&amp;type=watch&amp;count=true&amp;size=large" allowtransparency="true" frameborder="0" scrolling="0" width="184" height="30"></iframe>
            <iframe src="http://ghbtns.com/github-btn.html?user=nostalgiaz&amp;repo=bootstrap-switch&amp;type=fork&amp;count=true&amp;size=large" allowtransparency="true" frameborder="0" scrolling="0" width="144" height="30"></iframe>
          </p><br>
          <p class="version">Currently v3.3.2 · Compatible with Bootstrap 2 and 3</p>
        </div>
      </div>
      <div class="container">
        <h2 class="page-header">Getting Started</h2>
        <p>Include the dependencies: jQuery, Bootstrap and Bootstrap Switch CSS + Javascript.</p>
        <pre><code>[...]
&lt;link href="bootstrap.css" rel="stylesheet"&gt;
&lt;link href="bootstrap-switch.css" rel="stylesheet"&gt;
&lt;script src="jquery.js"&gt;&lt;/script&gt;
&lt;script src="bootstrap-switch.js"&gt;&lt;/script&gt;
[...]</code></pre>
        <p>Add your checkbox.</p>
        <pre><code>&lt;input type="checkbox" name="my-checkbox" checked&gt;</code></pre>
        <p>Initialize Bootstrap Switch.</p>
        <pre><code>$("[name='my-checkbox']").bootstrapSwitch();</code></pre>
        <p>Enjoy.</p>
        <div class="text-center"><a href="examples.html" class="btn btn-lg btn-primary">See Examples</a>&nbsp;<a href="options.html" class="btn btn-lg btn-outline">Browse Documentation</a></div>
      </div>
    </main>
    <footer class="bs-docs-footer">
      <div class="container">
        <p>Code licensed under <a href="http://www.apache.org/licenses/LICENSE-2.0" target="_blank">Apache License, Version 2.0</a><br>Created by <a href="https://github.com/nostalgiaz" target="_blank">Mattia Larentis</a><br>Mantained by <a href="https://github.com/lostcrew" target="_blank">Emanuele Marchi</a>
        </p>
      </div>
    </footer>
    <script src="docs/js/jquery.min.js"></script>
    <script src="docs/js/bootstrap.min.js"></script>
    <script src="docs/js/highlight.js"></script>
    <script src="dist/js/bootstrap-switch.js"></script>
    <script src="docs/js/main.js"></script>
  </body>
</html>