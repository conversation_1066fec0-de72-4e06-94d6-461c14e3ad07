{"name": "bootstrap-switch", "description": "Turn checkboxes and radio buttons in toggle switches.", "version": "3.3.2", "keywords": ["bootstrap", "switch", "javascript", "js"], "homepage": "http://www.bootstrap-switch.org", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://larentis.eu"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://lostcrew.it"}, {"name": "<PERSON>", "url": "http://www.bdmdesign.org"}], "main": "dist/js/bootstrap-switch.js", "dependencies": {"jquery": ">=1.9.0"}, "repository": {"type": "git", "url": "git://github.com/nostalgiaz/bootstrap-switch.git"}, "bugs": "https://github.com/nostalgiaz/bootstrap-switch/issues", "license": "Apache Version 2", "readmeFilename": "README.md", "devDependencies": {"coffee-script": "~1.8.0", "gulp": "~3.8.9", "gulp-changed": "^1.1.0", "gulp-coffee": "~2.2.0", "gulp-coffeelint": "~0.4.0", "gulp-connect": "^2.2.0", "gulp-header": "~1.2.2", "gulp-jade": "^0.10.0", "gulp-less": "^2.0.1", "gulp-load-plugins": "^0.8.0", "gulp-open": "~0.3.0", "gulp-rename": "~1.2.0", "gulp-uglify": "~1.0.1", "gulp-util": "~3.0.1", "jasmine-core": "^2.1.3", "karma": "~0.12.24", "karma-firefox-launcher": "~0.1.3", "karma-jasmine": "^0.3.2", "less-plugin-clean-css": "^1.2.0", "run-sequence": "~1.0.1"}, "scripts": {"build": "gulp dist", "test": "gulp test"}, "npmName": "bootstrap-switch", "npmFileMap": [{"basePath": "/dist/", "files": ["*.js", "bootstrap3/*.css", "bootstrap2/*.css"]}]}