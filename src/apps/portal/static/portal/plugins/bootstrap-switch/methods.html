<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Turn checkboxes and radio buttons in toggle switches.">
    <meta name="author" content="<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>">
    <title>Bootstrap Switch · Turn checkboxes and radio buttons in toggle switches</title>
    <link href="docs/css/bootstrap.min.css" rel="stylesheet">
    <link href="docs/css/highlight.css" rel="stylesheet">
    <link href="dist/css/bootstrap3/bootstrap-switch.css" rel="stylesheet">
    <link href="http://getbootstrap.com/assets/css/docs.min.css" rel="stylesheet">
    <link href="docs/css/main.css" rel="stylesheet">
    <script>
      var _gaq = _gaq || [];
      _gaq.push(['_setAccount', 'UA-********-1']);
      _gaq.push(['_trackPageview']);
      (function () {
        var ga = document.createElement('script');
        ga.type = 'text/javascript';
        ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(ga, s);
      })();
    </script>
  </head>
  <body><a href="https://github.com/nostalgiaz/bootstrap-switch" id="github"><img src="https://s3.amazonaws.com/github/ribbons/forkme_right_gray_6d6d6d.png" alt="Fork me on GitHub"></a>
    <header role="banner" class="navbar navbar-static-top bs-docs-nav">
      <div class="container">
        <div class="navbar-header">
          <button type="button" data-toggle="collapse" data-target="#collapse" class="navbar-toggle"><span class="sr-only">Toggle navigation</span><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></button><a href="../" class="navbar-brand">Bootstrap Switch</a>
        </div>
        <nav id="collapse" role="navigation" class="collapse navbar-collapse bs-navbar-collapse">
          <ul class="nav navbar-nav">
            <li><a href="https://github.com/nostalgiaz/bootstrap-switch/archive/master.zip" data-toggle="dropdown">Download</a></li>
            <li><a href="examples.html">Examples</a></li>
            <li class="dropdown"><a href="#" data-toggle="dropdown">Documentation <span class="caret"></span></a>
              <ul class="dropdown-menu">
                <li><a href="/options.html">Options</a></li>
                <li><a href="/methods.html">Methods</a></li>
                <li><a href="/events.html">Events</a></li>
                <li role="presentation" class="divider"></li>
                <li><a href="/documentation-2.html">Documentation (v2.0.1)</a></li>
              </ul>
            </li>
            <li><a href="https://github.com/nostalgiaz/bootstrap-switch/issues">Bug reports</a></li>
          </ul>
        </nav>
      </div>
    </header>
    <main id="content" role="main">
      <div id="content" class="bs-docs-header">
        <div class="container">
          <h1>Methods</h1>
        </div>
      </div>
      <div class="container">
        <p>In Bootstrap Switch, every option is also a method.</p>
        <p>If the second parameter is omitted, the method return the current value.</p>
        <p>You can invoke methods as follows:</p>
        <pre><code>$('input[name="my-checkbox"]').bootstrapSwitch('state', true, true);</code></pre>
        <h2>Additional Methods</h2>
        <table class="table table-bordered table-striped table-responsive">
          <thead>
            <tr>
              <th>Name</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>toggleState</td>
              <td>Toggle the switch state</td>
            </tr>
            <tr>
              <td>toggleAnimate</td>
              <td>Toggle the animate option</td>
            </tr>
            <tr>
              <td>toggleDisabled</td>
              <td>Toggle the disabled state</td>
            </tr>
            <tr>
              <td>toggleReadonly</td>
              <td>Toggle the readonly state</td>
            </tr>
            <tr>
              <td>toggleIndeterminate</td>
              <td>Toggle the indeterminate state</td>
            </tr>
            <tr>
              <td>toggleInverse</td>
              <td>Toggle the inverse option</td>
            </tr>
            <tr>
              <td>destroy</td>
              <td>Destroy the instance of Bootstrap Switch</td>
            </tr>
          </tbody>
        </table>
        <h2>Special Behaviours</h2>
        <ul>
          <li>The method <code>state</code> can receive an optional third parameter <code>skip</code>. if true, <code>switchChange</code> event is not executed. The default is false.</li>
          <li>The method <code>toggleState</code> can receive an optional second parameter <code>skip</code>. if true, <code>switchChange</code> event is not executed. The default is false.</li>
          <li>The method <code>wrapperClass</code> can accepts a falsy value as second parameter. If so, it resets the class to its default.</li>
        </ul>
      </div>
    </main>
    <footer class="bs-docs-footer">
      <div class="container">
        <p>Code licensed under <a href="http://www.apache.org/licenses/LICENSE-2.0" target="_blank">Apache License, Version 2.0</a><br>Created by <a href="https://github.com/nostalgiaz" target="_blank">Mattia Larentis</a><br>Mantained by <a href="https://github.com/lostcrew" target="_blank">Emanuele Marchi</a>
        </p>
      </div>
    </footer>
    <script src="docs/js/jquery.min.js"></script>
    <script src="docs/js/bootstrap.min.js"></script>
    <script src="docs/js/highlight.js"></script>
    <script src="dist/js/bootstrap-switch.js"></script>
    <script src="docs/js/main.js"></script>
  </body>
</html>