import { responsiveDisplayUtilities, layoutUtilities, generateBrandColorUtilities } from './tailwind.shared.js';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    // Portal templates
    "../../templates/**/*.html",
    "../../../templates/portal/**/*.html",
    "../../../templates/admin/**/*.html",
    
    // Widget templates (all widgets)
    "../../../templates/widgets/**/*.html",
    "../../../widgets/**/templates/**/*.html",
    
    // Portal JavaScript files
    "../js/**/*.{js,ts}",
    
    // Test files
    "../../../../../test-tailwind-generation.html",
    "../../../../../test*.html",
    
    // Vue components (if any portal-specific ones exist)
    "../../../**/*.vue"
  ],
  
  // Safelist patterns for JIT auto-generation (simplified for v4.1)
  safelist: [
    // Explicit color utilities that should always be available
    'bg-ws-primary-50', 'bg-ws-primary-100', 'bg-ws-primary-200', 'bg-ws-primary-300', 'bg-ws-primary-400', 'bg-ws-primary-500', 'bg-ws-primary-600', 'bg-ws-primary-700', 'bg-ws-primary-800', 'bg-ws-primary-900',
    'bg-ws-secondary-50', 'bg-ws-secondary-100', 'bg-ws-secondary-200', 'bg-ws-secondary-300', 'bg-ws-secondary-400', 'bg-ws-secondary-500', 'bg-ws-secondary-600', 'bg-ws-secondary-700', 'bg-ws-secondary-800', 'bg-ws-secondary-900',
    'text-ws-primary-50', 'text-ws-primary-100', 'text-ws-primary-200', 'text-ws-primary-300', 'text-ws-primary-400', 'text-ws-primary-500', 'text-ws-primary-600', 'text-ws-primary-700', 'text-ws-primary-800', 'text-ws-primary-900',
    'text-ws-secondary-50', 'text-ws-secondary-100', 'text-ws-secondary-200', 'text-ws-secondary-300', 'text-ws-secondary-400', 'text-ws-secondary-500', 'text-ws-secondary-600', 'text-ws-secondary-700', 'text-ws-secondary-800', 'text-ws-secondary-900',
    'border-ws-primary-50', 'border-ws-primary-100', 'border-ws-primary-200', 'border-ws-primary-300', 'border-ws-primary-400', 'border-ws-primary-500', 'border-ws-primary-600', 'border-ws-primary-700', 'border-ws-primary-800', 'border-ws-primary-900',
    'border-ws-secondary-50', 'border-ws-secondary-100', 'border-ws-secondary-200', 'border-ws-secondary-300', 'border-ws-secondary-400', 'border-ws-secondary-500', 'border-ws-secondary-600', 'border-ws-secondary-700', 'border-ws-secondary-800', 'border-ws-secondary-900',
    // State colors
    'bg-success-50', 'bg-success-100', 'bg-success-200', 'bg-success-300', 'bg-success-400', 'bg-success-500', 'bg-success-600', 'bg-success-700', 'bg-success-800', 'bg-success-900',
    'text-success-50', 'text-success-100', 'text-success-200', 'text-success-300', 'text-success-400', 'text-success-500', 'text-success-600', 'text-success-700', 'text-success-800', 'text-success-900',
    'border-success-50', 'border-success-100', 'border-success-200', 'border-success-300', 'border-success-400', 'border-success-500', 'border-success-600', 'border-success-700', 'border-success-800', 'border-success-900',
    'bg-warning-50', 'bg-warning-100', 'bg-warning-200', 'bg-warning-300', 'bg-warning-400', 'bg-warning-500', 'bg-warning-600', 'bg-warning-700', 'bg-warning-800', 'bg-warning-900',
    'text-warning-50', 'text-warning-100', 'text-warning-200', 'text-warning-300', 'text-warning-400', 'text-warning-500', 'text-warning-600', 'text-warning-700', 'text-warning-800', 'text-warning-900',
    'border-warning-50', 'border-warning-100', 'border-warning-200', 'border-warning-300', 'border-warning-400', 'border-warning-500', 'border-warning-600', 'border-warning-700', 'border-warning-800', 'border-warning-900',
    'bg-danger-50', 'bg-danger-100', 'bg-danger-200', 'bg-danger-300', 'bg-danger-400', 'bg-danger-500', 'bg-danger-600', 'bg-danger-700', 'bg-danger-800', 'bg-danger-900',
    'text-danger-50', 'text-danger-100', 'text-danger-200', 'text-danger-300', 'text-danger-400', 'text-danger-500', 'text-danger-600', 'text-danger-700', 'text-danger-800', 'text-danger-900',
    'border-danger-50', 'border-danger-100', 'border-danger-200', 'border-danger-300', 'border-danger-400', 'border-danger-500', 'border-danger-600', 'border-danger-700', 'border-danger-800', 'border-danger-900',
    // COMPLETE Standard TailwindCSS utilities for development convenience
    
    // Typography (all standard sizes and weights)
    'text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl', 'text-4xl', 'text-5xl', 'text-6xl',
    'font-thin', 'font-light', 'font-normal', 'font-medium', 'font-semibold', 'font-bold', 'font-extrabold', 'font-black',
    
    // Colors (standard gray + common colors for portal)
    'bg-white', 'bg-black', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200', 'bg-gray-300', 'bg-gray-400', 'bg-gray-500', 'bg-gray-600', 'bg-gray-700', 'bg-gray-800', 'bg-gray-900',
    'text-white', 'text-black', 'text-gray-50', 'text-gray-100', 'text-gray-200', 'text-gray-300', 'text-gray-400', 'text-gray-500', 'text-gray-600', 'text-gray-700', 'text-gray-800', 'text-gray-900',
    'border-white', 'border-black', 'border-gray-50', 'border-gray-100', 'border-gray-200', 'border-gray-300', 'border-gray-400', 'border-gray-500', 'border-gray-600', 'border-gray-700', 'border-gray-800', 'border-gray-900',
    
    // FULL Spacing Scale (so you never need to add spacing manually)
    'p-0', 'p-px', 'p-0.5', 'p-1', 'p-1.5', 'p-2', 'p-2.5', 'p-3', 'p-3.5', 'p-4', 'p-5', 'p-6', 'p-7', 'p-8', 'p-9', 'p-10', 'p-11', 'p-12', 'p-14', 'p-16', 'p-18', 'p-20', 'p-21', 'p-24', 'p-28', 'p-32',
    'px-0', 'px-px', 'px-0.5', 'px-1', 'px-1.5', 'px-2', 'px-2.5', 'px-3', 'px-3.5', 'px-4', 'px-5', 'px-6', 'px-7', 'px-8', 'px-9', 'px-10', 'px-11', 'px-12', 'px-14', 'px-16', 'px-20', 'px-24',
    'py-0', 'py-px', 'py-0.5', 'py-1', 'py-1.5', 'py-2', 'py-2.5', 'py-3', 'py-3.5', 'py-4', 'py-5', 'py-6', 'py-7', 'py-8', 'py-9', 'py-10', 'py-11', 'py-12', 'py-14', 'py-16', 'py-20', 'py-24',
    'm-0', 'm-px', 'm-0.5', 'm-1', 'm-1.5', 'm-2', 'm-2.5', 'm-3', 'm-3.5', 'm-4', 'm-5', 'm-6', 'm-7', 'm-8', 'm-9', 'm-10', 'm-11', 'm-12', 'm-14', 'm-16', 'm-20', 'm-24', 'm-auto',
    'mx-0', 'mx-px', 'mx-0.5', 'mx-1', 'mx-1.5', 'mx-2', 'mx-2.5', 'mx-3', 'mx-3.5', 'mx-4', 'mx-5', 'mx-6', 'mx-7', 'mx-8', 'mx-9', 'mx-10', 'mx-11', 'mx-12', 'mx-14', 'mx-16', 'mx-20', 'mx-24', 'mx-auto',
    'my-0', 'my-px', 'my-0.5', 'my-1', 'my-1.5', 'my-2', 'my-2.5', 'my-3', 'my-3.5', 'my-4', 'my-5', 'my-6', 'my-7', 'my-8', 'my-9', 'my-10', 'my-11', 'my-12', 'my-14', 'my-16', 'my-20', 'my-24',
    'mt-0', 'mt-px', 'mt-0.5', 'mt-1', 'mt-1.5', 'mt-2', 'mt-2.5', 'mt-3', 'mt-3.5', 'mt-4', 'mt-5', 'mt-6', 'mt-7', 'mt-8', 'mt-9', 'mt-10', 'mt-11', 'mt-12', 'mt-14', 'mt-16', 'mt-20', 'mt-24', 'mt-auto',
    'mr-0', 'mr-px', 'mr-0.5', 'mr-1', 'mr-1.5', 'mr-2', 'mr-2.5', 'mr-3', 'mr-3.5', 'mr-4', 'mr-5', 'mr-6', 'mr-7', 'mr-8', 'mr-9', 'mr-10', 'mr-11', 'mr-12', 'mr-14', 'mr-16', 'mr-20', 'mr-24', 'mr-auto',
    'mb-0', 'mb-px', 'mb-0.5', 'mb-1', 'mb-1.5', 'mb-2', 'mb-2.5', 'mb-3', 'mb-3.5', 'mb-4', 'mb-5', 'mb-6', 'mb-7', 'mb-8', 'mb-9', 'mb-10', 'mb-11', 'mb-12', 'mb-14', 'mb-16', 'mb-20', 'mb-24', 'mb-auto',
    'ml-0', 'ml-px', 'ml-0.5', 'ml-1', 'ml-1.5', 'ml-2', 'ml-2.5', 'ml-3', 'ml-3.5', 'ml-4', 'ml-5', 'ml-6', 'ml-7', 'ml-8', 'ml-9', 'ml-10', 'ml-11', 'ml-12', 'ml-14', 'ml-16', 'ml-20', 'ml-24', 'ml-auto',

    // Success notification specific colors (ensure they're always available)
    'bg-green-50', 'bg-red-50', 'bg-yellow-50', 'bg-blue-50',
    'text-green-400', 'text-green-700', 'text-green-800',
    'text-red-400', 'text-red-700', 'text-red-800',
    'text-yellow-400', 'text-yellow-700', 'text-yellow-800',
    'text-blue-400', 'text-blue-700', 'text-blue-800',
    'border-green-200', 'border-red-200', 'border-yellow-200', 'border-blue-200',
    
    // Space Between (INCLUDING space-y-0.5 and ALL spacing!)
    'space-x-0', 'space-x-px', 'space-x-0.5', 'space-x-1', 'space-x-1.5', 'space-x-2', 'space-x-2.5', 'space-x-3', 'space-x-3.5', 'space-x-4', 'space-x-5', 'space-x-6', 'space-x-7', 'space-x-8', 'space-x-9', 'space-x-10', 'space-x-11', 'space-x-12', 'space-x-14', 'space-x-16', 'space-x-20', 'space-x-24', 'space-x-reverse',
    'space-y-0', 'space-y-px', 'space-y-0.5', 'space-y-1', 'space-y-1.5', 'space-y-2', 'space-y-2.5', 'space-y-3', 'space-y-3.5', 'space-y-4', 'space-y-5', 'space-y-6', 'space-y-7', 'space-y-8', 'space-y-9', 'space-y-10', 'space-y-11', 'space-y-12', 'space-y-14', 'space-y-16', 'space-y-20', 'space-y-24', 'space-y-reverse',
    
    // Layout utilities (from shared config)
    ...responsiveDisplayUtilities,
    ...layoutUtilities,
    'grid-cols-1', 'grid-cols-2', 'grid-cols-3', 'grid-cols-4', 'grid-cols-5', 'grid-cols-6', 'grid-cols-7', 'grid-cols-8', 'grid-cols-9', 'grid-cols-10', 'grid-cols-11', 'grid-cols-12',
    'gap-0', 'gap-px', 'gap-0.5', 'gap-1', 'gap-1.5', 'gap-2', 'gap-2.5', 'gap-3', 'gap-3.5', 'gap-4', 'gap-5', 'gap-6', 'gap-7', 'gap-8', 'gap-9', 'gap-10', 'gap-11', 'gap-12', 'gap-14', 'gap-16', 'gap-20', 'gap-24',
    
    // Sizing (including modern size-* utilities)
    'w-0', 'w-px', 'w-0.5', 'w-1', 'w-1.5', 'w-2', 'w-2.5', 'w-3', 'w-3.5', 'w-4', 'w-5', 'w-6', 'w-8', 'w-10', 'w-12', 'w-16', 'w-20', 'w-24', 'w-32', 'w-40', 'w-48', 'w-56', 'w-64', 'w-72', 'w-80', 'w-96', 'w-auto', 'w-1/2', 'w-1/3', 'w-2/3', 'w-1/4', 'w-2/4', 'w-3/4', 'w-1/5', 'w-2/5', 'w-3/5', 'w-4/5', 'w-1/6', 'w-2/6', 'w-3/6', 'w-4/6', 'w-5/6', 'w-full',
    'h-0', 'h-px', 'h-0.5', 'h-1', 'h-1.5', 'h-2', 'h-2.5', 'h-3', 'h-3.5', 'h-4', 'h-5', 'h-6', 'h-8', 'h-10', 'h-12', 'h-16', 'h-20', 'h-24', 'h-32', 'h-40', 'h-48', 'h-56', 'h-64', 'h-72', 'h-80', 'h-96', 'h-auto', 'h-1/2', 'h-1/3', 'h-2/3', 'h-1/4', 'h-2/4', 'h-3/4', 'h-1/5', 'h-2/5', 'h-3/5', 'h-4/5', 'h-1/6', 'h-2/6', 'h-3/6', 'h-4/6', 'h-5/6', 'h-full', 'h-screen',
    // Modern size utilities (TailwindCSS v3.3+)
    'size-0', 'size-px', 'size-0.5', 'size-1', 'size-1.5', 'size-2', 'size-2.5', 'size-3', 'size-3.5', 'size-4', 'size-5', 'size-6', 'size-8', 'size-10', 'size-12', 'size-16', 'size-20', 'size-24', 'size-32', 'size-40', 'size-48', 'size-56', 'size-64', 'size-72', 'size-80', 'size-96', 'size-auto', 'size-full',
    
    // Borders & Effects
    'border', 'border-0', 'border-2', 'border-4', 'border-8', 'border-t', 'border-t-0', 'border-t-2', 'border-t-4', 'border-t-8', 'border-r', 'border-r-0', 'border-r-2', 'border-r-4', 'border-r-8', 'border-b', 'border-b-0', 'border-b-2', 'border-b-4', 'border-b-8', 'border-l', 'border-l-0', 'border-l-2', 'border-l-4', 'border-l-8',
    'rounded-none', 'rounded-sm', 'rounded', 'rounded-md', 'rounded-lg', 'rounded-xl', 'rounded-2xl', 'rounded-3xl', 'rounded-full',
    'shadow-xs', 'shadow-sm', 'shadow', 'shadow-md', 'shadow-lg', 'shadow-xl', 'shadow-2xl', 'shadow-inner', 'shadow-none',

    // Ring utilities
    'ring-0', 'ring-1', 'ring-2', 'ring-4', 'ring-8', 'ring-inset', 'ring-gray-300', 'ring-gray-400', 'ring-gray-500',

    // Divide utilities
    'divide-x', 'divide-y', 'divide-gray-200', 'divide-gray-300', 'divide-gray-400',
    
    // Transitions
    'transition-none', 'transition-all', 'transition', 'transition-colors', 'transition-opacity', 'transition-shadow', 'transition-transform',
    'duration-75', 'duration-100', 'duration-150', 'duration-200', 'duration-300', 'duration-500', 'duration-700', 'duration-1000',
    'ease-linear', 'ease-in', 'ease-out', 'ease-in-out',
    
    // Flexbox utilities
    'shrink-0', 'shrink', 'grow-0', 'grow',

    // Accessibility utilities
    'sr-only',

    // Responsive utilities for finder-v2 template
    'sm:grid-cols-3', 'sm:divide-x', 'sm:divide-y-0', 'sm:text-left', 'sm:items-center', 'sm:justify-between', 'sm:space-x-5', 'sm:pt-1', 'sm:mt-0', 'sm:text-2xl',

    // Common interactive states
    'hover:bg-gray-50', 'hover:bg-gray-100', 'hover:bg-gray-200', 'hover:text-gray-600', 'hover:text-gray-700', 'hover:text-gray-800', 'hover:border-gray-300', 'hover:border-gray-400',
    'focus:outline-none', 'focus:ring-1', 'focus:ring-2', 'focus:ring-4', 'focus:ring-blue-500', 'focus:ring-gray-300', 'focus:border-blue-500',
    // Brand-specific utilities
    'text-brand', 'bg-brand', 'border-brand', 'shadow-brand'
  ],

  // Enable dark mode (for future use)
  darkMode: 'class'
}
