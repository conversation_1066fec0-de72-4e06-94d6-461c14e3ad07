/* Base16 Atelier Dune Light - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/dune) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON>on/base16) */
/* https://github.com/jmblog/color-themes-for-highlightjs */

.ws-doc .hljs-comment {
  color: #7d7a68;
}

.ws-doc .hljs-title {
  color: #d73737;
}

.ws-doc .hljs-variable,
.ws-doc .hljs-attribute {
  color: #b854d4;
}

.ws-doc .hljs-value,
.ws-doc .hljs-required
{
  color: #E48F20;
}

.ws-doc .hljs-required {
  font-weight: bold;
}

.ws-doc .hljs-string {
  color: #60ac39;
}

.ws-doc .hljs-template {
  color: #1fad83;
  font-weight: bold;
}

.ws-doc .hljs-keyword {
  color: #6684e1;
}

.ws-doc .hljs,
.ws-doc.hljs {
  display: block;
  overflow-x: auto;
  background: #fefbec;
  color: #6e6b5e;
  padding: 0.5em;
  -webkit-text-size-adjust: none;
}
