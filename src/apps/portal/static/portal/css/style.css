html {
  position: relative;
  min-height: 100%;
}
body {
  /* Margin bottom by footer height */
  margin-bottom: 80px;
}
#footer {
  position: absolute;
  bottom: 0;
  height: 80px;
  min-height: 80px;
  width: 100%;
  padding: 25px 0;
}
.howto p {
  border-left: 2px solid #be3e1d;
  padding: 5px 0 5px 15px;
}
.howto .counter {
  font-size: 40px;
  color: #be3e1d;
  margin: 0 20px 0 0;
}
.form-wrapper {
  margin-top: 40px;
}
.mt10 {
  margin-top: 10px;
}
.mt20 {
  margin-top: 20px;
}
.mt40 {
  margin-top: 40px;
}
.mb80 {
  margin-bottom: 80px;
}
.mb40 {
  margin-bottom: 40px;
}
.mb20 {
  margin-bottom: 20px;
}
.features .feature img {
    max-width: 100%;
}

.widget-dashboard .wd-block-title {
  text-transform: uppercase;
  font-size: 24px;
  margin: 40px 0;
}
.widget-dashboard .wd-icon {
  text-align: right;
  color: #888888;
  font-size: 50px;
}
.widget-dashboard .wd-icon i {
  vertical-align: top;
}
.widget-dashboard .wd-icon img {
  max-width: 50px;
  vertical-align: top;
  top: -1px;
  position: relative;
  left: 6px;
}
.widget-dashboard .wd-list-marker {
  text-align: center;
  display: inline-block;
  font-size: 25px;
  margin-right: 8px;
}
.widget-dashboard .wd-list-item {
  border-bottom: 1px dashed #ccc;
}
.widget-dashboard .wd-list li a {
  width: 100%;
  text-align: left;
}
.widget-dashboard .wd-list-item h4 {
  margin: 0;
  display: inline-block;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
}
.widget-dashboard .wd-list-item .wd-list-item-info .label {
  top: 0;
}
.widget-dashboard .wd-list-item .wd-list-item-info > div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.widget-dashboard .wd-list-item .wd-list-item-info > p {
  width: 100%;
  overflow: hidden;
}
.widget-dashboard .wd-list-item p {
  margin: -1px 0 0 0;
  color: #888;
  font-size: 11px;
}
.widget-dashboard .wd-create-button {
  border: none;
  margin-bottom: 20px;
}
.widget-dashboard .wd-create-button h4 {
  margin: 2px 0 0 0;
}
.widget-dashboard .wd-list-empty {
  font-size: 16px;
  color: #888;
  padding: 5px 10px;
  border: none;
}

@media (max-width: 992px) {
  .widget-dashboard .col-md-2,
  .widget-dashboard .col-md-1 {
    display: none;
  }
}

.admin-mode {
  color: #341002;
  background: #fce6dd;
}
.admin-mode .title-divider {
  background-color: #fce6dd;
}
.admin-mode .title-divider span {
  background: #fce6dd;
}

.release-notes .list-changes {
  margin-left: 50px;
}

.timeline-breaker-color {
  background-color: #be3e1c;
}

.timeline-breaker-color:after{
  border-top-color: #be3e1c;
}

.timeline-item-in-future {
  border: 3px dashed #be3e1c;
}

.timeline-breaker-min-height {
  min-height: 30px;
}