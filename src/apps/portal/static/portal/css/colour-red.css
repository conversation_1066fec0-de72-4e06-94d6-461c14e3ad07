/*/////////////////////////////////////////////////////////////////////
 // 
 // Custom theme code styles
 // Written by Themelize.me (http://themelize.me)
 // 
 // This is an empty starter template for overriding styles
 // set by <PERSON><PERSON><PERSON> & the theme
 // 
 // ----------------------------------------------------
 // 
 // Remove unused code for better performances
 // 
 // ----------------------------------------------------
 // 
 // @see - Usefuls tools online for editing
 // 1. http://charliepark.org/bootstrap_buttons/ - Button style generator
 // 2. http://www.colorzilla.com/gradient-editor/ - CSS3 gradient maker
 // 
 // @note
 // To ensure custom styles are picked up
 // wrap definitions in body tag
 // ie.
 // body .navbar-inner {
 //   background: #ff0000;
 // }
 // 
 /////////////////////////////////////////////////////////////////////*/
.list-reset {
  margin: 0;
  padding: 0;
  list-style: none;
}
.border-box {
  -webkit-box-sizing: border-box;
  /* Safari/Chrome, other WebKit */
  -moz-box-sizing: border-box;
  /* Firefox, other Gecko */
  box-sizing: border-box;
  /* Opera/IE 8+ */
}
.branding {
  font-weight: 400;
  font-family: "Rambla", Arial, serif;
  padding: 0;
  margin: 6px 0 0 0;
  display: block;
  border: none;
  text-shadow: none;
  line-height: 1;
  color: #be3e1d;
  max-width: none;
  text-align: center;
  float: none;
  height: auto;
}
.branding h1 {
  font-size: 40px;
  font-weight: 400;
  margin: 0;
  padding: 0;
  line-height: 1;
  font-family: "Rambla", Arial, serif;
}
.branding h1 span {
  color: #464646;
  color: rgba(27, 27, 27, 0.8);
  font-weight: 700;
}
.branding:hover {
  text-decoration: none;
  cursor: pointer;
}
.header-button {
  font-size: 18px;
  color: white;
  background: #242424;
  padding: 0;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  display: block;
  height: 32px;
  width: 32px;
  line-height: 26px;
  text-align: center;
  margin-top: 20px;
  margin-left: 10px;
}
.header-button.open,
.header-button:hover {
  background: #be3e1d;
  color: white;
  cursor: pointer;
  text-decoration: none;
}
@media screen and (min-width: 768px) {
  .header-button {
    margin-top: 10px;
  }
}
/*
 * --------------------------------------------------
 * 1. Common Elements
 *--------------------------------------------------
 */
body a,
body a:focus {
  color: #be3e1d;
  text-decoration: none;
}
body a:hover {
  color: #923016;
  text-decoration: underline;
}
body #footer {
  border-top: 2px solid #be3e1d;
  /* primary colour */
}
body #footer #toplink i {
  color: #be3e1d;
  /* primary colour */
}
body #header-hidden-link a.show-hide,
body .flexslider-wrapper .flex-control-nav li a.flex-active,
body .static-banner .flex-control-nav li a.flex-active {
  background: #923016;
  /* primary colour */
}
body .flex-direction-nav a,
body .flex-direction-nav a:before {
  color: #be3e1d !important;
}
.btn {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.428571429;
  border-radius: 1px;
}
.btn span.caret {
  display: inline-block;
}
.btn-default {
  color: #4b4b4b;
  background-color: #f5f5f5;
  border-color: #e5e5e5;
  border-left: none;
  border-right: none;
  border-top: none;
}
.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  color: #4b4b4b;
  background-color: #e1e1e1;
  border-color: #c6c6c6;
}
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  background-image: none;
}
.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
  background-color: #f5f5f5;
  border-color: #e5e5e5;
}
.btn-default .badge {
  color: #f5f5f5;
  background-color: #4b4b4b;
}
.btn-primary {
  color: #ffffff;
  background-color: #be3e1d;
  border-color: #a8371a;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  color: #ffffff;
  background-color: #9b3218;
  border-color: #732512;
}
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  background-image: none;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #be3e1d;
  border-color: #a8371a;
}
.btn-primary .badge {
  color: #be3e1d;
  background-color: #ffffff;
}
.btn-primary-dark {
  color: #ffffff;
  background-color: #892d15;
  border-color: #6e2411;
}
.btn-primary-dark:hover,
.btn-primary-dark:focus,
.btn-primary-dark:active,
.btn-primary-dark.active,
.open .dropdown-toggle.btn-primary-dark {
  color: #ffffff;
  background-color: #66210f;
  border-color: #391309;
}
.btn-primary-dark:active,
.btn-primary-dark.active,
.open .dropdown-toggle.btn-primary-dark {
  background-image: none;
}
.btn-primary-dark.disabled,
.btn-primary-dark[disabled],
fieldset[disabled] .btn-primary-dark,
.btn-primary-dark.disabled:hover,
.btn-primary-dark[disabled]:hover,
fieldset[disabled] .btn-primary-dark:hover,
.btn-primary-dark.disabled:focus,
.btn-primary-dark[disabled]:focus,
fieldset[disabled] .btn-primary-dark:focus,
.btn-primary-dark.disabled:active,
.btn-primary-dark[disabled]:active,
fieldset[disabled] .btn-primary-dark:active,
.btn-primary-dark.disabled.active,
.btn-primary-dark[disabled].active,
fieldset[disabled] .btn-primary-dark.active {
  background-color: #892d15;
  border-color: #6e2411;
}
.btn-primary-dark .badge {
  color: #892d15;
  background-color: #ffffff;
}
.btn-block {
  display: block;
}
.btn-lg,
.btn-group-lg > .btn {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33;
  border-radius: 2px;
}
.btn-sm,
.btn-group-sm > .btn {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 1px;
}
.btn-xs,
.btn-group-xs > .btn {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 1px;
}
.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn:last-child:not(:first-child),
.btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group > .btn-group:first-child > .btn:last-child,
.btn-group > .btn-group:first-child > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn-group:last-child > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.tabbable:before,
.tabbable:after {
  content: " ";
  display: table;
}
.tabbable:after {
  clear: both;
}
.tabbable.row {
  margin-left: 0;
  margin-right: 0;
}
ul.nav-tabs {
  margin-bottom: 1em;
  border-bottom: none;
}
ul.nav-tabs li,
ul.nav-tabs li.open {
  float: none;
}
ul.nav-tabs li a,
ul.nav-tabs li.open a,
ul.nav-tabs li a:hover,
ul.nav-tabs li.open a:hover,
ul.nav-tabs li a:focus,
ul.nav-tabs li.open a:focus,
ul.nav-tabs li a:active,
ul.nav-tabs li.open a:active {
  border-radius: 0 !important;
  border: 1px solid #e6e6e6;
  margin-right: 0;
  background: white;
}
ul.nav-tabs li a:hover,
ul.nav-tabs li.open a:hover {
  background: #f7f7f7;
}
ul.nav-tabs li:last-child,
ul.nav-tabs li.open:last-child {
  margin-bottom: 0;
  border-bottom: none;
}
ul.nav-tabs li.active a,
ul.nav-tabs li.open.active a,
ul.nav-tabs li.active a:hover,
ul.nav-tabs li.open.active a:hover {
  color: #be3e1d;
  border: 1px solid #e6e6e6;
  border-top: 4px solid #be3e1d;
  border-radius: 0 !important;
}
ul.nav-tabs li.open a,
ul.nav-tabs li.open a:hover,
ul.nav-tabs li.open a:focus,
ul.nav-tabs li.open a:active {
  background: #f7f7f7;
}
ul.nav-tabs li.open.active a,
ul.nav-tabs li.open.active a:hover,
ul.nav-tabs li.open.active a:focus,
ul.nav-tabs li.open.active a:active {
  background: white;
}
.vertical-tabs ul.nav-stacked {
  margin-right: 0;
  border-bottom: 0;
  border-right: none;
}
.vertical-tabs ul.nav-stacked li {
  margin-right: -15px;
}
.vertical-tabs ul.nav-stacked li a {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  position: relative;
}
.vertical-tabs ul.nav-stacked li a i {
  font-size: 22px;
  position: absolute;
  top: 50%;
  right: 20px;
  margin-top: -10px;
  color: #e6e6e6;
}
.vertical-tabs ul.nav-stacked li a small {
  display: block;
  font-weight: normal;
}
.vertical-tabs ul.nav-stacked li a:hover i {
  color: #be3e1d;
}
.vertical-tabs ul.nav-stacked li.active a,
.vertical-tabs ul.nav-stacked li.active a:hover {
  border-top: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
  border-left: 4px solid #be3e1d;
}
.vertical-tabs ul.nav-stacked li.active a i,
.vertical-tabs ul.nav-stacked li.active a:hover i {
  color: #be3e1d;
}
.vertical-tabs .tab-content {
  margin-left: -15px;
  margin-right: -15px;
}
.bold-tabs ul li a {
  font-weight: bold;
  font-size: 18px;
  color: rgba(27, 27, 27, 0.8);
}
.bold-tabs ul li a i {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.bold-tabs ul li a small {
  display: block;
  font-weight: normal;
}
.bold-tabs ul li a:hover i {
  color: #be3e1d;
}
@media (min-width: 768px) {
  ul.nav-tabs {
    border-bottom: 1px solid #e6e6e6;
  }
  ul.nav-tabs li,
  ul.nav-tabs li.open {
    float: left;
    margin-right: 1px;
  }
  ul.nav-tabs li a,
  ul.nav-tabs li.open a,
  ul.nav-tabs li a:focus,
  ul.nav-tabs li.open a:focus,
  ul.nav-tabs li a:hover,
  ul.nav-tabs li.open a:hover {
    border: none;
    border-top: 4px solid transparent;
  }
  ul.nav-tabs li a:last-child,
  ul.nav-tabs li.open a:last-child,
  ul.nav-tabs li a:focus:last-child,
  ul.nav-tabs li.open a:focus:last-child,
  ul.nav-tabs li a:hover:last-child,
  ul.nav-tabs li.open a:hover:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }
  ul.nav-tabs li.active,
  ul.nav-tabs li.open.active {
    bottom: -1px;
  }
  ul.nav-tabs li.active a,
  ul.nav-tabs li.open.active a,
  ul.nav-tabs li.active a:hover,
  ul.nav-tabs li.open.active a:hover {
    border-top: 4px solid #be3e1d;
    border-right: 1px solid #e6e6e6;
    border-left: 1px solid #e6e6e6;
    border-bottom: none;
  }
  ul.nav-tabs li.dropdown .dropdown-menu {
    margin-top: 2px;
    min-width: 160px;
  }
  .vertical-tabs ul.nav-tabs {
    display: block;
    border-bottom: none;
    border-right: 1px solid #e6e6e6;
  }
  .vertical-tabs ul.nav-tabs li,
  .vertical-tabs ul.nav-tabs li.open {
    float: none;
    margin-right: -15px;
  }
  .vertical-tabs ul.nav-tabs li a,
  .vertical-tabs ul.nav-tabs li.open a,
  .vertical-tabs ul.nav-tabs li a:focus,
  .vertical-tabs ul.nav-tabs li.open a:focus {
    border-right: none;
    border-top: 1px solid transparent;
    border-left: 4px solid transparent;
  }
  .vertical-tabs ul.nav-tabs li a i,
  .vertical-tabs ul.nav-tabs li.open a i,
  .vertical-tabs ul.nav-tabs li a:focus i,
  .vertical-tabs ul.nav-tabs li.open a:focus i {
    -webkit-transform: rotate(0);
    -ms-transform: rotate(0);
    transform: rotate(0);
  }
  .vertical-tabs ul.nav-tabs li.active,
  .vertical-tabs ul.nav-tabs li.open.active {
    bottom: 0;
  }
  .vertical-tabs ul.nav-tabs li.active a,
  .vertical-tabs ul.nav-tabs li.open.active a,
  .vertical-tabs ul.nav-tabs li.active a:hover,
  .vertical-tabs ul.nav-tabs li.open.active a:hover {
    border-left: 4px solid #be3e1d;
    border-top: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;
    border-right: none;
  }
  .vertical-tabs ul.nav-tabs li.active {
    margin-right: -16px;
  }
  .vertical-tabs ul.nav-tabs li.dropdown .dropdown-menu {
    min-width: 100%;
    margin-top: 1px;
  }
  .vertical-tabs .tab-content {
    margin-right: auto;
    margin-left: auto;
  }
}
.pagination {
  display: block;
}
.pagination:before,
.pagination:after {
  content: " ";
  display: table;
}
.pagination:after {
  clear: both;
}
.pagination > li > a,
.pagination > li > span {
  color: #be3e1d;
}
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
  color: #923016;
}
.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  background: #be3e1d;
  border-color: #be3e1d;
}
.nav-tabs > li.dropdown {
  position: relative;
}
.nav-tabs > li.dropdown .dropdown-menu {
  box-shadow: none;
  border: 1px solid #e6e6e6;
  margin-top: 1px;
  padding: 0;
}
.nav-tabs > li.dropdown .dropdown-menu:before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ccc;
  border-bottom-color: rgba(0, 0, 0, 0.15);
  position: absolute;
  top: -7px;
  left: 12px;
}
.nav-tabs > li.dropdown .dropdown-menu:after {
  content: '';
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ffffff;
  position: absolute;
  top: -6px;
  left: 13px;
}
.nav-tabs > li.dropdown .dropdown-menu li {
  margin-right: 0 !important;
  border-bottom: none;
  bottom: 0 !important;
  margin-bottom: 0 !important;
  float: none !important;
}
.nav-tabs > li.dropdown .dropdown-menu li a {
  border: none;
  border-bottom: 1px solid #e6e6e6;
  background: white;
  color: rgba(27, 27, 27, 0.8);
  padding: 5px 8px !important;
}
.nav-tabs > li.dropdown .dropdown-menu li a:before {
  font-family: FontAwesome;
  font-size: 10px;
  color: rgba(27, 27, 27, 0.6);
  width: 30px;
  height: 30px;
  content: "\f0da";
  padding-right: 10px;
}
.nav-tabs > li.dropdown .dropdown-menu li a:last-child {
  margin: 0 !important;
  border-bottom: 1px solid #e6e6e6;
}
.nav-tabs > li.dropdown .dropdown-menu li.open a,
.nav-tabs > li.dropdown .dropdown-menu li.active a,
.nav-tabs > li.dropdown .dropdown-menu li a:hover {
  background: #f7f7f7;
}
.nav-tabs > li.dropdown .dropdown-menu .open > a,
.nav-tabs > li.dropdown .dropdown-menu .open > a:hover,
.nav-tabs > li.dropdown .dropdown-menu .open > a:focus {
  border-color: #e6e6e6;
}
.nav-tabs > li.dropdown.active .dropdown-menu li a {
  border: none !important;
}
.tab-content-bordered {
  border: 1px solid #e6e6e6;
  border-top: none;
  padding: 15px;
  margin-top: -1em;
}
/*
 * --------------------------------------------------
 * 2. Header & Navigation 
 *--------------------------------------------------
 */
body .header-hidden {
  background: #923016;
  /* primary colour */
}
body .header-upper {
  background: #be3e1d;
  /* primary colour */
}
body .header {
  border-bottom-color: #be3e1d;
  /* primary colour */
}
body .header .navbar-brand {
  color: #be3e1d;
  /* primary colour */
}
body .btn-navbar:hover,
body .btn-navbar.open,
body .navbar-btn.open,
body .navbar-btn:hover,
body .search-form-tigger:hover {
  background: #be3e1d;
  /* primary colour */
}
body.header-old #navigation .header {
  border-top-color: #be3e1d;
  /* primary colour */
}
body.header-old #navigation .header-hidden {
  background: #be3e1d;
  /* primary colour */
}
body.header-old #navigation #header-hidden-link a.show-hide,
body.header-old #navigation #header-hidden-link a.top-link {
  background: #be3e1d;
  /* primary colour */
}
body.header-old #navigation .navbar-brand {
  color: #be3e1d;
  /* primary colour */
}
body.header-old #navigation .btn-primary {
  color: #ffffff;
  background-color: #be3e1d;
  border-color: #a8371a;
  /* @see: Button maker: http://charliepark.org/bootstrap_buttons/ */
}
body.header-old #navigation .btn-primary:hover,
body.header-old #navigation .btn-primary:focus,
body.header-old #navigation .btn-primary:active,
body.header-old #navigation .btn-primary.active,
.open .dropdown-togglebody.header-old #navigation .btn-primary {
  color: #ffffff;
  background-color: #9b3218;
  border-color: #732512;
}
body.header-old #navigation .btn-primary:active,
body.header-old #navigation .btn-primary.active,
.open .dropdown-togglebody.header-old #navigation .btn-primary {
  background-image: none;
}
body.header-old #navigation .btn-primary.disabled,
body.header-old #navigation .btn-primary[disabled],
fieldset[disabled] body.header-old #navigation .btn-primary,
body.header-old #navigation .btn-primary.disabled:hover,
body.header-old #navigation .btn-primary[disabled]:hover,
fieldset[disabled] body.header-old #navigation .btn-primary:hover,
body.header-old #navigation .btn-primary.disabled:focus,
body.header-old #navigation .btn-primary[disabled]:focus,
fieldset[disabled] body.header-old #navigation .btn-primary:focus,
body.header-old #navigation .btn-primary.disabled:active,
body.header-old #navigation .btn-primary[disabled]:active,
fieldset[disabled] body.header-old #navigation .btn-primary:active,
body.header-old #navigation .btn-primary.disabled.active,
body.header-old #navigation .btn-primary[disabled].active,
fieldset[disabled] body.header-old #navigation .btn-primary.active {
  background-color: #be3e1d;
  border-color: #a8371a;
}
body.header-old #navigation .btn-primary .badge {
  color: #be3e1d;
  background-color: #ffffff;
}
body.header-old #navigation .navbar,
body.header-old #navigation .js-clingify-locked {
  border-bottom-color: #be3e1d;
  /* primary colour */
}
body .navbar-nav .divider-vertical {
  border-left-color: #7c2813;
  border-right-color: rgba(255, 255, 255, 0.15);
}
body .navbar-nav > li {
  border-left-color: #7c2813;
  border-right-color: rgba(255, 255, 255, 0.15);
}
body .navbar .nav li > .btn.btn-primary,
body .navbar .nav li > .btn.btn-navbar {
  color: #ffffff;
  background-color: #be3e1d;
  border-color: #a8371a;
}
body .navbar .nav li > .btn.btn-primary:hover,
body .navbar .nav li > .btn.btn-navbar:hover,
body .navbar .nav li > .btn.btn-primary:focus,
body .navbar .nav li > .btn.btn-navbar:focus,
body .navbar .nav li > .btn.btn-primary:active,
body .navbar .nav li > .btn.btn-navbar:active,
body .navbar .nav li > .btn.btn-primary.active,
body .navbar .nav li > .btn.btn-navbar.active,
.open .dropdown-togglebody .navbar .nav li > .btn.btn-primary,
.open .dropdown-togglebody .navbar .nav li > .btn.btn-navbar {
  color: #ffffff;
  background-color: #9b3218;
  border-color: #732512;
}
body .navbar .nav li > .btn.btn-primary:active,
body .navbar .nav li > .btn.btn-navbar:active,
body .navbar .nav li > .btn.btn-primary.active,
body .navbar .nav li > .btn.btn-navbar.active,
.open .dropdown-togglebody .navbar .nav li > .btn.btn-primary,
.open .dropdown-togglebody .navbar .nav li > .btn.btn-navbar {
  background-image: none;
}
body .navbar .nav li > .btn.btn-primary.disabled,
body .navbar .nav li > .btn.btn-navbar.disabled,
body .navbar .nav li > .btn.btn-primary[disabled],
body .navbar .nav li > .btn.btn-navbar[disabled],
fieldset[disabled] body .navbar .nav li > .btn.btn-primary,
fieldset[disabled] body .navbar .nav li > .btn.btn-navbar,
body .navbar .nav li > .btn.btn-primary.disabled:hover,
body .navbar .nav li > .btn.btn-navbar.disabled:hover,
body .navbar .nav li > .btn.btn-primary[disabled]:hover,
body .navbar .nav li > .btn.btn-navbar[disabled]:hover,
fieldset[disabled] body .navbar .nav li > .btn.btn-primary:hover,
fieldset[disabled] body .navbar .nav li > .btn.btn-navbar:hover,
body .navbar .nav li > .btn.btn-primary.disabled:focus,
body .navbar .nav li > .btn.btn-navbar.disabled:focus,
body .navbar .nav li > .btn.btn-primary[disabled]:focus,
body .navbar .nav li > .btn.btn-navbar[disabled]:focus,
fieldset[disabled] body .navbar .nav li > .btn.btn-primary:focus,
fieldset[disabled] body .navbar .nav li > .btn.btn-navbar:focus,
body .navbar .nav li > .btn.btn-primary.disabled:active,
body .navbar .nav li > .btn.btn-navbar.disabled:active,
body .navbar .nav li > .btn.btn-primary[disabled]:active,
body .navbar .nav li > .btn.btn-navbar[disabled]:active,
fieldset[disabled] body .navbar .nav li > .btn.btn-primary:active,
fieldset[disabled] body .navbar .nav li > .btn.btn-navbar:active,
body .navbar .nav li > .btn.btn-primary.disabled.active,
body .navbar .nav li > .btn.btn-navbar.disabled.active,
body .navbar .nav li > .btn.btn-primary[disabled].active,
body .navbar .nav li > .btn.btn-navbar[disabled].active,
fieldset[disabled] body .navbar .nav li > .btn.btn-primary.active,
fieldset[disabled] body .navbar .nav li > .btn.btn-navbar.active {
  background-color: #be3e1d;
  border-color: #a8371a;
}
body .navbar .nav li > .btn.btn-primary .badge,
body .navbar .nav li > .btn.btn-navbar .badge {
  color: #be3e1d;
  background-color: #ffffff;
}
body .navbar-nav li.dropdown > a:hover .caret {
  border-top-color: #333333;
  border-bottom-color: #333333;
}
body .navbar-nav li.dropdown > .dropdown-toggle .caret {
  border-top-color: rgba(255, 255, 255, 0.7);
  border-bottom-color: rgba(255, 255, 255, 0.7);
}
body .navbar-nav li.dropdown.open > .dropdown-toggle .caret,
body .navbar-nav li.dropdown.active > .dropdown-toggle .caret,
body .navbar-nav li.dropdown.open.active > .dropdown-toggle .caret {
  border-top-color: #333333;
  border-bottom-color: #333333;
}
@media (min-width: 992px) {
  .navbar-nav > li.home-link a i,
  #main-menu > li.home-link a i,
  .navbar-nav > li.home-link a:link i,
  #main-menu > li.home-link a:link i,
  .navbar-nav > li.home-link a:focus i,
  #main-menu > li.home-link a:focus i {
    color: #be3e1d;
    /* primary colour */
  }
  .header-old .navbar-nav > li.home-link a i,
  .header-old #main-menu > li.home-link a i,
  .header-old .navbar-nav > li.home-link a:link i,
  .header-old #main-menu > li.home-link a:link i,
  .header-old .navbar-nav > li.home-link a:focus i,
  .header-old #main-menu > li.home-link a:focus i {
    color: #be3e1d;
    /* primary colour */
  }
  .navbar-nav > li.open > .dropdown-toggle,
  #main-menu > li.open > .dropdown-toggle,
  .navbar-nav > li.open.active > .dropdown-toggle,
  #main-menu > li.open.active > .dropdown-toggle,
  .navbar-nav > li.open > .dropdown-toggle,
  #main-menu > li.open > .dropdown-toggle,
  .navbar-nav > li.open.active > .dropdown-toggle,
  #main-menu > li.open.active > .dropdown-toggle {
    border-top-color: #be3e1d !important;
    /* primary colour */
  }
  .navbar-collapse .dropdown-menu {
    border-bottom-color: #be3e1d;
    /* primary colour */
  }
  .navbar-collapse .dropdown-menu li a.menu-item:hover:before {
    color: #be3e1d;
    /* primary colour */
  }
  .navbar-collapse .dropdown-menu .dropdown-submenu > a::after {
    border-color: transparent transparent #be3e1d transparent;
  }
  .navbar-collapse .dropdown-menu li a:hover:before,
  .navbar-collapse .dropdown-menu li a.menu-item:hover:before {
    color: #be3e1d !important;
    /* primary colour */
  }
  .navbar-collapse .dropdown-menu li.active > a:before,
  .navbar-collapse .dropdown-menu li.active > a:hover:before,
  .navbar-collapse .dropdown-menu li.active > a:focus:before,
  .navbar-collapse .dropdown-menu li.dropdown-submenu .dropdown-menu .active > a:before,
  .navbar-collapse .dropdown-menu li.dropdown-submenu .dropdown-menu .active > a:hover:before,
  .navbar-collapse .dropdown-menu li.dropdown-submenu .dropdown-menu .active > a:focus:before,
  .navbar-collapse .dropdown-menu li a:hover:before,
  .navbar-collapse .dropdown-menu li a.menu-item:hover:before {
    color: #be3e1d !important;
    /* primary colour */
  }
}
.nav .caret {
  border-top-color: #be3e1d;
  border-bottom-color: #be3e1d;
}
.nav a:hover {
  border-top-color: #923016;
  border-bottom-color: #923016;
}
/*
 * --------------------------------------------------
 * 3. Content Area
 *-------------------------------------------------- 
 */
body .pricing-stack .well .price {
  color: #be3e1d;
  /* primary colour */
}
body .pricing-stack .well.active {
  border-color: #be3e1d;
  /* primary colour */
}
body .pricing-stack .well.active .price {
  background: #be3e1d;
  /* primary colour */
  border-color: #be3e1d;
}
body .section-menu ul.nav-list li a:hover i {
  color: #be3e1d;
}
body .section-menu ul.nav-list li.active > a,
body .section-menu ul.nav-list li.active > a:hover {
  color: #be3e1d;
  /* primary colour */
  border-left-color: #be3e1d;
  /* primary colour */
}
body .section-menu ul.nav-list li.active > a i,
body .section-menu ul.nav-list li.active > a:hover i {
  color: #be3e1d;
}
body .stats .stat .well {
  background: #be3e1d;
  background-image: -webkit-radial-gradient(circle, #be3e1d, #923016);
  background-image: radial-gradient(circle, #be3e1d, #923016);
  background-repeat: no-repeat;
}
body .stats .stat .well:after {
  border-top-color: #be3e1d;
  /* primary colour */
}
body .date-wrapper span.date-m {
  background: #be3e1d;
  /* primary colour */
}
body .tags .tag {
  color: #be3e1d;
  /* primary colour */
}
/*
 * --------------------------------------------------
 * 3. Misc
 * Other stuff
 *--------------------------------------------------
 */
body .primary-colour {
  color: #be3e1d;
  /* primary colour */
}
body .primary-colour-bg {
  background: #be3e1d;
  /* primary colour */
}
body .spacer {
  color: #be3e1d;
  /* primary colour */
}
body .spacer.dark {
  color: #923016;
}
body .social-media a:hover {
  background: #be3e1d;
  /* primary colour */
}
body a .de-em {
  color: rgba(190, 62, 29, 0.8);
}
.slider-appstrap-theme .tp-bullets li a:hover,
.slider-appstrap-theme .tp-bullets.round li a:hover,
.slider-appstrap-theme .tp-bullets.simplebullets.round li a:hover,
.flex-control-nav li a:hover,
.slider-appstrap-theme .tp-bullets .bullet:hover,
.slider-appstrap-theme .tp-bullets.round .bullet:hover,
.slider-appstrap-theme .tp-bullets.simplebullets.round .bullet:hover,
.flex-control-nav .bullet:hover,
.slider-appstrap-theme .tp-bullets li a.flex-active,
.slider-appstrap-theme .tp-bullets.round li a.flex-active,
.slider-appstrap-theme .tp-bullets.simplebullets.round li a.flex-active,
.flex-control-nav li a.flex-active,
.slider-appstrap-theme .tp-bullets .bullet.flex-active,
.slider-appstrap-theme .tp-bullets.round .bullet.flex-active,
.slider-appstrap-theme .tp-bullets.simplebullets.round .bullet.flex-active,
.flex-control-nav .bullet.flex-active,
.slider-appstrap-theme .tp-bullets li a.selected,
.slider-appstrap-theme .tp-bullets.round li a.selected,
.slider-appstrap-theme .tp-bullets.simplebullets.round li a.selected,
.flex-control-nav li a.selected,
.slider-appstrap-theme .tp-bullets .bullet.selected,
.slider-appstrap-theme .tp-bullets.round .bullet.selected,
.slider-appstrap-theme .tp-bullets.simplebullets.round .bullet.selected,
.flex-control-nav .bullet.selected {
  background: #be3e1d;
}
.slider-appstrap-theme .tp-caption a {
  color: #be3e1d;
}
.slider-appstrap-theme .tp-caption a:hover {
  color: #923016;
}
.slider-appstrap-theme .tp-bannertimer {
  background: #be3e1d;
}
.slider-appstrap-theme .tparrows:before,
.flex-direction-nav a:before {
  color: #be3e1d;
}
.bootstrap-switch > div > span.bootstrap-switch-primary {
  background: #be3e1d;
}
.bootstrap-switch.bootstrap-switch-focused {
  border-color: #be3e1d;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.dropdown-menu-primary {
  border-bottom-color: #be3e1d !important;
}
.dropdown-menu-primary a:hover:before {
  color: #be3e1d !important;
}
.timeline-item {
  border-bottom-color: #be3e1d;
}
.timeline-item.tag-featured:after,
.timeline-item.marker-highlight:after,
.timeline-item.highlight:after {
  color: #be3e1d;
}
.owl-controls.clickable .owl-buttons div:hover,
.owl-controls .owl-page.active span,
.owl-controls.clickable .owl-page:hover span {
  background: #be3e1d;
}
