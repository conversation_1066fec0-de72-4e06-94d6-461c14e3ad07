import { productionPatterns, brandColors, stateColors } from './tailwind.shared.js';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    // Development: Include ALL possible content to force generation
    "../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}",
    "../../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}",
    "../js/**/*.{js,ts}",
    "../../../**/*.{html,js,ts,jsx,tsx,vue}",
    
    // Force generation by including a wide pattern
    "./**/*.{html,js,ts,jsx,tsx,vue,php,twig,erb,blade}",
    
    // ESSENTIAL LAYOUT & POSITIONING CLASSES (for headers, selectors, etc.)
    { raw: 'inset-x-0 inset-y-0 inset-0 top-0 bottom-0 left-0 right-0' },
    { raw: 'z-10 z-20 z-30 z-40 z-50 z-auto' },
    { raw: 'absolute relative fixed sticky static' },
    { raw: 'flex inline-flex grid inline-grid block inline-block hidden' },
    { raw: 'items-center items-start items-end items-stretch items-baseline' },
    { raw: 'justify-center justify-start justify-end justify-between justify-around justify-evenly' },
    { raw: 'flex-col flex-row flex-wrap flex-nowrap' },
    { raw: 'flex-1 flex-auto flex-initial flex-none grow grow-0 shrink shrink-0' },
    { raw: 'w-full h-full min-w-0 min-h-0 max-w-none max-h-none' },
    { raw: 'overflow-hidden overflow-auto overflow-visible overflow-scroll' },
    { raw: 'float-left float-right float-none clear-both clear-left clear-right clear-none' },

    // RESPONSIVE UTILITIES (sm:, md:, lg:, xl:, 2xl:)
    { raw: 'sm:flex md:flex lg:flex xl:flex 2xl:flex' },
    { raw: 'sm:flex-1 md:flex-1 lg:flex-1 xl:flex-1 2xl:flex-1' },
    { raw: 'sm:flex-auto md:flex-auto lg:flex-auto xl:flex-auto 2xl:flex-auto' },
    { raw: 'sm:flex-none md:flex-none lg:flex-none xl:flex-none 2xl:flex-none' },
    { raw: 'sm:block md:block lg:block xl:block 2xl:block' },
    { raw: 'sm:hidden md:hidden lg:hidden xl:hidden 2xl:hidden' },
    { raw: 'sm:inline-block md:inline-block lg:inline-block xl:inline-block 2xl:inline-block' },
    { raw: 'sm:grid md:grid lg:grid xl:grid 2xl:grid' },
    { raw: 'sm:w-full md:w-full lg:w-full xl:w-full 2xl:w-full' },
    { raw: 'sm:h-full md:h-full lg:h-full xl:h-full 2xl:h-full' },

    // RESPONSIVE GRID CLASSES (missing from current config)
    { raw: 'sm:grid-cols-1 sm:grid-cols-2 sm:grid-cols-3 sm:grid-cols-4 sm:grid-cols-5 sm:grid-cols-6' },
    { raw: 'md:grid-cols-1 md:grid-cols-2 md:grid-cols-3 md:grid-cols-4 md:grid-cols-5 md:grid-cols-6' },
    { raw: 'lg:grid-cols-1 lg:grid-cols-2 lg:grid-cols-3 lg:grid-cols-4 lg:grid-cols-5 lg:grid-cols-6' },
    { raw: 'xl:grid-cols-1 xl:grid-cols-2 xl:grid-cols-3 xl:grid-cols-4 xl:grid-cols-5 xl:grid-cols-6' },
    { raw: '2xl:grid-cols-1 2xl:grid-cols-2 2xl:grid-cols-3 2xl:grid-cols-4 2xl:grid-cols-5 2xl:grid-cols-6' },
    
    // GRID POSITIONING CLASSES (for form selectors like interface.html)
    { raw: 'col-start-1 col-start-2 col-start-3 col-end-1 col-end-2 col-end-3' },
    { raw: 'row-start-1 row-start-2 row-start-3 row-end-1 row-end-2 row-end-3' },
    { raw: 'col-span-1 col-span-2 col-span-3 row-span-1 row-span-2 row-span-3' },
    { raw: 'self-center self-start self-end self-stretch self-auto' },
    { raw: 'justify-self-center justify-self-start justify-self-end justify-self-stretch justify-self-auto' },
    
    // Safelist patterns to force ALL standard utilities
    { raw: 'space-y-0.5 space-y-1 space-y-2 space-y-3 space-y-4 space-y-6 space-y-8' },
    { raw: 'space-x-0.5 space-x-1 space-x-2 space-x-3 space-x-4 space-x-6 space-x-8' },
    { raw: 'p-0.5 p-1 p-2 p-3 p-4 p-5 p-6 p-7 p-8 p-9 p-10 p-12 p-16 p-20 p-24' },
    { raw: 'px-0.5 px-1 px-2 px-3 px-4 px-5 px-6 px-8 py-0.5 py-1 py-2 py-3 py-4 py-6 py-8' },
    { raw: 'text-xs text-sm text-base text-lg text-xl text-2xl text-3xl text-4xl' },
    { raw: 'bg-gray-50 bg-gray-100 bg-gray-200 bg-gray-300 bg-gray-400 bg-gray-500' },

    // Finder-v2 widget specific utilities (for success notifications and layout)
    { raw: 'size-5 size-20 shrink-0 sr-only shadow-xs' },
    
    // RESPONSIVE SIZE CLASSES (for interface.html icons)
    { raw: 'sm:size-4 md:size-4 lg:size-4 xl:size-4 2xl:size-4' },
    { raw: 'sm:size-5 md:size-5 lg:size-5 xl:size-5 2xl:size-5' },
    { raw: 'ring-1 ring-inset ring-gray-300' },
    { raw: 'divide-y divide-x divide-gray-200 sm:divide-x sm:divide-y-0' },
    { raw: 'sm:grid-cols-3 sm:items-center sm:justify-between sm:space-x-5 sm:pt-1 sm:mt-0 sm:text-left sm:text-2xl' },
    { raw: 'bg-green-50 text-green-400 text-green-700 text-green-800 border-green-200' },
    { raw: 'bg-red-50 text-red-400 text-red-700 text-red-800 border-red-200' },
    { raw: 'bg-yellow-50 text-yellow-400 text-yellow-700 text-yellow-800 border-yellow-200' },
    { raw: 'bg-blue-50 text-blue-400 text-blue-700 text-blue-800 border-blue-200' },
    { raw: 'mb-6 ml-3 mt-2 rounded-md font-medium' },

    // Widget hierarchy design classes
    { raw: 'w-14 h-14 w-12 h-12 w-8 h-8 w-5 h-5 w-10 h-10 w-11 size-5 size-6 size-11' },
    { raw: 'bg-blue-100 bg-blue-200 bg-blue-600 bg-blue-700 text-blue-100 text-blue-300 text-blue-600 text-blue-700 text-blue-800 text-blue-900' },
    { raw: 'bg-yellow-100 bg-yellow-500 bg-yellow-600 text-yellow-500 text-yellow-600 text-yellow-900' },
    { raw: 'bg-gray-100 bg-gray-200 bg-gray-500 bg-gray-600 text-gray-200 text-gray-300 text-gray-500 text-gray-600 text-gray-700 text-gray-400' },
    { raw: 'border-2 border border-blue-100 border-blue-200 border-blue-300 border-blue-700 border-blue-800 border-yellow-200 border-yellow-300 border-gray-200 border-gray-300 border-green-700' },
    { raw: 'border-dashed rounded-xl rounded-3xl rounded-lg rounded-md shadow-xl hover:shadow-xl hover:shadow-lg shadow-lg shadow-sm shadow-xs' },
    { raw: 'space-y-12 space-y-6 space-y-4 space-y-3 space-y-2 space-x-1 space-x-2 space-x-3 space-x-4 space-x-6' },
    { raw: 'gap-1 gap-2 gap-3 gap-4 gap-6 gap-x-1 gap-x-2 gap-x-3 gap-x-4 gap-x-6 gap-x-12 sm:grid-cols-2' },
    { raw: 'py-12 px-8 py-6 py-4 px-6 py-3 px-4 py-2 py-2.5 px-3.5 pt-8 mt-8 mt-1 mb-4 mb-3 mt-3 mt-6 -m-1.5 -m-2.5 p-1.5 p-2.5 -mx-3 px-3 ml-6 mr-1 mr-2 mr-6' },
    
    // ADDITIONAL PADDING CLASSES (for form elements like interface.html)
    { raw: 'pr-8 pl-2 pl-4 pl-5 pr-2 pr-3 pr-4 pr-5 pr-6 pr-7 pr-9 pr-10' },
    { raw: 'bg-opacity-20 bg-opacity-25 opacity-80 opacity-70 opacity-0 cursor-not-allowed invisible visible' },
    { raw: 'hover:border-blue-300 hover:bg-yellow-600 hover:bg-gray-500 hover:bg-blue-700 hover:bg-gray-50 hover:text-ws-primary-700 hover:text-ws-primary-800 hover:bg-ws-primary-800 hover:-translate-y-0.5' },
    { raw: 'text-2xl text-xl text-lg text-sm text-xs text-base font-bold font-semibold font-medium' },
    
    // LINE HEIGHT CLASSES (for interface.html form styling)
    { raw: 'text-sm/6 sm:text-sm/6 text-base/6 text-lg/6' },
    { raw: 'bg-green-400 bg-green-500 bg-green-600 text-green-900 bg-blue-400 text-blue-900 text-4xl' },
    { raw: 'text-white text-gray-800 text-gray-900 text-ws-primary-700 text-ws-primary-600 text-ws-secondary-600' },
    { raw: 'bg-ws-primary-700 bg-ws-primary-800 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-ws-primary-600' },
    { raw: 'transition-all transition-colors duration-200 group group-hover:opacity-100 group-hover:visible group-hover:bg-white group-hover:text-ws-primary-600' },
    { raw: 'group-hover:text-blue-700 transform' },

    // HEADER & NAVIGATION CLASSES
    { raw: 'lg:gap-x-12 lg:flex-1 lg:justify-end lg:px-8 lg:hidden lg:flex' },
    { raw: 'ring-1 ring-gray-900/5 sm:ring-1 sm:ring-gray-900/10 sm:max-w-sm' },
    { raw: 'inset-y-0 right-0 max-w-md flex-none flex-auto sr-only' },
    { raw: 'stroke-linecap-round stroke-linejoin-round fill-rule-evenodd clip-rule-evenodd' },
    { raw: 'fixed z-50 overflow-y-auto divide-y divide-gray-500/10 -my-6' },

    // SVG & STROKE CLASSES (for icons and graphics)
    { raw: 'stroke-1 stroke-2 stroke-3 stroke-4 stroke-current fill-none fill-current' },
    { raw: 'viewBox stroke-linecap stroke-linejoin stroke-width-1 stroke-width-2' },

    // ADDITIONAL LAYOUT CLASSES (commonly used in templates)
    { raw: 'outline-none focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1' },
    { raw: 'cursor-pointer select-none pointer-events-none user-select-none' },
    
    // FORM & SELECTOR CLASSES (essential for proper form styling)
    { raw: 'appearance-none resize-none resize resize-x resize-y' },
    { raw: 'min-h-0 max-h-screen min-w-max max-w-screen-sm max-w-screen-md max-w-screen-lg' },
    { raw: 'text-left text-center text-right text-justify' },
    { raw: 'leading-none leading-tight leading-snug leading-normal leading-relaxed' },
    { raw: 'tracking-tight tracking-wide tracking-wider tracking-widest' },
    { raw: 'whitespace-nowrap whitespace-pre-wrap break-words break-all' },
  ],



  // Add missing colors for TailwindCSS v4.1
  theme: {
    extend: {
      colors: {
        purple: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        red: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        blue: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        green: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        yellow: {
          50: '#fefce8',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        indigo: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
        },
        pink: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        }
      }
    }
  },

  // Full safelist for production convenience
  safelist: [
    // Use shared production patterns for consistency
    ...productionPatterns,
    
    // Explicit purple utility classes (TailwindCSS v4.1 pattern matching is unreliable)
    'border-purple-200', 'border-purple-300', 'border-purple-600', 'border-purple-700',
    'text-purple-600', 'text-purple-700', 'text-purple-800', 'text-purple-900',
    'bg-purple-50', 'bg-purple-100', 'bg-purple-200', 'bg-purple-300', 'bg-purple-400', 'bg-purple-500', 'bg-purple-600', 'bg-purple-700', 'bg-purple-800', 'bg-purple-900',
    'hover:border-transparent', 'hover:bg-purple-600', 'hover:text-white', 'active:bg-purple-700',
    
    // Transparent borders and hover states
    'border-transparent', 'hover:border-transparent', 'hover:text-white', 
    'active:bg-purple-700', 'hover:bg-purple-600',
    
    // Other color utilities that might be missing
    'border-red-200', 'border-red-300', 'border-red-600', 'border-red-700',
    'text-red-600', 'text-red-700', 'bg-red-50', 'bg-red-100', 'bg-red-200', 'bg-red-600', 'bg-red-700',
    'border-blue-200', 'border-blue-300', 'border-blue-600', 'border-blue-700',
    'text-blue-600', 'text-blue-700', 'bg-blue-50', 'bg-blue-100', 'bg-blue-200', 'bg-blue-600', 'bg-blue-700',
    'border-green-200', 'border-green-300', 'border-green-600', 'border-green-700',
    'text-green-600', 'text-green-700', 'bg-green-50', 'bg-green-100', 'bg-green-200', 'bg-green-600', 'bg-green-700',
    
    // Hover and active states
    'hover:border-transparent', 'hover:text-white',
    
    // Brand colors (from original config)
    'bg-ws-primary-50', 'bg-ws-primary-100', 'bg-ws-primary-200', 'bg-ws-primary-300', 'bg-ws-primary-400', 'bg-ws-primary-500', 'bg-ws-primary-600', 'bg-ws-primary-700', 'bg-ws-primary-800', 'bg-ws-primary-900',
    'text-ws-primary-50', 'text-ws-primary-100', 'text-ws-primary-200', 'text-ws-primary-300', 'text-ws-primary-400', 'text-ws-primary-500', 'text-ws-primary-600', 'text-ws-primary-700', 'text-ws-primary-800', 'text-ws-primary-900'
  ],

  // Enable dark mode
  darkMode: 'class'
} 