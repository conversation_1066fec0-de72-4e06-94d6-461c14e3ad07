/** @type {import('tailwindcss').Config} */
export default {
  content: [
    // Development: Include ALL possible content to force generation
    "../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}",
    "../../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}",
    "../js/**/*.{js,ts}",
    "../../../**/*.{html,js,ts,jsx,tsx,vue}",
    
    // Force generation by including a wide pattern
    "./**/*.{html,js,ts,jsx,tsx,vue,php,twig,erb,blade}",
  ],



  // Add missing colors for TailwindCSS v4.1
  theme: {
    extend: {
      colors: {
        purple: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        red: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        blue: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        green: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        yellow: {
          50: '#fefce8',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        indigo: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
        },
        pink: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        }
      }
    }
  },

  // Minimal safelist - let JIT do the work for development
  safelist: [
    // Only dynamic patterns that cannot be detected statically
    { pattern: /^grid-cols-\d+$/ },
    { pattern: /^w-\[.*\]$/ },
    { pattern: /^h-\[.*\]$/ },

    // ESSENTIAL LAYOUT & POSITIONING CLASSES (for headers, selectors, etc.)
    'inset-x-0', 'inset-y-0', 'inset-0', 'top-0', 'bottom-0', 'left-0', 'right-0',
    'z-10', 'z-20', 'z-30', 'z-40', 'z-50', 'z-auto',
    'absolute', 'relative', 'fixed', 'sticky', 'static',
    'flex', 'inline-flex', 'grid', 'inline-grid', 'block', 'inline-block', 'hidden',
    'items-center', 'items-start', 'items-end', 'items-stretch', 'items-baseline',
    'justify-center', 'justify-start', 'justify-end', 'justify-between', 'justify-around', 'justify-evenly',
    'flex-col', 'flex-row', 'flex-wrap', 'flex-nowrap',
    'flex-1', 'flex-auto', 'flex-initial', 'flex-none', 'grow', 'grow-0', 'shrink', 'shrink-0',
    'w-full', 'h-full', 'min-w-0', 'min-h-0', 'max-w-none', 'max-h-none',
    'overflow-hidden', 'overflow-auto', 'overflow-visible', 'overflow-scroll',
    'float-left', 'float-right', 'float-none', 'clear-both', 'clear-left', 'clear-right', 'clear-none',

    // RESPONSIVE UTILITIES (sm:, md:, lg:, xl:, 2xl:)
    'sm:flex', 'md:flex', 'lg:flex', 'xl:flex', '2xl:flex',
    'sm:flex-1', 'md:flex-1', 'lg:flex-1', 'xl:flex-1', '2xl:flex-1',
    'sm:flex-auto', 'md:flex-auto', 'lg:flex-auto', 'xl:flex-auto', '2xl:flex-auto',
    'sm:flex-none', 'md:flex-none', 'lg:flex-none', 'xl:flex-none', '2xl:flex-none',
    'sm:block', 'md:block', 'lg:block', 'xl:block', '2xl:block',
    'sm:hidden', 'md:hidden', 'lg:hidden', 'xl:hidden', '2xl:hidden',
    'sm:inline-block', 'md:inline-block', 'lg:inline-block', 'xl:inline-block', '2xl:inline-block',
    'sm:grid', 'md:grid', 'lg:grid', 'xl:grid', '2xl:grid',
    'sm:w-full', 'md:w-full', 'lg:w-full', 'xl:w-full', '2xl:w-full',
    'sm:h-full', 'md:h-full', 'lg:h-full', 'xl:h-full', '2xl:h-full',

    // RESPONSIVE GRID CLASSES (missing from current config)
    'sm:grid-cols-1', 'sm:grid-cols-2', 'sm:grid-cols-3', 'sm:grid-cols-4', 'sm:grid-cols-5', 'sm:grid-cols-6',
    'md:grid-cols-1', 'md:grid-cols-2', 'md:grid-cols-3', 'md:grid-cols-4', 'md:grid-cols-5', 'md:grid-cols-6',
    'lg:grid-cols-1', 'lg:grid-cols-2', 'lg:grid-cols-3', 'lg:grid-cols-4', 'lg:grid-cols-5', 'lg:grid-cols-6',
    'xl:grid-cols-1', 'xl:grid-cols-2', 'xl:grid-cols-3', 'xl:grid-cols-4', 'xl:grid-cols-5', 'xl:grid-cols-6',
    '2xl:grid-cols-1', '2xl:grid-cols-2', '2xl:grid-cols-3', '2xl:grid-cols-4', '2xl:grid-cols-5', '2xl:grid-cols-6',
    
    // GRID POSITIONING CLASSES (for form selectors like interface.html)
    'col-start-1', 'col-start-2', 'col-start-3', 'col-end-1', 'col-end-2', 'col-end-3',
    'row-start-1', 'row-start-2', 'row-start-3', 'row-end-1', 'row-end-2', 'row-end-3',
    'col-span-1', 'col-span-2', 'col-span-3', 'row-span-1', 'row-span-2', 'row-span-3',
    'self-center', 'self-start', 'self-end', 'self-stretch', 'self-auto',
    'justify-self-center', 'justify-self-start', 'justify-self-end', 'justify-self-stretch', 'justify-self-auto',

    // Finder-v2 widget specific utilities (for success notifications and layout)
    'size-5', 'size-20', 'shrink-0', 'sr-only', 'shadow-xs',
    
    // RESPONSIVE SIZE CLASSES (for interface.html icons)
    'sm:size-4', 'md:size-4', 'lg:size-4', 'xl:size-4', '2xl:size-4',
    'sm:size-5', 'md:size-5', 'lg:size-5', 'xl:size-5', '2xl:size-5',
    'ring-1', 'ring-inset', 'ring-gray-300',
    'divide-y', 'divide-x', 'divide-gray-200', 'sm:divide-x', 'sm:divide-y-0',
    'sm:grid-cols-3', 'sm:items-center', 'sm:justify-between', 'sm:space-x-5', 'sm:pt-1', 'sm:mt-0', 'sm:text-left', 'sm:text-2xl',
    'bg-green-50', 'text-green-400', 'text-green-700', 'text-green-800', 'border-green-200',
    'bg-red-50', 'text-red-400', 'text-red-700', 'text-red-800', 'border-red-200',
    'bg-yellow-50', 'text-yellow-400', 'text-yellow-700', 'text-yellow-800', 'border-yellow-200',
    'bg-blue-50', 'text-blue-400', 'text-blue-700', 'text-blue-800', 'border-blue-200',
    'mb-6', 'ml-3', 'mt-2', 'rounded-md', 'font-medium',

    // Widget hierarchy design classes
    'w-14', 'h-14', 'w-12', 'h-12', 'w-8', 'h-8', 'w-5', 'h-5', 'w-10', 'h-10', 'w-11', 'size-5', 'size-6', 'size-11',
    'bg-blue-100', 'bg-blue-200', 'bg-blue-600', 'bg-blue-700', 'text-blue-100', 'text-blue-300', 'text-blue-600', 'text-blue-700', 'text-blue-800', 'text-blue-900',
    'bg-yellow-100', 'bg-yellow-500', 'bg-yellow-600', 'text-yellow-500', 'text-yellow-600', 'text-yellow-900',
    'bg-gray-100', 'bg-gray-200', 'bg-gray-500', 'bg-gray-600', 'text-gray-200', 'text-gray-300', 'text-gray-500', 'text-gray-600', 'text-gray-700', 'text-gray-400',
    'border-2', 'border', 'border-blue-100', 'border-blue-200', 'border-blue-300', 'border-blue-700', 'border-blue-800', 'border-yellow-200', 'border-yellow-300', 'border-gray-200', 'border-gray-300', 'border-green-700',
    'border-dashed', 'rounded-xl', 'rounded-3xl', 'rounded-lg', 'rounded-md', 'shadow-xl', 'hover:shadow-xl', 'hover:shadow-lg', 'shadow-lg', 'shadow-sm', 'shadow-xs',
    'space-y-12', 'space-y-6', 'space-y-4', 'space-y-3', 'space-y-2', 'space-x-1', 'space-x-2', 'space-x-3', 'space-x-4', 'space-x-6',
    'gap-1', 'gap-2', 'gap-3', 'gap-4', 'gap-6', 'gap-x-1', 'gap-x-2', 'gap-x-3', 'gap-x-4', 'gap-x-6', 'gap-x-12', 'sm:grid-cols-2',
    'py-12', 'px-8', 'py-6', 'py-4', 'px-6', 'py-3', 'px-4', 'py-2', 'py-2.5', 'px-3.5', 'pt-8', 'mt-8', 'mt-1', 'mb-4', 'mb-3', 'mt-3', 'mt-6', '-m-1.5', '-m-2.5', 'p-1.5', 'p-2.5', '-mx-3', 'px-3', 'ml-6', 'mr-1', 'mr-2', 'mr-6',
    
    // ADDITIONAL PADDING CLASSES (for form elements like interface.html)
    'pr-8', 'pl-2', 'pl-4', 'pl-5', 'pr-2', 'pr-3', 'pr-4', 'pr-5', 'pr-6', 'pr-7', 'pr-9', 'pr-10',
    'bg-opacity-20', 'bg-opacity-25', 'opacity-80', 'opacity-70', 'opacity-0', 'cursor-not-allowed', 'invisible', 'visible',
    'hover:border-blue-300', 'hover:bg-yellow-600', 'hover:bg-gray-500', 'hover:bg-blue-700', 'hover:bg-gray-50', 'hover:text-ws-primary-700', 'hover:text-ws-primary-800', 'hover:bg-ws-primary-800',
    'text-2xl', 'text-xl', 'text-lg', 'text-sm', 'text-xs', 'text-base', 'font-bold', 'font-semibold', 'font-medium',
    
    // LINE HEIGHT CLASSES (for interface.html form styling)
    'text-sm/6', 'sm:text-sm/6', 'text-base/6', 'text-lg/6',
    'bg-green-400', 'bg-green-500', 'bg-green-600', 'text-green-900', 'bg-blue-400', 'text-blue-900', 'text-4xl',
    'text-white', 'text-gray-800', 'text-gray-900', 'text-ws-primary-700', 'text-ws-primary-600', 'text-ws-secondary-600',
    'bg-ws-primary-700', 'bg-ws-primary-800', 'focus-visible:outline-2', 'focus-visible:outline-offset-2', 'focus-visible:outline-ws-primary-600',
    'transition-all', 'transition-colors', 'duration-200', 'group', 'group-hover:opacity-100', 'group-hover:visible', 'group-hover:bg-white', 'group-hover:text-ws-primary-600',
    'group-hover:text-blue-700', 'hover:-translate-y-0.5', 'transform', 'opacity-0',

    // HEADER & NAVIGATION CLASSES
    'lg:gap-x-12', 'lg:flex-1', 'lg:justify-end', 'lg:px-8', 'lg:hidden', 'lg:flex',
    'ring-1', 'ring-gray-900/5', 'sm:ring-1', 'sm:ring-gray-900/10', 'sm:max-w-sm',
    'inset-y-0', 'right-0', 'max-w-md', 'flex-none', 'flex-auto', 'sr-only',
    'stroke-linecap-round', 'stroke-linejoin-round', 'fill-rule-evenodd', 'clip-rule-evenodd',
    'fixed', 'z-50', 'overflow-y-auto', 'divide-y', 'divide-gray-500/10', '-my-6',

    // SVG & STROKE CLASSES (for icons and graphics)
    'stroke-1', 'stroke-2', 'stroke-3', 'stroke-4', 'stroke-current', 'fill-none', 'fill-current',
    'viewBox', 'stroke-linecap', 'stroke-linejoin', 'stroke-width-1', 'stroke-width-2',

    // ADDITIONAL LAYOUT CLASSES (commonly used in templates)
    'outline-none', 'focus:outline-none', 'focus:ring-2', 'focus:ring-indigo-500', 'focus:ring-offset-1',
    'cursor-pointer', 'select-none', 'pointer-events-none', 'user-select-none',
    
    // FORM & SELECTOR CLASSES (essential for proper form styling)
    'appearance-none', 'resize-none', 'resize', 'resize-x', 'resize-y',
    'min-h-0', 'max-h-screen', 'min-w-max', 'max-w-screen-sm', 'max-w-screen-md', 'max-w-screen-lg',
    'text-left', 'text-center', 'text-right', 'text-justify',
    'leading-none', 'leading-tight', 'leading-snug', 'leading-normal', 'leading-relaxed',
    'tracking-tight', 'tracking-wide', 'tracking-wider', 'tracking-widest',
    'whitespace-nowrap', 'whitespace-pre-wrap', 'break-words', 'break-all',

    // Brand colors (from original config) - these are custom and may not be detected
    'bg-ws-primary-50', 'bg-ws-primary-100', 'bg-ws-primary-200', 'bg-ws-primary-300', 'bg-ws-primary-400', 'bg-ws-primary-500', 'bg-ws-primary-600', 'bg-ws-primary-700', 'bg-ws-primary-800', 'bg-ws-primary-900',
    'text-ws-primary-50', 'text-ws-primary-100', 'text-ws-primary-200', 'text-ws-primary-300', 'text-ws-primary-400', 'text-ws-primary-500', 'text-ws-primary-600', 'text-ws-primary-700', 'text-ws-primary-800', 'text-ws-primary-900',
    'border-ws-primary-50', 'border-ws-primary-100', 'border-ws-primary-200', 'border-ws-primary-300', 'border-ws-primary-400', 'border-ws-primary-500', 'border-ws-primary-600', 'border-ws-primary-700', 'border-ws-primary-800', 'border-ws-primary-900',
    
    'bg-ws-secondary-50', 'bg-ws-secondary-100', 'bg-ws-secondary-200', 'bg-ws-secondary-300', 'bg-ws-secondary-400', 'bg-ws-secondary-500', 'bg-ws-secondary-600', 'bg-ws-secondary-700', 'bg-ws-secondary-800', 'bg-ws-secondary-900',
    'text-ws-secondary-50', 'text-ws-secondary-100', 'text-ws-secondary-200', 'text-ws-secondary-300', 'text-ws-secondary-400', 'text-ws-secondary-500', 'text-ws-secondary-600', 'text-ws-secondary-700', 'text-ws-secondary-800', 'text-ws-secondary-900',
    'border-ws-secondary-50', 'border-ws-secondary-100', 'border-ws-secondary-200', 'border-ws-secondary-300', 'border-ws-secondary-400', 'border-ws-secondary-500', 'border-ws-secondary-600', 'border-ws-secondary-700', 'border-ws-secondary-800', 'border-ws-secondary-900',
    
    // State colors
    'bg-success-50', 'bg-success-100', 'bg-success-200', 'bg-success-300', 'bg-success-400', 'bg-success-500', 'bg-success-600', 'bg-success-700', 'bg-success-800', 'bg-success-900',
    'text-success-50', 'text-success-100', 'text-success-200', 'text-success-300', 'text-success-400', 'text-success-500', 'text-success-600', 'text-success-700', 'text-success-800', 'text-success-900',
    'border-success-50', 'border-success-100', 'border-success-200', 'border-success-300', 'border-success-400', 'border-success-500', 'border-success-600', 'border-success-700', 'border-success-800', 'border-success-900',
    
    'bg-warning-50', 'bg-warning-100', 'bg-warning-200', 'bg-warning-300', 'bg-warning-400', 'bg-warning-500', 'bg-warning-600', 'bg-warning-700', 'bg-warning-800', 'bg-warning-900',
    'text-warning-50', 'text-warning-100', 'text-warning-200', 'text-warning-300', 'text-warning-400', 'text-warning-500', 'text-warning-600', 'text-warning-700', 'text-warning-800', 'text-warning-900',
    'border-warning-50', 'border-warning-100', 'border-warning-200', 'border-warning-300', 'border-warning-400', 'border-warning-500', 'border-warning-600', 'border-warning-700', 'border-warning-800', 'border-warning-900',
    
    'bg-danger-50', 'bg-danger-100', 'bg-danger-200', 'bg-danger-300', 'bg-danger-400', 'bg-danger-500', 'bg-danger-600', 'bg-danger-700', 'bg-danger-800', 'bg-danger-900',
    'text-danger-50', 'text-danger-100', 'text-danger-200', 'text-danger-300', 'text-danger-400', 'text-danger-500', 'text-danger-600', 'text-danger-700', 'text-danger-800', 'text-danger-900',
    'border-danger-50', 'border-danger-100', 'border-danger-200', 'border-danger-300', 'border-danger-400', 'border-danger-500', 'border-danger-600', 'border-danger-700', 'border-danger-800', 'border-danger-900',
  ],

  // Enable dark mode
  darkMode: 'class'
} 