/**
 * Shared TailwindCSS Configuration for Wheel-Size Services
 * 
 * This module provides common configuration elements that should be
 * consistent across both Portal and Widget TailwindCSS builds.
 */

// Brand color palette - consistent across all builds
export const brandColors = {
  'ws-primary': {
    50: '#f0f9ff',
    100: '#e0f2fe', 
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  'ws-secondary': {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  }
};

// State colors for consistent UI feedback
export const stateColors = {
  success: {
    50: '#f0fdf4', 100: '#dcfce7', 200: '#bbf7d0', 300: '#86efac', 400: '#4ade80',
    500: '#22c55e', 600: '#16a34a', 700: '#15803d', 800: '#166534', 900: '#14532d'
  },
  warning: {
    50: '#fefce8', 100: '#fef3c7', 200: '#fde68a', 300: '#fcd34d', 400: '#fbbf24',
    500: '#f59e0b', 600: '#d97706', 700: '#b45309', 800: '#92400e', 900: '#78350f'
  },
  danger: {
    50: '#fef2f2', 100: '#fee2e2', 200: '#fecaca', 300: '#fca5a5', 400: '#f87171',
    500: '#ef4444', 600: '#dc2626', 700: '#b91c1c', 800: '#991b1b', 900: '#7f1d1d'
  }
};

// Essential responsive display utilities that should always be available
export const responsiveDisplayUtilities = [
  // Base display utilities
  'block', 'inline-block', 'inline', 'flex', 'inline-flex', 'grid', 'inline-grid', 'hidden',
  
  // Small screens (sm: 640px+)
  'sm:block', 'sm:inline-block', 'sm:inline', 'sm:flex', 'sm:inline-flex', 'sm:grid', 'sm:inline-grid', 'sm:hidden',
  
  // Medium screens (md: 768px+)
  'md:block', 'md:inline-block', 'md:inline', 'md:flex', 'md:inline-flex', 'md:grid', 'md:inline-grid', 'md:hidden',
  
  // Large screens (lg: 1024px+)
  'lg:block', 'lg:inline-block', 'lg:inline', 'lg:flex', 'lg:inline-flex', 'lg:grid', 'lg:inline-grid', 'lg:hidden',
  
  // Extra large screens (xl: 1280px+)
  'xl:block', 'xl:inline-block', 'xl:inline', 'xl:flex', 'xl:inline-flex', 'xl:grid', 'xl:inline-grid', 'xl:hidden',
  
  // 2XL screens (2xl: 1536px+)
  '2xl:block', '2xl:inline-block', '2xl:inline', '2xl:flex', '2xl:inline-flex', '2xl:grid', '2xl:inline-grid', '2xl:hidden'
];

// Common layout utilities
export const layoutUtilities = [
  'flex-row', 'flex-row-reverse', 'flex-col', 'flex-col-reverse', 
  'flex-wrap', 'flex-wrap-reverse', 'flex-nowrap',
  'items-start', 'items-end', 'items-center', 'items-baseline', 'items-stretch',
  'justify-start', 'justify-end', 'justify-center', 'justify-between', 'justify-around', 'justify-evenly'
];

// Generate brand color utilities for safelist
export const generateBrandColorUtilities = () => {
  const utilities = [];
  const prefixes = ['bg', 'text', 'border'];
  const colors = ['ws-primary', 'ws-secondary', 'success', 'warning', 'danger'];
  const shades = ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900'];
  
  for (const prefix of prefixes) {
    for (const color of colors) {
      for (const shade of shades) {
        utilities.push(`${prefix}-${color}-${shade}`);
      }
    }
  }
  
  return utilities;
};

// Regex patterns for production builds
export const productionPatterns = [
  // Colors
  {pattern: /^(bg|text|border)-(gray|red|blue|green|yellow|purple|pink|indigo|ws-primary|ws-secondary|success|warning|danger)-(50|100|200|300|400|500|600|700|800|900)$/},
  
  // Spacing
  {pattern: /^(p|px|py|pt|pr|pb|pl|m|mx|my|mt|mr|mb|ml)-(0|px|0\.5|1|1\.5|2|2\.5|3|3\.5|4|5|6|7|8|9|10|11|12|14|16|20|24|28|32)$/},
  
  // Space between
  {pattern: /^(space-x|space-y)-(0|px|0\.5|1|1\.5|2|2\.5|3|3\.5|4|5|6|7|8|9|10|11|12|14|16|20|24)$/},
  
  // Sizing
  {pattern: /^(w|h)-(0|px|0\.5|1|1\.5|2|2\.5|3|3\.5|4|5|6|8|10|12|16|20|24|32|40|48|56|64|72|80|96|auto|full|screen)$/},
  {pattern: /^(w|h)-(1\/2|1\/3|2\/3|1\/4|2\/4|3\/4|1\/5|2\/5|3\/5|4\/5|1\/6|2\/6|3\/6|4\/6|5\/6)$/},
  
  // Typography
  {pattern: /^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl)$/},
  {pattern: /^font-(thin|light|normal|medium|semibold|bold|extrabold|black)$/},
  
  // Display utilities (base and responsive)
  {pattern: /^(flex|inline-flex|grid|inline-grid|block|inline-block|inline|hidden)$/},
  {pattern: /^(sm|md|lg|xl|2xl):(flex|inline-flex|grid|inline-grid|block|inline-block|inline|hidden)$/},
  
  // Layout
  {pattern: /^(items|justify)-(start|end|center|between|around|evenly|stretch|baseline)$/},
  
  // Borders and effects
  {pattern: /^(border|rounded).*$/},
  {pattern: /^(shadow|transition|duration|ease).*$/},
  
  // Interactive states
  {pattern: /^(hover|focus|active):.*$/}
];

// Common breakpoints - ensure consistency
export const breakpoints = {
  sm: '640px',
  md: '768px', 
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};
