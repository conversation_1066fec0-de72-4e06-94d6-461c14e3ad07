import time

import boto3
from botocore.exceptions import ClientError

from django.core.management.base import BaseCommand, CommandError


CLOUDFRONT_DISTRIBUTION_ID = 'E2DVLUVPXKS73X'


class Command(BaseCommand):
    help = 'Invalidate AWS CloudFront Distribution Paths'

    def add_arguments(self, parser):
        parser.add_argument('-s', '--static',
                            action='store_true', dest='static', default=False,
                            help='Invalidate all static files')
        parser.add_argument('-p', '--path', help='Invalidate specific path, e.g.: "/widget/*" ')

    def handle(self, *args, **options):

        paths = self.get_invalidation_paths(options['static'], options['path'])

        client = boto3.client('cloudfront')

        try:
            response = client.create_invalidation(
                DistributionId=CLOUDFRONT_DISTRIBUTION_ID,
                InvalidationBatch={
                    'Paths': {
                        'Quantity': len(paths),
                        'Items': paths
                    },
                    'CallerReference': str(time.time())
                }
            )
        except ClientError as e:
            raise CommandError(e.response['Error']['Code'] + ': ' + e.response['Error']['Message'])
        except Exception as exc:
            CommandError(str(exc))
        else:
            self.stdout.write('Invalidation Request ID:  %s' % response['Invalidation']['Id'])
            self.stdout.write('Invalidation paths:')

            for path in response['Invalidation']['InvalidationBatch']['Paths']['Items']:
                self.stdout.write(path)

    def get_invalidation_paths(self, is_static, path):
        ret = []

        if is_static:
            ret.extend(['/static/*', '/code/*'])
        if path and path not in ret:
            ret.append(path)

        return ret
