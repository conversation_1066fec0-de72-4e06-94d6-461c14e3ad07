from collections import namedtuple, OrderedDict

from django.http import Http404
from django.views.generic import DetailView
from django.utils.translation import gettext_lazy as _


Page = namedtuple('Page', ['index', 'name', 'title'])


class ApiTutorialView(DetailView):
    context_object_name = 'page'

    PAGES = (
        ('', ''),
        ('overview', 'Overview'),
        ('makes', 'List of Manufacturers'),
        ('models', 'List of Models'),
        ('model', 'Detailed Info About Model'),
        ('model-year', 'Detailed Info About Model for Specified Year'),
        ('bolt-patterns', 'List of Bolt Patterns'),
        ('bolt-pattern', 'Models Matching Given Bolt Pattern'),
        ('tires', 'List of Tires'),
        ('tire', 'Models Matching Given Tire'),
        ('search-tire', 'Search By Tire'),
        ('search-rim', 'Search By Rim'),
        ('search-model', 'Search by Model'),
        ('', ''),
    )

    TUTORIAL_PAGES = OrderedDict(
        (page[0], dict(index=index, name=page[0], title=_(page[1])))
        for index, page in enumerate(PAGES) if page[0]
    )

    def get_object(self, queryset=None):
        page_name = self.kwargs['page'] or 'overview'

        if page_name not in self.TUTORIAL_PAGES:
            raise Http404

        page = self.TUTORIAL_PAGES[page_name]
        page['prev'] = self.TUTORIAL_PAGES.get(self.PAGES[page['index'] - 1][0], None)
        page['next'] = self.TUTORIAL_PAGES.get(self.PAGES[page['index'] + 1][0], None)
        return page

    def get_template_names(self):
        return ['api/tutorial/tutorial-%s.html' % self.object['name']]


