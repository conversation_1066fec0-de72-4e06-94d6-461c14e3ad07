/**
 * Unified TailwindCSS Theme Configuration
 * Shared across Portal and Widget Components
 * 
 * This file provides the single source of truth for:
 * - Color palette
 * - Typography
 * - Spacing
 * - Component styling
 */

export const colors = {
  // Brand Colors - Wheel-Size.com theme
  'ws-primary': {
    50:  '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#be3e1d',  // Main brand color
    800: '#991b1b',
    900: '#7f1d1d',
  },
  
  // Secondary Colors (neutral grays)
  'ws-secondary': {
    50:  '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },

  // Standard Colors for utility classes
  gray: {
    50:  '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },

  // Semantic Colors
  success: {
    50:  '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },

  warning: {
    50:  '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },

  danger: {
    50:  '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },

  // Basic colors
  white: '#ffffff',
  black: '#000000',
};

export const fontFamily = {
  sans: ['"Open Sans"', 'system-ui', '-apple-system', 'sans-serif'],
  heading: ['Rambla', 'system-ui', '-apple-system', 'sans-serif'],
  accent: ['Calligraffitti', 'cursive'],
};

export const spacing = {
  xs: '0.25rem',  // 4px
  sm: '0.5rem',   // 8px
  md: '1rem',     // 16px
  lg: '1.5rem',   // 24px
  xl: '2rem',     // 32px
  '2xl': '3rem',  // 48px
  '3xl': '4rem',  // 64px
};

export const borderRadius = {
  sm: '0.125rem',  // 2px
  md: '0.375rem',  // 6px
  lg: '0.5rem',    // 8px
  xl: '0.75rem',   // 12px
};

export const boxShadow = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
};

export const container = {
  maxWidth: '1200px',
  contentMaxWidth: '800px',
};

// Safelist patterns for JIT auto-generation
export const safelist = [
  // Generate all color utilities for brand colors
  { pattern: /^(bg|text|border)-ws-(primary|secondary)-(50|100|200|300|400|500|600|700|800|900)$/ },
  
  // Generate semantic color utilities
  { pattern: /^(bg|text|border)-(success|warning|danger)-(50|100|200|300|400|500|600|700|800|900)$/ },
  
  // Generate standard gray utilities
  { pattern: /^(bg|text|border)-gray-(50|100|200|300|400|500|600|700|800|900)$/ },
  
  // Generate common spacing utilities
  { pattern: /^(p|m)(t|r|b|l|x|y)?-(0|1|2|3|4|5|6|8|10|12|16|20|24|32|40|48|56|64)$/ },
  
  // Generate hover states for brand colors
  { pattern: /^hover:(bg|text|border)-ws-(primary|secondary)-(50|100|200|300|400|500|600|700|800|900)$/ },
  
  // Generate focus states
  { pattern: /^focus:(bg|text|border|ring)-ws-(primary|secondary)-(50|100|200|300|400|500|600|700|800|900)$/ },
];

// Content paths for both portal and widgets
export const contentPaths = [
  // Portal templates
  "../../templates/**/*.html",
  "../../../templates/**/*.html",
  "../js/**/*.js",
  
  // Widget templates (all widgets)
  "../../../widgets/**/templates/**/*.html",
  
  // Vue components (finder-v2)
  "../../../../widgets/finder_v2/app/src/**/*.{vue,js,ts}",
  
  // Test files
  "../../../../../tests/**/*.html",
  "../../../../../test-*.html",
];

// Complete theme export for Tailwind configs
export const tailwindTheme = {
  extend: {
    colors,
    fontFamily,
    spacing,
    borderRadius,
    boxShadow,
    maxWidth: {
      container: container.maxWidth,
      content: container.contentMaxWidth,
    }
  }
};

// CSS custom properties for @theme directive
export const cssTheme = Object.entries(colors).reduce((acc, [colorName, colorValues]) => {
  if (typeof colorValues === 'object') {
    Object.entries(colorValues).forEach(([shade, value]) => {
      acc[`--color-${colorName}-${shade}`] = value;
    });
  } else {
    acc[`--color-${colorName}`] = colorValues;
  }
  return acc;
}, {
  // Typography
  '--font-family-sans': fontFamily.sans.join(', '),
  '--font-family-heading': fontFamily.heading.join(', '),
  '--font-family-accent': fontFamily.accent.join(', '),
  
  // Spacing
  ...Object.entries(spacing).reduce((acc, [key, value]) => {
    acc[`--spacing-${key}`] = value;
    return acc;
  }, {}),
  
  // Border Radius
  ...Object.entries(borderRadius).reduce((acc, [key, value]) => {
    acc[`--radius-${key}`] = value;
    return acc;
  }, {}),
  
  // Shadows
  ...Object.entries(boxShadow).reduce((acc, [key, value]) => {
    acc[`--shadow-${key}`] = value;
    return acc;
  }, {}),
  
  // Layout
  '--max-width-container': container.maxWidth,
  '--max-width-content': container.contentMaxWidth,
});

export default {
  colors,
  fontFamily,
  spacing,
  borderRadius,
  boxShadow,
  container,
  safelist,
  contentPaths,
  tailwindTheme,
  cssTheme,
}; 