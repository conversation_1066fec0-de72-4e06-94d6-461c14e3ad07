# CLAUDE.md
Last Modified: 2025-01-28 17:00 UTC+6

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Docker Environment

### Container Configuration
- **Container Name**: `ws_services` (Django application)
- **Base Image**: Amazon Linux with Python 3.12.0 via pyenv
- **Working Directory**: `/code`
- **Python Path**: `/root/.pyenv/versions/3.12.0/bin/python`
- **Package Manager**: Poetry (system-wide installation)
- **Private PyPI**: https://pypi.wheel-size.com/ (for WS packages)

### Django Settings Modules
- **Development**: `src.settings.dev_docker` (PostgreSQL, full integration)
- **Testing**: `src.settings.test` (SQLite in-memory, optimized for speed)
- **Production**: `src.settings.aws_prod` (AWS production configuration)

### Environment Variables
```bash
# Automatically configured in container
DJANGO_SETTINGS_MODULE=src.settings.dev_docker
PYTHONPATH=/code:/code/src
DISABLE_LESS_COMPILATION=true  # For testing
```

## Common Development Commands

### Docker-Based Development (Current Setup)
```bash
# Start development environment
docker-compose up -d

# Check container status
docker ps --filter name=ws_services

# Access container shell
docker exec -it ws_services bash

# View application logs
docker logs ws_services --tail 50 -f

# Django management commands (in container)
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py migrate
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py makemigrations
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py shell
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py collectstatic --noinput
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py ws_load

# Restart container after dependency changes
docker-compose build web && docker-compose up -d
```

### Poetry Commands (In Container)
```bash
# Access container and use Poetry
docker exec -it ws_services bash

# Inside container:
cd /code
export PATH=/root/.pyenv/versions/3.12.0/bin:$PATH
poetry install
poetry add package-name
poetry remove package-name
poetry show --outdated
```

### Frontend Development (Widget Apps)
```bash
# Build Finder-v2 widget (Vue 3 + TailwindCSS v4)
cd src/apps/widgets/finder_v2/app
npm install
npm run build  # Production build
npm run dev    # Development with hot reload

# Test Finder-v2 widget
npm run test          # Run tests
npm run test:coverage # Run with coverage
npm run lint          # ESLint check
```

### Testing Environment

#### Test Execution Methods
```bash
# 1. Django Test Runner (Recommended for Django tests)
docker exec ws_services bash /code/scripts/run_tests.sh <test_path>

# 2. Pytest Runner (For pytest-specific features)
docker exec ws_services bash /code/scripts/test_runner.sh <test_path>

# 3. Direct Python Execution (For standalone scripts)
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python <test_script>
```

#### Current Active Test Files (15 total, post-cleanup)
```bash
# Root level tests (standalone scripts)
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python tests/verify_production_csrf.py
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python tests/test_tailwind_classes_verification.py

# Widget security tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/test_widget_csrf_security.py

# Finder-v2 specific tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_integration.py
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_api_proxy.py
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py

# Theme system tests (requires selenium dependencies)
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/test_theme_system.py

# Run all tests in a directory
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/

# Interactive HTML tests (open in browser)
open tests/widget/test-calc-widget.html
open tests/widget/test_copy_functionality.html
```

#### Test Dependencies
```bash
# Install missing test dependencies in container
docker exec ws_services pip install beautifulsoup4 pytest pytest-django selenium

# Or add to pyproject.toml and rebuild container
# [tool.poetry.group.dev.dependencies]
# beautifulsoup4 = "^4.12.0"
# pytest = "^7.4.0"
# pytest-django = "^4.5.2"
# selenium = "^4.15.0"
```

#### Test Settings Configuration
- **Unit Tests**: Use `src.settings.test` (SQLite in-memory, fast)
- **Integration Tests**: Use `src.settings.dev_docker` (PostgreSQL, full environment)
- **Test Files**: Located in `/code/tests/` with organized structure
- **Documentation**: See `/code/tests/pytest-usage-guide.md` for comprehensive guide

### Database Access
```bash
# Access PostgreSQL database
docker exec -it postgres15 psql -U ws_services_user -d ws_services_db

# Check database connections
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py dbshell

# Database migrations
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py migrate
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py makemigrations
```

### Container Management
```bash
# Container status and health
docker ps --filter name=ws_services
docker logs ws_services --tail 50 -f

# Restart services
docker-compose restart web
docker-compose restart nginx

# Rebuild after dependency changes (REQUIRED)
docker-compose build web
docker-compose up -d

# Clean up Docker resources
docker system prune -f
```

### File System Paths (In Container)
```bash
# Application code
/code/src/

# Test files
/code/tests/

# Python packages
/root/.pyenv/versions/3.12.0/lib/python3.12/site-packages/

# Scripts
/code/scripts/

# Static files
/code/src/static/

# Vue.js widgets
/code/src/apps/widgets/finder_v2/app/
```

## Architecture Overview

### Widget System Architecture
This is a Django-based SaaS platform providing embeddable wheel/tire finder widgets for client websites. The system uses a multi-tenant architecture where each client gets customizable widgets with unique configurations.

**Core Components:**
- **Widget Types**: Base classes defining widget behavior (`src/apps/widgets/widget_type.py`)
- **API Proxy**: Secure API gateway with CSRF protection (`src/apps/widgets/api_proxy/`)
- **Widget Apps**: Individual widget implementations (finder, finder_v2, calc)
- **Admin Interface**: Django admin for widget configuration
- **Translation System**: Multi-language support for widgets

### Widget Request Flow
1. Client website loads widget JavaScript from services.wheel-size.com
2. Widget JavaScript generates CSRF token based on User-Agent
3. Widget makes API calls to `/widget/{type}/api/` endpoints with CSRF token
4. Server validates token via `WsProtectMixin` and proxies to backend API
5. Response returned to widget with proper CORS headers

### CSRF Protection Model
The widget system uses custom CSRF protection designed for cross-domain operation:
- **Token Generation**: Base64-encoded User-Agent with algorithmic transformation
- **Validation**: Server validates token on every API request regardless of origin
- **Security**: No hostname bypass vulnerabilities, strict token validation

### Key File Locations
- **Settings**: `src/settings/` (environment-specific configurations)
- **Widget Types**: `src/apps/widgets/{widget_name}/widget_type.py`
- **API Proxy**: `src/apps/widgets/api_proxy/views.py` (CSRF protection)
- **Templates**: `src/templates/widgets/{widget_name}/`
- **Static Files**: Widget build outputs in `src/apps/widgets/{widget_name}/static/`

### Database Configuration
- **Development**: PostgreSQL 15 via `postgres15` container
- **Testing**: SQLite in-memory for unit tests, PostgreSQL for integration tests
- **Multiple DBs**: `default`, `stg`, `prod` connections available
- **Container Access**: `docker exec -it postgres15 psql -U ws_services_user -d ws_services_db`

### Widget Development Patterns
- **Widget Type Registration**: All widget types auto-registered via imports in `widget_type.py`
- **Configuration**: Default configs in `default_config/config.py`
- **Themes**: Visual themes in `default_config/themes.py`
- **Forms**: Admin forms in `forms.py`
- **Translations**: JSON translation files in `translations/`

### Frontend Architecture (Finder-v2)
- **Vue 3**: Modern reactive frontend framework
- **TailwindCSS v4**: Utility-first CSS framework
- **Vite**: Build tool and development server
- **Vitest**: Testing framework

### Security Considerations
- **CSRF Token**: Required for all widget API requests
- **Cross-Origin**: Widgets work on client domains but maintain security
- **Widget CSRF Settings**: Configured per environment in Django settings
- **API Throttling**: Rate limiting on widget API endpoints

### Environment Variables (Docker Container)
Key environment variables automatically configured in `ws_services` container:
- `DJANGO_SETTINGS_MODULE=src.settings.dev_docker`: Development settings
- `PYTHONPATH=/code:/code/src`: Python import paths
- `DISABLE_LESS_COMPILATION=true`: Disable LESS compilation during tests
- `DEBUG=True`: Enable debug mode in development
- Widget CSRF settings configured in Django settings files

### Common Development Workflows

#### 1. Adding New Widget Type
```bash
# Create widget app structure
docker exec -it ws_services bash
cd /code/src/apps/widgets/
mkdir new_widget_name
# Create widget_type.py, forms.py, default_config/, etc.
```

#### 2. Frontend Development (Finder-v2)
```bash
# Vue.js development workflow
docker exec -it ws_services bash
cd /code/src/apps/widgets/finder_v2/app
npm install
npm run dev    # Development with hot reload
npm run build  # Production build

# Deploy changes
cd /code
./deploy-finder-v2.sh
```

#### 3. Testing Workflow
```bash
# Before committing code
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/

# After bug fixes
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/test_widget_csrf_security.py

# Before deployment
docker exec ws_services bash /code/scripts/run_tests.sh tests/
```

#### 4. Dependency Management
```bash
# Add new Python dependency
docker exec -it ws_services bash
cd /code
poetry add package-name

# Rebuild container after dependency changes (REQUIRED)
docker-compose build web
docker-compose up -d
```

#### 5. Troubleshooting Common Issues
```bash
# Container connection issues
docker ps --filter name=ws_services
docker-compose up -d web

# Import errors - check Python path
docker exec ws_services echo $PYTHONPATH

# Missing dependencies
docker exec ws_services pip install beautifulsoup4 pytest selenium

# Permission issues
docker exec ws_services ls -la /code/tests/
```


# Finder-v2 Vue.js Development Deployment Script
Automates the complete build-and-deploy cycle for finder-v2 widget
Usage: ./deploy-finder-v2.sh
Run from wheel-size-services project root directory 
Run every time after any changes in finder v2 widget


---
description: 
globs: 
alwaysApply: false
---
# Django Project Structure Guide

## Settings Configuration

### Environment-Specific Settings
- **Development**: [src/settings/dev_docker.py](mdc:src/settings/dev_docker.py) - Docker development environment
- **Production**: [src/settings/aws_prod.py](mdc:src/settings/aws_prod.py) - AWS production settings
- **Testing**: [src/settings/test.py](mdc:src/settings/test.py) - Optimized test settings with LESS compilation disabled
- **Base**: [src/settings/base.py](mdc:src/settings/base.py) - Shared Django settings

## Widget Architecture

### Common Widget Infrastructure
- **Base models**: [src/apps/widgets/common/models/config.py](mdc:src/apps/widgets/common/models/config.py) - `WidgetConfig` model
- **Subscriptions**: [src/apps/widgets/common/models/subscription.py](mdc:src/apps/widgets/common/models/subscription.py) - `WidgetSubscription` model
- **LESS compiler**: [src/apps/widgets/common/less/compiler.py](mdc:src/apps/widgets/common/less/compiler.py) - CSS compilation with test mode support

### Widget Types
- **Finder-v2**: [src/apps/widgets/finder_v2/](mdc:src/apps/widgets/finder_v2) - Modern Vue 3 + TailwindCSS widget
- **Legacy Finder**: [src/apps/widgets/finder/](mdc:src/apps/widgets/finder) - Legacy LESS-based widget
- **Calculator**: [src/apps/widgets/calc/](mdc:src/apps/widgets/calc) - Tire calculator widget

### API Proxy
- **Main proxy**: [src/apps/widgets/api_proxy/views.py](mdc:src/apps/widgets/api_proxy/views.py) - Widget API proxy with parameter normalization
- **URLs**: [src/apps/widgets/api_proxy/urls.py](mdc:src/apps/widgets/api_proxy/urls.py) - API routing

## Development Environment

### Docker Configuration
- **Docker Compose**: [docker-compose.yml](mdc:docker-compose.yml) - Development environment setup
- **Dockerfile**: [docker/Dockerfile](mdc:docker/Dockerfile) - Django container configuration

### Deployment Scripts
- **Finder-v2 deploy**: [deploy-finder-v2.sh](mdc:deploy-finder-v2.sh) - Vue.js build and deployment
- **Test runner**: [scripts/run_tests.sh](mdc:scripts/run_tests.sh) - Clean test execution

## Key Django Apps

### Portal
- **Main portal**: [src/apps/portal/](mdc:src/apps/portal) - User interface and authentication

### Widgets
- **Widget main**: [src/apps/widgets/main/](mdc:src/apps/widgets/main) - Widget management views
- **Common widgets**: [src/apps/widgets/common/](mdc:src/apps/widgets/common) - Shared widget functionality

### Sync
- **Data sync**: [src/apps/sync/](mdc:src/apps/sync) - Data synchronization utilities

## Templates and Static Files

### Templates
- **Widget templates**: [src/templates/widgets/](mdc:src/templates/widgets) - Widget HTML templates
- **Portal templates**: [src/templates/portal/](mdc:src/templates/portal) - Portal HTML templates

### Static Files
- **Widget static**: Widget-specific static files in respective app directories
- **Shared theme**: [src/shared/tailwind-theme.js](mdc:src/shared/tailwind-theme.js) - TailwindCSS theme configuration

## Testing Structure

### Test Organization
- **Widget tests**: [tests/widget/](mdc:tests/widget) - Widget-specific tests
- **General tests**: [tests/](mdc:tests) - General application tests
- **Test configuration**: [pytest.ini](mdc:pytest.ini) - Pytest settings
- `docs/development/pytest-usage-guide.md` - Pytest Usage Guide for wheel-size-services. This guide covers how to use pytest for testing in the wheel-size-services Docker environment.

## Documentation

### Development Docs
- **API documentation**: [docs/api/](mdc:docs/api) - API specifications
- **Development guides**: [docs/development/](mdc:docs/development) - Development workflows
- **Security docs**: [docs/security/](mdc:docs/security) - Security guidelines
- **Upgrade docs**: [docs/upgrade/](mdc:docs/upgrade) - Django upgrade documentation

## URL Configuration

### Main URLs
- **Root URLs**: [src/urls/services.py](mdc:src/urls/services.py) - Main URL configuration
- **Widget URLs**: [src/apps/widgets/urls.py](mdc:src/apps/widgets/urls.py) - Widget routing

## Database Models

### Widget Models
- **Widget Config**: Defined in [src/apps/widgets/common/models/config.py](mdc:src/apps/widgets/common/models/config.py)
- **Widget Subscription**: Defined in [src/apps/widgets/common/models/subscription.py](mdc:src/apps/widgets/common/models/subscription.py)

### Import Patterns
```python
# Correct imports for widget models
from src.apps.widgets.common.models.config import WidgetConfig
from src.apps.widgets.common.models.subscription import WidgetSubscription
```

## Development Commands

### Local Development (Updated Commands)
```bash
# Start development environment
docker-compose up -d

# Access Django shell
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py shell

# Run migrations
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py migrate

# Collect static files
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry run /root/.pyenv/versions/3.12.0/bin/python manage.py collectstatic --no-input"
```

### Admin Interface
- **URL**: `http://development.local:8000/admin/`
- **Models**: Registered in respective app `admin.py` files

## Current Project Status (Post-Cleanup)

### Test Directory Structure
The test directory has been comprehensively cleaned and organized:
- **15 active test files** covering security, widgets, themes, and integration
- **3 documentation files** with comprehensive guides
- **Clean structure** after removing 12 obsolete/temporary files

### Key Documentation Files
- **`/code/tests/README.md`** - Comprehensive test directory documentation
- **`/code/tests/pytest-usage-guide.md`** - Detailed pytest usage in Docker environment
- **`/code/tests/pytest-quick-start.md`** - Quick setup guide
- **`/code/docs/settings/docker-project-configuration.md`** - Docker environment details

### Docker Environment Best Practices
1. **Always use correct container name**: `ws_services` (not `web` or other names)
2. **Use full Python path**: `/root/.pyenv/versions/3.12.0/bin/python`
3. **Rebuild after dependency changes**: `docker-compose build web && docker-compose up -d`
4. **Check container status**: `docker ps --filter name=ws_services`
5. **Use proper settings modules**: `dev_docker` for development, `test` for testing

### Quick Reference Commands
```bash
# Essential container commands
docker ps --filter name=ws_services                    # Check status
docker logs ws_services --tail 50 -f                   # View logs
docker exec -it ws_services bash                       # Access shell
docker-compose restart web                             # Restart service

# Django management
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py shell
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py migrate

# Testing
docker exec ws_services bash /code/scripts/run_tests.sh tests/
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python tests/verify_production_csrf.py

# Static files collection
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry run /root/.pyenv/versions/3.12.0/bin/python manage.py collectstatic --no-input"
```

This documentation reflects the current state of the wheel-size-services project as of 2025-01-28, with accurate Docker commands, container names, and file paths for the production environment.